<template>
    <div class="slds-card slds-card_boundary">
        <div class="slds-card__body slds-card__body_inner">
            <!-- Summary Button -->
            <div class="slds-m-bottom_medium">
                <div class="button-container">
                    <lightning-button 
                        variant="brand" 
                        label="Summarize" 
                        onclick={handleSummaryClick}
                        disabled={isLoadingSummary}
                        class="equal-width-button">
                    </lightning-button>
                    <lightning-button 
                        variant="success" 
                        label="Insert" 
                        onclick={handleInsertClick}
                        disabled={isInsertDisabled}
                        class="equal-width-button">
                    </lightning-button>
                </div>
            </div>
            
            <!-- Summary Section -->
            <div class="summary-section">
                <!-- Loading Indicator -->
                <template if:true={isLoadingSummary}>
                    <div class="slds-card">
                        <div class="slds-card__header">
                            <h3 class="slds-text-heading_small">Conversation Summary</h3>
                        </div>
                        <div class="slds-card__body slds-card__body_inner slds-text-align_center">
                            <lightning-spinner alternative-text="Generating summary..." size="small"></lightning-spinner>
                            <p class="slds-text-body_small slds-m-top_x-small slds-text-color_weak">
                                {loadingMessage}
                            </p>
                        </div>
                    </div>
                </template>
                
                <!-- Summary Result Display -->
                <template if:true={summaryResult}>
                    <div class="slds-card">
                        <div class="slds-card__header">
                            <h3 class="slds-text-heading_small">Conversation Summary</h3>
                        </div>
                        <div class="slds-card__body slds-card__body_inner">
                            <lightning-textarea 
                                value={summaryResult} 
                                onchange={handleSummaryTextChange}
                                class="slds-text-body_regular summary-textarea"
                                variant="label-hidden"
                                rows="15">
                            </lightning-textarea>
                        </div>
                    </div>
                </template>
                
                <!-- Error Display -->
                <template if:true={summaryError}>
                    <div class="slds-notify slds-notify_alert slds-alert_error">
                        <span class="slds-assistive-text">Error</span>
                        <h2 class="slds-text-heading_small">
                            <lightning-icon icon-name="utility:error" size="x-small" class="slds-m-right_x-small"></lightning-icon>
                            {summaryError}
                        </h2>
                    </div>
                </template>
                
                <!-- Initial State Message -->
                <template if:true={showInitialMessage}>
                    <div class="slds-card">
                        <div class="slds-card__body slds-card__body_inner slds-text-align_center">
                            <lightning-icon icon-name="utility:summary" size="medium" class="slds-m-bottom_small"></lightning-icon>
                            <h3 class="slds-text-heading_small slds-m-bottom_small">Generate Conversation Summary</h3>
                            <p class="slds-text-body_regular slds-text-color_weak">
                                Click "Summarize" to create a summary of the current Agent Assist conversation.
                            </p>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>