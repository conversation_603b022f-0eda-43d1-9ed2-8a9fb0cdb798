public with sharing class CCM_WarrantyReturnShareHandle extends CCM_Core implements Triggers.Handler{
    private List<Log__c> lstLog;
    public void handle() {
        lstLog = new List<Log__c>();
        Set<Id> setCustomerId = new Set<Id>();
        List<Customer_Profile__c> lstValidCustomerProfile = new List<Customer_Profile__c>();
        Map<String,Integer> custMap = new Map<String,Integer>();
        Map<String,Customer_Profile__c> customerProfileMap = new Map<String,Customer_Profile__c>();
        filterValidCustomerProfile(lstValidCustomerProfile, setCustomerId);
        if(setCustomerId.size()>0){
            calculatePartsCostCal(setCustomerId,custMap);
            getCustomerProfileInfo(setCustomerId,customerProfileMap);
            calculateWarrantyReturnShare(lstValidCustomerProfile,customerProfileMap,custMap);
            if (!lstLog.isEmpty()) {
                CCM_DmlUtils.doInsertIgnoreResults(lstLog, getClassName(), getMethodName());
            }
        }

    }

    private void calculateWarrantyReturnShare(
        List<Customer_Profile__c> lstValidCustomerProfile,
        Map<String,Customer_Profile__c> customerProfileMap,
        Map<String,Integer> custMap
    ) {
        for (Customer_Profile__c objCP : lstValidCustomerProfile) {
            Integer customerProfileYear = Integer.valueOf(objCP.Effective_Year__c);
            String reverseOrderkey = objCP.Customer__c + objCP.Effective_Year__c;
            if(custMap.containsKey(reverseOrderkey)) {
                objCP.Reverse_Order_Count_YTD__c = custMap.get(reverseOrderkey);
            } else {
                objCP.Reverse_Order_Count_YTD__c = 0;
            }

            if(Trigger.isInsert && customerProfileYear >= 2024){
                if(customerProfileMap.containsKey(objCP.Customer__c)){
                    if(customerProfileMap.get(objCP.Customer__c).Warranty_Return_Item_Total_Amount__c !=null){
                        objCP.Warranty_Return_Share__c = customerProfileMap.get(objCP.Customer__c).Warranty_Return_Item_Total_Amount__c;
                    }
                    if(customerProfileMap.get(objCP.Customer__c).Warranty_Return_Item_Total_Amount__c != null){
                        objCP.Warranty_Return_Item_Total_Amount__c = customerProfileMap.get(objCP.Customer__c).Warranty_Return_Item_Total_Amount__c;
                    }
                    if(customerProfileMap.get(objCP.Customer__c).Warranty_Return_Share__c != null){
                        objCP.Warranty_Return_Share__c = customerProfileMap.get(objCP.Customer__c).Warranty_Return_Share__c;
                    }
                }
            } else if(Trigger.isUpdate && customerProfileYear >= 2024) {
                if(objCP.Order_Total_Amount__c != null && objCP.Order_Total_Amount__c > 0){
                    objCP.Warranty_Return_Share__c = ((objCP.Warranty_Return_Item_Total_Amount__c/ objCP.Order_Total_Amount__c) * 100).setScale(2);
                }
            }
        }
    }

    private void filterValidCustomerProfile(List<Customer_Profile__c> lstValidCustomerProfile, Set<Id> setCustomerId) {
        List<Customer_Profile__c> lstNew = (List<Customer_Profile__c>) Trigger.new;
        for (Customer_Profile__c objCP : lstNew) {
            if (String.isNotEmpty(objCP.Customer__c)) {
                setCustomerId.add(objCP.Customer__c);
            }
            lstValidCustomerProfile.add(objCP);
        }
    }

    private void calculatePartsCostCal(Set<Id> setCustomerId,Map<String,Integer> custMap) {
        List<AggregateResult > custList = [SELECT Customer__c CUSTOMER_ID,CALENDAR_YEAR(CreatedDate) REVERSEORDER_YEAR,COUNT(Id) REVERSEORDER_COUNT
                                        FROM Reverse_Order_Request__c
                                        WHERE  Customer__c=:setCustomerId
                                        GROUP By Customer__c,CALENDAR_YEAR(CreatedDate)];
        for (AggregateResult cu : custList) {
            String key = (String)cu.get('CUSTOMER_ID') + cu.get('REVERSEORDER_YEAR');
            custMap.put(key,(Integer)cu.get('REVERSEORDER_COUNT'));
        }
    }

    private void getCustomerProfileInfo(Set<Id> setCustomerId,Map<String,Customer_Profile__c> customerProfileMap){
        for(Customer_Profile__c objCP : [SELECT Id, Customer__c, Warranty_Return_Item_Total_Amount__c,Order_Total_Amount__c
                                        FROM Customer_Profile__c
                                        WHERE Customer__c IN :setCustomerId]){
            String key = objCP.Customer__c;
            if(!customerProfileMap.containsKey(key) && ((objCP.Warranty_Return_Item_Total_Amount__c != null && objCP.Warranty_Return_Item_Total_Amount__c > 0) || (objCP.Order_Total_Amount__c != null && objCP.Order_Total_Amount__c > 0))){
                customerProfileMap.put(key,objCP);
            }
        }
    }
}