public with sharing class CCM_Community_OrderApplicationListCtl {
    public static String aggregateSqlPO = 'SELECT count(Id) total1 FROM Purchase_Order__c WHERE IsDeleted = false '
                + 'AND Sync_Status__c != \'Success\' ';

    // update, napoleon, purchase order, purchase_order__c without convertyCurrency
    public static String sqlString = 'SELECT Id, '
                    + 'Name, '
                    + 'Billing_Address__c, '
                    + 'Billing_Address__r.Name, '
                    + 'Billing_Address__r.Address1__c, '
                    + 'Billing_Address__r.Address2__c, '
                    + 'Billing_Address__r.Country__c, '
                    + 'Billing_Address__r.State__c, '
                    + 'Billing_Address__r.City__c, '
                    + 'Billing_Address__r.Contact__c, '
                    + 'Billing_Address__r.Contact__r.Name, '
                    + 'Billing_Address_Name__c, '
                    + 'Shipping_Address__c, '
                    + 'Shipping_Address__r.Name, '
                    + 'Shipping_Address_Name__c, '
                    + 'Shipping_Address__r.Address1__c, '
                    + 'Shipping_Address__r.Address2__c, '
                    + 'Shipping_Address__r.Country__c, '
                    + 'Shipping_Address__r.State__c, '
                    + 'Shipping_Address__r.City__c, '
                    + 'Shipping_Address__r.Contact__c, '
                    + 'Shipping_Address__r.Contact__r.Name, '
                    + 'Shipping_Method__c, '
                    + 'Shipping_By__c, '
                    + 'Delivery_Supplier__c, '
                    + 'Customer__c, '
                    + 'Customer__r.Name,'
                    + 'Customer__r.AccountNumber,'
                    + 'Status__c, '
                    + 'tolabel(Sync_Status__c) syncStatus, '
                    + 'Approval_Status__c, '
                    + 'Submit_Date__c, '
                    + 'Total_quantity__c, '
                    + 'Total_Price__c, '
                    + 'Total_Amount__c, '
                    + 'Notes__c, '
                    + 'IsDeleted, '
                    + 'Email__c, '
                    + 'Phone__c, '
                    + 'Fax_Number__c, '
                    + 'Customer_PO_Num__c,'
                    + 'Expected_Delivery_Date__c,'
                    + 'Customer_Freight_Account__c,'
                    + 'tolabel(Payment_Term__c) paymentTermLabel, '
                    + 'tolabel(Freight_Term__c) freightTermLabel, '
                    + 'Sales_Rep__c,'
                    + 'Sales_Manager__c,'
                    + 'Sales_Manager__r.Name,'
                    + 'Product_Price__c,'
                    + 'CurrencyIsoCode,'
                    + 'Brand_Scope__c,'
                    + 'RecordType.DeveloperName,'
                    + 'Is_Delegate__c,'
                    + 'CreatedBy.Name,'
                    + '(SELECT Id, '
                    + '     Name, '
                    + '     Brand__c, '
                    + '     Product__c, '
                    + '     ProductCode__c, '
                    + '     Product__r.Name, '
                    + '     Product__r.Description, '
                    + '     Product__r.Item_Number__c, '
                    + '     Product__r.ProductCode, '
                    + '     Product__r.Brand_Name__c, '
                    + '     Quantity__c, '
                    + '     List_Price__c, '
                    + '     Unit_Price__c, '
                    + '     MSRP__c, '
                    + '     Sub_Total__c '
                    + '     FROM Purchase_Order_Items__r ) '
                    + 'FROM Purchase_Order__c '
                    + 'WHERE IsDeleted = false '
                    + 'AND Sync_Status__c != \'Success\' ';

    @AuraEnabled
    public static String initData(Decimal pageNumber, Integer pageSize,String filterString, Boolean isInit){
        InitData initD = new InitData();
        if (isInit){
            List<Util.SelectItem> brandNameOpts = Util.getSelectOptions(new Sales_Program__c(),'Brands__c');
            initD.brandOptions = brandNameOpts;
        }

        // update, napoleon, 23-1-5, cca cs team own delegate permissions
        List<User> lstCurUser = [
                                SELECT
                                    Id,Username,Profile.Name,UserRole.DeveloperName
                                FROM User
                                WHERE ID = :UserInfo.getUserId() LIMIT 1];

        for (User usrTmp : lstCurUser) {
            // check if user can delegate order at inner system;
            if (
                Label.CCM_Order_Delegation_Available_Username_List.split(CCM_Constants.SEMICOLON_REGEXP_SPACE_IGNORED).contains(lstCurUser[0].Username)
                || Label.CCM_Order_Delegation_Available_Profile_List.split(CCM_Constants.SEMICOLON_REGEXP_SPACE_IGNORED).contains(lstCurUser[0].Profile.Name)
                ) {
                initD.hasDelegatePermission = true;
            }
            // check if user can delegate parts order at inner system;
            if(
                Label.CCM_Parts_Order_Delegation_Available_Profile_List.split(CCM_Constants.SEMICOLON_REGEXP_SPACE_IGNORED).contains(lstCurUser[0].Profile.Name)
                || ((lstCurUser[0].UserRole.DeveloperName.contains('CA') && CCM_Constants.CANADA_CS_TEAM_USER_ROLE_DEVELOPER_NAME.contains(lstCurUser[0].UserRole.DeveloperName))
                || !(lstCurUser[0].UserRole.DeveloperName.contains('CA')))
                ){
                initD.hasPartsDelegatePermission = true;
            }
        }
        // update end

        User currentUsr = Util.getUserInfo(UserInfo.getUserId());
        if(CCM_Constants.CANADA_USER_ROLE_DEVELOPER_NAME.contains(currentUsr.UserRole.DeveloperName)){
            initD.isCCA = true;
        }

        Integer offset = ((Integer)pageNumber - 1) * pageSize;
        String filterCondition = getFilterCondition(filterString, initD.isOnlyDelegateParts);
        sqlString += filterCondition;
        //计算Purchase Order总数
        aggregateSqlPO += filterCondition;
        sqlString += ' ORDER BY CreatedDate DESC LIMIT 2000 ';

        List<DataTableWrapper> wrappers = new List<DataTableWrapper>();
        List<Purchase_Order__c> allOrderList = Database.query(sqlString);

        FilterWrapper filters = (FilterWrapper)JSON.deserialize(filterString, FilterWrapper.class);
        if(String.isNotBlank(filters.model)){
            Map<Id,Purchase_Order__c> poIdMapToPO = new Map<Id,Purchase_Order__c>();
            for(Purchase_Order__c po : allOrderList){
                poIdMapToPO.put(po.Id, po);
            }

            allOrderList.clear();
            Set<Purchase_Order__c> poSet = new Set<Purchase_Order__c>();
            String model = '%'+filters.model+'%';
            for(Purchase_Order_Item__c poi : [SELECT Id,Purchase_Order__c FROM Purchase_Order_Item__c WHERE Purchase_Order__c IN: poIdMapToPO.keySet() AND ProductCode__c LIKE: model]){
                poSet.add(poIdMapToPO.get(poi.Purchase_Order__c));

            }
            allOrderList.addAll(poSet);
        }

        if (allOrderList != null && allOrderList.size() > 0){
            for (Purchase_Order__c po : allOrderList){
                DataTableWrapper wrapper = new DataTableWrapper();
                wrapper.id = po.Id;
                wrapper.orderNumber = po.Name;
                wrapper.poNumber = po.Name;
                wrapper.customerPONumber = po.Customer_PO_Num__c;
                if (po.Submit_Date__c != null){
                    wrapper.submitDate = String.valueOf(Date.valueOf(po.Submit_Date__c));
                }

                wrapper.createdBy = po.CreatedBy.Name;
                wrapper.status = po.Status__c;
                wrapper.currencyIsoCode = po.CurrencyIsoCode;
                wrapper.totalPrice = String.valueOf(po.Total_Amount__c);
                if (po.RecordType.DeveloperName == 'Place_Order'){
                    wrapper.orderType = 'Order';
                }else if (po.RecordType.DeveloperName == 'Place_Parts_Order'){
                    wrapper.orderType = 'Parts Order';
                }

                // 临时关闭 edit 功能
                if (Label.A_LOCK_ORDER_FUNC_TMP == 'True') {
                    if (po.Status__c == 'Draft'){
                        wrapper.editStyleCss = 'hidebtn';
                        wrapper.deleteStyleCss = 'showbtn';
                    } else {
                        wrapper.editStyleCss = 'hidebtn';
                        wrapper.deleteStyleCss = 'hidebtn';
                    }
                    // 保持原来的逻辑
                } else {
                    if (po.Status__c == 'Draft'){
                        wrapper.editStyleCss = 'showbtn';
                        wrapper.deleteStyleCss = 'showbtn';
                    } else {
                        wrapper.editStyleCss = 'hidebtn';
                        wrapper.deleteStyleCss = 'hidebtn';
                    }
                }

                wrapper.customerName = po.Customer__r == null ? '' : po.Customer__r.Name;
                wrapper.accountNumber = po.Customer__r == null ? '' : po.Customer__r.AccountNumber;
                if (String.isNotBlank(po.Brand_Scope__c)){
                    List<String> brandNames = po.Brand_Scope__c.split('&');
                    Util.SelectItem brandItem = Util.getBrandOption(brandNames);
                    wrapper.brand = brandItem.label;
                }
                wrapper.syncStatus =  (String)po.get('syncStatus');
                wrappers.add(wrapper);
            }
        }

        if (wrappers != null && wrappers.size() > 0){
            /*initD.allDatas = wrappers;*/
            //算出Purchase Order数据的总量
            List<AggregateResult> resultPO = Database.query(aggregateSqlPO);
            Integer countPO = (Integer) resultPO[0].get('total1');
            initD.totalRecords = countPO;

            if(String.isNotBlank(filters.model)){
                initD.totalRecords = allOrderList.size();
            }
            initD.currentData = getCurrentData(wrappers, pageNumber, pageSize);
        }

        return JSON.serialize(initD);
    }

    //根据RecordId获取Object Name
    @AuraEnabled
    public static String getObjectName(String recordId){
        return JSON.serialize(Util.getObjectInfo(recordId));
    }

    public static String getFilterCondition(String filterString, Boolean isOnlyDelegateParts){
        String condicationStr = '';
        if (String.isNotBlank(filterString)){
            FilterWrapper filters = (FilterWrapper)JSON.deserialize(filterString, FilterWrapper.class);
            if (String.isNotBlank(filters.poRecordType) && !isOnlyDelegateParts){
                condicationStr += ' AND RecordType.DeveloperName =\'' + filters.poRecordType.trim() + '\'';
            }else if(isOnlyDelegateParts){
                condicationStr += ' AND RecordType.DeveloperName = \'Place_Parts_Order\'';
            }
            if (String.isNotBlank(filters.poNumber)) {
                condicationStr += ' AND Name LIKE \'%' + filters.poNumber.trim() + '%\'';
            }
            /*if (String.isNotBlank(filters.orderNumber)) {
                condicationStr += ' AND Name LIKE \'%' + filters.orderNumber.trim() + '%\'';
            }*/
            if (String.isNotBlank(filters.cutomerPoNum)) {
                condicationStr += ' AND Customer_PO_Num__c LIKE \'%' + filters.cutomerPoNum.trim() + '%\'';
            }
            if (String.isNotBlank(filters.submitDateMin)) {
                String minDate = filters.submitDateMin + 'T00:00:00Z';
                condicationStr += ' AND Submit_Date__c >= ' + minDate;
            }
            if (String.isNotBlank(filters.submitDateMax)) {
                String maxDate = filters.submitDateMax + 'T23:59:00Z';
                condicationStr += ' AND Submit_Date__c <= ' + maxDate;
            }
            if (String.isNotBlank(filters.status)) {
                condicationStr += ' AND Status__c =\'' + filters.status + '\'';
            }

            if (String.isNotBlank(filters.customerName)) {
                String name = filters.customerName.trim().replace('\'','\\\'');
                condicationStr += ' AND Customer__r.Name LIKE \'%' + name + '%\'';
            }
            if (String.isNotBlank(filters.accountNumber)) {
                condicationStr += ' AND Customer__r.AccountNumber =\'' + filters.accountNumber.trim() + '\'';
            }
            // 2022-07-14: 添加customer owner搜索条件
            if (String.isNotBlank(filters.customerOwner)) {
                condicationStr += ' AND Customer__r.OwnerId =\'' + filters.customerOwner.trim() + '\'';
            }

            if (filters.brand != null && filters.brand.size() > 0){
                Boolean isFirst = true;
                for(String str : filters.brand){
                    if (isFirst){
                        condicationStr += ' AND ( Brand_Scope__c LIKE \'%' + str.trim() + '%\'';
                    }else {
                        condicationStr += ' OR Brand_Scope__c LIKE \'%' + str.trim() + '%\'';
                    }
                    isFirst = false;
                }

                condicationStr += ' )';
            }
            if (String.isNotBlank(filters.model)) {

                sqlString = sqlString.replaceAll('%%%', ' WHERE ProductCode__c =\'' + filters.model.trim() + '\'');

            }
            if (String.isNotBlank(filters.shipto)) {
                condicationStr += ' AND (Shipping_Address__r.State__c LIKE \'%' + filters.shipto.trim() + '%\'';
                condicationStr += ' OR Shipping_Address__r.City__c LIKE \'%' + filters.shipto.trim() + '%\')';
            }

            if(String.isNotBlank(filters.createdBy)) {
                condicationStr += ' AND CreatedById =\'' + filters.createdBy.trim() + '\'';
            }
        }
        return condicationStr;
    }

    //分页
    public static List<DataTableWrapper> getCurrentData(List<DataTableWrapper> allData,  Decimal pageNumber, Integer pageSize) {
        List<DataTableWrapper> currentData = new List<DataTableWrapper>();
        Integer min = ((Integer)pageNumber - 1) * pageSize;
        Integer max = (Integer)pageNumber * pageSize -1;
        for (Integer i = min ; i <= max; i++ ) {
            if (i < allData.size()) {
                currentData.add(allData.get(i));
            }
        }
        return currentData;
    }

    //删除行记录
    @AuraEnabled
    public static String deleteOrderInfo(String recordId) {
        String result = CCM_Contants.deleteOrderInfo(recordId);
        return result;
    }

    //过滤条件
    public class FilterWrapper{
        public String poRecordType {get; set;}
        public String poNumber {get; set;}
        public String orderNumber {get; set;}
        public String cutomerPoNum {get; set;}
        public String submitDateMin {get; set;}
        public String submitDateMax {get; set;}
        public String status {get; set;}
        public List<String> brand {get; set;}
        public String customerName {get; set;}
        public String accountNumber {get; set;}
        public String customerOwner {get; set;} // 2022-07-14: 添加customer owner查询
        public String model {get; set;}
        public String shipto {get; set;}
        public String createdBy {get;set;}

        public FilterWrapper(){
            this.brand = new List<String>();
        }
    }

    //Order List View
    public class DataTableWrapper {
        @AuraEnabled public String id {get; set;}
        @AuraEnabled public String orderNumber  {get;set;}
        @AuraEnabled public String poNumber {get; set;}
        @AuraEnabled public String customerPONumber {get; set;}
        @AuraEnabled public String submitDate {get; set;}
        @AuraEnabled public String status {get; set;}
        @AuraEnabled public String createdBy {get; set;}
        @AuraEnabled public String orderType {get; set;}
        @AuraEnabled public String totalPrice {get; set;}
        @AuraEnabled public String editStyleCss {get; set;}
        @AuraEnabled public String deleteStyleCss {get; set;}
        @AuraEnabled public String customerName {get; set;}
        @AuraEnabled public String currencyIsoCode {get; set;}
        @AuraEnabled public String accountNumber {get; set;}
        @AuraEnabled public String brand {get; set;}
        @AuraEnabled public String syncStatus {get; set;}
        public DataTableWrapper(){
            this.submitDate = '';
            this.totalPrice = '0.00';
            this.editStyleCss = 'hidebtn';
            this.deleteStyleCss = 'hidebtn';
        }
    }

    //最终返回的结果
    public class InitData {
        @AuraEnabled public List<DataTableWrapper> currentData {get; set;}
        /*@AuraEnabled List<DataTableWrapper> allDatas {get; set;}*/
        @AuraEnabled public Integer totalRecords {get; set;}
        @AuraEnabled public List<Util.SelectItem> brandOptions {get; set;}
        @AuraEnabled public Boolean hasDelegatePermission {get; set;}
        // napoleon, 23-1-5, inner parts order permission
        @AuraEnabled public Boolean hasPartsDelegatePermission {get; set;}
        @AuraEnabled public Boolean isCCA {get; set;}
        @AuraEnabled public Boolean isOnlyDelegateParts {get; set;}

        public InitData(){
            this.currentData = new List<DataTableWrapper>();
            /*this.allDatas = new List<DataTableWrapper>();*/
            this.totalRecords = 0;
            this.brandOptions = new List<Util.SelectItem>();
            this.hasDelegatePermission = false;
            this.isOnlyDelegateParts = false;
            this.isCCA = false;
            // napoleon, 23-1-5, inner parts order permission
            this.hasPartsDelegatePermission = false;
        }
    }
}