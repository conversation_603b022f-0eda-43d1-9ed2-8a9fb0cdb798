<!--
 - Created by gluo006 on 7/24/2019.
 -->

<aura:component description="CCM_Community_Service" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes"
                controller="CCM_NewClaim"
                access="global">
    <ltng:require scripts="{!join(',', $Resource.Validator)}"/>
    <aura:attribute name="claimNo" type="String"/>
    <aura:attribute name="replacementCost" type="Decimal" default="0"/>
    <aura:attribute name="brand" type="String"/>
    <aura:attribute name="brandList" type="List" default=""/>
    <aura:attribute name="boolIsAltaQuip" type="Boolean" default="false" />
    <aura:attribute name="SerialNum" type="String"/>
    <aura:attribute name="warrantyItemId" type="String" default=""/>
    <aura:attribute name="modelNum" type="String"/>
    <aura:attribute name="modelNumList" type="List"/>
    <aura:attribute name="productName" type="String"/>
    <aura:attribute name="boolPickup" type="Boolean" default="false"/>
    <aura:attribute name="iscustomerpickupfee" type="Boolean" default="true"/>
    <aura:attribute name="isValidRecall" type="Boolean" default="false"/>
    <aura:attribute name="strUsePickup" type="String" default="No"/>
    <aura:attribute name="decPickupDistance" type="Decimal" default="0"/>
    <aura:attribute name="decPickupFeeSubtotal" type="Decimal" default="0"/>
    <aura:attribute name="claimStatus" type="String"/>
    <aura:attribute name="paymentStatus" type="String"/>
    <aura:attribute name="firstName" type="String"/>
    <aura:attribute name="lastName" type="String"/>
    <aura:attribute name="emailAddress" type="String"/>
    <aura:attribute name="placeOfPurchase" type="String"/>
    <aura:attribute name="purchaseDate" type="String"/>
    <aura:attribute name="formattedPurchaseDate" type="String"/>
    <aura:attribute name="purchaseUseType" type="String"/>
    <aura:attribute name="warrantyStatus" type="String" default=""/>
    <aura:attribute name="warrantyStatusFrench" type="String" default=""/>
    <aura:attribute name="realWarrantyStatus" type="String" default=""/>
    <aura:attribute name="additionalDes" type="String"/>
    <aura:attribute name="overTime" type="String"/>
    <aura:attribute name="malfunctionParts" type="List"/>
    <aura:attribute name="majorIssue" type="String"/>
    <aura:attribute name="partsItemList" type="List" default="[]"/>
    <aura:attribute name="auditTrailList" type="List"/>
    <aura:attribute name="labourHours" type="Decimal" default="0"/>
    <aura:attribute name="finialLaborHour" type="Decimal" default="0"/>
    <aura:attribute name="majorIssueObj" type="Map" default="{Name: '', Id: ''}"/>
    <aura:attribute name="productId" type="String"/>
    <aura:attribute name="currencySymbol" type="String"/>
    <aura:attribute name="repairDescription" type="String"/>
    <aura:attribute name="overTimeHour" type="String" default=""/>
    <aura:attribute name="additionalTimeHour" type="String" default="0"/>
    <aura:attribute name="overTimeDescription" type="String"/>
    <aura:attribute name="partsCost" type="String" default="0"/>
    <aura:attribute name="markup" type="String" default="0"/>
    <aura:attribute name="isshowmarkup" type="Boolean" default="false"/>
    <aura:attribute name="isaq" type="Boolean" default="false"/>
    <aura:attribute name="markupbfb" type="String" default="0"/>
    <aura:attribute name="totalPrice" type="String" default="0"/>
    <aura:attribute name="LaborRate" type="Decimal" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="dropOfDate" type="String"/>
    <aura:attribute name="repairDate" type="String"/>
    <aura:attribute name="isDisabled" type="Boolean" default="false"/>
    <aura:attribute name="isAddPartsDisabled" type="Boolean" default="false"/>
    <aura:attribute name="diagnosisFee" type="String" default="15"/>
    <aura:attribute name="laborCostSubtotal" type="Decimal" default="0"/>
    <aura:attribute name="warrantyId" type="String" default=""/>
    <aura:attribute name="activeSections" type="List" default="" />
    <aura:attribute name="auditComments" type="String" default="" />
    <aura:attribute name="serviceOption" type="String" default="" />
    <aura:attribute name="repairType" type="String"/>
    <aura:attribute name="recallOption" type="String"/>
    <aura:attribute name="failureCode" type="String" default="" />
    <aura:attribute name="failureCodeList" type="List" default="" />
    <aura:attribute name="rateList" type="List" default="" />
    <aura:attribute name="project" type="String"/>
    <aura:attribute name="projectList" type="List"/>
    <aura:attribute name="isView" type="Boolean" default="false"/>
    <aura:attribute name="isEdit" type="Boolean" default="false"/>
    <aura:attribute name="isDraft" type="Boolean" default="false"/>
    <aura:attribute name="draftPartsChanged" type="Boolean" default="false" />
    <aura:attribute name="inventory" type="String" default=""/>
    <aura:attribute name="recallOptionList" type="List"/>
    <aura:attribute name="expirationDate" type="String"/>
    <aura:attribute name="repairPartsName" type="String"/>
    <aura:attribute name="isDiagramDisabled" type="Boolean" default="false"/>
    <aura:attribute name="minDate" type="Date" default="" />
    <aura:attribute name="replacementBaseFee" type="Decimal" default="0"/>
    <aura:attribute name="isNotDistributor" type="Boolean" default="true" />

     <aura:attribute name="isPickUp" type="Boolean" default="false" />

    <!--address info-->
    <aura:attribute name="ShippingStreet" type="String" default="street"/>
    <aura:attribute name="ShippingCity" type="String" default="city"/>
    <aura:attribute name="ShippingCountry" type="String" default="country"/>
    <aura:attribute name="ShippingState" type="String" default="province"/>
    <aura:attribute name="ShippingPostalCode" type="String" default="postalCode"/>
    <aura:attribute name="replacementFirstName" type="String" default=""/>
    <aura:attribute name="replacementLastName" type="String" default=""/>
    <aura:attribute name="replacementPhone" type="String" default=""/>

    <!--modal-->
    <aura:attribute name="vfHost" type="String" default="msdev--steven.lightning.force.com"/>
    <aura:attribute name="prefixURL" type="String" default="/sfc/servlet.shepherd/version/renditionDownload?rendition=ORIGINAL_Png&amp;versionId="/>
    <aura:attribute name="contents" type="List" default="[]"/>
    <aura:attribute name="columns" type="List" default="[]"/>
    <aura:attribute name="explosiveDataList" type="List" default="[]"/>
    <aura:attribute name="billToList" type="List" default="[]"/>
    <aura:attribute name="billToId" type="String" default=""/>
    <aura:attribute name="isRecall" type="Boolean" default="false"/>
<!--    //to identify the model is battery or charger-->
    <aura:attribute name="isProduct" type="Boolean" default="true"/>
    <aura:attribute name="isShowAdditionalInformation" type="Boolean" default="false"/>

    <aura:attribute name="isServiceLive" type="Boolean" default="false" />
    <aura:attribute name="zipCode" type="String" default=""/>
    <aura:attribute name="zipCodeSearch" type="String" default=""/>
    <aura:attribute name="tierId" type="String" default=""/>
    <aura:attribute name="tierlist" type="List" default="[]"/>

    <aura:attribute name="sequenceNo" type="String" default=""/>
    <aura:attribute name="versionList" type="List" default="[]"/>
    <aura:attribute name="version" type="String" default=""/>

    <aura:attribute name="orgCode" type="String" default="CCA" />
    <aura:attribute name="gsthst" type="Decimal" />
    <aura:attribute name="gst" type="Decimal" />
    <aura:attribute name="hst" type="Decimal" />
    <aura:attribute name="qst" type="Decimal" />
    <aura:attribute name="pst" type="Decimal" />
    <aura:attribute name="totalTax" type="Decimal" />

    <aura:attribute name="showAdditionalTime" type="Boolean" default="true" />
    <aura:attribute name="showActualTime" type="Boolean" default="false" />
    <aura:attribute name="explanationOptions" type="List" default="[]" />
    <aura:attribute name="explanationOptionSelected" type="String" default="" />

    <aura:attribute name="partsCombimeLaborTimeCode" type="List" default="[]" />
    <aura:attribute name="selectedItemId" type="String" default="" />
    <!-- add haibo: french -->
    <aura:attribute name="purchaseUseTypeList" type="List" default="[]" />
    <aura:attribute name="purchaseUseTypeFrench" type="String"/>

    <aura:attribute name="Level1ExplanationOptions" type="List" default="[]" />
    <aura:attribute name="Level1ExplanationOption" type="String"/>
    <aura:attribute name="Level2ExplanationOptions" type="List" default="[]"/>
    <aura:attribute name="Level2ExplanationOption" type="String"/>
    <aura:attribute name="Level2EORequired" type="Boolean" default="false" />
    <aura:attribute name="Level2ExtraRequired" type="Boolean" default="false" />
    <aura:attribute name="partsOption" type="List" default="[]" />
    <aura:attribute name="partsOptionValues" type="List" default="[]" />
    <aura:attribute name="Level2ExplanationOptionIsError" type="Boolean" default="true"/>
    <aura:attribute name="isShowLevel2ExplanationOption" type="Boolean" default="true"/>
    <aura:attribute name="AltaquipClaimRestrictionName" type="Boolean" default="false"/>
    <aura:attribute name="AltaquipClaimRestrictionModel" type="Boolean" default="false"/>

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:handler event="c:CCM_ProductLookUpChange" name="getProductChange" action="{!c.getMasterProductId}"/>
    <!--check replacement clear the major issue value in the child component-->
    <aura:registerEvent name="onClear" type="c:CCM_ProductLookUpChange"/>
    <aura:handler name="change" value="{!v.repairDate}" action="{!c.handleRepairDateChange}"/>
    <aura:handler name="change" value="{!v.SerialNum}" action="{!c.trimSerialNumber}"/>
    <aura:handler name="change" value="{!v.dropOfDate}" action="{!c.handleDropOfDateChange}"/>
    <aura:handler name="change" value="{!v.zipCode}" action="{!c.handleZipCodeChange}"/>
    <ltng:require styles="{!$Resource.swiperCSS}" />
    <ltng:require scripts="{!$Resource.swiperJS}"
                  afterScriptsLoaded="{!c.afterScriptsLoaded}" />
    <aura:handler name="change" value="{!v.labourHours}" action="{!c.changeLabourHour}"/>
    <aura:handler name="change" value="{!v.LaborRate}" action="{!c.getFinialLaborHour}" />
    <aura:handler name="change" value="{!v.selectedItemId}" action="{!c.selectInventory}" />
    <aura:handler name="change" value="{!v.tierId}" action="{!c.selectTier}" />
    <aura:handler name="change" value="{!v.purchaseDate}" action="{!c.handlePurchaseDateChange}"/>
    <aura:handler name="change" value="{!v.firstName}" action="{!c.handleAltaquipClaimRestriction}"/>
    <aura:handler name="change" value="{!v.lastName}" action="{!c.handleAltaquipClaimRestriction}"/>
    <aura:handler name="change" value="{!v.modelNum}" action="{!c.handleAltaquipClaimRestriction}"/>

    <section class="slds-p-around_x-small">
        <lightning:spinner variant="brand" class="spinner slds-hide" alternativeText="{!$Label.c.CCM_Portal_Loading}" size="large" aura:id="spinner"/>
        <div class="slds-p-around_medium slds-text-heading_small">
            {!v.claimNo}
        </div>
        <article class="slds-card">
            <div class="slds-grid">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__body">
                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                              title="{!$Label.c.CCM_Portal_BasicInformation}">
                                <span><strong>{!$Label.c.CCM_Portal_BasicInformation}</strong></span>
                         </span>
                        </h2>
                    </div>
                </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                <div class="">
                    <div class="customerInfo">
                        <div class="slds-grid slds-wrap secondLine">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                <label class="slds-form-element__label">{!$Label.c.CCM_Portal_ClaimStatus}:</label>
                                {!(or(v.isView, v.isEdit)? v.claimStatus : $Label.c.CCM_Portal_Draft)}
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                <label class="slds-form-element__label">{!$Label.c.CCM_Portal_PaymentStatus}:</label>
                                {!(or(v.isView, v.isEdit)? v.paymentStatus : $Label.c.CCM_Portal_NA)}
                            </div>
                        </div>
                        <div class="slds-grid slds-wrap firstLine">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium field-required">
                                <lightning:select name="" label="{!$Label.c.CCM_Portal_Brand}" aura:id="brand" value="{!v.brand}" >
                                    <option text="{!$Label.c.CCM_Portal_NonePlaceTips}" value="" ></option>
                                    <aura:iteration items="{!v.brandList}" var="brand" indexVar="index">
                                        <option text="{!brand}" value="{!brand}" selected="{!(brand == v.brand)}"  ></option>
                                    </aura:iteration>
                                </lightning:select>
                                <div aura:id="brand-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium field-required">
                                <lightning:input aura:id="serialNumber" label="{!$Label.c.CCM_Portal_SerialNumber}" value="{!v.SerialNum}" class="field-required" onblur="{!c.getInfoBySerialNumber}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255"/>
                                <div aura:id="serialNumber-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                <lightning:select name="" label="{!$Label.c.CCM_Portal_ModelNumber}" aura:id="modelNumber" value="{!v.modelNum}" onchange="{!c.getWarrantyInfo}" class="field-required">
                                    <option text="{!$Label.c.CCM_Portal_NonePlaceTips}" value=""></option>
                                    <aura:iteration items="{!v.modelNumList}" var="item">
                                        <option text="{!item.Product_Code__c}" name="{!item.Product_Code__c}" value="{!item.Id}" selected="{!item.Id == v.selectedItemId}"/>
                                    </aura:iteration>
                                </lightning:select>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small">
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label" >
                                        {!$Label.c.CCM_Portal_ProductName}:
                                    </label>
                                    <div class="slds-form-element__control wordBreak">
                                        {!v.productName}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="slds-grid slds-wrap secondLine">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                <lightning:input aura:id="emailAddress" label="{!$Label.c.CCM_Portal_EmailAddress}" value="{!v.emailAddress}" onblur="{!c.getCustomerInfo}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255"/>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                <lightning:input aura:id="firstName" label="{!$Label.c.CCM_Portal_Firstname}" value="{!v.firstName}"  onblur="{!c.getInfoBySerialNumber}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255" />
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                <lightning:input aura:id="lastName" label="{!$Label.c.CCM_Portal_Lastname}" value="{!v.lastName}" onblur="{!c.getInfoBySerialNumber}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255"/>
                            </div>
                        </div>
                        <div class="slds-grid slds-wrap secondLine">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium field-required">
                                <!--<lightning:input type="date" aura:id="dropOfDate" label="Drop of date:" value="{!v.dropOfDate}" />-->
                                <label class="slds-form-element__label">{!$Label.c.CCM_Portal_DropoffDate}</label>
                                <!-- <ui:inputDate aura:id="dropOfDate" displayDatePicker="true" format="MM/dd/yy" updateOn="change" value="{!v.dropOfDate}"/> -->
                                <!-- update by nick 20200824:Add a restriction to Drop off date, make sure partner cannot select a date before one month  -->
                                <lightning:input class="dropshipDatePicker" aura:id="dropOfDate" type="date" value="{!v.dropOfDate}" min="{!v.minDate}"/>
                                <div aura:id="dropOfDate-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                            </div>
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium field-required">
                                <label class="slds-form-element__label">{!$Label.c.CCM_Portal_RepairDate}</label>
                                <lightning:input aura:id="repairDate" type="date" value="{!v.repairDate}"/>
                                <div aura:id="repairDate-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                            </div>
                        </div>
                        <aura:if isTrue="{!and(v.isServiceLive, and(not(empty(v.serviceOption)), v.serviceOption!='Service Attempt'))}">
                            <div class="slds-grid slds-wrap secondLine">
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                    <c:CCM_AutoMatchPickList
                                        aura:id="required-Field"
                                        objectType="ZIP_Tiers__c" label="{!$Label.c.CCM_Portal_ZipCode}"
                                        labelField="Zip__c"
                                        labelField1="State_CD__c"
                                        fieldList="Zip__c,State__c,State_CD__c"
                                        value="{!v.zipCode}"
                                        inputValue="{!v.zipCodeSearch}"
                                        required="true"/>
                                </div>
                                <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium field-required">
                                    <lightning:select name="" label="{!$Label.c.CCM_Portal_BusinessName}" aura:id="businessname" value="{!v.tierId}" class="field-required">
                                        <option text="{!$Label.c.CCM_Portal_NonePlaceTips}" value=""></option>
                                        <aura:iteration items="{!v.tierlist}" var="item">
                                            <option text="{!item.Business_Name__c}" value="{!item.Id}" selected="{!item.Id == v.tierId}"/>
                                        </aura:iteration>
                                    </lightning:select>
                                        <div aura:id="businessname-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                                    </div>
                                </div>
                        </aura:if>

                    </div>
                    <div class="slds-p-top_large slds-p-horizontal_small  purchaseInfo">
                        <div class="slds-grid">
                            <div class="slds-size_1-of-2 slds-text-align_right slds-p-right_xx-small">
                                <p><strong>{!$Label.c.CCM_Portal_PlaceOfPurchase}</strong></p>
                                <p><strong>{!$Label.c.CCM_Portal_Purchasedate}</strong></p>
                                <p class="slds-m-bottom_medium"><strong>{!$Label.c.CCM_Portal_Purpose}</strong></p>
                            </div>
                            <div class="slds-p-left_xx-small">
                                <p>{!v.placeOfPurchase}</p>
                                <p>{!v.formattedPurchaseDate}</p>
                                <!-- add haibo: french -->
                                <aura:if isTrue="{!($Label.c.CCM_Portal_Language == 'fr')}">
                                    <p>{!v.purchaseUseTypeFrench}</p>
                                    <aura:set attribute="else">
                                        <p>{!v.purchaseUseType}</p>
                                    </aura:set>
                                </aura:if>
                            </div>
                        </div>
                        <p class="slds-border_top slds-p-vertical_small slds-text-align_center">
                            <aura:if isTrue="{!v.warrantyStatus == 'Out of Warranty'}">
                            <img src="{!$Resource.SystemIcon + '/RedIcon.png'}"/>&nbsp;
                                <!-- add haibo: french -->
                                <aura:if isTrue="{!($Label.c.CCM_Portal_Language == 'fr')}">
                                    {!v.warrantyStatusFrench}
                                    <aura:set attribute="else">
                                        {!v.warrantyStatus}
                                    </aura:set>
                                </aura:if>
                            </aura:if>
                            <aura:if isTrue="{!v.warrantyStatus == 'Pending'}">
                            <img src="{!$Resource.SystemIcon + '/YellowIcon.png'}"/>&nbsp;
                                <aura:if isTrue="{!($Label.c.CCM_Portal_Language == 'fr')}">
                                        {!v.warrantyStatusFrench}
                                        <aura:set attribute="else">
                                            {!v.warrantyStatus}
                                        </aura:set>
                                    </aura:if>
                                </aura:if>
                            <aura:if isTrue="{!v.warrantyStatus == 'Vailid Warranty'}">
                            <img src="{!$Resource.SystemIcon + '/GreenIcon.png'}"/>&nbsp;{!$Label.c.CCM_Portal_ValidWarranty}
                            </aura:if>
                        </p>

                    </div>
                </div>
            </div>
        </article>
        <article class="slds-card slds-scrollable_y">
            <div class="slds-grid">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__body">
                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                              title="{!$Label.c.CCM_Portal_ServiceInformation}">
                                <span><strong>{!$Label.c.CCM_Portal_ServiceInformation}</strong></span>
                         </span>
                        </h2>
                    </div>
                </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                <div class="slds-wrap">
                    <div class="slds-grid slds-m-top--xx-small slds-p-bottom_xx-small slds-m-right_medium">
                        <div class="slds-size_1-of-3 slds-m-right_medium field-required">
                            <lightning:select aura:id="serviceoption" name="" label="{!$Label.c.CCM_Portal_ServiceOption}" onchange="{!c.selectServiceOption}" value="{!v.serviceOption}">
                                <option value="">{!$Label.c.CCM_Portal_NonePlaceTips}</option>
                                <aura:if isTrue="{!!v.isValidRecall}">
                                    <option value="Replacement">{!$Label.c.CCM_Portal_Replacement}</option>
                                    <option value="Repair">{!$Label.c.CCM_Portal_Repair}</option>
                                    <aura:set attribute="else">
                                        <aura:if isTrue="{!v.isRecall}">
                                            <option value="Recall">{!$Label.c.CCM_Portal_Recall}</option>
                                            <aura:set attribute="else">
                                                <option value="Replacement">{!$Label.c.CCM_Portal_Replacement}</option>
                                                <aura:if isTrue="{!v.isProduct}">
                                                    <option value="Repair">{!$Label.c.CCM_Portal_Repair}</option>
                                                </aura:if>
                                            </aura:set>
    
                                        </aura:if>
                                    </aura:set>
                                </aura:if>
                                <aura:if isTrue="{!v.isServiceLive}">
                                    <option value="Service Attempt">{!$Label.c.CCM_Portal_ServiceAttempt}</option>
                                </aura:if>
                            </lightning:select>
                            <div aura:id="serviceoption-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </div>
                        
                        <aura:if isTrue="{!v.isShowLevel2ExplanationOption}">
                            <div class="slds-size_2-of-3 slds-p-top_large slds-hide" style="color: red;" aura:id="EXReminder">
                                {!$Label.c.CCM_Portal_PleaseSelectOneOfTheFollowingFourOptions}
                            </div>
                        </aura:if>
                    </div>

                    <div class="slds-grid slds-m-top--xx-small slds-hide" aura:id="recallType">
                        <div class="slds-p-bottom_xx-small slds-size_1-of-3 slds-m-right_medium field-required">
                            <lightning:select name="" label="{!$Label.c.CCM_Portal_Project}" value="{!v.project}" onchange="{!c.selectProject}">
                                <option value="">{!$Label.c.CCM_Portal_NonePlaceTips}</option>
                                <aura:iteration items="{!v.projectList}" var="project">
                                    <option text="{!project.Name}" value="{!project.Id}" selected="{!project.Id == v.project}"/>
                                </aura:iteration>
                            </lightning:select>
                        </div>
                        <div class="slds-p-bottom_xx-small slds-size_1-of-3 slds-m-right_medium field-required">
                            <lightning:select name="" label="{!$Label.c.CCM_Portal_RecallOption}" value="{!v.recallOption}" onchange="{!c.selectRecallOption}" aura:id="recallOption">
                                <option value="">{!$Label.c.CCM_Portal_NonePlaceTips}</option>
                                <aura:iteration items="{!v.recallOptionList}" var="recallOption">
                                    <option value="{!recallOption}" selected="{!recallOption == v.recallOption}">{!recallOption}</option>
                                </aura:iteration>
                            </lightning:select>
                            <div aura:id="recallOption-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </div>
                        <div class="slds-p-bottom_xx-small slds-size_1-of-3 slds-m-right_medium slds-hide" aura:id="contactCustomerService">
                            <p class="redColor">{!$Label.c.CCM_Portal_Pleasecontactthecustomerservice}</p>
                        </div>
                    </div>
                    
                    <div class="slds-grid slds-m-top--xx-small slds-hide" aura:id="replacementType">
                        <div class="slds-p-bottom_xx-small slds-size_1-of-3 slds-m-right_medium field-required">
                            <lightning:select aura:id="replacementoption" name="" label="{!$Label.c.CCM_PortalReplacementOption}" value="{!v.inventory}" onchange="{!c.selectInventory}">
                                <option value="">{!$Label.c.CCM_Portal_NonePlaceTips}</option>
                                <option value="Chervon inventory">{!$Label.c.CCM_Portal_Chervoninventory}</option>
                                <aura:if isTrue="{!not(v.isServiceLive)}">
                                    <option value="Dealer inventory">{!$Label.c.CCM_Portal_Dealerinventory}</option>
                                </aura:if>
                            </lightning:select>
                            <div aura:id="replacementoption-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </div>
                        
                        <aura:if isTrue="{!v.isShowLevel2ExplanationOption}">
                            <div class="slds-p-bottom_xx-small slds-size_1-of-4 slds-m-right_medium field-required">
                                <lightning:select aura:id="Level1ExplanationOption" name="" label="{!$Label.c.CCM_Portal_Level1ExplanationOption}" value="{!v.Level1ExplanationOption}" onchange="{!c.Level1ExplanationOptionchange}">
                                    <option text="{!$Label.c.CCM_Portal_NonePlaceTips}" value=""></option>   
                                    <aura:iteration items="{!v.Level1ExplanationOptions}" var="temp">
                                        <option text="{!temp.label}" value="{!temp.value}" selected="{!temp.value == v.Level1ExplanationOption}"/>
                                    </aura:iteration>
                                </lightning:select>
                                <div aura:id="Level1ExplanationOption-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                            </div>
    
                            <div aura:id="Level2ExplanationDetails" class="slds-p-bottom_xx-small slds-size_1-of-3 slds-grid slds-hide">
                                <aura:iteration items="{!v.Level2ExplanationOptions}" var="temp">
                                    <aura:if isTrue="{!and(temp.label == $Label.c.CCM_Portal_PartsOutOfStock, v.Level1ExplanationOption == 'Part(s) Out Of Stock')}">
                                        <div class="slds-size_1-of-2">
                                            <c:CCM_MultipleSelect aura:id="Level2ExplanationOption"
                                                                options="{!v.partsOption}" 
                                                                label="{!temp.value}" 
                                                                value="{!v.partsOptionValues}"
                                                                isError="{!v.Level2ExplanationOptionIsError}"  
                                                                required="{!v.Level2EORequired}"
                                                                onChange="{!c.partsValuesToString}"/>
                                        </div>                                 
                                    </aura:if>
                                    <aura:if isTrue="{!and(temp.label == $Label.c.CCM_Portal_PartNotAvailableOnSBOM, v.Level1ExplanationOption == 'Part Not Available On SBOM')}">
                                        <div class="slds-size_1-of-2">
                                            <lightning:input aura:id="Level2ExplanationOption" type="text" label="{!temp.value}" value="{!v.Level2ExplanationOption}" required="{!v.Level2EORequired}"/>
                                        </div>                                 
                                    </aura:if>
                                    <aura:if isTrue="{!and(temp.label == $Label.c.CCM_Portal_CustomerDissatisfactionDuetoLongRepairTime, v.Level1ExplanationOption == 'Customer Dissatisfaction Due to Long Repair Time')}">
                                        <div class="slds-size_1-of-2">
                                            <lightning:input aura:id="Level2ExplanationOption" type="date" label="{!temp.value}" value="{!v.Level2ExplanationOption}" onblur="{!c.dateToText}" required="{!v.Level2EORequired}"/>
                                        </div>    
                                    </aura:if>
                                </aura:iteration> 
                            </div>
                        </aura:if>
                    </div>

                    <div class="slds-grid slds-m-top--xx-small slds-hide" aura:id="repairType">
                        <div class="slds-p-bottom_xx-small slds-size_1-of-4 slds-m-right_medium field-required" aura:id="repairTypeSelect">
                            <lightning:select aura:id="repairtype" name="" label="{!$Label.c.CCM_Portal_RepairType}" onchange="{!c.selectRepairType}" value="{!v.repairType}">
                                <option value="">{!$Label.c.CCM_Portal_NonePlaceTips}</option>
                                <option value="Parts">{!$Label.c.CCM_Portal_Parts}</option>
                                <option value="Labor Time Only">{!$Label.c.CCM_Portal_LaborTimeOnly}</option>
                            </lightning:select>
                            <div aura:id="repairtype-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </div>
                        <div class="slds-p-bottom_xx-small slds-size_1-of-4 slds-m-right_medium" aura:id="failureCode">
                            <lightning:select name="" label="{!$Label.c.CCM_Portal_FailureCode}" value="{!v.failureCode}">
                                <option value="">{!$Label.c.CCM_Portal_NonePlaceTips}</option>
                                <aura:iteration items="{!v.failureCodeList}" var="failureCode">
                                    <option text="{!failureCode.label}" value="{!failureCode.value}" selected="{!failureCode.value == v.failureCode}"/>
                                </aura:iteration>
                            </lightning:select>
                        </div>
                        <div class="slds-p-bottom_xx-small slds-size_1-of-4 slds-m-right_medium field-required">
                            <c:CCM_Community_LookUp aura:id="majorIssue"
                                                    fieldName="{!$Label.c.CCM_Portal_Repairable_Parts}"
                                                    productId="{!v.modelNum}"
                                                    inputVal="{!v.repairPartsName}"
                                                    selectedValue="{!v.majorIssueObj}"
                                                    isDisabled="{!v.isAddPartsDisabled}"
                            />
                            <div aura:id="majorIssue-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </div>
                        <aura:if isTrue="{!not(v.isServiceLive)}">
                            <div class="slds-p-bottom_xx-small slds-m-right_large slds-size_1-of-8">
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label" >
                                        {!$Label.c.CCM_Portal_LaborTime}
                                    </label>
                                    <div class="slds-form-element__control lineHeight" aura:id="laborHourPopUp">
                                        {!v.labourHours} {!$Label.c.CCM_Portal_Minutes}
                                    </div>
                                    <div class="slds-form-element__control lineHeight laborHoursInput slds-hide" aura:id="laborHourInput">
                                        <lightning:input  label="" value="{!v.labourHours}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255"/> minutes
                                    </div>
                                </div>
                            </div>
                        </aura:if>
                    </div>
                    <aura:if isTrue="{!(
                                        (!v.AltaquipClaimRestrictionName || !v.AltaquipClaimRestrictionModel) &amp;&amp;
                                        (v.serviceOption == 'Repair' || v.serviceOption == 'Replacement') &amp;&amp; 
                                        (v.boolPickup || v.isPickUp)
                                        )}">
                        <aura:if isTrue="{!not(v.isServiceLive)}">
                            <div class="slds-grid slds-m-top--xx-small" aura:id="pickup">
                                <div class="slds-p-bottom_xx-small slds-size_1-of-3 slds-m-right_medium">
                                    <lightning:select name="" label="{!$Label.c.CCM_Portal_PickupandDelivery}" value="{!v.strUsePickup}" onchange="{!c.handlePickupChosen}">
                                        <option value="No">{!$Label.c.CCM_Portal_NO}</option>
                                        <option value="Yes">{!$Label.c.CCM_Portal_YES}</option>
                                    </lightning:select>
                                </div>
                                <div class="slds-p-bottom_xx-small slds-size_1-of-3 slds-m-right_medium">
                                    <div class="slds-grid slds-wrap">
                                        <div class="slds-col slds-size_2-of-3">
                                            <lightning:input
                                                label="{!$Label.c.CCM_Portal_TotalDistanceDriven}"
                                                type="number"
                                                disabled="{!equals(v.strUsePickup, 'No')}"
                                                value="{!v.decPickupDistance}"
                                                onchange="{!c.handlePickupDistanceChange}"
                                            />
                                        </div>
                                        <div class="slds-col slds-size_1-of-3 slds-align-bottom">&nbsp;{!$Label.c.CCM_Portal_Miles}</div>
                                    </div>
                                </div>
                                <div class="slds-p-bottom_xx-small slds-size_1-of-3 slds-m-right_medium">
                                    <div class="slds-grid slds-wrap">
                                        <div class="slds-col slds-size_2-of-3">
                                            <lightning:input
                                                label="{!$Label.c.CCM_Portal_PickupFee}"
                                                type="number"
                                                disabled="{!v.iscustomerpickupfee}"
                                                value="{!v.decPickupFeeSubtotal}"
                                                onchange="{!c.handlePickupFeeSubtotalChange}"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </aura:if>
                    </aura:if>
                      <aura:if
                        isTrue="{!and(equals(v.inventory, 'Chervon inventory'), equals(v.serviceOption, 'Replacement'))}">
                        <div class="slds-grid slds-m-top--xx-small slds-hide" aura:id="addressInfo">
                            <div class="slds-p-bottom_xx-small slds-size_1-of-3 slds-m-right_medium field-required">
                                <lightning:inputAddress aura:id="address" addressLabel="{!$Label.c.CCM_Portal_Address}" streetLabel="{!$Label.c.CCM_Portal_Street}"
                                    cityLabel="{!$Label.c.CCM_Portal_City}" countryLabel="{!$Label.c.CCM_Portal_Country}" provinceLabel="{!$Label.c.CCM_Portal_State}"
                                    postalCodeLabel="{!$Label.c.CCM_Portal_ZipCode}" street="{!v.ShippingStreet}" city="{!v.ShippingCity}"
                                    country="{!v.ShippingCountry}" province="{!v.ShippingState}"
                                    postalCode="{!v.ShippingPostalCode}" fieldLevelHelp="address" />
                                <div aura:id="address-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                            </div>
                            <div
                                class="slds-p-bottom_xx-small slds-size_1-of-4 slds-m-right_medium slds-p-top--medium field-required">
                                <lightning:input aura:id="firstNa" label="{!$Label.c.CCM_Portal_Firstname}" value="{!v.replacementFirstName}"
                                    messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255" />
                                <div aura:id="firstNa-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                                <lightning:input aura:id="LastNa" label="{!$Label.c.CCM_Portal_Lastname}" value="{!v.replacementLastName}"
                                    messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255" />
                                <div aura:id="LastNa-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                            </div>
                            <div
                                class="slds-p-bottom_xx-small slds-size_1-of-4 slds-m-right_medium slds-p-top--medium field-required">
                                <lightning:input aura:id="phone" label="{!$Label.c.CCM_Portal_Phone}" value="{!v.replacementPhone}"
                                    messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255" />
                                <div aura:id="phone-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                            </div>
                        </div>
                    </aura:if>
                    <aura:set attribute="else">
                        <div class="slds-grid slds-m-top--xx-small slds-hide" aura:id="addressInfo">
                            <div class="slds-p-bottom_xx-small slds-size_1-of-3 slds-m-right_medium">
                                <lightning:inputAddress addressLabel="{!$Label.c.CCM_Portal_Address}" streetLabel="{!$Label.c.CCM_Portal_Street}" cityLabel="{!$Label.c.CCM_Portal_City}"
                                    countryLabel="{!$Label.c.CCM_Portal_Country}" provinceLabel="{!$Label.c.CCM_Portal_State}" postalCodeLabel="{!$Label.c.CCM_Portal_ZipCode}"
                                    street="{!v.ShippingStreet}" city="{!v.ShippingCity}" country="{!v.ShippingCountry}"
                                    province="{!v.ShippingState}" postalCode="{!v.ShippingPostalCode}"
                                    fieldLevelHelp="address" />
                            </div>
                            <div class="slds-p-bottom_xx-small slds-size_1-of-4 slds-m-right_medium slds-p-top--medium">
                                <lightning:input label="{!$Label.c.CCM_Portal_Firstname}" value="{!v.replacementFirstName}"
                                    messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255" />
                                <lightning:input label="{!$Label.c.CCM_Portal_Lastname}" value="{!v.replacementLastName}"
                                    messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255" />
                            </div>
                            <div class="slds-p-bottom_xx-small slds-size_1-of-4 slds-m-right_medium slds-p-top--medium">
                                <lightning:input label="{!$Label.c.CCM_Portal_Phone}" value="{!v.replacementPhone}"
                                    messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan255bytes}" maxlength="255" />
                            </div>
                        </div>
                    </aura:set>
                </div>
                <div class="slds-grid slds-wrap slds-m-top--xx-small">
                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-size_1-of-1 field-required">
                        <lightning:textarea aura:id="description" name="" label="{!$Label.c.CCM_Portal_Description}" value="{!v.repairDescription}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan500bytes}" maxlength="500"/>
                        <div aura:id="description-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                    </div>
                </div>
                <div class="slds-wrap slds-m-bottom_medium">
                    <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols slds-table_striped" role="grid">
                        <thead>
                        <tr class="slds-line-height_reset">
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsName}">{!$Label.c.CCM_Portal_PartsName}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsNumber}">{!$Label.c.CCM_Portal_PartsNumber}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Quantity}">{!$Label.c.CCM_Portal_Quantity}</span>
                                    </div>
                                </a>
                            </th>
                            <aura:if isTrue="{!v.isNotDistributor}">
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_UnitPrice}">{!$Label.c.CCM_Portal_UnitPrice}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Totals}">{!$Label.c.CCM_Portal_Totals}</span>
                                        </div>
                                    </a>
                                </th>
                            </aura:if>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                    </div>
                                </a>
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                            <aura:iteration items="{!v.partsItemList}" var="partsItem" indexVar="index">
                                <tr aria-selected="false" class="slds-hint-parent">
                                    <th scope="row">
                                        <div class="slds-truncate partsNumber" title="{!partsItem.Name}">
                                            <aura:if isTrue="{!partsItem.addFromAdditionParts}">
                                                <c:CCM_Community_LookUp aura:id="masterProduct"
                                                                        fieldName=""
                                                                        productId="{!v.modelNum}"
                                                                        majorIssue="{!v.majorIssue}"
                                                                        index="{!index}"
                                                                        inputVal="{!partsItem.Name}"
                                                                        selectedValue="{!partsItem}"/>
                                                <aura:set attribute="else">
                                                    <aura:if isTrue="{!v.isDraft}">
                                                        <!-- {!(v.draftPartsChanged ? partsItem.Name : partsItem.ProductName__c)} -->
                                                        {!partsItem.Name}
                                                        <aura:set attribute="else">
                                                            <aura:if isTrue="{!v.isView}">
                                                                {!partsItem.ProductName__c}
                                                                <aura:set attribute="else">
                                                                    <aura:if isTrue="{!partsItem.editable}">
                                                                        {!partsItem.Name}
                                                                        <aura:set attribute="else">
                                                                            <c:CCM_Community_LookUp aura:id="masterProduct"
                                                                                                    fieldName=""
                                                                                                    productId="{!v.modelNum}"
                                                                                                    majorIssue="{!v.majorIssue}"
                                                                                                    index="{!index}"
                                                                                                    inputVal="{!partsItem.Name}"
                                                                                                    selectedValue="{!partsItem}"
                                                                            />
                                                                        </aura:set>
                                                                    </aura:if>
                                                                </aura:set>
                                                            </aura:if>
                                                        </aura:set>
                                                    </aura:if>
                                                </aura:set>
                                            </aura:if>


                                            <aura:if  isTrue="{!partsItem.showWearable}">
                                                <p class="required">{!$Label.c.CCM_Portal_MaintenancepartsTips} {!partsItem.warrantyDate} {!$Label.c.CCM_Portal_workingconditionsTips}</p>
                                            </aura:if>
                                        </div>
                                    </th>
                                    <th scope="row">
                                        <div class="slds-truncate" title="{!partsItem.itemNumber}">
                                            <aura:if isTrue="{!v.isDraft}">
                                                <!-- {!(v.draftPartsChanged ? partsItem.itemNumber : partsItem.Product__r.ProductCode)} -->
                                                {! partsItem.itemNumber}
                                                <aura:set attribute="else">
                                                    {!(v.isView? partsItem.Product__r.ProductCode : partsItem.itemNumber)}
                                                </aura:set>
                                            </aura:if>
                                        </div>
                                    </th>
                                    <th scope="row">
                                        <div class="slds-truncate quantity" title="{!partsItem.quantity}" data-index="{!index}" data-parts="{!index}">
                                            <aura:if isTrue="{!v.isDraft}">
                                                <!-- <lightning:input class="partsInput" type="number" label="" value="{!(v.draftPartsChanged ? partsItem.quantity : partsItem.Quantity__c)}" onchange="{!c.calculatePartsTotalPrice}"/> -->
                                                <lightning:input class="partsInput" type="number" label="" value="{!partsItem.quantity}" onchange="{!c.calculatePartsTotalPrice}"/>
                                                <aura:set attribute="else">
                                                    <lightning:input class="partsInput" type="number" label="" value="{!(v.isView? partsItem.Quantity__c : partsItem.quantity)}" onchange="{!c.calculatePartsTotalPrice}"/>
                                                </aura:set>
                                            </aura:if>
                                        </div>
                                    </th>
                                    <aura:if isTrue="{!v.isNotDistributor}">
                                        <th scope="row">
                                            <div class="slds-truncate" title="{!partsItem.price}">
                                                <aura:if isTrue="{!v.isDraft}">
                                                    <!-- {!v.currencySymbol}&nbsp;{! (v.draftPartsChanged ? partsItem.price : partsItem.Price__c)} -->
                                                    {!v.currencySymbol}&nbsp;{! partsItem.price}
                                                    <aura:set attribute="else">
                                                        {!v.currencySymbol}&nbsp;{!(v.isView? partsItem.Price__c : partsItem.price)}
                                                    </aura:set>
                                                </aura:if>
                                            </div>
                                        </th>
                                        <th scope="row">
                                            <div class="slds-truncate" title="{!partsItem.total}">
                                                <aura:if isTrue="{!v.isDraft}">
                                                    <!-- {!v.currencySymbol}&nbsp;{!(v.draftPartsChanged ? partsItem.total : partsItem.Total__c)} -->
                                                    {!v.currencySymbol}&nbsp;{! partsItem.total }
                                                    <aura:set attribute="else">
                                                        {!v.currencySymbol}&nbsp;{!(v.isView? partsItem.Total__c : partsItem.total)}
                                                    </aura:set>
                                                </aura:if>
                                            </div>
                                        </th>
                                    </aura:if>
                                    <th scope="row">
                                        <div onclick="{!c.handleDelete}" data-index="{!index}" class="deleteBtn">
                                            <lightning:icon iconName="utility:delete" alternativeText="{!$Label.c.CCM_Portal_Delete}" size="x-small"/>
                                            <!--<lightning:button label="" iconName="utility:delete" iconPosition="left"  variant="neutral" />-->
                                        </div>
                                    </th>
                                </tr>
                            </aura:iteration>
                        </tbody>
                    </table>
                    <div class="slds-text-align_right">
                        <lightning:button class="{!v.isAddPartsDisabled ? 'slds-m-vertical_medium disabled' : 'slds-m-vertical_medium'}" label="{!$Label.c.CCM_Portal_AdditionalParts}" iconName="utility:add" iconPosition="left" variant="brand" onclick="{!c.AdditionalParts}" disabled="{!v.isAddPartsDisabled}"/>
                    </div>
                    <aura:if isTrue="{!!v.isDiagramDisabled}">
                        <div class="explode"><a onclick="{!C.openModal}">{!$Label.c.CCM_Portal_ViewFromExplodedDiagram}</a></div>
                    </aura:if>
                    <div aura:id="boomModal" class="slds-hide">
                        <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                            <div class="slds-modal__container">
                                <lightning:spinner aura:id="proListSpinner" class="slds-hide modalSpinner" alternativeText="{!$Label.c.CCM_Portal_Loading}" size="small" variant="brand" />
                                <header class="slds-modal__header">
                                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                                            title="{!$Label.c.CCM_Portal_Close}" onclick="{!c.onClickModalCancel}">
                                        <lightning:icon alternativeText="{!$Label.c.CCM_Portal_Close}" iconName="utility:close" size="small" variant="bare"/>
                                    </button>
                                </header>
                                <div class="slds-modal__content slds-p-around_medium">
                                    <div class="slds-grid slds-m-bottom_x-large slds-grid_vertical-align-end" id="top">
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium slds-size_1-of-4 slds-is-relative">
                                            <lightning:select name="" label="{!$Label.c.CCM_Portal_Version}" aura:id="version" value="{!v.version}">
                                                <option text="{!$Label.c.CCM_Portal_NonePlaceTips}" value=""/>
                                                <aura:iteration items="{!v.versionList}" var="version">
                                                    <option text="{!version}" value="{!version}"/>
                                                </aura:iteration>
                                            </lightning:select>
                                            <div class="slds-p-top_x-small slds-is-absolute" style="width: 120%">
                                                <a href="{!$Label.c.CCM_Exploded_Diagram_Note_4_Different_Version}" target="_blank">{!$Label.c.CCM_Portal_differentVersionsTips}</a>
                                            </div>
                                        </div>
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                            <lightning:button variant="brand" label="{!$Label.c.CCM_Portal_Search}" onclick="{! c.handleSearch }" />
                                        </div>
                                    </div>
                                    <div aura:id="explosiveContent" class="slds-hide">
                                        <!-- Slider main container -->
                                        <div class="swiper-container"  id="top">
                                        <!-- Additional required wrapper -->
                                        <div class="swiper-wrapper">
                                            <!-- Slides -->
                                            <aura:iteration items="{!v.contents}" var="content">
                                                <div class="swiper-slide"><lightning:fileCard fileId="{!content}" description=""/></div>
                                                <!--                                <div class="swiper-slide"><lightning:fileCard fileId="069S000000105rqIAA" description="Sample Description"/></div>-->
                                            </aura:iteration>
                                        </div>
                                        <!-- If we need navigation buttons -->
                                        <div class="swiper-button-prev"></div>
                                        <div class="swiper-button-next"></div>
                                    </div>
                                        <div class="slds-grid slds-m-vertical--medium slds-grid_vertical-align-end">
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium slds-size_1-of-4">
                                            <lightning:input label="{!$Label.c.CCM_Portal_SequenceNo}" aura:id="sequenceNo" value="{!v.sequenceNo}"/>
                                        </div>
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                            <a class="slds-button slds-button_brand slds-float--right" onclick="{! c.highLightSequenceNo }" id="sequenceNo">{!$Label.c.CCM_Portal_Go}</a>
                                        </div>
                                    </div>
                                        <div class="table_container slds-size_1-of-1 slds-medium-size_1-of-1 slds-p-right_medium slds-p-top_xx-small slds-p-bottom_xx-small slds-scrollable_x  slds-m-top_large">
                                        <lightning:spinner aura:id="proListSpinner" class="slds-hide" alternativeText="{!$Label.c.CCM_Portal_Loading}" size="small" variant="brand" />
                                        <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols">
                                            <thead>
                                            <tr class="slds-line-height_reset" aria-selected="false">
<!--                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 5%">-->
<!--                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">-->
<!--                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">-->
<!--                                                            <span class="slds-truncate" title=""></span>-->
<!--                                                        </div>-->
<!--                                                    </a>-->
<!--                                                </th>-->
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 8%">
                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_SequenceNo}">{!$Label.c.CCM_Portal_SequenceNo}</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 10%">
                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_ProductModel}">{!$Label.c.CCM_Portal_ProductModel}</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 10%">
                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsNumber}">{!$Label.c.CCM_Portal_PartsNumber}</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 30%">
                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PartsName}">{!$Label.c.CCM_Portal_PartsName}</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <!--                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width:6%">-->
                                                <!--                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">-->
                                                <!--                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">-->
                                                <!--                                            <span class="slds-truncate" title="Action">Action</span>-->
                                                <!--                                        </div>-->
                                                <!--                                    </a>-->
                                                <!--                                </th>-->
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <aura:iteration items="{!v.explosiveDataList}" var="parts" indexVar="index">
                                                <tr aria-selected="false" class="{!parts.highLight ? 'slds-hint-parent highLight' : 'slds-hint-parent'}" aura:id="{!('line' + index)}" id="{!parts.ExplosionID__c}">
<!--                                                    <td class="slds-text-align_right" role="gridcell">-->
<!--                                                        <lightning:input type="checkbox" label="" checked="{!parts.isSelect}"/>-->
<!--                                                    </td>-->
                                                    <td data-label="Product Model#" scope="row">
                                                        <div class="slds-truncate" title="">
                                                            {!parts.ExplosionID__c}
                                                        </div>
                                                    </td>
                                                    <td data-label="Product Model#" scope="row">
                                                        <div class="slds-truncate" title="">
                                                            {!parts.Product__r.ProductCode}
                                                        </div>
                                                    </td>
                                                    <td data-label="Parts Number">
                                                        <div class="slds-truncate" title="">
                                                            {!parts.Parts__r.ProductCode}
                                                        </div>
                                                    </td>
                                                    <td data-label="Description">
                                                        <div class="slds-truncate" title="">
                                                            <!-- add haibo: french -->
                                                            <aura:if isTrue="{!($Label.c.CCM_Portal_Language == 'fr')}">
                                                                {!parts.Parts__r.Product_Name_French__c}
                                                                <aura:set attribute="else">
                                                                    {!parts.Parts__r.Name}
                                                                </aura:set>
                                                            </aura:if>
                                                        </div>
                                                    </td>
                                                    <!--                                    <th scope="row">-->
                                                    <!--                                        <div onclick="{!c.addToCartSingle}" data-index="{!index}" class="deleteBtn">-->
                                                    <!--                                            <lightning:icon iconName="utility:cart" alternativeText="delete" size="x-small"/>-->
                                                    <!--                                        </div>-->
                                                    <!--                                    </th>-->
                                                </tr>
                                            </aura:iteration>
                                            </tbody>
                                        </table>
                                    </div>
                                    </div>
                                </div>
                                <footer class="slds-modal__footer">
<!--                                    <lightning:button onclick="{!c.addSelectedToCart}" class="slds-float&#45;&#45;right slds-m-vertical&#45;&#45;medium" variant="brand" label="Add To Cart" iconName="utility:add" iconPosition="left" />-->
                                    <a class="slds-button slds-button_brand slds-float--right slds-m-vertical--medium slds-m-right--medium" onclick="{!c.backToTop}">{!$Label.c.CCM_Portal_BackToTop}</a>
                                </footer>>
                            </div>
                        </section>
                        <div class="slds-backdrop slds-backdrop_open"></div>
                    </div>
                    <div aura:id="replacementModal" class="slds-hide">
                        <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                            <div class="slds-modal__container replacementModal">
                                <header class="slds-modal__header">
                                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                                            title="{!$Label.c.CCM_Portal_Close}" onclick="{!c.onClickReplacementModalCancel}">
                                        <lightning:icon alternativeText="{!$Label.c.CCM_Portal_Close}" iconName="utility:close" size="small" variant="bare"/>
                                    </button>
                                </header>
                                <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                                    {!$Label.c.CCM_Portal_NoteTips}
                                </div>
                                <footer class="slds-modal__footer">
                                    <button class="slds-button slds-button_neutral" onclick="{!c.onClickReplacementModalCancel}">{!$Label.c.CCM_Portal_Cancel}</button>
                                    <button class="slds-button slds-button_brand" onclick="{!c.addAdditionalPartsViaReplacement}">{!$Label.c.CCM_Portal_Confirm}</button>
                                </footer>
                            </div>
                        </section>
                        <div class="slds-backdrop slds-backdrop_open"></div>
                    </div>
                </div>
            </div>
        </article>
        <article class="slds-card slds-m-top--medium">
            <div class="slds-grid">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__body">
                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                            <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                  title="{!$Label.c.CCM_Portal_PaymentInformation}">
                                    <span><strong>{!$Label.c.CCM_Portal_PaymentInformation}</strong></span>
                             </span>
                        </h2>
                    </div>
                </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                <div class="slds-size--1-of-2 slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium field-required">
                    <lightning:select name="" label="{!$Label.c.CCM_Portal_BillTo}" aura:id="billTo" value="{!v.billToId}">
                        <option text="{!$Label.c.CCM_Portal_NonePlaceTips}" value=""></option>
                        <aura:iteration items="{!v.billToList}" var="billTo">
                            <option text="{!(billTo.Account_Address__r.Address1__c+ ' ' + billTo.Account_Address__r.City__c+ ' ' + billTo.Account_Address__r.State__c+ ' ' + billTo.Account_Address__r.Country__c + ' ' + billTo.Account_Address__r.Postal_Code__c)}" value="{!billTo.Id}" selected="{!billTo.Id == v.billToId}"/>
                        </aura:iteration>
                    </lightning:select>
                    <div aura:id="billTo-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                </div>
                <aura:if isTrue="{!v.orgCode == 'CCA'}">
                    <div class="slds-grid slds-gutters">
                        <div class="slds-col slds-size--1-of-4 slds-p-top_xx-small slds-p-bottom_xx-small">
                            <lightning:input aura:id="GST" type="number" formatter="currency" label="{!$Label.c.CCM_Portal_GST}" value="{!v.gst}" onchange="{!c.handleTaxChange}" step="0.01"></lightning:input>
                            <div aura:id="GST-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </div>
                        <div class="slds-col slds-size--1-of-4 slds-p-top_xx-small slds-p-bottom_xx-small">
                            <lightning:input aura:id="HST" type="number" formatter="currency" label="{!$Label.c.CCM_Portal_HST}" value="{!v.hst}" onchange="{!c.handleTaxChange}" step="0.01"></lightning:input>
                            <div aura:id="HST-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </div>
                        <div class="slds-col slds-size--1-of-4 slds-p-top_xx-small slds-p-bottom_xx-small">
                            <lightning:input aura:id="QST" type="number" formatter="currency" label="{!$Label.c.CCM_Portal_QST}" value="{!v.qst}" onchange="{!c.handleTaxChange}" step="0.01"></lightning:input>
                            <div aura:id="QST-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </div>
                        <div class="slds-col slds-size--1-of-4 slds-p-top_xx-small slds-p-bottom_xx-small">
                            <lightning:input aura:id="PST" type="number" formatter="currency" label="{!$Label.c.CCM_Portal_PST}" value="{!v.pst}" onchange="{!c.handleTaxChange}" step="0.01"></lightning:input>
                            <div aura:id="PST-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </div>
                    </div>
                    <div>
                        <p class="redColor">{!$Label.c.CCM_Portal_CustomersInCandaAreObligate}</p>
                        <p class="redColor">{!$Label.c.CCM_Portal_IfYouHaveAnyQuestionsAboutTax}</p>
                    </div>
                </aura:if>
            </div>
        </article>
        <aura:if isTrue="{!and((and(not(v.boolIsAltaQuip), not(v.isServiceLive))), v.isShowAdditionalInformation)}">
            <article class="slds-card accordionCon">
                <div>
                    <lightning:accordion allowMultipleSectionsOpen="true" activeSectionName="{! v.activeSections }" >
                        <aura:if isTrue="{!v.showActualTime}">
                            <lightning:accordionSection name="A" label="{!$Label.c.CCM_Portal_AdditionalInformation}">
                                <aura:set attribute="body">
                                    <div class="slds-grid slds-wrap">
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium overtime">
                                            <lightning:input class="overtimeInput" name="" label="{!$Label.c.CCM_Portal_ActualTime}" value="{!v.overTimeHour}" onblur="{!c.getFinialLaborHour}"/><span class="hour">{!$Label.c.CCM_Portal_Minutes}</span>
                                        </div>
                                        <div class="{!v.overTimeHour ? 'field-required slds-p-top_xx-small slds-p-bottom_xx-small slds-size_1-of-2 description ' : 'slds-p-top_xx-small slds-p-bottom_xx-small slds-size_1-of-2 description '}">
                                            <lightning:input aura:id="additionalExplantion" name="" label="{!$Label.c.CCM_Portal_Explanation}" value="{!v.overTimeDescription}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan2000bytes}" maxlength="2000"/>
                                            <div aura:id="additionalExplantion-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                                        </div>
                                    </div>
                                </aura:set>
                            </lightning:accordionSection>
                        </aura:if>
                        <aura:if isTrue="{!v.showAdditionalTime}">
                            <lightning:accordionSection name="A" label="{!$Label.c.CCM_Portal_AdditionalInformation}">
                                <aura:set attribute="body">
                                    <span style="color: red;">{!$Label.c.CCM_Portal_Inthisfieldyouwillenterinthe}&nbsp;<span style="text-decoration: underline">{!$Label.c.CCM_Portal_additionalminutes}</span>&nbsp;{!$Label.c.CCM_Portal_needForRepairTips}</span>
                                    <div class="slds-grid slds-wrap">
                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium overtime">
                                            <lightning:input class="overtimeInput" name="" label="{!$Label.c.CCM_Portal_AdditionalLaborTime}" value="{!v.additionalTimeHour}" onblur="{!c.getFinialLaborHour}"/><span class="hour">{!$Label.c.CCM_Portal_Minutes}</span>
                                        </div>

                                        <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-size_1-of-2 description">
                                            <span style="color: red;">{!$Label.c.CCM_Portal_Your_recommendedlabortimeis}&nbsp;{!v.labourHours}&nbsp;{!$Label.c.CCM_Portal_Minutes}, {!$Label.c.CCM_Portal_selectthespecificreason}
                                            </span>
                                            <div class="field-required">
                                                <label class="slds-form-element__label">{!$Label.c.CCM_Portal_ExplanationOptions}</label>
                                            </div>
                                            <aura:iteration items="{!v.explanationOptions}" var="option">
                                                <lightning:input aura:id="explanationOptions" type="checkbox" label="{!option.label}" name="{!option.value}" checked="{!option.selected}" onchange="{!c.handleExplanationOptionsSelected}"/>
                                            </aura:iteration>
                                            <div aura:id="additionalExplantion-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                                            <lightning:input name="" label="{!$Label.c.CCM_Portal_AdditionalComments}" value="{!v.overTimeDescription}" messageWhenTooLong="{!$Label.c.CCM_Portal_Pleaseenternomorethan2000bytes}" maxlength="2000"/>
                                        </div>
                                    </div>
                                </aura:set>
                            </lightning:accordionSection>
                        </aura:if>
                    </lightning:accordion>
                </div>
            </article>
        </aura:if>
        <div class="footer slds-p-vertical_large slds-float_right totalCon">
            <aura:if isTrue="{!and(not(v.boolIsAltaQuip), not(v.isServiceLive))}">
                <aura:if isTrue="{!v.isNotDistributor}">
                     <div>{!$Label.c.CCM_Portal_AdminCostDiagnosisFee} <strong><lightning:formattedNumber value="{!v.diagnosisFee}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/></strong></div>
                </aura:if>
                <aura:if isTrue="{!not(v.isNotDistributor)}">
                     <div>{!$Label.c.CCM_Portal_AdminCost} <strong><lightning:formattedNumber value="{!v.diagnosisFee}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/></strong></div>
                </aura:if>

                <div class="slds-border_bottom slds-p-bottom_x-small">{!$Label.c.CCM_Portal_LaborHours}: <strong>{!v.finialLaborHour} {!$Label.c.CCM_Portal_hours} @ <lightning:formattedNumber value="{!v.LaborRate}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/></strong>/{!$Label.c.CCM_Portal_hour}</div>
            </aura:if>
            <div class=""><p class="slds-border_bottom slds-p-vertical_x-small">{!$Label.c.CCM_Portal_LaborCostSubtotal} <strong><lightning:formattedNumber value="{!(v.laborCostSubtotal)}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/></strong></p></div>
            <aura:if isTrue="{!and(equals(v.strUsePickup, 'Yes'), equals(v.isServiceLive, false))}">
                <p class="slds-border_bottom slds-p-vertical_x-small">
                    {!$Label.c.CCM_Portal_PickupFee}:
                    <strong>
                        <lightning:formattedNumber
                            value="{!(v.decPickupFeeSubtotal)}"
                            style="currency"
                            currencyCode="{!v.currencySymbol}"
                            currencyDisplayAs="code"
                            minimumFractionDigits="2"
                        />
                    </strong>
                </p>
            </aura:if>
            <aura:if isTrue="{!v.isNotDistributor}">
                <aura:if isTrue="{!v.boolIsAltaQuip}">
                    <div class=""><p class="slds-border_bottom slds-p-vertical_x-small">{!$Label.c.CCM_Portal_MaterialCostSubtotalMSRP} <strong><lightning:formattedNumber value="{!v.partsCost}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/></strong></p></div>
                </aura:if>
                <aura:if isTrue="{!not(v.boolIsAltaQuip)}">
                    <div class=""><p class="slds-border_bottom slds-p-vertical_x-small">{!$Label.c.CCM_Portal_MaterialCostSubtotal}: <strong><lightning:formattedNumber value="{!v.partsCost}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/></strong></p></div>
                </aura:if>
                 <aura:if isTrue="{!and(v.isshowmarkup, not(v.isServiceLive))}">
                    <div class=""><p class="slds-border_bottom slds-p-vertical_x-small">{!$Label.c.CCM_Portal_WarrantyPartsCreditMarkUp}:
                        <lightning:helptext  content="{!$Label.c.CCM_Portal_warrantyTips}"/>
                        <strong><lightning:formattedNumber value="{!v.markup}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/></strong>
                        </p>
                    </div>
                </aura:if>
                <aura:if isTrue="{!and(and(v.isServiceLive, v.isshowmarkup), v.serviceOption=='Repair')}">
                    <div class=""><p class="slds-border_bottom slds-p-vertical_x-small">{!$Label.c.CCM_Portal_WarrantyPartsCreditMarkUp}
                        <lightning:helptext  content="{!$Label.c.CCM_Portal_warrantyTips}"/>
                        <strong><lightning:formattedNumber value="{!v.markup}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/></strong>
                        </p>
                    </div>
                </aura:if>
                <aura:if isTrue="{!v.orgCode == 'CCA'}">
                    <div class=""><p class="slds-border_bottom slds-p-vertical_x-small">{!$Label.c.CCM_Portal_TotalTax}
                        <strong><lightning:formattedNumber value="{!v.totalTax}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/></strong>
                        </p>
                    </div>
                </aura:if>
                <div class="slds-p-vertical_x-small"><strong>{!$Label.c.CCM_Portal_Total}: <lightning:formattedNumber value="{!v.totalPrice}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code" minimumFractionDigits="2"/></strong></div>
            </aura:if>
            <div class="slds-m-top_large">
                <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Portal_Cancel}" title="{!$Label.c.CCM_Portal_Cancel}" onclick="{!c.handleCancel}" />
                <aura:if isTrue="{!!v.isView}">
                    <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Portal_Save}" title="{!$Label.c.CCM_Portal_Save}" onclick="{!c.handleSaveAsDraft}" />
                </aura:if>
                <lightning:button class="{!v.isDisabled ? 'slds-p-horizontal_x-large disabled' : 'slds-p-horizontal_x-large'}" variant="brand" label="{!$Label.c.CCM_Portal_Submit}" title="{!$Label.c.CCM_Portal_Submit}" disabled="{!v.isDisabled}" onclick="{!c.handleSubmit}" />
            </div>
        </div>
    </section>
</aura:component>