<template>
    <div class="width90 slds-container_center">
        <div class="slds-grid slds-grid_align-end slds-var-m-top_medium slds-var-m-bottom_medium">
            <template if:true={isTurgeonDealerParent}>
                <lightning-button
                    variant="brand"
                    label={label.CCM_Portal_Export}
                    title={label.CCM_Portal_Export}
                    onclick={handleClickExportClaim}
                >
                </lightning-button>
            </template>
                
            <lightning-button
                class="slds-m-left_medium"
                variant="brand"
                label={label.CCM_Portal_RaiseAClaim}
                title={label.CCM_Portal_RaiseAClaim}
                onclick={handleClickRaiseClaim}
            >
            </lightning-button> 
        </div>
        <table
            class="slds-table slds-table_bordered slds-table_striped slds-table_fixed-layout"
        >
            <thead>
                <tr class="slds-line-height_reset">
                    <template if:true={isTurgeonDealerParent}>
                        <th class="" scope="col" style="width: 16rem;">
                            <div class="slds-truncate" title={label.CCM_Portal_DealerName}>
                                {label.CCM_Portal_DealerName}
                            </div>
                        </th>
                    </template>
                    <th class="" scope="col" style="width: 9rem;">
                        <div class="slds-truncate" title={label.CCM_Portal_PromotionClaimRequestNumber}>
                            {label.CCM_Portal_PromotionClaimRequestNumber}
                        </div>
                    </th>
                    <th class="" scope="col" style="width: 20%;">
                        <div class="slds-truncate" title={label.CCM_Portal_PromotionName}>
                            {label.CCM_Portal_PromotionName}
                        </div>
                    </th>
                    <th class="" scope="col">
                        <div class="slds-truncate" title={label.CCM_Portal_PromotionCode}>
                            {label.CCM_Portal_PromotionCode}
                        </div>
                    </th>
                    <th class="" scope="col">
                        <div class="slds-truncate" title={label.CCM_Portal_TotalClaimAmount}>
                            {label.CCM_Portal_TotalClaimAmount}
                        </div>
                    </th>
                    <th class="" scope="col">
                        <div class="slds-truncate" title={label.CCM_Portal_CreatedDate}>
                            {label.CCM_Portal_CreatedDate}
                        </div>
                    </th>
                    <th class="" scope="col">
                        <div class="slds-truncate" title={label.CCM_Portal_LastModifiedDate}>
                            {label.CCM_Portal_LastModifiedDate}
                        </div>
                    </th>
                    <th class="" scope="col">
                        <div class="slds-truncate" title={label.CCM_Portal_CreatedBy}>
                            {label.CCM_Portal_CreatedBy}
                        </div>
                    </th>
                    <th class="" scope="col">
                        <div class="slds-truncate" title={label.CCM_Portal_ClaimStatus}>
                            {label.CCM_Portal_ClaimStatus}
                        </div>
                    </th>
                    <th class="" scope="col" style="width: 6rem;">
                        <div class="slds-truncate" title={label.CCM_Portal_Action}>{label.CCM_Portal_Action}</div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <template for:each={claimRequestList} for:item="claim">
                    <tr key={claim.Id} class="slds-hint-parent">
                        <template if:true={isTurgeonDealerParent}>
                            <td>
                                <div
                                    class="slds-truncate"
                                    title={claim.Customer__r.Name}
                                >
                                    {claim.Customer__r.Name}
                                </div>
                            </td>      
                        </template>
                        <td>
                            <div
                                class="slds-truncate"
                                title={claim.Name}
                            >
                                {claim.Name}
                            </div>
                        </td>
                        <td>
                            <template if:true={claim.Promotion_Code__c}>
                            <div
                                class="slds-truncate"
                                title={claim.Promotion_Code__r.Name}
                            >
                                {claim.Promotion_Code__r.Name}
                            </div>
                            </template>
                        </td>
                        <td>
                            <template if:true={claim.Promotion_Code__c}>
                            <div
                                class="slds-truncate"
                                title={claim.Promotion_Code__r.Promo_Code__c}
                            >
                                {claim.Promotion_Code__r.Promo_Code__c}
                            </div>
                            </template>
                        </td>
                        <td>
                            <div
                                class="slds-truncate"
                                title={claim.Total_Claim_Amount__c}
                            >
                                <lightning-formatted-number value={claim.Total_Claim_Amount__c} format-style="currency" currency-code="USD">
                                </lightning-formatted-number>
                            </div>
                        </td>
                        <td>
                            <div
                                class="slds-truncate"
                                title={claim.CreatedDate}
                            >
                                {claim.CreatedDateStr}
                            </div>
                        </td>
                        <td>
                            <div
                                class="slds-truncate"
                                title={claim.LastModifiedDate}
                            >
                                {claim.LastModifiedDateStr}
                            </div>
                        </td>
                        <td>
                            <template if:true={claim.CreatedBy}>
                                <div
                                    class="slds-truncate"
                                    title={claim.CreatedBy.Name}
                                >
                                    {claim.CreatedBy.Name}
                                </div>
                            </template>
                        </td>
                        <td>
                            <div class="slds-truncate" title={claim.Claim_Status__c}>
                                <!-- add haibo: french -->
                                {claim.claimStatusLabel}
                            </div>
                        </td>
                        <td>
                            <lightning-button-icon
                                icon-name="utility:preview"
                                alternative-text="View"
                                data-id={claim.Id}
                                class="slds-m-left_xx-small"
                                title="View"
                                onclick={handleClaimClickView}
                            >
                            </lightning-button-icon>
                            <template if:true={claim.isDraft}>
                                <lightning-button-icon
                                    icon-name="utility:edit"
                                    alternative-text="Edit"
                                    data-id={claim.Id}
                                    class="slds-m-left_xx-small"
                                    title="Edit"
                                    onclick={handleClaimClickEdit}
                                >
                                </lightning-button-icon>
                            </template>
                        </td>
                    </tr>
                </template>
            </tbody>
        </table>
        <div class="slds-m-top_medium">
            <c-ccm-pagination
                total-counts={totalCounts}
                page-size={pageSize}
                ongotopage={handleGoToPage}
                onpagesizechange={handlePageSizeChange}
                current-page-index={pageIndex}
            ></c-ccm-pagination>
        </div>
        <template if:true={showSpinner}>
            <div class="spinner-wrapper" style="position: absolute;top: 0;bottom: 0;left: 0;right: 0;">
                <lightning-spinner alternative-text="Loading" size="medium"></lightning-spinner>
            </div>
        </template>
    </div>
    <c-ccm-excel-download file-name={excelFileName}></c-ccm-excel-download>
</template>