/**
 * @Author:          <PERSON>
 * @Date:            2018-09-20
 * @Description:
 * @Test_Class:      HerokuAPI
 * @Related_Class:
 * @Last_Modified_by: <PERSON>
 * @Last_Modified_time: 2018-09-30 14:25:50
 * @Modifiy_Purpose: 
 */
@isTest
private class HerokuAPITest {
	@testSetup static void methodName() {     //该注解与Java中的@SetUp功能类似
		//RecordType rd = TestDataFactory.getRecordType('PersonAccount', 'Account');
		RecordType rd = TestDataFactory.getRecordType('EGO_Customer', 'Account');
		RecordType kitRecord = TestDataFactory.getRecordType('Kit', 'Product2');
		RecordType alertRecord = TestDataFactory.getRecordType('Email_Alert', 'System_Configuration__c');

		Account accPerson = new Account();
    	accPerson.RecordTypeId = rd.Id;
    	accPerson.FirstName = 'testFirstName';
    	accPerson.LastName = 'testLastName';
    	accPerson.PersonEmail = '<EMAIL>';
    	accPerson.Product_Type__c='EGO';
    	// accPerson.Brand__c = 'EGO';
    	insert accPerson;

    	Case cs = TestDataFactory.createCase(); 
    	cs.AccountId = accPerson.Id;
    	cs.Brand_Name__c = 'EGO';
    	insert cs;
        
        Pricebook2 standardPricebook = new Pricebook2(Id = Test.getStandardPricebookId(),IsActive = true);
        
        List<Product2> prodList = new List<Product2>();
        Product2 testMasterPro = TestDataFactory.createProduct();
    	testMasterPro.Country_of_Origin__c = 'United States';
    	testMasterPro.ProductCode = 'ST1502SF';
    	testMasterPro.Kit__c = true;
    	testMasterPro.RecordTypeId = kitRecord.Id;
        testMasterPro.Source__c = 'EBS';
        prodList.add(testMasterPro);

        Product2 testMasterPro1= TestDataFactory.createProduct();
        testMasterPro1.Country_of_Origin__c = 'United States';
        testMasterPro1.ProductCode = 'BA1502SF';
        testMasterPro1.Kit__c = true;
        testMasterPro1.Source__c = 'EBS';
        testMasterPro1.RecordTypeId = kitRecord.Id;

        Product2 testMasterPro2= TestDataFactory.createProduct();
        testMasterPro2.Country_of_Origin__c = 'United States';
        testMasterPro2.ProductCode = 'BA1502SR';
        testMasterPro2.Kit__c = true;
        testMasterPro2.Source__c = 'EBS';
        testMasterPro2.RecordTypeId = kitRecord.Id;
        prodList.add(testMasterPro2);

    	insert prodList;

		PricebookEntry pe = new PricebookEntry(pricebook2Id = standardPricebook.id, product2Id = testMasterPro.Id,UnitPrice=30);
        insert pe;
        
    	Order orderNew = TestDataFactory.createOrder();
    	orderNew.AccountId = accPerson.Id;
    	orderNew.Pricebook2Id = System.Test.getStandardPricebookId();
    	orderNew.Case__c = cs.Id;
    	insert orderNew;
        
        OrderItem oi = new OrderItem();
        oi.OrderId = orderNew.id;
        oi.UnitPrice = 7;
        oi.Quantity = 1;
        oi.PricebookEntryId = pe.Id;
        insert oi;

    	Warranty__c war = TestDataFactory.createWarranty();
    	war.AccountCustomer__c = accPerson.Id;
    	insert war;
		
        RecordType prt = [SELECT id FROM RecordType WHERE sobjecttype = 'Product2' and developerName = 'Product' LIMIT 1]; 
        RecordType prt1 = [SELECT id FROM RecordType WHERE sobjecttype = 'Product2' and developerName = 'Kit' LIMIT 1]; 
        List<Product2> updProdList = new List<Product2>();
    	Product2 testPro = TestDataFactory.createProduct();
    	testPro.Country_of_Origin__c = 'United States';
    	testPro.ProductCode = 'BA1400';
    	testPro.Name = 'EGO 56V BA1400 Battery 2.5Ah';
    	testPro.Kit__c = false;
    	testPro.Parent_Product__c = testMasterPro.Id;
    	testPro.Type__c = 'Battery';
        testPro.RecordTypeId = prt.Id;
        updProdList.add(testPro);

        Product2 testPro1 = TestDataFactory.createProduct();
        testPro1.Country_of_Origin__c = 'United States';
        testPro1.ProductCode = 'BA1400T';
        testPro1.Name = 'EGO 56V BA1400 Battery 2.5Ah';
        testPro1.Kit__c = false;
        testPro1.Parent_Product__c = testMasterPro.Id;
        testPro1.Type__c = 'Battery';
        testPro1.RecordTypeId = prt.Id;
        updProdList.add(testPro1);

        Product2 testPro2 = TestDataFactory.createProduct();
        testPro2.Country_of_Origin__c = 'United States';
        testPro2.ProductCode = 'QC1400';
        testPro2.Name = 'EGO 56V BA1400 Charger 2.5Ah';
        testPro2.Kit__c = false;
        testPro2.Parent_Product__c = testMasterPro.Id;
        testPro2.Type__c = 'Charger';
        testPro2.RecordTypeId = prt.Id;
        updProdList.add(testPro2);
        insert updProdList;

        List<Kit_Item__c> litItemList = new List<Kit_Item__c>();
    	Kit_Item__c kitItem1 = TestDataFactory.createKitItem(testMasterPro.Id, testPro.Id);
        kitItem1.Sequence__c = 'test';
        litItemList.add(kitItem1);
        Kit_Item__c kitItem2 = TestDataFactory.createKitItem(testMasterPro.Id, testPro1.Id);
        kitItem2.Sequence__c = 'test';
        litItemList.add(kitItem2);
        Kit_Item__c kitItem3 = TestDataFactory.createKitItem(testMasterPro.Id, testPro2.Id);
        litItemList.add(kitItem3);
    	insert litItemList;

    	Warranty_Item__c wi = TestDataFactory.createWarrantyItem(war.Id);
    	wi.Product__c = testPro.Id;
    	insert wi;

    	System_Configuration__c sc = new System_Configuration__c();
    	sc.RecordTypeId = alertRecord.Id;
    	sc.Name = 'WebSite_EmailSender';
    	sc.EmailAddress__c = '<EMAIL>';
    	insert sc;

    	System_Configuration__c sc2 = new System_Configuration__c();
    	sc2.RecordTypeId = alertRecord.Id;
    	sc2.Name = 'APP_EmailSender';
    	sc2.EmailAddress__c = '<EMAIL>';
    	insert sc2;
	}

	private static Account getUser(){
		for (Account eachAcc : [SELECT Id, Name, LastName, FirstName, PersonEmail,Product_Type__c,Account.Site_Origin__pc 
								FROM Account 
								WHERE PersonEmail =: '<EMAIL>']) {
			return eachAcc;
		}
		return null;
	}

	private static Case getCase(){
		Account acc = getUser();
		for (Case eachCase : [SELECT Id, AccountId
								FROM Case 
								WHERE AccountId =: acc.Id]) {
			return eachCase;
		}
		return null;
	}

    static testMethod void testGetUser() {
		RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'getUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd8321507ea76d3c9dab6f7df553bfc3e');        
		restReq.addHeader('emailAddress', '<EMAIL>');        
		restReq.addHeader('brandName', 'ego');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testSelectUserData(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'selectUserData');        
		restReq.addHeader('appKey', 'IoTHerokuToSF');        
		restReq.addHeader('sign', '955f1c43a3a8f1ce290f9ff99786052f');        
		restReq.addHeader('emailAddress', '<EMAIL>');        
		restReq.addHeader('brandName', 'ego');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testGetUserData(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'getUserData');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '358f223a57dbb0e271301f409cb83489');        
		restReq.addHeader('customerId', getUser().Id); 

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testGetUserInfo() {
		RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'getUserInfo');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '1aa6665330f23bf554e37e09fcb1308b');        
		restReq.addHeader('customerId', getUser().Id); 

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testCheckUserExist(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'checkUserExist'); 
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'fa1966771e9b69a27a9dc13fa7ad1db5');        
		restReq.addHeader('customerId', getUser().Id);

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testListAllOrder(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'listAllOrder');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '73b2c49f93ac819688896d5deb25bc96');        
		restReq.addHeader('customerId', getUser().Id);

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testListCase(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'listCase');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd7d77ac37aae6823f876c629dc01c36d');        
		restReq.addHeader('customerId', getUser().Id);
		restReq.addHeader('brandName', 'EGO');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testListCaseType(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'listCaseType');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '2ae7917c29b4142ef223e0feabcdb6dc');        
		restReq.addHeader('customerId', getUser().Id);

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testCloseCase(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'closeCase');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd5808f5873fed5662bcb3de407146a04');        
		restReq.addHeader('caseId', getCase().Id);        
		restReq.addHeader('customerId', getUser().Id);

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testReopenCase(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'reopenCase');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd622585da17952602b1c3628287da43d');        
		restReq.addHeader('caseId', getCase().Id);        
		restReq.addHeader('customerId', getUser().Id);

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testListWarranty(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'listWarranty');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '8908eab5532ff998d9399ae704800e56');     
		restReq.addHeader('customerId', getUser().Id);

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testSendEmailReset(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'sendEmail');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '462e73f9d792dd917e516423c35cab90');     
		restReq.addHeader('customerId', getUser().Id);
		restReq.addHeader('emailType', 'reset');
		restReq.addHeader('jwtToken', 'AAAAAAAAAAAA');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testSendEmailResetIoT(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'sendEmail');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '462e73f9d792dd917e516423c35cab90');     
		restReq.addHeader('customerId', getUser().Id);
		restReq.addHeader('emailType', 'resetIoT');
		restReq.addHeader('jwtToken', 'AAAAAAAAAAAA');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testSendEmailMigrate(){
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'sendEmail');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '462e73f9d792dd917e516423c35cab90');     
		restReq.addHeader('customerId', getUser().Id);
		restReq.addHeader('emailType', 'migrate');
		restReq.addHeader('jwtToken', 'AAAAAAAAAAAA');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testWrongOne(){
    	// 错误一：请求头为空
    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testWrongTwo(){
		// 错误二：没有方法名
		RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';       
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '462e73f9d792dd917e516423c35cab90');     
		restReq.addHeader('customerId', getUser().Id);
		restReq.addHeader('emailType', 'migrate');
		restReq.addHeader('jwtToken', 'AAAAAAAAAAAA');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testWrongThree(){
		// 错误三：没有签名
		RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'sendEmail');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');             
		restReq.addHeader('customerId', getUser().Id);
		restReq.addHeader('emailType', 'migrate');
		restReq.addHeader('jwtToken', 'AAAAAAAAAAAA');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testWrongFour(){
		// 错误四：签名错误
		RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'sendEmail');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '462e73f9d792dd917ec35cab90');     
		restReq.addHeader('customerId', getUser().Id);
		restReq.addHeader('emailType', 'migrate');
		restReq.addHeader('jwtToken', 'AAAAAAAAAAAA');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testCreateUser(){
    	Test.startTest();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'createUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '4bc82df64259d9a252e72567a2dd1980');        
		restReq.requestBody = Blob.valueOf('{"brandName":"EGO","emailAddress":"<EMAIL>","firstName":"zack","lastName":"zhai","password":"pass"}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testCreateEUUser(){
    	Test.startTest();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'createUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '4bc82df64259d9a252e72567a2dd1980');        
		restReq.requestBody = Blob.valueOf('{"brandName":"EGO","emailAddress":"<EMAIL>","firstName":"zack","lastName":"zhai","password":"pass", "siteOrigin": "United Kingdom","address1": "test","country": "Untited Kingdom","postcode": "12345","address2": "city","address3": "state","company": "comapny","marketingOptIn": true}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }


    static testMethod void testUpdateUser(){
    	Test.startTest();

    	Account testAcc = getUser();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'updateUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd27986c242d4056fc71e912c0ae77693');

		restReq.requestBody = Blob.valueOf('{"brandName":"EGO","customerId":"'+testAcc.Id+'","emailAddress":"<EMAIL>","firstName":"Zack","lastName":"Zhai","mobilePhone":"***********","password":"Pass","sendMarketingEmails":true}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testUpdateEUEGOUser(){
    	Test.startTest();

    	Account testAcc = getUser();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'updateUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd27986c242d4056fc71e912c0ae77693');

		restReq.requestBody = Blob.valueOf('{"brandName":"EGO","customerId":"'+testAcc.Id+'","emailAddress":"<EMAIL>","firstName":"Zack","lastName":"Zhai","mobilePhone":"***********","password":"Pass","sendMarketingEmails":true,"siteOrigin": "United Kingdom","address1": "test","country": "Untited Kingdom","postcode": "12345","address2": "city","address3": "state","company": "comapny","marketingOptIn": true}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    /*static testMethod void testUpdateEUSkilUser(){
    	Test.startTest();

    	Account testAcc = getUser();
    	testAcc.Product_Type__c = 'Skil';
    	update(testAcc);

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'updateUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd27986c242d4056fc71e912c0ae77693');

		restReq.requestBody = Blob.valueOf('{"brandName":"Skil","customerId":"'+testAcc.Id+'","emailAddress":"<EMAIL>","firstName":"Zack","lastName":"Zhai","mobilePhone":"***********","password":"Pass","sendMarketingEmails":true,"siteOrigin": "United Kingdom","address1": "test","country": "Untited Kingdom","postcode": "12345","address2": "city","address3": "state","company": "comapny","marketingOptIn": true}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testUpdateEUSkilSawUser(){
    	Test.startTest();

    	Account testAcc = getUser();
    	testAcc.Product_Type__c = 'SkilSaw';
    	update(testAcc);

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'updateUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd27986c242d4056fc71e912c0ae77693');

		restReq.requestBody = Blob.valueOf('{"brandName":"SkilSaw","customerId":"'+testAcc.Id+'","emailAddress":"<EMAIL>","firstName":"Zack","lastName":"Zhai","mobilePhone":"***********","password":"Pass","sendMarketingEmails":true,"siteOrigin": "United Kingdom","address1": "test","country": "Untited Kingdom","postcode": "12345","address2": "city","address3": "state","company": "comapny","marketingOptIn": true}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }*/

    static testMethod void testCreateCase(){
    	Test.startTest();

    	Account testAcc = getUser();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'createCase');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '9886ce39466e9908a19f043cd209e157');

		restReq.requestBody = Blob.valueOf('{"attachmentURL":"https://www.attachment_URL.com","caseType":"Warranty Order","customerId":"'+testAcc.Id+'","description":"description","phoneNumber":"**********"}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testRegisterWarranty(){
    	Test.startTest();

    	Account testAcc = getUser();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'registerWarranty');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '717a04cb74efe4baeac2b29ddf595daf');

		restReq.requestBody = Blob.valueOf('{"customerId":"' + testAcc.Id + '","kit":false,"masterModelNumber":"ST1502SF","placeOfPurchase":"Home_Depot","productUseType":"Residential","purchaseDate":"110817","receiptStatus":"lostReceipt","receiptUrl":"https://test.com.cn/url","warrantyItems":[{"productModelNumber":"BA1400","productName":"EGO 56V Li-Ion 21\' Mower 7.5 Ah Kit (Self-Propel)","productType":"Battery","serialNumber":"110817"}]}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testRegisterWarranty1(){
        Test.startTest();

        Account testAcc = getUser();

        RestRequest restReq = new RestRequest();
        restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
        restReq.httpMethod = 'POST';
        restReq.addHeader('method', 'registerWarranty');        
        restReq.addHeader('appKey', 'R@CWebSiteAPI');        
        restReq.addHeader('sign', '717a04cb74efe4baeac2b29ddf595daf');

        restReq.requestBody = Blob.valueOf('{"customerId":"' + testAcc.Id + '","kit":false,"masterModelNumber":"ST1502SF","placeOfPurchase":"Home_Depot","productUseType":"Residential","purchaseDate":"110817","receiptStatus":"lostReceipt","receiptUrl":"https://test.com.cn/url","warrantyItems":[{"productModelNumber":"BA1400","productName":"EGO 56V Li-Ion 21\' Mower 7.5 Ah Kit (Self-Propel)","productType":"Battery","serialNumber":"110817"},{"productModelNumber":"BA1400T","productName":"EGO 56V Li-Ion 21\' Mower 7.5 Ah Kit (Self-Propel)","productType":"Battery","serialNumber":"110817"},{"productModelNumber":"QC1400","productName":"EGO 56V Li-Ion 21\' Mower 7.5 Ah Kit (Self-Propel)","productType":"Product","serialNumber":"110817"}]}');
        RestResponse restRes = new RestResponse();

        RestContext.request = restReq;
        RestContext.response = restRes;

        HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
        System.debug('----DoGetResult:' + result);

        Test.stopTest();
    }

    static testMethod void testRegisterWarrantyWrong(){
    	Test.startTest();

    	Account testAcc = getUser();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'registerWarranty');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '717a04cb74efe4baeac2b29ddf595daf');

		restReq.requestBody = Blob.valueOf('{"customerId":"' + testAcc.Id + '","kit":false,"masterModelNumber":"ST150","placeOfPurchase":"Home_Depot","productUseType":"Residential","purchaseDate":"110817","receiptStatus":"lostReceipt","receiptUrl":"https://test.com.cn/url","warrantyItems":[{"productModelNumber":"BA1400","productName":"EGO 56V Li-Ion 21\' Mower 7.5 Ah Kit (Self-Propel)","productType":"Battery","serialNumber":"110817"}]}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testWrongFive(){
    	// POST请求中没有请求头
    	Test.startTest();

    	Account testAcc = getUser();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testWrongSix(){
    	// POST请求中没有对应的接口
    	Test.startTest();

    	Account testAcc = getUser();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'regWarranty');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '7f0697f1431eada714e44a2685cbaaf7');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testWrongSeven(){
    	// GET请求中没有对应的接口
    	Test.startTest();

    	Account testAcc = getUser();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'regWarranty');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '7f0697f1431eada714e44a2685cbaaf7');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testUpdateUser2(){
    	Test.startTest();

    	Account testAcc = getUser();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'updateUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd27986c242d4056fc71e912c0ae77693');

		restReq.requestBody = Blob.valueOf('{"brandName":"Skil","customerId":"'+testAcc.Id+'","emailAddress":"<EMAIL>","firstName":"Zack","lastName":"Zhai","mobilePhone":"***********","password":"Pass","sendMarketingEmails":true}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testUpdateUser3(){
    	Test.startTest();

    	Account testAcc = getUser();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'updateUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd27986c242d4056fc71e912c0ae77693');

		restReq.requestBody = Blob.valueOf('{"brandName":"SkilSaw","customerId":"'+testAcc.Id+'","emailAddress":"<EMAIL>","firstName":"Zack","lastName":"Zhai","mobilePhone":"***********","password":"Pass","sendMarketingEmails":true}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    /*static testMethod void testCreateUser2(){
    	Test.startTest();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'createUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '4bc82df64259d9a252e72567a2dd1980');        
		restReq.requestBody = Blob.valueOf('{"brandName":"Skil","emailAddress":"<EMAIL>","firstName":"zack","lastName":"zhai","password":"pass"}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }*/

    static testMethod void testCreateUser2(){
    	Test.startTest();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'createUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '4bc82df64259d9a252e72567a2dd1980');        
		restReq.requestBody = Blob.valueOf('{"brandName":"Skil","emailAddress":"<EMAIL>","firstName":"zack","lastName":"zhai","password":"pass", "siteOrigin": "United Kingdom","address1": "test","country": "Untited Kingdom","postcode": "12345","address2": "city","address3": "state","company": "comapny","marketingOptIn": true}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testCreateUser3(){
    	Test.startTest();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'createUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '4bc82df64259d9a252e72567a2dd1980');        
		restReq.requestBody = Blob.valueOf('{"brandName":"SkilSaw","mobilePhone":"11","sendMarketingEmails":true,"emailAddress":"<EMAIL>","firstName":"zack","lastName":"zhai","password":"pass", "siteOrigin": "United Kingdom","address1": "test","country": "Untited Kingdom","postcode": "12345","address2": "city","address3": "state","company": "comapny","marketingOptIn": true}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testCreateUser4(){
    	Test.startTest();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'createUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '4bc82df64259d9a252e72567a2dd1980');        
		restReq.requestBody = Blob.valueOf('{"brandName":"Skil","mobilePhone":"11","sendMarketingEmails":true,"emailAddress":"<EMAIL>","firstName":"zack","lastName":"zhai","password":"pass", "siteOrigin": "United Kingdom","address1": "test","country": "Untited Kingdom","postcode": "12345","address2": "city","address3": "state","company": "comapny","marketingOptIn": true}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testCreateUser5(){
    	Test.startTest();

    	RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'POST';
		restReq.addHeader('method', 'createUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', '4bc82df64259d9a252e72567a2dd1980');        
		restReq.requestBody = Blob.valueOf('{"brandName":"SkilSaw","mobilePhone":"11","sendMarketingEmails":true,"emailAddress":"<EMAIL>","firstName":"zack","lastName":"zhai","password":"pass", "siteOrigin": "United Kingdom","address1": "test","country": "Untited Kingdom","postcode": "12345","address2": "city","address3": "state","company": "comapny","marketingOptIn": true}');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doPost();        
		System.debug('----DoGetResult:' + result);

		Test.stopTest();
    }

    static testMethod void testGetUser2() {
		RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'getUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd8321507ea76d3c9dab6f7df553bfc3e');        
		restReq.addHeader('emailAddress', '<EMAIL>');        
		restReq.addHeader('brandName', 'skil');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testGetUser3() {
		RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'getUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd8321507ea76d3c9dab6f7df553bfc3e');        
		restReq.addHeader('emailAddress', '<EMAIL>');        
		restReq.addHeader('brandName', 'SkilSaw');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testGetUser4() {
		RestRequest restReq = new RestRequest();
		restReq.requestURI = '/services/apexrest/HerokuApi/v1/';
		restReq.httpMethod = 'GET';
		restReq.addHeader('method', 'getUser');        
		restReq.addHeader('appKey', 'R@CWebSiteAPI');        
		restReq.addHeader('sign', 'd8321507ea76d3c9dab6f7df553bfc3e');        
		restReq.addHeader('emailAddress', '<EMAIL>');        
		restReq.addHeader('brandName', '');

		RestResponse restRes = new RestResponse();

		RestContext.request = restReq;
		RestContext.response = restRes;

		HerokuEntity.ResponseEntity result = HerokuAPI.doGet();        
		System.debug('----DoGetResult:' + result);
    }

    static testMethod void testMethod10() {
		HerokuAPIUtils.getResetPasswordEmailTemplate(getUser());
		HerokuAPIUtils.sendResetPasswordEmail(getUser(),'test');
    }


}