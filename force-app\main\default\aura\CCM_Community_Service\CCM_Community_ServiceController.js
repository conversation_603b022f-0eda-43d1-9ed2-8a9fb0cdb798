/**
 * Created by gluo006 on 8/12/2019.
 */
({
    doInit: function (component, event, helper) {
        // add haibo: french
        component.set('v.purchaseUseTypeList', [
            {
                fr: $A.get("$Label.c.CCM_Portal_IndustrialProfessionalCommercial"),
                en: 'Industrial/Professional/Commercial'
            },
            {
                fr: $A.get("$Label.c.CCM_Portal_Residential"),
                en: 'Residential'
            },
            {
                fr: $A.get("$Label.c.CCM_Portal_Rental"),
                en: 'Rental'
            }
        ]);
        component.set("v.brandList", ($A.get("$Label.c.CCM_Service_Claim_Brand_Default_List") || "").split(";"));
        let action = component.get("c.getDealerInfo");
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === "SUCCESS") {
                var result1 = JSON.parse(response.getReturnValue());
                let boolIsAltaQuip = result1.boolIsAltaQuip;
                component.set("v.boolIsAltaQuip", boolIsAltaQuip);
                if (boolIsAltaQuip === true) {
                    component.set("v.diagnosisFee", 0);
                }
                if (result1.profileName.includes('Distributor')) {
                    component.set("v.isNotDistributor", false);
                }
                if (result1.profileName.includes('ServiceLive')) {
                    component.set('v.isServiceLive', true);
                    component.set("v.diagnosisFee", 0);
                }
                component.set('v.orgCode', result1.orgCode);
                let host = window.location.origin;
                component.set("v.vfHost", host);
                let currency = $A.get("$Locale.currencyCode");
                component.set("v.currencySymbol", currency);
                let isView = helper.getUrlParameter("view");
                let isEdit = helper.getUrlParameter("edit");
                let claimId = helper.getUrlParameter("recordId");
                if (isView) {
                    component.set("v.isView", isView);
                    component.set("v.isDisabled", true);
                    component.set("v.isAddPartsDisabled", true);
                    helper.getViewInfo(component, claimId);
                } else if (isEdit) {
                    component.set("v.isEdit", isEdit);
                    helper.getViewInfo(component, claimId);
                } else {
                    let columns = [
                        { label: $A.get("$Label.c.CCM_Portal_NO"), fieldName: "Index_Number__c" },
                        { label: $A.get("$Label.c.CCM_Portal_ProductModelNum"), fieldName: "Parts__r.ProductCode" },
                        { label: $A.get("$Label.c.CCM_Portal_PartNumber"), fieldName: "Parts__r.Item_Number__c" },
                        { label: $A.get("$Label.c.CCM_Portal_Description"), fieldName: "Parts__r.Description" }
                    ];
                    component.set("v.columns", columns);
                    //initial the total price add the diagnosis fee
                    component.set("v.totalPrice", component.get("v.diagnosisFee"));
                }
                let result = new Date().getFullYear() + "-" + Number(new Date().getMonth() - 11) + "-" + new Date().getDate();
                let minDate = $A.localizationService.formatDate(result, "MMMM dd yyyy");
                component.set("v.minDate", minDate);
                console.log("minDate--->" + minDate);
            } else {
                $A.get("e.force:showToast")
                    .setParams({
                        title: $A.get("$Label.c.CCM_Portal_Error"),
                        message: response.getError()[0].message,
                        type: "Error"
                    })
                    .fire();
            }
        });
        $A.enqueueAction(action);

        if (!helper.getUrlParameter("recordId")) {
            helper.getExplanationOptions(component);
        }
        helper.getCaseExplanationOptions(component);
    },
    changeLabourHour: function (component, event, helper) {
        helper.calculateTotalHourAndPrice(component);
    },
    afterScriptsLoaded: function () {

    },
    handleDropOfDateChange: function (component, event, helper) {
        var dropOfDate = component.get('v.dropOfDate');
        var today = new Date();
        if (new Date(dropOfDate) <= today) {
            helper.calculateWarrantyStatus(component);
            var partsItemList = component.get('v.partsItemList');
            var newPartsList = [];
            partsItemList.forEach(function (item) {
                if (item.isWearable) {
                    var purchaseDate = component.get('v.purchaseDate');
                    var warrantyDate = new Date(new Date().setDate(new Date(component.get('v.purchaseDate')).getDate() + item.warrantyDate));
                    warrantyDate = warrantyDate.getFullYear() + '-' + Number(warrantyDate.getMonth() + 1) + '-' + warrantyDate.getDate();
                    if (new Date(warrantyDate) < new Date(dropOfDate)) {
                        item.showWearable = true;
                        item.price = item.fakePrice;
                        item.total = item.fakePrice;
                        helper.calculateTotalHourAndPrice(component);
                    } else {
                        item.price = item.proPrice;
                        item.showWearable = false;
                        helper.calculateTotalHourAndPrice(component);
                    }
                }
                newPartsList.push(item);
            })
            component.set('v.partsItemList', newPartsList);
        } else {
            $A.get("e.force:showToast").setParams({
                "title": $A.get("$Label.c.CCM_Portal_Error"),
                "message": $A.get("$Label.c.CCM_Portal_Thedropofdatemustbebeforeorthesameastoday"),
                "type": "Error"
            }).fire();
            component.set('v.dropOfDate', today);
        }
        let action = component.get("c.GenerateProjectPicklistValue");
        action.setParams({
            "productId": component.get('v.modelNum'),
            "customerId": component.get('v.customerId'),
            "dropOffDate": component.get('v.dropOfDate')
        });

        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                var data = JSON.parse(results);
                if (data.Name.length !== 0) {
                    component.set('v.isValidRecall', true);
                } else {
                    component.set('v.isValidRecall', false);
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                    }
                } else {
                    alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                }
            }
        });
        $A.enqueueAction(action);

        // check wearing parts when drop off date change
        let serviceOption = component.get('v.serviceOption');
        let repairType = component.get('v.repairType');
        if (serviceOption === 'Repair' && repairType === 'Parts') {
            // check repairable parts
            let majorIssueObj = component.get('v.majorIssueObj');
            let partsAvailable = helper.checkWearablePartsAvaibility(component, majorIssueObj, component.get('v.purchaseDate'), component.get('v.purchaseUseType'), component.get('v.partsItemList'), undefined);
            if (!partsAvailable) {
                component.set('v.partsItemList', []);
                helper.calculateTotalHourAndPrice(component);
                return;
            }

            // check additional parts
            var partsItemList = component.get('v.partsItemList');
            var labourHours = 0;
            labourHours = component.get('v.labourHours');
            for (let idx = partsItemList.length - 1; idx >= 0; idx--) {
                let item = partsItemList[idx];
                let partsAvailable = helper.checkWearablePartsAvaibility(component, item, component.get('v.purchaseDate'), component.get('v.purchaseUseType'), partsItemList, idx);
                if (partsAvailable) {
                    if (labourHours) {
                        labourHours = Number(labourHours) + Number(item.LaborTime) * Number(item.quantity);
                    }
                    else {
                        labourHours = Number(item.LaborTime) * Number(item.quantity);
                    }
                }
            }
            let finalHours = 0;
            if(component.get('v.overTimeHour') !== '' && component.get('v.overTimeHour') != '0'){
                labourHours = component.get('v.overTimeHour');
            }
            else {
                let additionalHour = component.get('v.additionalTimeHour');
                if (additionalHour) {
                    finalHours = Number(labourHours) + Number(additionalHour);
                }
            }
            component.set('v.labourHours', Number(labourHours));
            component.set('v.finialLaborHour', Number(finalHours));
            helper.calculateTotalHourAndPrice(component);
        }

    },
    handleRepairDateChange: function (component, event, helper) {
        var dropDate = component.find('dropOfDate').get('v.value');
        var repairDate = component.find('repairDate').get('v.value');
        if (repairDate < dropDate) {
            $A.get("e.force:showToast").setParams({
                "title": $A.get("$Label.c.CCM_Portal_Error"),
                "message": $A.get("$Label.c.CCM_Portal_Therepairdatemustbelaterthanorthesameasthedropoffdate"),
                "type": "Error"
            }).fire();
        } else {
            var warrantyStatus = component.get('v.realWarrantyStatus');
            // add haibo: french
            if (warrantyStatus == 'Vailid Warranty') {
                component.set('v.warrantyStatus', 'Vailid Warranty');
                component.set('v.warrantyStatusFrench', $A.get("$Label.c.CCM_Portal_ValidWarranty"));
            } else {
                component.set('v.warrantyStatus', 'Out of Warranty');
                component.set('v.warrantyStatusFrench', $A.get("$Label.c.CCM_Portal_OutOfWarranty"));
            }
        }
    },
    getCustomerInfo: function (component, event, helper) {
        var email = component.get('v.emailAddress');
        let firstName = component.get('v.firstName');
        let lastName = component.get('v.lastName');
        if (email || firstName || lastName) {
            helper.getCustomerInfoByEmail(component);
        } else {
            component.set('v.firstName', '');
            component.set('v.lastName', '');
        }
    },
    getInfoBySerialNumber: function (component, event, helper) {
        // component.set("v.brandList", ($A.get("$Label.c.CCM_Service_Claim_Brand_Default_List") || "").split(";"));
        helper.getInfoBySerialNum(component);
    },
    getWarrantyInfo: function (component, event, helper) {
        let itemid = event.getSource().get("v.value"),
        warrantyItemList = component.get("v.modelNumList") || [],
        selectedWarrantyItem = warrantyItemList.find(item => item.Id === itemid) || {},
        { Id } = selectedWarrantyItem;
        
        component.set('v.additionalTimeHour', '0');
        component.set('v.totalPrice', '0');
        component.set('v.serviceOption', '');
        //reset the data
        component.set('v.repairType', '');
        component.set('v.inventory', '');
        component.set('v.repairType', '');
        component.set('v.failureCode', '');
        component.set('v.majorIssueObj', {});
        component.set('v.recallOption', '');
        component.set('v.project', '');
        component.set('v.labourHours', 0);
        component.set('v.isDisabled', false);
        component.set('v.isAddPartsDisabled', false);
        component.set('v.isDiagramDisabled', false);
        component.set('v.recallOptionList', []);
        component.set('v.laborCostSubtotal', 0);
        component.set('v.finialLaborHour', 0);
        component.set('v.partsCost', 0.00);
        component.set('v.markup', 0.00);
        component.set('v.replacementBaseFee', 0);
        component.set('v.Level1ExplanationOption', '');
        component.set('v.Level2ExplanationOption', '');
        component.set('v.Level2ExplanationOptionExtra', '');
        component.set('v.Level2ExplanationOptionFinish', '');
        component.set('v.Level2EORequired', false);
        component.set('v.Level2ExtraRequired', false);
        component.set('v.repairPartsName', '');
        component.set('v.partsItemList', []);
        helper.hideEle(component, 'repairType');
        helper.hideEle(component, 'recallType');
        helper.hideEle(component, 'replacementType');
        helper.hideEle(component, 'addressInfo');
        helper.hideEle(component, 'contactCustomerService');
        helper.hideEle(component, 'EXReminder');
        helper.hideEle(component,'Level2ExplanationDetails');
        helper.showEle(component, 'repairTypeSelect');
        helper.showEle(component, 'failureCode');

        let selectProductId = '';
        if(selectedWarrantyItem && selectedWarrantyItem.Product__r) {
            selectProductId = selectedWarrantyItem.Product__r.Id;
            component.set('v.modelNum', selectProductId);
            component.set('v.selectedItemId', selectedWarrantyItem.Id);

        }
        else {
            component.set('v.modelNum', selectProductId);
            component.set('v.selectedItemId', '');
        }
        helper.initPartsCombineLaborTimeCost(component);
        component.set("v.warrantyItemId", Id);
        component.set('v.WarrantyStatus', selectedWarrantyItem.ActualIndicator__c);
        component.set('v.realWarrantyStatus', selectedWarrantyItem.ActualIndicator__c);
        if (selectProductId == '') {
            component.set('v.placeOfPurchase', '');
            component.set('v.productName', '');
            component.set('v.boolPickup', false);
            component.set('v.isPickUp', false);
            component.set('v.purchaseDate', '');
            component.set('v.purchaseUseType', '');
            component.set('v.warrantyStatus', '');
        } else {
            var rateList = component.get('v.rateList');
            var modelNumList = component.get('v.modelNumList');
            modelNumList.forEach(function (item) {
                if (item.Id == Id) {
                    if (item.Warranty__r.Place_of_Purchase_picklist__c) {
                        component.set('v.placeOfPurchase', item.Warranty__r.Place_of_Purchase_picklist__c);
                    }
                    if (item.Product_Name__c) {
                        component.set('v.productName', item.Product_Name__c);
                    }
                    if (item.Product__r.Pick_Up__c === true) {
                        component.set('v.boolPickup', true);
                        component.set('v.isPickUp', true);
                    }
                    if (item.Product__r.Category_1__c) {
                        if (rateList.length > 0) {
                            rateList.forEach(e => {
                                if (e.Category__c == item.Product__r.Category_1__c) {
                                    component.set('v.LaborRate', e.Labor_Rate__c);
                                }
                            });
                        }
                    }
                    if (item.Warranty__r.Purchase_Date__c) {
                        component.set('v.purchaseDate', item.Warranty__r.Purchase_Date__c);
                    }
                    if (item.Warranty__r.Product_Use_Type2__c) {
                        // add haibo: french
                        if ($A.get("$Label.c.CCM_Portal_Language") == 'fr') {
                            let purchaseUseTypeList = component.get('v.purchaseUseTypeList');
                            purchaseUseTypeList.forEach((typeItem)=>{
                                if (typeItem.en == item.Warranty__r.Product_Use_Type2__c) {
                                    component.set('v.purchaseUseTypeFrench', typeItem.fr);
                                }
                            })
                        }
                        component.set('v.purchaseUseType', item.Warranty__r.Product_Use_Type2__c);
                    }
                    if (item.Warranty__r.AccountCustomer__r.PersonEmail) {
                        component.set('v.emailAddress', item.Warranty__r.AccountCustomer__r.PersonEmail);
                    }
                    if (item.Warranty__r.AccountCustomer__r.FirstName) {
                        component.set('v.firstName', item.Warranty__r.AccountCustomer__r.FirstName);
                    }
                    if (item.Warranty__r.AccountCustomer__r.LastName) {
                        component.set('v.lastName', item.Warranty__r.AccountCustomer__r.LastName);
                    }
                    if (item.Warranty__r.AccountCustomer__c) {
                        component.set('v.customerId', item.Warranty__r.AccountCustomer__c);
                    }

                    if (item.Brand_Name__c) {
                        component.set('v.brand', item.Brand_Name__c);
                        component.set('v.brandList', item.Brand_Name__c);
                    }
                    if (item.Expiration_Date_New__c) {
                        component.set('v.expirationDate', item.Expiration_Date_New__c);
                        helper.calculateWarrantyStatus(component);
                    }
                    if (!component.get('v.dropOfDate')) {
                        component.set('v.warrantyStatus', 'Pending');
                    } else {
                        helper.calculateWarrantyStatus(component);
                    }
                    if (item.Warranty__c) {
                        component.set('v.warrantyId', item.Warranty__c);
                    }
                    if (item.Product__r && (item.Product__r.Product_Type__c == 'Battery' || item.Product__r.Product_Type__c == 'Charger')) {
                        component.set('v.isProduct', false);
                    } else {
                        component.set('v.isProduct', true);
                    }
                    helper.calculateTotalHourAndPrice(component);
                    if (component.get("v.inventory") && component.get("v.inventory") == 'Dealer inventory') {
                        var action = component.get("c.ShowMessageByInventory");
                        action.setParams({
                            customerId: component.get("v.customerId"),
                            inventory: component.get("v.inventory"),
                            productId: component.get("v.modelNum")
                        });
                        action.setCallback(this, function (response) {
                            let state = response.getState();
                            if (state === "SUCCESS") {
                                let result = JSON.parse(response.getReturnValue());
                                let partsCost = result.PartsCost;
                                if ($A.util.isEmpty(partsCost) === false) {
                                    helper.updatePartsCost(component, partsCost);
                                }
                            } else {
                                $A.get("e.force:showToast")
                                    .setParams({
                                        title: $A.get("$Label.c.CCM_Portal_Error"),
                                        message: response.getError()[0].message,
                                        type: "Error"
                                    }).fire();
                            }
                        });
                        $A.enqueueAction(action);
                    }


                }
            });
        }
        helper.checkShowAdditionalInformation(component, selectedWarrantyItem.Product_Code__c);
        helper.searchAllPartsByProduct(component);
        // this.handleAltaquipClaimRestriction(component, event, helper);
    },
    handlePickupChosen: function (component, event, helper) {
        let strUsePickup = component.get("v.strUsePickup");
        if (strUsePickup === "No") {
            component.set("v.decPickupDistance", 0);
            component.set("v.decPickupFeeSubtotal", 0);
            helper.calculateTotalHourAndPrice(component);
        } else if (strUsePickup === "Yes") {
            component.set("v.decPickupFeeSubtotal", component.get("v.boolIsAltaQuip") ? 150 : 50);
            helper.calculateTotalHourAndPrice(component);
        }
    },
    handlePickupDistanceChange: function (component, event, helper) {
        let decInputValue = event.getSource().get("v.value"), decPickupDistance, strUsePickup;
        if (decInputValue === "" || Number.isNaN(decInputValue)) {
            component.set("v.decPickupDistance", 0);
        }
        decPickupDistance = component.get("v.decPickupDistance");
        if (component.get("v.boolIsAltaQuip")) {
            if (decPickupDistance < 25) {
                component.set("v.decPickupFeeSubtotal", 150);
                component.set("v.iscustomerpickupfee", true);
            } else if (decPickupDistance >= 25 && decPickupDistance <= 60) {
                component.set("v.decPickupFeeSubtotal", 250);
                component.set("v.iscustomerpickupfee", true);
            } else if (decPickupDistance > 60) {
                component.set("v.iscustomerpickupfee", false);
            }
        } else {
            if (decPickupDistance <= 50) {
                component.set("v.decPickupFeeSubtotal", 50);
            } else {
                component.set("v.decPickupFeeSubtotal", decPickupDistance);
            }
        }

        helper.calculateTotalHourAndPrice(component);
    },
    handlePickupFeeSubtotalChange: function (component, event, helper) {
        helper.calculateTotalHourAndPrice(component);
    },
    selectServiceOption: function (component, event, helper) {
        if (component.get('v.isView')) {
            return;
        }
        //reset the data
        component.set('v.repairType', '');
        component.set('v.inventory', '');
        component.set('v.repairType', '');
        component.set('v.failureCode', '');
        component.set('v.majorIssueObj', {});
        component.set('v.recallOption', '');
        component.set('v.project', '');
        component.set('v.labourHours', 0);
        component.set('v.isDisabled', false);
        component.set('v.isAddPartsDisabled', false);
        component.set('v.isDiagramDisabled', false);
        component.set('v.recallOptionList', []);
        component.set('v.laborCostSubtotal', 0);
        component.set('v.finialLaborHour', 0);
        component.set('v.partsCost', 0.00);
        component.set('v.markup', 0.00);
        component.set('v.replacementBaseFee', 0);
        component.set('v.Level1ExplanationOption', '');
        component.set('v.Level2ExplanationOption', '');
        component.set('v.Level2EORequired', false);
        component.set('v.Level2ExtraRequired', false);
        helper.hideEle(component, 'repairType');
        helper.hideEle(component, 'recallType');
        helper.hideEle(component, 'replacementType');
        helper.hideEle(component, 'addressInfo');
        helper.hideEle(component, 'contactCustomerService');
        helper.hideEle(component, 'EXReminder');
        helper.hideEle(component,'Level2ExplanationDetails');
        helper.showEle(component, 'repairTypeSelect');
        helper.showEle(component, 'failureCode');
        var serviceOption = event.getSource().get('v.value');
        component.set('v.serviceOption', serviceOption);
        if (component.get('v.isServiceLive')) {
            if (serviceOption == 'Service Attempt') {
                component.set('v.isAddPartsDisabled', true);
                component.set('v.isDiagramDisabled', true);
                component.set('v.partsItemList', []);
                component.set('v.labourHours', 0);
                component.set('v.tierId', null);
                component.set('v.tierlist', []);
                component.set('v.zipCode', null);
                component.set('v.zipCodeSearch', null);
                helper.calculateTotalHourAndPrice(component);
            }
            else if (serviceOption == 'Replacement') {
                component.set('v.isAddPartsDisabled', false);
                component.set('v.partsItemList', []);
                component.set('v.labourHours', 0);
                component.set('v.Level2EORequired', true);
                component.set('v.Level2ExtraRequired', true);
                helper.showEle(component, 'EXReminder');
                helper.hideEle(component, 'repairType');
                helper.hideEle(component, 'recallType');
                helper.showEle(component, 'replacementType');
                helper.hideEle(component, 'addressInfo');
                helper.selectTier(component, event, helper);
            }
            else if (serviceOption == 'Repair') {
                component.set('v.isAddPartsDisabled', false);
                helper.getFailureCode(component);
                helper.selectTier(component, event, helper);
                helper.showEle(component, 'repairType');
                helper.hideEle(component, 'recallType');
                helper.hideEle(component, 'replacementType');
                helper.hideEle(component, 'addressInfo');
            }
        } else {
            if (serviceOption == 'Replacement') {
                component.set('v.isAddPartsDisabled', false);
                component.set('v.partsItemList', []);
                component.set('v.labourHours', 0);
                component.set('v.Level2EORequired', true);
                component.set('v.Level2ExtraRequired', true);
                helper.calculateTotalHourAndPrice(component);
                helper.showEle(component, 'EXReminder');
                helper.hideEle(component, 'repairType');
                helper.hideEle(component, 'recallType');
                helper.showEle(component, 'replacementType');
                helper.hideEle(component, 'addressInfo');
            } else if (serviceOption == 'Repair') {
                component.set('v.isAddPartsDisabled', false);
                helper.getFailureCode(component);
                helper.calculateTotalHourAndPrice(component);
                helper.showEle(component, 'repairType');
                helper.hideEle(component, 'recallType');
                helper.hideEle(component, 'replacementType');
                helper.hideEle(component, 'addressInfo');
            } else if (serviceOption == 'Recall') {
                component.set('v.isAddPartsDisabled', true);
                component.set('v.partsItemList', []);
                component.set('v.labourHours', 0);
                helper.calculateTotalHourAndPrice(component);
                helper.hideEle(component, 'repairType');
                helper.showEle(component, 'recallType');
                helper.hideEle(component, 'replacementType');
                helper.hideEle(component, 'addressInfo');
                helper.getProject(component);
                var warrantyStatus = component.get('v.realWarrantyStatus');
                if (warrantyStatus != 'Vailid Warranty' && component.get('v.isValidRecall') && component.get('v.isRecall')) {
                    component.set('v.warrantyStatus', 'Vailid Warranty');
                    component.set('v.isDisabled', false);
                }
            } else {
                component.set('v.isAddPartsDisabled', false);
                component.set('v.partsItemList', []);
                component.set('v.labourHours', 0);
                helper.calculateTotalHourAndPrice(component);
                helper.hideEle(component, 'repairType');
                helper.hideEle(component, 'recallType');
                helper.hideEle(component, 'replacementType');
                helper.hideEle(component, 'addressInfo');
            }
        }
    },
    selectRepairType: function (component, event, helper) {
        if (component.get('v.isView')) {
            return;
        }
        var repairType = event.getSource().get('v.value');
        component.set('v.repairType', repairType);
        if (repairType == 'Parts') {
            helper.hideEle(component, 'laborHourInput');
            helper.showEle(component, 'laborHourPopUp');
            component.set('v.isAddPartsDisabled', false);
        } else {
            helper.hideEle(component, 'laborHourPopUp');
            helper.showEle(component, 'laborHourInput');
            component.set('v.isAddPartsDisabled', true);
            component.set('v.partsItemList', []);
            component.set('v.labourHours', 0);
            //remove the validate style
            $A.util.removeClass(component.find('majorIssue'), 'field-error');
            $A.util.addClass(component.find('majorIssue-error-required'), 'slds-hide');
            helper.calculateTotalHourAndPrice(component);
        }
    },
    selectProject: function (component, event, helper) {
        var projectId = event.getSource().get('v.value');
        var solution;
        component.get('v.projectList').forEach(function (item) {
            if (item.Id == projectId) {
                solution = item.Solution;
            }
        })
        if (solution == 'Replacement') {
            component.set('v.recallOptionList', ['Replacement']);
            helper.hideEle(component, 'contactCustomerService');
        } else if (solution == 'Repair') {
            if (component.get('v.isProduct')) {
                component.set('v.recallOptionList', ['Replacement', 'Repair']);
            } else {
                component.set('v.recallOptionList', ['Replacement']);
            }
            helper.hideEle(component, 'contactCustomerService');
        } else if (solution == 'Refund') {
            component.set('v.recallOptionList', ['Refund']);
            helper.showEle(component, 'contactCustomerService');
        } else if (solution == 'Recall') {
            component.set('v.recallOptionList', ['Repair', 'Replacement']);
            helper.showEle(component, 'contactCustomerService');
        }
    },
    selectRecallOption: function (component, event, helper) {
        if (component.get('v.isView')) {
            return;
        }
        //reset the data
        component.set('v.repairType', '');
        component.set('v.inventory', '');
        component.set('v.repairType', '');
        component.set('v.failureCode', '');
        component.set('v.majorIssueObj', {});
        component.set('v.labourHours', 0);
        component.set('v.partsCost', 0);
        component.set('v.totalPrice', 0);
        component.set('v.replacementBaseFee', 0);
        component.set('v.isDisabled', false);
        component.set('v.isAddPartsDisabled', false);
        helper.hideEle(component, 'repairType');
        helper.hideEle(component, 'replacementType');
        helper.hideEle(component, 'addressInfo');
        helper.hideEle(component, 'contactCustomerService');
        helper.hideEle(component,'Level1ExplanationOptions');
        var recallOption = event.getSource().get('v.value');
        if (recallOption.toUpperCase() == 'REPLACEMENT') {
            helper.showEle(component, 'replacementType');
        } else if (recallOption.toUpperCase() == 'REPAIR') {
            helper.showEle(component, 'repairType');
            helper.hideEle(component, 'repairTypeSelect');
            helper.hideEle(component, 'failureCode');
            helper.getFailureCode(component);
        } else {
            component.set('v.isDisabled', true);
        }
    },
    selectInventory: function (component, event, helper) {
        if (component.get("v.isView")) {
            return;
        }
        let appEvent = $A.get("e.c:CCM_ProductLookUpChange"),
            partsLookup = component.find("majorIssue");
        appEvent && appEvent.fire();
        partsLookup && partsLookup.clearDropdownOptions();
        component.set("v.labourHours", 0);
        component.set("v.markup", 0);
        component.set("v.replacementBaseFee", 0);
        component.set("v.partsItemList", []);
        component.set("v.isAddPartsDisabled", false);
        component.set("v.isDiagramDisabled", false);
        switch (component.get("v.inventory")) {
            case "Dealer inventory":
                helper.hideEle(component, "addressInfo");
                helper.getInventoryInfo(component);
                break;
            case "Chervon inventory":
                helper.showEle(component, "addressInfo");
                helper.getInventoryInfo(component);
                break;
            case "":
                helper.hideEle(component, "addressInfo");
                helper.clearAddress(component);
                helper.updatePartsCost(component, 0);
                break;
        }
    },
    AdditionalParts: function (component, event, helper) {
        var serviceOption = component.get('v.serviceOption');
        if (serviceOption == 'Replacement' || (serviceOption == 'Recall' && component.get('v.recallOption') == 'Replacement')) {
            if (component.get('v.partsItemList').length == 0) {
                $A.util.removeClass(
                    component.find('replacementModal'),
                    "slds-hide"
                );
            } else {
                helper.AdditionalParts(component);
            }
        } else {
            helper.AdditionalParts(component);
        }
    },
    getMasterProductId: function (component, event, helper) {
        var parts = event.getParam('masterProductId');
        if (parts == '' && component.get('v.isDraft')) {
            component.set('v.majorIssueObj', {});
            component.set('v.labourHours', '0');
            component.set('v.partsItemList', []);
            helper.calculateTotalHourAndPrice(component);
            return;
        }
        if (parts.ProductCode) {
            component.set('v.draftPartsChanged', true);
        }
        var index = event.getParam('communityPartsIndex');
        console.log('index:', index);
        var partsItemList = component.get('v.partsItemList');
        var rowIndex = event.getParam('rowIndex');
        var partsCost = 0;
        var labourHours = 0;
        if (index == '') {
            labourHours = component.get('v.labourHours');
            var arr = [];
            partsItemList.forEach((item, idx) => {
                let partsAvailable = true;
                if (idx == rowIndex) {
                    item.itemNumber = parts.ProductCode;
                    item.quantity = 1;
                    item.Name = parts.Name;
                    item.price = parts.price;
                    item.proPrice = item.price;
                    item.total = parts.price;
                    item.editable = true;
                    item.fakePrice = parts.fakePrice;
                    item.LaborTime = parts.LaborTime;
                    item.partsId = parts.partsId;
                    item.isWearable = parts.isWearable;
                    item.partsWearable = parts.partsWearable;
                    item.wearablePeriodCommercial = parts.wearablePeriodCommercial;
                    item.wearablePeriodResidential = parts.wearablePeriodResidential;
                    partsAvailable = helper.checkWearablePartsAvaibility(component, parts, component.get('v.purchaseDate'), component.get('v.purchaseUseType'), partsItemList, rowIndex);
                    if (!partsAvailable) {
                        return;
                    }
                    if (parts.isWearable) {
                        item.warrantyDate = parts.warrantyDate;
                        var dropOfDate = component.get('v.dropOfDate');
                        if (dropOfDate) {

                            var warrantyDate = new Date(new Date().setDate(new Date(component.get('v.purchaseDate')).getDate() + parts.warrantyDate));
                            warrantyDate = warrantyDate.getFullYear() + '-' + Number(warrantyDate.getMonth() + 1) + '-' + warrantyDate.getDate();
                            if (new Date(warrantyDate) < new Date(dropOfDate)) {
                                item.showWearable = true;
                                item.price = item.fakePrice;
                                item.total = item.fakePrice;
                            } else {
                                item.showWearable = false;
                            }
                        } else {
                            //alert to show required fields
                            $A.get("e.force:showToast").setParams({
                                "title": $A.get("$Label.c.CCM_Portal_Error"),
                                "message": $A.get("$Label.c.CCM_Portal_Pleaseselectthedropofdate"),
                                "type": "error"
                            }).fire();
                            return;
                        }
                    }
                    if (labourHours) {
                        labourHours = Number(labourHours) + Number(item.LaborTime);
                    }
                    else {
                        labourHours = Number(item.LaborTime);
                    }
                }
                partsCost += Number(item.total) ? Number(item.total) : 0;
                arr.push(item);
            });

            let laborTimeCombine = helper.calculateLaborTimeForCombineParts(component);
            if (laborTimeCombine) {
                labourHours = laborTimeCombine;
            }

            let finalHours = 0;
            if(component.get('v.overTimeHour') !== '' && component.get('v.overTimeHour') != '0'){
                labourHours = component.get('v.overTimeHour');
                finalHours = labourHours;
            }
            else {
                let additionalHour = component.get('v.additionalTimeHour');
                if (additionalHour) {
                    finalHours = Number(labourHours) + Number(additionalHour);
                }
                else {
                    finalHours = Number(labourHours);
                }
            }
            component.set('v.labourHours', Number(labourHours));
            component.set('v.finialLaborHour', Number(finalHours));
            component.set('v.partsItemList',[]);
            component.set('v.partsItemList', arr);
        } else {
            var partsItem = {};
            partsItem.kitItemId = parts.kitItemId;
            partsItem.itemNumber = parts.ProductCode;
            component.set('v.repairPartsName', parts.Name);
            partsItem.Name = parts.Name;
            partsItem.quantity = 1;
            partsItem.editable = true;
            partsItem.proPrice = parts.price;
            partsItem.price = parts.price;
            partsItem.total = parts.price;
            partsItem.fakePrice = parts.fakePrice;
            partsItem.LaborTime = parts.LaborTime;
            partsItem.partsId = parts.partsId;
            partsItem.isWearable = parts.isWearable;
            partsItem.partsWearable = parts.partsWearable;
            partsItem.wearablePeriodCommercial = parts.wearablePeriodCommercial;
            partsItem.wearablePeriodResidential = parts.wearablePeriodResidential;
            let partsAvailable = helper.checkWearablePartsAvaibility(component, parts, component.get('v.purchaseDate'), component.get('v.purchaseUseType'), partsItemList, undefined);
            if (parts.isWearable) {
                partsItem.warrantyDate = parts.warrantyDate;
                var dropOfDate = component.get('v.dropOfDate');
                if (dropOfDate) {
                    var warrantyDate = new Date(new Date().setDate(new Date(component.get('v.purchaseDate')).getDate() + parts.warrantyDate));
                    warrantyDate = warrantyDate.getFullYear() + '-' + Number(warrantyDate.getMonth() + 1) + '-' + warrantyDate.getDate();
                    if (new Date(warrantyDate) < new Date(dropOfDate)) {
                        partsItem.showWearable = true;
                        partsItem.price = parts.fakePrice;
                        partsItem.total = parts.fakePrice;
                    } else {
                        partsItem.showWearable = false;
                    }
                } else {

                    //alert to show required fields
                    $A.get("e.force:showToast").setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Error"),
                        "message": $A.get("$Label.c.CCM_Portal_Pleaseselectthedropofdate"),
                        "type": "error"
                    }).fire();
                    return;
                }
            }
            if (partsItemList.length >= 1) {
                partsItemList = [];
            }
            if (partsAvailable) {
                labourHours = Number(partsItem.LaborTime);

                let laborTimeCombine = helper.calculateLaborTimeForCombineParts(component);
                if (laborTimeCombine) {
                    labourHours = laborTimeCombine;
                }
                //calculate the labor hour
                let finalHours = 0;
                if(component.get('v.overTimeHour') !== '' && component.get('v.overTimeHour') != '0'){
                    labourHours = component.get('v.overTimeHour');
                    finalHours = labourHours;
                }
                else {
                    let additionalHour = component.get('v.additionalTimeHour');
                    if (additionalHour) {
                        finalHours = Number(labourHours) + Number(additionalHour);
                    }
                    else {
                        finalHours = Number(labourHours);
                    }
                }
                component.set('v.labourHours', Number(labourHours));
                component.set('v.finialLaborHour', Number(finalHours));
                partsItemList.push(partsItem);
                partsCost += Number(partsItem.total) ? Number(partsItem.total) : 0;
            }
            component.set('v.partsItemList', partsItemList);
        }
        helper.calculateTotalHourAndPrice(component);
    },
    getFinialLaborHour: function(component, event, helper){
        var actualHour = component.get("v.boolIsAltaQuip") === true ? 60 : component.get('v.overTimeHour');
        let additionalHour = component.get("v.additionalTimeHour");
        let finalHours = 0;
        if(actualHour == ''){
            actualHour = component.get('v.labourHours');
            if (additionalHour !== '') {
                finalHours = Number(actualHour) + Number(additionalHour);
            }
        }
        else {
            finalHours = actualHour;
        }
        component.set('v.finialLaborHour', (Number(finalHours)/60).toFixed(2));
        var laborHourSubtotal = (Number(finalHours)/60) * Number(component.get('v.LaborRate'));
        component.set('v.laborCostSubtotal', laborHourSubtotal.toFixed(2));
        helper.calculateTotalHourAndPrice(component);
    },
    calculatePartsTotalPrice: function (component, event, helper) {
        let self = this;
        let quantity = Number(event.getSource().get('v.value'));
        let index = Number(event.target.getAttribute('data-index'));
        let partsItemList = component.get('v.partsItemList');
        let partsItemListNew = JSON.parse(JSON.stringify(component.get('v.partsItemList')));
        // component.set('v.partsItemList',[]);
        partsItemList[index].quantity = quantity;
        partsItemListNew[index].quantity = quantity;

        if (!partsItemList[index].price) {
            return;
        }
        let total = (Number(partsItemList[index].price) * quantity).toFixed(2);
        let totalNew = (Number(partsItemListNew[index].price) * quantity).toFixed(2);
        partsItemList[index].total = total;
        partsItemListNew[index].total = totalNew;
        if (partsItemList[index].total === partsItemListNew[index].total) {
            component.set('v.partsItemList', partsItemList);
            console.log('equal');
        } else {
            component.set('v.partsItemList', partsItemListNew);
            console.log('not equal');
        }
        // component.set('v.partsItemList', partsItemList);
        helper.calculateTotalHourAndPrice(component);
    },
    onClickModalCancel: function (component) {
        $A.util.addClass(
            component.find('boomModal'),
            "slds-hide"
        );
    },
    onClickReplacementModalCancel: function (component) {
        $A.util.addClass(
            component.find('replacementModal'),
            "slds-hide"
        );
    },
    addAdditionalPartsViaReplacement: function (component, event, helper) {
        $A.util.addClass(
            component.find('replacementModal'),
            "slds-hide"
        );
        helper.AdditionalParts(component);
    },
    openModal: function (component, event, helper) {
        $A.util.removeClass(
            component.find('boomModal'),
            "slds-hide"
        );
        var mySwiper = new Swiper('.swiper-container', {
            // Optional parameters
            direction: 'horizontal',
            loop: false,
            observer: true,
            observeParents: true,
            queueEndCallbacks: true,
            // Navigation arrows
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            }
        });
        helper.GenerateVersionList(component, component.get('v.modelNum'));
    },
    //explosive search
    handleSearch: function (component, event, helper) {
        helper.searchParts(component);
    },
    highLightSequenceNo: function (component) {
        var sequenceNo = component.find('sequenceNo').get('v.value');
        var explosiveDataList = component.get('v.explosiveDataList');
        var partsIndex, selectedSequenceNo;
        for (var i = 0; i < explosiveDataList.length; i++) {
            explosiveDataList[i].highLight = false;
            if (explosiveDataList[i].ExplosionID__c == sequenceNo) {
                explosiveDataList[i].highLight = true;
                partsIndex = i;
                if (i > 5) {
                    selectedSequenceNo = explosiveDataList[i - 5].ExplosionID__c;
                }
            }
        }
        if (selectedSequenceNo) {
            document.getElementById(selectedSequenceNo).scrollIntoView();
        }
        component.set('v.explosiveDataList', explosiveDataList)
    },
    backToTop: function (component) {
        document.getElementById('top').scrollIntoView();
    },
    handleCancel: function (component, event, helper) {
        window.location.href = '/s/servicehome';
    },
    handleDelete: function (component, event, helper) {
        var index = Number(event.target.getAttribute('data-index'));
        var partsItemData = component.get('v.partsItemList');
        if (partsItemData[index].editable) {
            var laborHour = Number(partsItemData[index].LaborTime) * Number(partsItemData[index].quantity);
            var totalHour = component.get('v.labourHours') ? component.get('v.labourHours') : 0;
            partsItemData.splice(index, 1);
            component.set('v.partsItemList', JSON.parse(JSON.stringify(partsItemData)));
            component.set('v.labourHours', (Number(totalHour) - Number(laborHour)));
            component.set('v.repairPartsName', '');
        }
        helper.calculateTotalHourAndPrice(component);
    },
    handleSaveAsDraft: function (component, event, helper) {
        var tempName = component.get('v.AltaquipClaimRestrictionName');
        var tempModel = component.get('v.AltaquipClaimRestrictionModel');
        if(tempName == true && tempModel == false){
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Portal_Error"),
                "message": $A.get("$Label.c.CCM_Portal_OnlyClaimsForTheFollowingModelNumbersArePermitted"),
                "type": "error",
                "mode": "sticky"
            }).fire();
            return;
        }
        helper.showEle(component, 'spinner');
        helper.saveClaimAsDraft(component);
    },
    handleSubmit: function (component, event, helper) {
        var comps = component.find("dropOfDate");
        if (!Validator.pass(comps)) {
            return;
        }
        var tempName = component.get('v.AltaquipClaimRestrictionName');
        var tempModel = component.get('v.AltaquipClaimRestrictionModel');
        if(tempName == true && tempModel == false){
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Portal_Error"),
                "message": $A.get("$Label.c.CCM_Portal_OnlyClaimsForTheFollowingModelNumbersArePermitted"),
                "type": "error",
                "mode": "sticky"
            }).fire();
            return;
        }
        //test
        helper.showEle(component, 'spinner');
        var serviceOption = component.get('v.serviceOption');
        if (component.get('v.warrantyStatus') == 'Vailid Warranty' || serviceOption == 'Recall') {
            if (component.get('v.isEdit') && !component.get('v.isDraft')) {
                component.set('v.isDisabled', true);
                helper.saveClaim(component);
            } else {
                var isValid = helper.getValidation(component);

                if (isValid || serviceOption == 'Recall') {
                    helper.saveClaim(component);
                } else {
                    helper.hideEle(component, 'spinner');
                    //alert to show required fields
                    $A.get("e.force:showToast").setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Error"),
                        "message": $A.get("$Label.c.CCM_Portal_Cannotsavethedatapleasereviewerrorfields"),
                        "type": "error"
                    }).fire();
                }
            }
        } else {
            helper.hideEle(component, 'spinner');
            //alert to show required fields
            $A.get("e.force:showToast").setParams({
                "title": $A.get("$Label.c.CCM_Portal_Error"),
                "message": $A.get("$Label.c.CCM_Portal_Cannotsavethedatapleasereviewwarrantystatus"),
                "type": "error"
            }).fire();
        }

    },
    trimSerialNumber: function (component) {
        let strSerialNo = component.get("v.SerialNum"),
            strTrimmedSerialNo = (strSerialNo || "").trim(),
            boolNeedToUpdate = strSerialNo !== strTrimmedSerialNo;
        if (boolNeedToUpdate === true) {
            component.set("v.SerialNum", strTrimmedSerialNo);
        }
    },
    selectTier: function (component, event, helper) {
        helper.selectTier(component, event, helper);
    },
    handleZipCodeChange: function (component, event, helper) {
        helper.getBusinessName(component);
    },
    handleAltaquipClaimRestriction: function(component, event, helper){
        if (component.get('v.isView')) {
            return;
        }
        var firstName = component.get('v.firstName');
        var lastName = component.get('v.lastName');
        var modelNum = component.get('v.modelNum');
        var productCode;
        let matchModel = component.get('v.modelNumList').filter(item => item.Product__r.Id == modelNum);
        if(matchModel && matchModel[0]){
            productCode = matchModel[0].Product_Code__c;
        }
        if(firstName && lastName && productCode){
            helper.getAltaquipClaimRestriction(component, firstName, lastName, productCode);
        }
    },
    handleTaxChange: function (component, event, helper) {
        let totalTax = 0;
        if (component.get('v.qst')) {
            totalTax = totalTax + Number(component.get('v.qst'));
        }
        if (component.get('v.pst')) {
            totalTax = totalTax + Number(component.get('v.pst'));
        }
        if (component.get('v.gst')) {
            totalTax = totalTax + Number(component.get('v.gst'));
        }
        if (component.get('v.hst')) {
            totalTax = totalTax + Number(component.get('v.hst'));
        }
        component.set('v.totalTax', totalTax);
        helper.calculateTotalHourAndPrice(component);
    },
    handleExplanationOptionsSelected: function (component, event, helper) {
        let options = component.find("explanationOptions");
        let result = [];
        for (let i = 0; i < options.length; i++) {
            if (options[i].get("v.checked")) {
                result.push(options[i].get('v.name'));
            }
        }
        component.set('v.explanationOptionSelected', result.join(';'));
    },
    handlePurchaseDateChange: function(component, event, helper) {
        let purchaseDate = component.get("v.purchaseDate");
        if (purchaseDate) {
            let dateParts = purchaseDate.split('-');
            let formattedDate = `${dateParts[1]}-${dateParts[2]}-${dateParts[0]}`; // MM-dd-yyyy
            component.set("v.formattedPurchaseDate", formattedDate);
        } else {
            component.set("v.formattedPurchaseDate", "");
        }
    },
    dateToText: function (component, event, helper){
        comsole.log(component.get('v.Level2ExplanationOption'));
    },
    Level1ExplanationOptionchange: function (component, event, helper){
        if (component.get('v.isView')) {
            return;
        }
        component.set('v.Level2ExplanationOption', '');
        component.set('v.partsOptionValues', '');
        if(component.get('v.Level1ExplanationOption') == ''){
            helper.hideEle(component, 'Level2ExplanationDetails');
        }else{
            helper.showEle(component, 'Level2ExplanationDetails');
        }
    },
    partsValuesToString: function (component, event, helper){
        let partsOptionValues = component.get('v.partsOptionValues');
        let result = '';
        partsOptionValues.forEach((temp, index) => {
            result += temp;
            result += ';';
        });
        component.set('v.Level2ExplanationOption', result);
    }
})