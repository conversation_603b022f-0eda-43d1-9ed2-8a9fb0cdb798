<?xml version="1.0" encoding="UTF-8"?>
<Workflow xmlns="http://soap.sforce.com/2006/04/metadata">
    <alerts>
        <fullName>Sell_Through_Claim_Sync_Failed</fullName>
        <description>Sell Through Claim Sync Failed</description>
        <protected>false</protected>
        <recipients>
            <recipient><EMAIL></recipient>
            <type>user</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>ALL1/Sell_Through_Claim_Sync_Failed</template>
    </alerts>
    <alerts>
        <fullName>Send_Email_To_Owner</fullName>
        <ccEmails><EMAIL></ccEmails>
        <description>Send Email To Owner</description>
        <protected>false</protected>
        <recipients>
            <type>creator</type>
        </recipients>
        <recipients>
            <type>owner</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>Sales_Cloud/Claim_Request_Email</template>
    </alerts>
    <fieldUpdates>
        <fullName>CR_Status_to_Pending</fullName>
        <field>Claim_Status__c</field>
        <literalValue>Pending Review</literalValue>
        <name>CR Status to Pending</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>CR_Submit_Date_Update</fullName>
        <field>Submit_Date__c</field>
        <formula>TODAY()</formula>
        <name>CR Submit Date Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>CurrentApproverUpdate</fullName>
        <field>Current_Approver__c</field>
        <literalValue>BEAM</literalValue>
        <name>CurrentApproverUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>UpdateLastApprovalDate</fullName>
        <field>Last_Approval_Date__c</field>
        <formula>TODAY()</formula>
        <name>UpdateLastApprovalDate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>UpdateStatusToApproved</fullName>
        <field>Claim_Status__c</field>
        <literalValue>Approved</literalValue>
        <name>UpdateStatusToApproved</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>UpdateStatusToReject</fullName>
        <field>Claim_Status__c</field>
        <literalValue>Rejected</literalValue>
        <name>UpdateStatusToReject</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>updateStatusToDraft</fullName>
        <field>Claim_Status__c</field>
        <literalValue>Draft</literalValue>
        <name>updateStatusToDraft</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <rules>
        <fullName>Send Email to Owner When Closed</fullName>
        <actions>
            <name>Send_Email_To_Owner</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( ISCHANGED( Claim_Status__c ) ,TEXT(Claim_Status__c) =&apos;Issued&apos;)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Send Notification When Sync Failed</fullName>
        <actions>
            <name>Sell_Through_Claim_Sync_Failed</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Claim_Request__c.Sync_Status__c</field>
            <operation>equals</operation>
            <value>Failed</value>
        </criteriaItems>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
</Workflow>
