/**
 * @description       : test class for CCM_LeadAutoAssignmentHandler
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 02-29-2024
 * @last modified by  : <EMAIL>
**/
@IsTest
private class CCM_LeadAutoAssignmentHandlerTest {
    @IsTest
    static void testMethod1() {
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'BEAM', 'BEAM');

        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'District_Sales_Manager_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');
        Test.startTest();
        System.runAs(beamUser) {
            CCM_DealerLocationHandler2.isRun = false;
            CCM_SharingUtil.isSharingOnly = true;
            CCM_UpaftCtrl.inFutureContext = true;
            Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            Lead theLead = Test_SalesData.createProspectData();
            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                Brands__c = 'EGO',
                Payment_Term__c = 'NA001',
                Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.',
                Freight_Term__c = 'PPC',
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Approval_Status__c = 'Draft',
                Sales_Group__c = 'SG01',
                Authorized_Brand_Name_To_Oracle__c = 'test1',
                ORG_Code__c = 'CNA',
                RecordTypeId = CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_ID
            );
            insert sp;
            Attachment_Management__c objAM = new Attachment_Management__c(Prospect__c = theLead.Id, Attachment_Type__c = 'Brand Program');
            insert objAM;
            ContentVersion objCV = new ContentVersion(VersionData = Blob.valueOf('test'), Title = 'test', PathOnClient = 'test.txt');
            insert objCV;
            insert new ContentDocumentLink(LinkedEntityId = objAM.Id, ContentDocumentId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :objCV.Id][0].ContentDocumentId);
            List<Account_Address__c> lstAccountAddresses = new List<Account_Address__c>{
                new Account_Address__c(Prospect__c = theLead.Id, RecordTypeId = CCM_Contants.SHIPPING_ADDRESS_RECORDTYPEID),
                new Account_Address__c(Prospect__c = theLead.Id, RecordTypeId = CCM_Contants.BILLING_ADDRESS_RECORDTYPEID)
            };
            insert lstAccountAddresses;
            insert new Address_With_Program__c(Program__c = sp.Id, Account_Address__c = lstAccountAddresses[1].Id);
            theLead.Street = 'test street';
            theLead.City = 'New York';
            theLead.State = 'NY';
            theLead.Country = 'US';
            theLead.PostalCode = '11111';
            theLead.Intended_Brand__c = 'EGO';
            theLead.Distributor_or_Dealer__c = 'Direct Dealer';
            theLead.OwnerId = salesManager1.Id;
            theLead.Is_Flex_Auto_Only__c = false;
            theLead.Status = 'Pending Review';
            theLead.Customer_Cluster__c = 'CNA-CG01';
            theLead.Director_Approver__c = UserInfo.getUserId();
            theLead.Sales_Channel__c = 'SC01';
            theLead.Sales_Group__c = 'SG01';
            theLead.Status = 'Assigned';
            theLead.ORG_Code__c = 'CNA';
            update theLead;
        }
        try {
            CCM_V_Lead_Validations.requirePhoneOrMobileOrEmail(new Lead());
        } catch (Exception objE) {
            System.assert(objE != null);
        }
        Test.stopTest();
    }
    @IsTest
    static void testMethod2() {
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'BEAM', 'BEAM');
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');
        Test.startTest();
        System.runAs(beamUser) {
            CCM_DealerLocationHandler2.isRun = false;
            Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            Lead theLead = new Lead(
                LastName = 'TestLead001',
                Company = 'TestCompanyName',
                User_Type__c = 'Enterprise',
                Invoicing_Method__c = 'MAIL',
                Email = '<EMAIL>',
                Credit_Limit__c = '1',
                Risk_Code__c = 'L',
                Sales_Group__c = 'SG01',
                State = 'NY',
                Country = 'US',
                PostalCode = '11111',
                Street = 'test street',
                City = 'New York',
                Intended_Brand__c = 'EGO',
                Distributor_or_Dealer__c = 'Direct Dealer',
                Is_Flex_Auto_Only__c = false,
                OwnerId = salesManager1.Id
            );
            insert theLead;
            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                Brands__c = 'EGO',
                Payment_Term__c = 'NA001',
                Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.',
                Freight_Term__c = 'PPC',
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Approval_Status__c = 'Draft',
                Sales_Group__c = 'SG01',
                Authorized_Brand_Name_To_Oracle__c = 'test2',
                ORG_Code__c = 'CNA',
                RecordTypeId = CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_ID
            );
            insert sp;
        }
        Test.stopTest();
    }
    @IsTest
    static void testMethod3() {
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'BEAM', 'BEAM');
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');
        Test.startTest();
        System.runAs(beamUser) {
            CCM_DealerLocationHandler2.isRun = false;
            Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            Lead theLead = new Lead(
                LastName = 'TestLead001',
                Company = 'TestCompanyName',
                User_Type__c = 'Enterprise',
                Invoicing_Method__c = 'MAIL',
                Email = '<EMAIL>',
                Credit_Limit__c = '1',
                Risk_Code__c = 'L',
                Sales_Group__c = 'SG01',
                State = 'NY',
                Country = 'US',
                PostalCode = '11111',
                Street = 'test street',
                City = 'New York',
                Intended_Brand__c = 'EGO',
                Distributor_or_Dealer__c = 'Direct Dealer',
                Is_Flex_Auto_Only__c = true
            );
            insert theLead;
            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                Brands__c = 'EGO',
                Payment_Term__c = 'NA001',
                Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.',
                Freight_Term__c = 'PPC',
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Approval_Status__c = 'Draft',
                Sales_Group__c = 'SG01',
                Authorized_Brand_Name_To_Oracle__c = 'test3',
                ORG_Code__c = 'CNA',
                RecordTypeId = CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_ID
            );
            insert sp;
        }
        Test.stopTest();
    }
    @IsTest
    static void testMethod4() {
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'BEAM', 'BEAM');
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');
        Test.startTest();
        System.runAs(beamUser) {
            CCM_DealerLocationHandler2.isRun = false;
            Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            Lead theLead = Test_SalesData.createProspectData();
            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                Brands__c = 'EGO',
                Payment_Term__c = 'NA001',
                Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.',
                Freight_Term__c = 'PPC',
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Approval_Status__c = 'Draft',
                Sales_Group__c = 'SG01',
                Authorized_Brand_Name_To_Oracle__c = 'test4'
            );
            insert sp;
            theLead.Street = 'test street';
            theLead.City = 'New York';
            theLead.State = 'NY';
            theLead.Country = 'US';
            theLead.PostalCode = '11111';
            theLead.Intended_Brand__c = 'EGO';
            theLead.Distributor_or_Dealer__c = 'Direct Dealer';
            theLead.OwnerId = salesManager1.Id;
            theLead.Is_Flex_Auto_Only__c = false;
            update theLead;
        }
        Test.stopTest();
    }
    @IsTest
    static void testMethod5() {
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'BEAM', 'BEAM');
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'CA_TT_Sales_Manager', 'CCA Sales Agency');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'CA_TT_Sales_Manager', 'CCA Sales Agency');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'CA_TT_Sales_Manager', 'CCA Sales Agency');
        Test.startTest();
        System.runAs(beamUser) {
            // Test_SalesData.createProspectAssignmentRules_CA(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            CCM_DealerLocationHandler2.isRun = false;
            List<Prospect_Assignment_Rule__c> rules = new List<Prospect_Assignment_Rule__c>();
            Prospect_Assignment_Rule__c rule1 = new Prospect_Assignment_Rule__c(
                ORG_Code__C = CCM_Constants.ORG_CODE_CCA,
                Brand__c = 'EGO',
                Nation__c = 'CA',
                // State__c = 'OK',
                Distributor_or_Dealer__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_CA_DISTRIBUTOR,
                // Cluster__c = 'CA-CG09',
                // Sub_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_CA,
                Sales_Channel__c = 'SC01',
                Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
                User__c = salesManager1.Id,
                Director__c = salesDirector.Id,
                Is_Flex_Auto_Only__c = false
            );
            rules.add(rule1);
            Prospect_Assignment_Rule__c rule2 = new Prospect_Assignment_Rule__c(
                ORG_Code__c = CCM_Constants.ORG_CODE_CCA,
                Brand__c = 'SKIL/SKILSAW/FLEX',
                Nation__c = 'CA',
                // State__c = 'BC',
                Distributor_or_Dealer__c = 'Canada Distributor;Canada Home Center',
                // Cluster__c = 'CA-CG09',
                // Sub_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_CA,
                Sales_Channel__c = 'SC01',
                Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
                User__c = salesManager2.Id,
                Director__c = salesDirector.Id,
                Is_Flex_Auto_Only__c = false
            );
            rules.add(rule2);
            insert rules;

            Lead theLead = Test_SalesData.createProspectData_CA();
            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                // ORG_Code__c = 'CCA',
                Brands__c = 'EGO',
                Payment_Term__c = CCM_Constants.SALES_PROGRAM_PAYMENT_TERM[57],
                Payment_Term_Description__c ='2% discount is given if the invoice is paid within 90 days, 91th will be overdue day.',
                Freight_Term__c = CCM_Constants.SALES_PROGRAM_FREIGHT_TERM[4],
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Approval_Status__c = 'Draft',
                Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
                // Order_Type__c = CCM_Constants.SALES_PROGRAM_ORDER_TYPE_CA,
                Authorized_Brand_Name_To_Oracle__c = 'test5'
            );
            insert sp;
            theLead.Street = 'test street';
            theLead.City = 'BC';
            theLead.State = 'AB';
            theLead.Country = 'CA';
            theLead.PostalCode = '11111';
            theLead.Intended_Brand__c = 'EGO';
            theLead.Distributor_or_Dealer__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_CA_DISTRIBUTOR;
            theLead.OwnerId = salesManager1.Id;
            theLead.Status = 'Open';
            theLead.Is_Flex_Auto_Only__c = false;
            theLead.Upload_Flag__c = false;
            try {
                update theLead;
            } catch (Exception e) {
                Boolean expectedExceptionThrown = e.getMessage().contains('Cannot find assignment rule according to information your input, Please contact the system adminstrator.')? True:False;
                System.AssertEquals(expectedExceptionThrown, true);
            }

        }
        Test.stopTest();
    }

    @IsTest
    static void testMethod6() {
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'CA_TT_Sales_Manager', 'System Administrator');
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', CCM_Constants.TERRITORY_USER_RELATIONSHIP_ROLE_CA_TT_SALES_MANAGER_Developer_Name, 'CCA Sales Agency');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', CCM_Constants.TERRITORY_USER_RELATIONSHIP_ROLE_CA_TT_SALES_MANAGER_Developer_Name, 'CCA Sales Agency');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', CCM_Constants.TERRITORY_USER_RELATIONSHIP_ROLE_CA_TT_SALES_MANAGER_Developer_Name, 'CCA Sales Agency');
        List<Prospect_Assignment_Rule__c> rules = new List<Prospect_Assignment_Rule__c>();
        Prospect_Assignment_Rule__c rule1 = new Prospect_Assignment_Rule__c(
            ORG_Code__C = CCM_Constants.ORG_CODE_CCA,
            Brand__c = 'EGO',
            Nation__c = 'CA',
            // State__c = 'OK',
            Distributor_or_Dealer__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_CA_DISTRIBUTOR,
            // Cluster__c = 'CA-CG09',
            // Sub_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_CA,
            Sales_Channel__c = 'SC01',
            Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
            User__c = salesManager1.Id,
            Director__c = beamUser.Id,
            Is_Flex_Auto_Only__c = false
        );
        rules.add(rule1);
        Prospect_Assignment_Rule__c rule2 = new Prospect_Assignment_Rule__c(
            ORG_Code__c = CCM_Constants.ORG_CODE_CCA,
            Brand__c = 'SKIL/SKILSAW/FLEX',
            Nation__c = 'CA',
            // State__c = 'BC',
            Distributor_or_Dealer__c = 'Canada Distributor;Canada Home Center',
            // Cluster__c = 'CA-CG09',
            // Sub_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_CA,
            Sales_Channel__c = 'SC01',
            Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
            User__c = salesManager2.Id,
            Director__c = beamUser.Id,
            Is_Flex_Auto_Only__c = false
        );
        rules.add(rule2);
        System.runAs(beamUser) {
            insert rules;
        }
        System.runAs(salesManager1) {
            CCM_DealerLocationHandler2.isRun = false;
            CCM_Lead_Validation_Rules_Handler.isRun = false;
            Lead theLead = new Lead(
                ORG_Code__c = CCM_Constants.ORG_CODE_CCA,
                LastName = 'TestLead001',
                Company = 'TestCompanyName',
                User_Type__c = 'Enterprise',
                Invoicing_Method__c = 'MAIL',
                Email = '<EMAIL>',
                Credit_Limit__c = '1',
                Risk_Code__c = 'L',
                Sales_Group__c =  CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
                State = 'AB',
                Country = 'CA',
                PostalCode = '11111',
                Street = 'test street',
                City = 'AB',
                Intended_Brand__c = 'EGO',
                Distributor_or_Dealer__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_CA_DISTRIBUTOR,
                Is_Flex_Auto_Only__c = true,
                Status = 'Open'
            );
            insert theLead;
        }
    }

    @IsTest
    static void testMethod7() {
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'BEAM', 'BEAM');
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');
        Test.startTest();
        System.runAs(beamUser) {
            // Test_SalesData.createProspectAssignmentRules_CA(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            CCM_DealerLocationHandler2.isRun = false;
            List<Prospect_Assignment_Rule__c> rules = new List<Prospect_Assignment_Rule__c>();
            Prospect_Assignment_Rule__c rule1 = new Prospect_Assignment_Rule__c(
                ORG_Code__C = CCM_Constants.ORG_CODE_CCA,
                Brand__c = 'EGO',
                Nation__c = 'CA',
                // State__c = 'OK',
                Distributor_or_Dealer__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_CA_DISTRIBUTOR,
                // Cluster__c = 'CA-CG09',
                // Sub_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_CA,
                Sales_Channel__c = 'SC01',
                Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
                User__c = salesManager1.Id,
                Director__c = salesDirector.Id,
                Is_Flex_Auto_Only__c = false
            );
            rules.add(rule1);
            Prospect_Assignment_Rule__c rule2 = new Prospect_Assignment_Rule__c(
                ORG_Code__c = CCM_Constants.ORG_CODE_CCA,
                Brand__c = 'SKIL/SKILSAW/FLEX',
                Nation__c = 'CA',
                // State__c = 'BC',
                Distributor_or_Dealer__c = 'Canada Distributor;Canada Home Center',
                // Cluster__c = 'CA-CG09',
                // Sub_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_CA,
                Sales_Channel__c = 'SC01',
                Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
                User__c = salesManager2.Id,
                Director__c = salesDirector.Id,
                Is_Flex_Auto_Only__c = false
            );
            rules.add(rule2);
            insert rules;

            Lead theLead = new Lead(
                LastName = 'TestLead001',
                Company = 'TestCompanyName',
                ORG_Code__c = CCM_Constants.ORG_CODE_CCA,
                User_Type__c = 'Enterprise',
                Invoicing_Method__c = 'MAIL',
                Email = '<EMAIL>',
                Credit_Limit__c = '1',
                Risk_Code__c = 'L',
                Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
                State = 'BC',
                Country = 'CA',
                PostalCode = '11111',
                Street = 'test street',
                City = 'AB',
                Intended_Brand__c = 'EGO',
                Distributor_or_Dealer__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_CA_DISTRIBUTOR,
                Is_Flex_Auto_Only__c = false
            );
            insert theLead;
            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                Brands__c = 'EGO',
                ORG_Code__c = CCM_Constants.ORG_CODE_CCA,
                Payment_Term__c = CCM_Constants.SALES_PROGRAM_PAYMENT_TERM[57],
                Payment_Term_Description__c ='2% discount is given if the invoice is paid within 90 days, 91th will be overdue day.',
                Freight_Term__c = CCM_Constants.SALES_PROGRAM_FREIGHT_TERM[4],
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Approval_Status__c = 'Draft',
                Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
                // Order_Type__c = CCM_Constants.SALES_PROGRAM_ORDER_TYPE_CA,
                Authorized_Brand_Name_To_Oracle__c = 'test2'
            );
            insert sp;
        }
        Test.stopTest();
    }

    @IsTest
    static void testMethod8() {
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'District_Sales_Manager_East', 'NA Sales Manager');
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');
        Test.startTest();
        System.runAs(beamUser) {
            CCM_DealerLocationHandler2.isRun = false;
            Prospect_Assignment_Rule__c rule1 = new Prospect_Assignment_Rule__c(
                ORG_Code__C = CCM_Constants.ORG_CODE_CNA,
                Brand__c = 'EGO',
                Nation__c = 'United States',
                State__c = 'NY',
                Distributor_or_Dealer__c = 'Direct Dealer',
                Cluster__c = 'CNA-CG11',
                Sub_Cluster__c = 'KA001',
                Sales_Channel__c = 'SC01',
                Sales_Group__c = 'SG22',
                User__c = beamUser.Id,
                Director__c = salesDirector.Id,
                Is_Flex_Auto_Only__c = false
		    );
            insert rule1;
            Lead theLead = new Lead(
                LastName = 'TestLead001',
                Company = 'TestCompanyName',
                User_Type__c = 'Enterprise',
                Invoicing_Method__c = 'MAIL',
                Email = '<EMAIL>',
                Credit_Limit__c = '1',
                Risk_Code__c = 'L',
                Sales_Group__c = 'SG22',
                State = 'NY',
                Country = 'US',
                PostalCode = '11111',
                Street = 'test street',
                City = 'New York',
                Intended_Brand__c = 'EGO',
                Distributor_or_Dealer__c = 'Direct Dealer',
                Is_Flex_Auto_Only__c = false
            );
            insert theLead;
            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                Brands__c = 'EGO',
                Payment_Term__c = 'NA001',
                Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.',
                Freight_Term__c = 'PPC',
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Approval_Status__c = 'Draft',
                Sales_Group__c = 'SG22',
                Authorized_Brand_Name_To_Oracle__c = 'test2'
            );
            insert sp;
        }
        Test.stopTest();
    }

    @isTest
    static void testMethod10() {
        Test.startTest();
        try {
        User beamUser = Test_SalesData.createUser('TestBeamUser', 'BEAM', 'BEAM');
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'District_Sales_Manager', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');
        System.runAs(beamUser) {
            CCM_DealerLocationHandler2.isRun = false;
            Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            Lead theLead = Test_SalesData.createProspectData();
            Sales_Program__c sp = new Sales_Program__c(
                Name = 'TestSP001',
                Brands__c = 'EGO',
                Payment_Term__c = 'NA001',
                Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.',
                Freight_Term__c = 'PPC',
                Price_List__c = 'Standard Price List',
                Starting_Date_of_Payment_Term__c = 'Invoice Date',
                Order_Lead_Time__c = 5,
                Prospect__c = theLead.Id,
                Approval_Status__c = 'Approved',
                RecordTypeId = CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_ID,
                Sales_Group__c = 'SG01',
                Authorized_Brand_Name_To_Oracle__c = 'test1'
            );
            insert sp;
            Attachment_Management__c objAM = new Attachment_Management__c(Prospect__c = theLead.Id, Attachment_Type__c = 'Brand Program');
            insert objAM;
            ContentVersion objCV = new ContentVersion(VersionData = Blob.valueOf('test'), Title = 'test', PathOnClient = 'test.txt');
            insert objCV;
            insert new ContentDocumentLink(LinkedEntityId = objAM.Id, ContentDocumentId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :objCV.Id][0].ContentDocumentId);
            /*List<Account_Address__c> lstAccountAddresses = new List<Account_Address__c>{
                new Account_Address__c(Prospect__c = theLead.Id, RecordTypeId = CCM_Contants.SHIPPING_ADDRESS_RECORDTYPEID),
                new Account_Address__c(Prospect__c = theLead.Id, RecordTypeId = CCM_Contants.BILLING_ADDRESS_RECORDTYPEID)
            };
            insert lstAccountAddresses;
            insert new Address_With_Program__c(Program__c = sp.Id, Account_Address__c = lstAccountAddresses[1].Id);*/
            theLead.Street = 'test street';
            theLead.City = 'New York';
            theLead.State = 'NY';
            theLead.Country = 'US';
            theLead.PostalCode = '11111';
            theLead.Intended_Brand__c = 'EGO';
            theLead.Distributor_or_Dealer__c = 'Direct Dealer';
            theLead.OwnerId = salesManager1.Id;
            theLead.Sales_Group__c = 'SG28';
            theLead.Status = 'Assigned';
            update theLead;
        }

            CCM_V_Lead_Validations.requirePhoneOrMobileOrEmail(new Lead());
        } catch (Exception objE) {
            System.assert(objE != null);
        }
        Test.stopTest();
    }
}