import { api, LightningElement, track, wire } from 'lwc';
import { loadStyle, loadScript } from 'lightning/platformResourceLoader';
import PromotionResource from '@salesforce/resourceUrl/PromotionResource';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getCustomerInfo from '@salesforce/apex/CCM_ClaimRequestCtl.getCustomerInfo';
import getPromotionInfoByCode from '@salesforce/apex/CCM_ClaimRequestCtl.getPromotionInfoByCode';
import getReimbursementType from '@salesforce/apex/CCM_ClaimRequestCtl.getReimbursementType';
import getClaimProducts from '@salesforce/apex/CCM_ClaimRequestCtl.getClaimProducts';
import getWholeOrderPromtionInfo from '@salesforce/apex/CCM_ClaimRequestCtl.getWholeOrderPromtionInfo';
import getPromotionRulesByPromotionId from '@salesforce/apex/CCM_ClaimRequestCtl.getPromotionRulesByPromotionId';
import saveClaimRequest from '@salesforce/apex/CCM_ClaimRequestCtl.saveClaimRequest';
import submitClaimReqeust from '@salesforce/apex/CCM_ClaimRequestCtl.submitClaimReqeust';
import getAllFiles from '@salesforce/apex/CCM_ClaimRequestCtl.getAllFiles';
import { NavigationMixin } from 'lightning/navigation';
import { CurrentPageReference } from 'lightning/navigation';
import getClaimRequestWrapper from '@salesforce/apex/CCM_ClaimRequestCtl.getClaimRequestWrapper';
import deleteContentDocument from '@salesforce/apex/CCM_ClaimRequestCtl.deleteContentDocument';
import getCustomer from '@salesforce/apex/CCM_ClaimRequestCtl.getCustomer';
import insertDocumentLink from '@salesforce/apex/CCM_ClaimRequestCtl.insertDocumentLink';
import { getRecord } from 'lightning/uiRecordApi';
import USER_ID from '@salesforce/user/Id';
import PROFILE_NAME_FIELD from '@salesforce/schema/User.Profile.Name';
import getPromotionCodeByCustomer from '@salesforce/apex/CCM_ClaimRequestCtl.getPromotionCodeByCustomer';
import sellThroughPOSExtensions from '@salesforce/label/c.sellThroughPOSExtensions';
import CCM_Portal_Loading from "@salesforce/label/c.CCM_Portal_Loading";
import CCM_Portal_BasicInformation from "@salesforce/label/c.CCM_Portal_BasicInformation";
import CCM_Portal_Customer from "@salesforce/label/c.CCM_Portal_Customer";
import CCM_Portal_PromotionCode from "@salesforce/label/c.CCM_Portal_PromotionCode";
import CCM_Portal_PromotionName from "@salesforce/label/c.CCM_Portal_PromotionName";
import CCM_Portal_PromotionWindows from "@salesforce/label/c.CCM_Portal_PromotionWindows";
import CCM_Portal_ReimbursementType from "@salesforce/label/c.CCM_Portal_ReimbursementType";
import CCM_Portal_BillTo from "@salesforce/label/c.CCM_Portal_BillTo";
import CCM_Portal_ShipTo from "@salesforce/label/c.CCM_Portal_ShipTo";
import CCM_Portal_GSTHST from "@salesforce/label/c.CCM_Portal_GSTHST";
import CCM_Portal_QST from "@salesforce/label/c.CCM_Portal_QST";
import CCM_Portal_ClaimDescription from "@salesforce/label/c.CCM_Portal_ClaimDescription";
import CCM_Portal_PromotionalItems from "@salesforce/label/c.CCM_Portal_PromotionalItems";
import CCM_Portal_Line from "@salesforce/label/c.CCM_Portal_Line";
import CCM_Portal_Model from "@salesforce/label/c.CCM_Portal_Model";
import CCM_Portal_MSRP from "@salesforce/label/c.CCM_Portal_MSRP";
import CCM_Portal_PromotionPrice from "@salesforce/label/c.CCM_Portal_PromotionPrice";
import CCM_Portal_SellInPrice from "@salesforce/label/c.CCM_Portal_SellInPrice";
import CCM_Portal_ChervonFundedAmount from "@salesforce/label/c.CCM_Portal_ChervonFundedAmount";
import CCM_Portal_SalePrice from "@salesforce/label/c.CCM_Portal_SalePrice";
import CCM_Portal_Quantity from "@salesforce/label/c.CCM_Portal_Quantity";
import CCM_Portal_TotalSales from "@salesforce/label/c.CCM_Portal_TotalSales";
import CCM_Portal_ClaimAmount from "@salesforce/label/c.CCM_Portal_ClaimAmount";
import CCM_Portal_Invalidclaimamount from "@salesforce/label/c.CCM_Portal_Invalidclaimamount";
import CCM_Portal_AddItemSymbol from "@salesforce/label/c.CCM_Portal_AddItemSymbol";
import CCM_Portal_NA from "@salesforce/label/c.CCM_Portal_NA";
import CCM_Portal_Delete from "@salesforce/label/c.CCM_Portal_Delete";
import CCM_Portal_FreeGoods from "@salesforce/label/c.CCM_Portal_FreeGoods";
import CCM_Portal_TotalClaimAmount from "@salesforce/label/c.CCM_Portal_TotalClaimAmount";
import CCM_Portal_TotalClaimAmountTaxExclusive from "@salesforce/label/c.CCM_Portal_TotalClaimAmountTaxExclusive";
import CCM_Portal_TotalClaimAmountTaxInclusive from "@salesforce/label/c.CCM_Portal_TotalClaimAmountTaxInclusive";
import CCM_Portal_TotalDiscountAmount from "@salesforce/label/c.CCM_Portal_TotalDiscountAmount";
import CCM_Portal_TotalSalesAmount_SellThroughClaim from "@salesforce/label/c.CCM_Portal_TotalSalesAmount_SellThroughClaim";
import CCM_Portal_UploadFiles from "@salesforce/label/c.CCM_Portal_UploadFiles";
import CCM_Portal_PleaseuploadsupportingPOSDocumentation from "@salesforce/label/c.CCM_Portal_PleaseuploadsupportingPOSDocumentation";
import CCM_Portal_Files from "@salesforce/label/c.CCM_Portal_Files";
import CCM_Portal_File from "@salesforce/label/c.CCM_Portal_File";
import CCM_Portal_Prev from "@salesforce/label/c.CCM_Portal_Prev";
import CCM_Portal_Submit from "@salesforce/label/c.CCM_Portal_Submit";
import CCM_Portal_ClaimStatus from "@salesforce/label/c.CCM_Portal_ClaimStatus";
import CCM_Portal_Reason from "@salesforce/label/c.CCM_Portal_Reason";
import CCM_Portal_Invoice from "@salesforce/label/c.CCM_Portal_Invoice";
import CCM_Portal_Action from "@salesforce/label/c.CCM_Portal_Action";
import CCM_Portal_InvoiceNumber from "@salesforce/label/c.CCM_Portal_InvoiceNumber";
import CCM_Portal_InvoiceDate from "@salesforce/label/c.CCM_Portal_InvoiceDate";
import CCM_Portal_Amount from "@salesforce/label/c.CCM_Portal_Amount";
import CCM_Portal_Next from "@salesforce/label/c.CCM_Portal_Next";
import CCM_Portal_CreateClaim from "@salesforce/label/c.CCM_Portal_CreateClaim";
import CCM_Portal_SaveUpload from "@salesforce/label/c.CCM_Portal_SaveUpload";
import CCM_Portal_SelectCustomerFirst from "@salesforce/label/c.CCM_Portal_SelectCustomerFirst";
import CCM_Portal_Pleaseselectcustomerfirst from "@salesforce/label/c.CCM_Portal_Pleaseselectcustomerfirst";
import CCM_Portal_CannotAccess from "@salesforce/label/c.CCM_Portal_CannotAccess";
import CCM_Portal_Thispromotioncodecannotaccess from "@salesforce/label/c.CCM_Portal_Thispromotioncodecannotaccess";
import CCM_Portal_PleaseSelectPromotionWindow from "@salesforce/label/c.CCM_Portal_PleaseSelectPromotionWindow";
import CCM_Portal_Pleaseselectpromotionwindowfirst from "@salesforce/label/c.CCM_Portal_Pleaseselectpromotionwindowfirst";
import CCM_Portal_DuplicateProduct from "@salesforce/label/c.CCM_Portal_DuplicateProduct";
import CCM_Portal_ThisProducthasbeenadded from "@salesforce/label/c.CCM_Portal_ThisProducthasbeenadded";
import CCM_Portal_SelectProductFirst from "@salesforce/label/c.CCM_Portal_SelectProductFirst";
import CCM_Portal_Pleaseselectproductfirst from "@salesforce/label/c.CCM_Portal_Pleaseselectproductfirst";
import CCM_Portal_SalePriceError from "@salesforce/label/c.CCM_Portal_SalePriceError";
import CCM_Portal_SalePricewithin1ispermitted from "@salesforce/label/c.CCM_Portal_SalePricewithin1ispermitted";
import CCM_Portal_Claimhasbeensaved from "@salesforce/label/c.CCM_Portal_Claimhasbeensaved";
import CCM_Portal_SaveFailed from "@salesforce/label/c.CCM_Portal_SaveFailed";
import CCM_Portal_AttachmentRequired from "@salesforce/label/c.CCM_Portal_AttachmentRequired";
import CCM_Portal_Pleaseuploadaattachment from "@salesforce/label/c.CCM_Portal_Pleaseuploadaattachment";
import CCM_Portal_Claimhasbeensubmited from "@salesforce/label/c.CCM_Portal_Claimhasbeensubmited";
import CCM_Portal_SubmitFailed from "@salesforce/label/c.CCM_Portal_SubmitFailed";
import CCM_Portal_CustomerRequired from "@salesforce/label/c.CCM_Portal_CustomerRequired";
import CCM_Portal_Pleaseselectacustomer from "@salesforce/label/c.CCM_Portal_Pleaseselectacustomer";
import CCM_Portal_WindowRequired from "@salesforce/label/c.CCM_Portal_WindowRequired";
import CCM_Portal_Pleaseselectawindow from "@salesforce/label/c.CCM_Portal_Pleaseselectawindow";
import CCM_Portal_BillToAddressRequired from "@salesforce/label/c.CCM_Portal_BillToAddressRequired";
import CCM_Portal_PleaseSelectAaddress from "@salesforce/label/c.CCM_Portal_PleaseSelectAaddress";
import CCM_Portal_ThresholdProductRequired from "@salesforce/label/c.CCM_Portal_ThresholdProductRequired";
import CCM_Portal_PleaseAddthresholdproduct from "@salesforce/label/c.CCM_Portal_PleaseAddthresholdproduct";
import CCM_Portal_MSRPError from "@salesforce/label/c.CCM_Portal_MSRPError";
import CCM_Portal_noMSRPTips from "@salesforce/label/c.CCM_Portal_noMSRPTips";
import CCM_Portal_OfferingProductRequired from "@salesforce/label/c.CCM_Portal_OfferingProductRequired";
import CCM_Portal_PleaseAddofferingproduct from "@salesforce/label/c.CCM_Portal_PleaseAddofferingproduct";
import CCM_Portal_Error from "@salesforce/label/c.CCM_Portal_Error";
import CCM_Portal_Success from "@salesforce/label/c.CCM_Portal_Success";
import CCM_Portal_ShipToAddressRequired from "@salesforce/label/c.CCM_Portal_ShipToAddressRequired";
import CCM_Portal_TotalClaimAmountNeedGreaterThan0 from "@salesforce/label/c.CCM_Portal_TotalClaimAmountNeedGreaterThan0";
import CCM_Portal_CheckTotalClaimAmount from "@salesforce/label/c.CCM_Portal_CheckTotalClaimAmount";
import CCM_Portal_ClaimSubmissionPeriodEnded from "@salesforce/label/c.CCM_Portal_ClaimSubmissionPeriodEnded";
import CCM_Portal_ClaimSubmissionPeriodEndedTips from "@salesforce/label/c.CCM_Portal_ClaimSubmissionPeriodEndedTips";

export default class CcmPortalSellThroughCreate extends NavigationMixin(
    LightningElement
) {
    // 定义 label
    @track label = {
        CCM_Portal_Loading,
        CCM_Portal_BasicInformation,
        CCM_Portal_Customer,
        CCM_Portal_PromotionCode,
        CCM_Portal_PromotionName,
        CCM_Portal_PromotionWindows,
        CCM_Portal_ReimbursementType,
        CCM_Portal_BillTo,
        CCM_Portal_ShipTo,
        CCM_Portal_GSTHST,
        CCM_Portal_QST,
        CCM_Portal_ClaimDescription,
        CCM_Portal_PromotionalItems,
        CCM_Portal_Line,
        CCM_Portal_Model,
        CCM_Portal_MSRP,
        CCM_Portal_PromotionPrice,
        CCM_Portal_SellInPrice,
        CCM_Portal_ChervonFundedAmount,
        CCM_Portal_SalePrice,
        CCM_Portal_Quantity,
        CCM_Portal_TotalSales,
        CCM_Portal_ClaimAmount,
        CCM_Portal_Invalidclaimamount,
        CCM_Portal_AddItemSymbol,
        CCM_Portal_NA,
        CCM_Portal_Delete,
        CCM_Portal_FreeGoods,
        CCM_Portal_TotalClaimAmount,
        CCM_Portal_TotalClaimAmountTaxExclusive,
        CCM_Portal_TotalClaimAmountTaxInclusive,
        CCM_Portal_TotalSalesAmount_SellThroughClaim,
        CCM_Portal_TotalDiscountAmount,
        CCM_Portal_UploadFiles,
        CCM_Portal_PleaseuploadsupportingPOSDocumentation,
        CCM_Portal_Files,
        CCM_Portal_File,
        CCM_Portal_Prev,
        CCM_Portal_Submit,
        CCM_Portal_BasicInformation,
        CCM_Portal_ClaimStatus,
        CCM_Portal_Reason,
        CCM_Portal_Invoice,
        CCM_Portal_Action,
        CCM_Portal_InvoiceNumber,
        CCM_Portal_InvoiceDate,
        CCM_Portal_Amount,
        CCM_Portal_Next,
        CCM_Portal_CreateClaim,
        CCM_Portal_UploadFiles,
        CCM_Portal_SaveUpload,
        CCM_Portal_SelectCustomerFirst,
        CCM_Portal_Pleaseselectcustomerfirst,
        CCM_Portal_CannotAccess,
        CCM_Portal_Thispromotioncodecannotaccess,
        CCM_Portal_PleaseSelectPromotionWindow,
        CCM_Portal_Pleaseselectpromotionwindowfirst,
        CCM_Portal_DuplicateProduct,
        CCM_Portal_ThisProducthasbeenadded,
        CCM_Portal_SelectProductFirst,
        CCM_Portal_Pleaseselectproductfirst,
        CCM_Portal_SalePriceError,
        CCM_Portal_SalePricewithin1ispermitted,
        CCM_Portal_Claimhasbeensaved,
        CCM_Portal_SaveFailed,
        CCM_Portal_AttachmentRequired,
        CCM_Portal_Pleaseuploadaattachment,
        CCM_Portal_Claimhasbeensubmited,
        CCM_Portal_SubmitFailed,
        CCM_Portal_CustomerRequired,
        CCM_Portal_Pleaseselectacustomer,
        CCM_Portal_WindowRequired,
        CCM_Portal_Pleaseselectawindow,
        CCM_Portal_BillToAddressRequired,
        CCM_Portal_PleaseSelectAaddress,
        CCM_Portal_ThresholdProductRequired,
        CCM_Portal_PleaseAddthresholdproduct,
        CCM_Portal_MSRPError,
        CCM_Portal_noMSRPTips,
        CCM_Portal_OfferingProductRequired,
        CCM_Portal_PleaseAddofferingproduct,
        CCM_Portal_Error,
        CCM_Portal_Success,
        CCM_Portal_ShipToAddressRequired,
        CCM_Portal_TotalClaimAmountNeedGreaterThan0,
        CCM_Portal_CheckTotalClaimAmount,
        CCM_Portal_ClaimSubmissionPeriodEnded,
        CCM_Portal_ClaimSubmissionPeriodEndedTips
    };
    allowExtensions = sellThroughPOSExtensions
        ? sellThroughPOSExtensions
        : '.pdf, .png, .jpg, .jpeg, .ppt, .pptx, .xlsx, .xls, .csv, .msg';
    @api
    isInternal = false;
    @api
    recordId;

    @track
    productSourceThreshold = [];
    @track
    productSourceOffering = [];

    @track
    addressSource = [];
    addressSourceAll;

    @track
    shipToAddressSource = [];
    shipToAddressSourceAll;

    @track
    claimItemsThreshold = [];
    @track
    claimItemsOffering = [];

    @track
    invoiceItems=[];

    // promotion code from backend, not user input
    promotionCode = '';
    customerId = '';
    customerName = '';
    claimDescription = '';

    showAddLine = true;

    @track
    selectedAddress = {};

    @track
    shipToSelectedAddress = {};

    @track
    reimbursementTypes = [];
    reimbursementType = '';

    @track
    windows = [];

    promotionId = '';

    salePrice;
    quantity;

    lineNo = 1;

    // when isPMAPP = false, sales price inputs in "Free Goods" can not input, display $0
    // isPMAPP = true when promotion type is price discount,
    // but now when promotion type is price discount, there is no "Free Goods"
    isPMAPP = false;

    realtimeSearch = false;

    promoCodeInput = '';
    promotionName = '';

    @track
    uploadedFiles = [];

    deleteClaimItemIds = [];
    @track
    offeringSummaryList = [];

    realtimeSearchCustomer = true;
    @track
    selectedCustomer = {};
    @track
    customerSource = [];
    isBeam = false;

    @track
    promotionCodeSource = [];

    showSpinner = false;

    // update by winfried on 2022.10.21
    // 为CCA用户添加GST/HST、QST两个字段
    GSTOrHSTCurrency = 0;
    QSTCurrency = 0;
    totalClaimAmountInclusive = 0;
    isCCA = false;

    @track
    currencyCode = '';
    @track
    totalFunding = 0;//yanko add
    @track
    sumTotalSalesBeforeChanges = 0;
    @api
    stWholeOrder = false; //austin add
    @api
    displayMSRP = false; // add by austin
    // 25.2.5 Add Buy More Save More promotion type
    isBMSM;
    // 25.2.5 end
    @api
    promotionProduct = []; //promotion关联的所有promotion product，用作对比区间落点显示

    get changeCurrencyCode() {
        if (this.isCCA) {
            this.currencyCode = 'CAD';
        } else {
            this.currencyCode = 'USD';
        }
    }

    get totalSalesAmount(){
        let _totalSalesAmount = 0;
        if(this.isBMSM){
            this.claimItemsThreshold.forEach(item => {
                _totalSalesAmount += isNaN(item.scaleTotalSales) ? 0 : item.scaleTotalSales - 0;
            });
        }
        return _totalSalesAmount;
    }

    get totalDiscountAmount(){
        let _totalDiscountAmount = 0;
        let _totalSalesAmount = 0;
        let _totalSalesQty = 0;
        if(this.isBMSM){
            this.claimItemsThreshold.forEach(item => {
                _totalSalesAmount += isNaN(item.scaleTotalSales) ? 0 : item.scaleTotalSales - 0;
                _totalSalesQty += isNaN(item.quantity) ? 0 : item.quantity - 0;
            });
            let _thresholdAmount = this.rules[0] && this.rules[0].thresholds[0] && this.rules[0].thresholds[0].minimumTotalAmount && isNaN(this.rules[0].thresholds[0].minimumTotalAmount.value) ? 0 : Number(this.rules[0].thresholds[0].minimumTotalAmount.value);
            let _thresholdQty = this.rules[0] && this.rules[0].thresholds[0] && this.rules[0].thresholds[0].minimumTotalQuantity && isNaN(this.rules[0].thresholds[0].minimumTotalQuantity.value) ? 0 : Number(this.rules[0].thresholds[0].minimumTotalQuantity.value);
            let _offeringSaveAmount = this.rules[0] && this.rules[0].offerings[0] && this.rules[0].offerings[0].amountOff && isNaN(this.rules[0].offerings[0].amountOff.value) ? 0 : Number(this.rules[0].offerings[0].amountOff.value);
            let _meetThresholdTimes = 0;
            if(_thresholdAmount > 0){
                _meetThresholdTimes = Math.floor(_totalSalesAmount / _thresholdAmount);
            } else if (_thresholdQty > 0){
                _meetThresholdTimes = Math.floor(_totalSalesQty / _thresholdQty);
            }
            _totalDiscountAmount = this.decimalPlaces(_meetThresholdTimes * _offeringSaveAmount);
        }
        return _totalDiscountAmount;
    }

    get totalClaimAmount() {
        let _amount = 0;
        let _totalSalesAmount = 0;
        let _totalSalesQty = 0;
        if(this.isBMSM){
            this.claimItemsThreshold.forEach(item => {
                _totalSalesAmount += isNaN(item.scaleTotalSales) ? 0 : item.scaleTotalSales - 0;
                _totalSalesQty += isNaN(item.quantity) ? 0 : item.quantity - 0;
            });
            let _thresholdAmount = this.rules[0] && this.rules[0].thresholds[0] && this.rules[0].thresholds[0].minimumTotalAmount && isNaN(this.rules[0].thresholds[0].minimumTotalAmount.value) ? 0 : Number(this.rules[0].thresholds[0].minimumTotalAmount.value);
            let _thresholdQty = this.rules[0] && this.rules[0].thresholds[0] && this.rules[0].thresholds[0].minimumTotalQuantity && isNaN(this.rules[0].thresholds[0].minimumTotalQuantity.value) ? 0 : Number(this.rules[0].thresholds[0].minimumTotalQuantity.value);
            let _offeringSaveAmount = this.rules[0] && this.rules[0].offerings[0] && this.rules[0].offerings[0].amountOff && isNaN(this.rules[0].offerings[0].amountOff.value) ? 0 : Number(this.rules[0].offerings[0].amountOff.value);
            let _offeringPercentChervonFunding = this.rules[0] && this.rules[0].offerings[0] && this.rules[0].offerings[0].percentChervonFunding && isNaN(this.rules[0].offerings[0].percentChervonFunding.value) ? 0 : Number(this.rules[0].offerings[0].percentChervonFunding.value);
            let _meetThresholdTimes = 0;
            if(_thresholdAmount > 0){
                _meetThresholdTimes = Math.floor(_totalSalesAmount / _thresholdAmount);
            } else if (_thresholdQty > 0){
                _meetThresholdTimes = Math.floor(_totalSalesQty / _thresholdQty);
            }
            _amount = _meetThresholdTimes * _offeringSaveAmount * (_offeringPercentChervonFunding / 100);
        } else {
            if (this.claimItemsThreshold.length > 0) {
                this.claimItemsThreshold.forEach(item => {
                    _amount += isNaN(item.scaleAmount) ? 0 : item.scaleAmount - 0;
                });
            }
            if (this.claimItemsOffering.length > 0) {
                this.claimItemsOffering.forEach(item => {
                    _amount += isNaN(item.scaleAmount) ? 0 : item.scaleAmount - 0;
                });
            }
        }
        this.totalClaimAmountInclusive = this.decimalPlaces(
            Number(_amount) +
                Number(this.GSTOrHSTCurrency) +
                Number(this.QSTCurrency)
        );
        return this.decimalPlaces(_amount);
    }

    @track
    steps = [
        { label: this.label.CCM_Portal_CreateClaim, value: 1 },
        { label: this.label.CCM_Portal_UploadFiles, value: 2 },
    ];

    currentStep = 1;

    get firstStep() {
        return this.currentStep === 1;
    }

    get secondStep() {
        return this.currentStep === 2;
    }

    mode;
    @wire(CurrentPageReference)
    setCurrentPageReference(currentPageReference) {
        if (
            currentPageReference.type === 'standard__recordPage' &&
            currentPageReference.attributes
        ) {
            const _attributes = currentPageReference.attributes;
            if (_attributes.recordId) {
                // eslint-disable-next-line @lwc/lwc/no-api-reassignments
                this.recordId = _attributes.recordId;
            }
            if (
                currentPageReference.state &&
                currentPageReference.state.c__mode &&
                currentPageReference.state.c__mode === 'view'
            ) {
                this.mode = 'view';
            }
        }
        if (
            currentPageReference.type === 'comm__namedPage' &&
            currentPageReference.state
        ) {
            const _state = currentPageReference.state;
            if (_state.c__claimid) {
                // eslint-disable-next-line @lwc/lwc/no-api-reassignments
                this.recordId = _state.c__claimid;
            }
            if (!_state.c__claimid && _state.c__promotioncode) {
                // eslint-disable-next-line @lwc/lwc/no-api-reassignments
                this.promoCodeInput = decodeURI(_state.c__promotioncode);
            }
        }
    }

    /**
     * Allen, Comment on 2021/05/27
     * get current user profile
     */
    @wire(getRecord, {
        recordId: USER_ID,
        fields: [PROFILE_NAME_FIELD],
    })
    wireuser({ error, data }) {
        if (error) {
            this.error = error;
        } else if (data) {
            let _profileName = data.fields.Profile.value.fields.Name.value;
            if (_profileName === 'BEAM') {
                this.isBeam = true;
            }
        }
    }

    get hasThresholdProduct() {
        return (
            (this.productSourceThreshold.length > 0 && this.stWholeOrder == false) ||
            (this.claimItemsThreshold.length > 0 && this.stWholeOrder == false) // add by asutin
        );
    }

    get hasWholeThreshold() {
        return (this.productSourceThreshold.length > 0 || this.claimItemsThreshold.length > 0)
    }

    get hasOfferingProduct() {
        return (
            this.productSourceOffering.length > 0 ||
            this.claimItemsOffering.length > 0
        );
    }

    get showNextButton() {
        return (
            this.claimItemsThreshold.length > 0 ||
            this.claimItemsOffering.length > 0
        );
    }

    @track
    status;
    statusText;

    /**
     * Allen, Comment on 2021/05/27
     * display "Save & Upload" when create,
     * display "Update & Upload" when update
     */
    get saveButtonLabel() {
        if (
            this.recordId &&
            this.status &&
            (this.status === 'Draft' || this.status === 'Rejected')
        ) {
            return this.label.CCM_Portal_UploadFiles;
        }
        return this.label.CCM_Portal_SaveUpload;
    }

    /**
     * Allen, Comment on 2021/05/27
     * status=draft, reject can edit
     */
    get isEdit() {
        if (this.mode && this.mode === 'view') {
            return false;
        }
        // if(this.isBeam){
        //     return false
        // }
        if (
            this.recordId &&
            this.status &&
            this.status !== 'Draft' &&
            this.status !== 'Rejected'
        ) {
            return false;
        }
        return true;
    }

    rejectReason = '';
    get isRejected() {
        if (this.recordId && this.status && this.status === 'Rejected') {
            return true;
        }
        return false;
    }

    rules = [];

    connectedCallback() {
        if (!this.connect) {
            this.connect = true;
            loadScript(this, PromotionResource + '/lodash.min.js');
            loadStyle(this, PromotionResource + '/promotion.css');

            Promise.all([getCustomerInfo(), getReimbursementType()]).then(
                values => {
                    if (values[0]) {
                        // dealor portal user, get user id, name, sell through promotions that can access
                        if (!this.isInternal) {
                            this.customerId = values[0].customerId;
                            this.customerName = values[0].customerName;
                            if (
                                values[0].promotionCodeList &&
                                Array.isArray(values[0].promotionCodeList)
                            )
                                this.promotionCodeSource =
                                    values[0].promotionCodeList.map(item => {
                                        return {
                                            label: item.promotionCodeForExternal,
                                            description: item.promotionName,
                                        };
                                    });
                        }
                        if (!this.recordId && this.promoCodeInput) {
                            this.searchPromotion();
                        }
                        // update by winfried on 2022.12.21
                        // 控制GST/HST、QST两个字段可见性
                        this.isCCA = values[0].isCCA;
                        this.changeCurrencyCode;
                    }
                    // get reimbursementTypes picklist
                    if (values[1]) {
                        if (values[1] && Array.isArray(values[1])) {
                            this.reimbursementTypes = values[1].map(val => {
                                return {
                                    label: val.Name,
                                    value: val.Id,
                                };
                            });
                            if (this.reimbursementTypes.length > 0) {
                                this.reimbursementType =
                                    this.reimbursementTypes[0].value;
                            }
                        }
                    }
                },
                error => {
                    console.error(error);
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: this.label.CCM_Portal_Error,
                            message: error.body.message,
                            variant: 'error',
                        })
                    );
                }
            );

            if (this.recordId) {
                this.showSpinner = true;
                // get claim detail when view or approve claim
                getClaimRequestWrapper({ recordId: this.recordId }).then(
                    data => {
                        this.showSpinner = false;
                        // eslint-disable-next-line @lwc/lwc/no-api-reassignments
                        this.recordId = data.recordId;
                        this.promotionCode = data.promotionCode;
                        this.promotionId = data.promotionId;
                        this.customerName = data.customerName;
                        this.claimDescription = data.claimDescription;
                        this.reimbursementType = data.reimbursementType;
                        this.status = data.status;
                        this.statusText = data.statusText;
                        this.rejectReason = data.rejectReason;
                        this.offeringSummaryList = data.offeringSummaryList;
                        this.promotionName = data.promotionName;

                        this.invoiceItems = data.invoiceList;
                        if(data.invoiceList && data.invoiceList.length > 0){
                            this.showInvoice = true;
                        }else{
                            this.showInvoice = false;
                        }
                        // update by winfried on 2022.10.21
                        // 为CCA用户添加GST/HST、QST两个字段
                        this.isCCA =
                            data.customerOrgCode == 'CCA' ? true : false;
                        this.changeCurrencyCode;
                        this.GSTOrHSTCurrency = data.gstOrHSTCurrency;
                        this.QSTCurrency = data.qstCurrency;
                        this.totalClaimAmountInclusive =
                            data.totalClaimAmountInclusive;

                        if (data.promotionBasicInfo) {
                            if (
                                data.promotionBasicInfo.promotionType ===
                                'Price Discount'
                            ) {
                                this.isPMAPP = true;
                            } else {
                                this.isPMAPP = false;
                            }
                        }
                        if (this.recordId) {
                            this.selectedCustomer = {
                                label: data.customerName,
                                value: data.customerId,
                            };
                        }
                        //add by austin,对whole order显示进行判断
                        if(data.promotionBasicInfo.promotionType == 'Whole Order Promo'){
                            this.stWholeOrder = true;
                        }
                        //end
                        // 25.2.5 Add buy more save more promotion type
                        if(data.promotionBasicInfo.promotionType == 'Buy More Save More'){
                            this.isBMSM = true;
                        }else{
                            this.isBMSM = false;
                        }
                        // 25.2.5 end
                        this.customerId = data.customerId;
                        let _claimItemsThreshold = [];
                        let _claimItemsOffering = [];
                        data.thresholdClaimItems.forEach((claim, index) => {
                            let _item = {
                                lineNo: index + 1,
                                recordId: claim.recordId,
                                productId: claim.productId,
                                productName: claim.productName,
                                productCode: claim.productCode,
                                MSRP: claim.MSRP,
                                promoPrice: claim.promoPrice,
                                sellInPrice: claim.sellInPrice,
                                type: claim.type,
                                quantity: claim.quantity,
                                salePrice: claim.salePrice,
                                scaleTotalSales: claim.totalSales,
                                chervonFundedAmount: claim.chervonFundedAmount,
                                scaleAmount: claim.claimAmount,
                                brand: claim.brand,
                                salePriceMin: claim.promoPrice - 1,
                                salePriceMax: claim.promoPrice + 1,
                                displayMSRP:true,
                            };
                            _claimItemsThreshold.push(_item);
                        });
                        data.offeringClaimItems.forEach((claim, index) => {
                            let _item = {
                                lineNo: index + 1,
                                recordId: claim.recordId,
                                productId: claim.productId,
                                productName: claim.productName,
                                productCode: claim.productCode,
                                MSRP: claim.MSRP,
                                promoPrice: this.isPMAPP ? claim.promoPrice : 0,
                                sellInPrice: this.isPMAPP ? claim.sellInPrice : 0,
                                type: claim.type,
                                quantity: claim.quantity,
                                salePrice: this.isPMAPP ? claim.salePrice : 0,
                                scaleTotalSales: claim.totalSales,
                                chervonFundedAmount: claim.chervonFundedAmount,
                                scaleAmount: claim.claimAmount,
                                brand: claim.brand,
                                salePriceMin: this.isPMAPP
                                    ? claim.promoPrice - 1
                                    : 0,
                                salePriceMax: this.isPMAPP
                                    ? claim.promoPrice + 1
                                    : 1,
                            };
                            _claimItemsOffering.push(_item);
                        });
                        this.claimItemsThreshold = _claimItemsThreshold;
                        this.claimItemsOffering = _claimItemsOffering;

                        //yanko add to see if changes rules
                        this.sumTotalSalesBeforeChanges = 0;
                        this.claimItemsThreshold.forEach(item => {
                            this.sumTotalSalesBeforeChanges = Number(this.sumTotalSalesBeforeChanges) + Number(isNaN(item.scaleTotalSales) ? 0 : Number(item.scaleTotalSales));
                        });

                        if (data.promotionBasicInfo) {
                            this.windowId = data.promotionWindowId;
                            // window list
                            if (
                                data.promotionBasicInfo.windowList &&
                                Array.isArray(
                                    data.promotionBasicInfo.windowList
                                )
                            ) {
                                this.windows =
                                    data.promotionBasicInfo.windowList.map(
                                        item => {
                                            return {
                                                // label: item.winStartDate + " - " + item.winEndDate,
                                                label: item.winStartDateEndDate,
                                                value: item.windowId,
                                                selected:
                                                    item.windowId ===
                                                    data.promotionWindowId
                                                        ? true
                                                        : false,
                                            };
                                        }
                                    );
                            }

                            // bill address list
                            if (
                                data.promotionBasicInfo.billList &&
                                Array.isArray(data.promotionBasicInfo.billList)
                            ) {
                                let _addresses = [];
                                _addresses =
                                    data.promotionBasicInfo.billList.map(
                                        item => {
                                            return {
                                                label: item.name,
                                                value: item.authBrandAndAddressId,
                                                brand: item.brand,
                                                address: {
                                                    country: item.country,
                                                    state: item.state,
                                                    city: item.city,
                                                    address1: item.address1,
                                                },
                                            };
                                        }
                                    );
                                this.addressSourceAll = _addresses;
                                this.addressSource = _addresses;
                                let _address = this.addressSource.find(
                                    item => item.value === data.address
                                );
                                this.selectedAddress = _address;
                                // let _cmpAddress = this.template.querySelector('.autoCompleteAddress');
                                // if(_cmpAddress && this.addressSource.length>0){
                                //     _cmpAddress.setSource(this.addressSource);
                                //     _cmpAddress.setInputVal(_address.label);
                                //     _cmpAddress.setSelectedData(this.selectedAddress);
                                // }
                            }
                            if(data.promotionBasicInfo.shiptoList &&
                                Array.isArray(data.promotionBasicInfo.shiptoList)) {
                                let _addresses = [];
                                _addresses =
                                    data.promotionBasicInfo.shiptoList.map(
                                        item => {
                                            return {
                                                label: item.name,
                                                value: item.authBrandAndAddressId,
                                                address: {
                                                    country: item.country,
                                                    state: item.state,
                                                    city: item.city,
                                                    address1: item.address1,
                                                },
                                            };
                                        }
                                    );
                                this.shipToAddressSourceAll = _addresses;
                                this.shipToAddressSource = _addresses;
                                let _address = this.shipToAddressSource.find(
                                    item => item.value === data.shiptoAddress
                                );
                                this.shipToSelectedAddress = _address;
                            }
                            if (
                                data.promotionBasicInfo.thresholdProducts &&
                                Array.isArray(
                                    data.promotionBasicInfo.thresholdProducts
                                )
                            ) {
                                let _productsThreshold = [];
                                _productsThreshold =
                                    data.promotionBasicInfo.thresholdProducts.map(
                                        item => {
                                            item.label = item.productCode;
                                            item.description = item.productName;
                                            item.value = item.productId;
                                            return item;
                                        }
                                    );
                                this.productSourceThreshold =
                                    _productsThreshold;
                            }
                            if (
                                data.promotionBasicInfo.offeringProducts &&
                                Array.isArray(
                                    data.promotionBasicInfo.offeringProducts
                                )
                            ) {
                                let _productsOffering = [];
                                _productsOffering =
                                    data.promotionBasicInfo.offeringProducts.map(
                                        item => {
                                            item.label = item.productCode;
                                            item.description = item.productName;
                                            item.value = item.productId;
                                            return item;
                                        }
                                    );
                                this.productSourceOffering = _productsOffering;
                                //yanko add callback no promotionProduct
                                this.promotionProduct = _productsOffering;
                            }
                        }

                        this.rules = data.promotionRules;

                        //yanko callback has add offering buttom
                        if(data.offeringClaimItems.length == 0){
                            this.claimItemsOffering = [];
                            this.productSourceOffering = [];
                        }

                        getAllFiles({ claimId: this.recordId }).then(
                            files => {
                                let _files = files.map((item, index) => {
                                    return {
                                        lineNo: index + 1,
                                        name: item.Title,
                                        documentId: item.Id,
                                    };
                                });
                                this.uploadedFiles = _files;
                            },
                            error => {
                                console.error(error);
                                this.dispatchEvent(
                                    new ShowToastEvent({
                                        title: this.label.CCM_Portal_Error,
                                        message: error.body.message,
                                        variant: 'error',
                                    })
                                );
                            }
                        );

                        // edit claim, get sell through promotion that can access
                        getPromotionCodeByCustomer({
                            customerId: this.customerId,
                        }).then(
                            promos => {
                                if (promos && Array.isArray(promos)) {
                                    this.promotionCodeSource = promos.map(
                                        item => {
                                            return {
                                                label: item.promotionCodeForExternal,
                                                description: item.promotionName,
                                            };
                                        }
                                    );
                                }
                            },
                            error => {
                                console.error(error);
                                this.dispatchEvent(
                                    new ShowToastEvent({
                                        title: this.label.CCM_Portal_Error,
                                        message: error.body.message,
                                        variant: 'error',
                                    })
                                );
                            }
                        );
                    },
                    error => {
                        console.error(error);
                        this.dispatchEvent(
                            new ShowToastEvent({
                                title: this.label.CCM_Portal_Error,
                                message: error.body.message,
                                variant: 'error',
                            })
                        );
                    }
                );
            }
        }
    }

    renderedCallback() {
        if (!this.isSetAddress) {
            let _cmpAddress = this.template.querySelector(
                '.autoCompleteAddress'
            );
            if (
                _cmpAddress &&
                this.selectedAddress &&
                this.addressSource.length > 0
            ) {
                _cmpAddress.setSource(this.addressSource);
                _cmpAddress.setInputVal(this.selectedAddress.label);
                _cmpAddress.setSelectedData(this.selectedAddress);
            }

            _cmpAddress = this.template.querySelector(
                '.shipToAutoCompleteAddress'
            );
            if (
                _cmpAddress &&
                this.shipToSelectedAddress &&
                this.shipToAddressSource.length > 0
            ) {
                _cmpAddress.setSource(this.shipToAddressSource);
                _cmpAddress.setInputVal(this.shipToSelectedAddress.label);
                _cmpAddress.setSelectedData(this.shipToSelectedAddress);
                this.isSetAddress = true;
            };
        }
    }

    handleProgressStepClick(event) {
        if (this.recordId) {
            this.currentStep = event.currentTarget.value;
        }
    }

    handleClickAddItemThreshold() {
        this.claimItemsThreshold.push({
            lineNo: this.claimItemsThreshold.length + 1,
        });
    }

    handleClickAddItemOffering() {
        this.claimItemsOffering.push({
            lineNo: this.claimItemsOffering.length + 1,
        });
    }

    /**
     * Allen, Comment on 2021/05/27
     * search promotion after input blur
     */
    handlePromotionCodeBlur(event) {
        if (
            this.isInternal &&
            event.currentTarget.value &&
            (!this.selectedCustomer ||
                JSON.stringify(this.selectedCustomer) === '{}')
        ) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_SelectCustomerFirst,
                    message: this.label.CCM_Portal_Pleaseselectcustomerfirst,
                    variant: 'warning',
                })
            );
            event.currentTarget.value = '';
            return;
        }
        this.promoCodeInput = event.currentTarget.value;
        this.searchPromotion('in-promocode');
    }

    /**
     * Allen, Comment on 2021/05/27
     * search promotion
     */
    searchPromotion(searchwhere) {
        let _code = this.promoCodeInput;
        let _customerId = this.isInternal
            ? this.selectedCustomer.value
            : this.customerId;
        if (_code !== '' && _customerId) {
            this.showSpinner = true;
            getPromotionInfoByCode({
                promotionCode: _code,
                customerId: _customerId,
            }).then(
                data => {
                    this.showSpinner = false;
                    //add by austin
                    let _productsOffering = [];
                    if(data.offeringProducts){
                        _productsOffering = data.offeringProducts.map(item => {
                            item.label = item.ProductCode;
                            item.description = item.productName;
                            item.value = item.productId;
                            return item;
                        });
                    }
                    this.promotionProduct = _productsOffering;
                    //end
                    if (data && data.promotionId) {
                        this.promotionCode = _code;
                        if (this.promotionId !== data.promotionId) {
                            this.clearFields();
                        }
                        this.promotionId = data.promotionId;
                        if (data.promotionType === 'Price Discount') {
                            this.isPMAPP = true;
                        } else {
                            this.isPMAPP = false;
                        }
                        //add by austin, sell through whole order
                        if (data.promotionType === 'Whole Order Promo') {
                            this.stWholeOrder = true;
                        }
                        //end
                        // 25.2.5 Add buy more save more promotion type
                        if(data.promotionType == 'Buy More Save More'){
                            this.isBMSM = true;
                        } else {
                            this.isBMSM = false;
                        }
                        // 25.2.5 end
                        if (data.billList && Array.isArray(data.billList)) {
                            let _addresses = [];
                            _addresses = data.billList.map(item => {
                                return {
                                    label: item.name,
                                    value: item.authBrandAndAddressId,
                                    brand: item.brand,
                                    address: {
                                        country: item.country,
                                        state: item.state,
                                        city: item.city,
                                        address1: item.address1,
                                    },
                                };
                            });
                            this.addressSourceAll = _addresses;
                            this.addressSource = _addresses;
                            let _cmpAddress = this.template.querySelector(
                                '.autoCompleteAddress'
                            );
                            if (_cmpAddress && this.addressSource.length > 0) {
                                _cmpAddress.setSource(this.addressSource);
                            }
                        }

                        if (data.shiptoList && Array.isArray(data.shiptoList)) {
                            let _addresses = [];
                            _addresses = data.shiptoList.map(item => {
                                return {
                                    label: item.name,
                                    value: item.authBrandAndAddressId,
                                    address: {
                                        country: item.country,
                                        state: item.state,
                                        city: item.city,
                                        address1: item.address1,
                                    },
                                };
                            });
                            this.shipToAddressSourceAll = _addresses;
                            this.shipToAddressSource = _addresses;
                            let _cmpAddress = this.template.querySelector(
                                '.shipToAutoCompleteAddress'
                            );
                            if (_cmpAddress && this.shipToAddressSource.length > 0) {
                                _cmpAddress.setSource(this.shipToAddressSource);
                            }
                        }

                        if (data.windowList && Array.isArray(data.windowList)) {
                            if (data.windowList.length > 1) {
                                this.windows = data.windowList.map(item => {
                                    return {
                                        // label: item.winStartDate + " - " + item.winEndDate,
                                        label: item.winStartDateEndDate,
                                        value: item.windowId,
                                        selected: false,
                                    };
                                });
                            } else {
                                this.windows = data.windowList.map(item => {
                                    this.windowId = item.windowId;
                                    return {
                                        // label: item.winStartDate + " - " + item.winEndDate,
                                        label: item.winStartDateEndDate,
                                        value: item.windowId,
                                        selected: true,
                                    };
                                });
                            }
                        }
                        if (this.stWholeOrder) {
                            this.claimItemsOffering = [];
                        } else { //bogo, price discount
                            let _productsThreshold = [];
                            if(data.thresholdProducts){
                                _productsThreshold = data.thresholdProducts.map(
                                    item => {
                                        item.label = item.productCode;
                                        item.description = item.productName;
                                        item.value = item.productId;
                                        return item;
                                    }
                                );
                            }
                            this.productSourceThreshold = _productsThreshold;
                            if (this.productSourceThreshold.length === 0) {
                                this.claimItemsThreshold = [];
                            }

                            let _productsOffering = [];
                            if(data.offeringProducts){
                                _productsOffering = data.offeringProducts.map(item => {
                                    item.label = item.productCode;
                                    item.description = item.productName;
                                    item.value = item.productId;
                                    return item;
                                });
                            }
                            this.productSourceOffering = _productsOffering;
                            if (
                                _productsThreshold.length > 0 &&
                                this.claimItemsThreshold.length === 0
                            ) {
                                this.claimItemsThreshold.push({ lineNo: 1 });
                            }
                            if (
                                _productsOffering.length > 0 &&
                                this.claimItemsOffering.length === 0
                            ) {
                                this.claimItemsOffering.push({ lineNo: 1 });
                            }
                        }
                        this.rules = data.promotionRules;
                    } else if (data) {
                        this.dispatchEvent(
                            new ShowToastEvent({
                                title: this.label.CCM_Portal_CannotAccess,
                                message: this.label.CCM_Portal_Thispromotioncodecannotaccess,
                                variant: 'warning',
                            })
                        );
                        if (searchwhere === 'in-customer') {
                            let _cmpCustomer =
                                this.template.querySelector('.customers');
                            if (_cmpCustomer) {
                                _cmpCustomer.displayText = '';
                            }
                        }
                        if (searchwhere === 'in-promocode') {
                            this.promotionCode = '';
                        }
                    }
                },
                error => {
                    this.showSpinner = false;
                    console.error(error);
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: this.label.CCM_Portal_Error,
                            message: error.body.message,
                            variant: 'error',
                        })
                    );
                }
            );
        } else {
            this.clearFields();
        }
    }

    clearFields() {
        this.selectedAddress = {};
        this.shipToSelectedAddress = {};
        this.addressSource = [];
        this.shipToAddressSource = [];
        this.windowId = '';
        this.windows = [];
        let _cmpAddress = this.template.querySelector('.autoCompleteAddress');
        if (_cmpAddress) {
            _cmpAddress.setInputVal('');
            _cmpAddress.setSelectedData(undefined);
        }
        _cmpAddress = this.template.querySelector('.shipToAutoCompleteAddress');
        if (_cmpAddress) {
            _cmpAddress.setInputVal('');
            _cmpAddress.setSelectedData(undefined);
        }
        this.claimDescription = '';
        this.productSourceThreshold = [];
        this.productSourceOffering = [];
        let _claimItemsThreshold = [];
        // _claimItemsThreshold.push({ lineNo:1});
        this.claimItemsThreshold = _claimItemsThreshold;
        let _claimItemsOffering = [];
        // _claimItemsOffering.push({ lineNo:1});
        this.claimItemsOffering = _claimItemsOffering;
    }

    handleClaimDescriptionChange(event) {
        this.claimDescription = event.currentTarget.value;
    }

    handleSearchInputChange(event) {
        let _productsThreshold = [];

        getWholeOrderPromtionInfo({ PromotionId: this.promotionId , productName:event.detail}).then(
            products => {
                _productsThreshold = products.thresholdProducts.map(
                    item => {
                        item.label = item.productCode;
                        item.description = item.productName;
                        item.value = item.productId;
                        return item;
                    }
                );
                this.productSourceThreshold = _productsThreshold;
                // if (this.productSourceThreshold.length === 0) {
                //     this.claimItemsThreshold = [];
                // }
                if (
                    _productsThreshold.length > 0 &&
                    this.claimItemsThreshold.length === 0
                ) {
                    this.claimItemsThreshold.push({ lineNo: 1 });
                }
            }
        );
    }

    /**
     * Allen, Comment on 2021/05/27
     * handle add product to threshold
     */
    handleProductSelectedThreshold(e) {
        if (!this.windowId) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_PleaseSelectPromotionWindow,
                    message: this.label.CCM_Portal_Pleaseselectpromotionwindowfirst,
                    variant: 'warning',
                })
            );
            e.currentTarget.displayText = '';
            return;
        }
        let _product = this.productSourceThreshold.find(
            item => item.value === JSON.parse(e.detail).value
        );

        let _index = parseInt(e.currentTarget.dataset.index, 10);
        let _existProduct = this.claimItemsThreshold.find(
            (item, index) =>
                item.productId === _product.value && index !== _index
        );
        if (!_existProduct) {
            let _windowPrice = _product.windowMSRP.find(
                msrp => msrp.windowId === this.windowId
            );
            let _item = {
                productId: _product.productId,
                productName: _product.productName,
                productCode: _product.productCode,
                // MSRP: _product.MSRP,
                MSRP: _windowPrice ? _windowPrice.MSRP : 0,
                promoPrice: _product.promoPrice,
                sellInPrice: _product.sellInPrice,
                chervonFundedAmount: _product.chervonFundedAmount,
                type: _product.type,
                brand: _product.brand,
                salePriceMin: _product.promoPrice - 1,
                salePriceMax: _product.promoPrice + 1,
            };
            Object.assign(this.claimItemsThreshold[_index], _item);
        } else {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_DuplicateProduct,
                    message: this.label.CCM_Portal_ThisProducthasbeenadded,
                    variant: 'warning',
                })
            );
            e.currentTarget.displayText = '';
        }
    }

     /**
     * Austin, Comment on 2023/05/16
     * handle add product to threshold
     */
     handleProductSelectedWholeOrderThreshold(e) {
        if (!this.windowId) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_PleaseSelectPromotionWindow,
                    message: this.label.CCM_Portal_Pleaseselectpromotionwindowfirst,
                    variant: 'warning',
                })
            );
            e.currentTarget.displayText = '';
            return;
        }
        let _product = this.productSourceThreshold.find(
            item => item.value === JSON.parse(e.detail).value
        );

        let _index = parseInt(e.currentTarget.dataset.index, 10);
        let _existProduct = this.claimItemsThreshold.find(
            (item, index) =>
                item.productId === _product.value && index !== _index
        );
        if (!_existProduct) {
            if (_product.windowMSRP.length != 0) {
                this.displayMSRP = true;
            } else {
                this.displayMSRP = false;
            }
            let _item = {
                productId: _product.productId,
                productName: _product.productName,
                productCode: _product.productCode,
                MSRP: _product.windowMSRP.length != 0 ?  _product.windowMSRP[0].MSRP: null,
                promoPrice: _product.promoPrice,
                sellInPrice: _product.sellInPrice,
                type: _product.type,
                brand: _product.brand,
                promotionId: _product.promotionId,
                salePriceMin: _product.promoPrice - 1,
                salePriceMax: _product.promoPrice + 1,
                displayMSRP: this.displayMSRP,
            };
            Object.assign(this.claimItemsThreshold[_index], _item);
        } else {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_DuplicateProduct,
                    message: this.label.CCM_Portal_ThisProducthasbeenadded,
                    variant: 'warning',
                })
            );
            e.currentTarget.displayText = '';
            // e.currentTarget.setInputVal('');
        }
    }

    /**
     * Allen, Comment on 2021/05/27
     * handle add product to offering
     */
    handleProductSelectedOffering(e) {
        if (!this.windowId) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_PleaseSelectPromotionWindow,
                    message: this.label.CCM_Portal_Pleaseselectpromotionwindowfirst,
                    variant: 'warning',
                })
            );
            e.currentTarget.displayText = '';
            return;
        }
        let _product = this.productSourceOffering.find(
            item => item.value === JSON.parse(e.detail).value
        );
        let _index = parseInt(e.currentTarget.dataset.index, 10);
        let _existProduct = this.claimItemsOffering.find(
            (item, index) =>
                item.productId === _product.value && index !== _index
        );
        if (!_existProduct) {
            let _windowPrice = _product.windowMSRP.find(
                msrp => msrp.windowId === this.windowId
            );
            let _item = {
                productId: _product.productId,
                productName: _product.productName,
                productCode: _product.productCode,
                MSRP: _windowPrice ? _windowPrice.MSRP : 0,
                promoPrice: this.isPMAPP ? _product.promoPrice : 0,
                sellInPrice: this.isPMAPP ? _product.sellInPrice : 0,
                chervonFundedAmount: _product.chervonFundedAmount,
                type: _product.type,
                brand: _product.brand,
                salePriceMin: this.isPMAPP ? _product.promoPrice - 1 : 0,
                salePriceMax: this.isPMAPP ? _product.promoPrice + 1 : 1,
                rule: _product.rule,
                offering: _product.offering,
            };
            Object.assign(this.claimItemsOffering[_index], _item);
        } else {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_DuplicateProduct,
                    message: this.label.CCM_Portal_ThisProducthasbeenadded,
                    variant: 'warning',
                })
            );
            e.currentTarget.displayText = '';
        }
    }

    handleAddressSearchInputChange(event) {
        let classList = event.currentTarget.classList;
        if(classList && classList.length > 0) {
            classList.forEach(item=>{
                if(item === 'autoCompleteAddress') {
                    if (this.addressSourceAll && Array.isArray(this.addressSourceAll)) {
                        let _addressAll = JSON.parse(JSON.stringify(this.addressSourceAll));
                        if (event.detail === '') {
                            this.addressSource = _addressAll;
                            this.selectedAddress = {};
                            event.currentTarget.setInputVal('');
                            event.currentTarget.setSelectedData(undefined);
                        } else {
                            this.addressSource = _addressAll.filter(
                                item => item.label.indexOf(event.detail) > -1
                            );
                        }
                        let _cmpAddress = this.template.querySelector(
                            '.autoCompleteAddress'
                        );
                        if (_cmpAddress && this.addressSource.length > 0) {
                            _cmpAddress.setSource(this.addressSource);
                            if (event.detail !== '') {
                                _cmpAddress.filterDataByKeyword(event.detail);
                            }
                        }
                    }
                }
                else if(item === 'shipToAutoCompleteAddress') {
                    if (this.shipToAddressSourceAll && Array.isArray(this.shipToAddressSourceAll)) {
                        let _addressAll = JSON.parse(JSON.stringify(this.shipToAddressSourceAll));
                        if (event.detail === '') {
                            this.shipToAddressSource = _addressAll;
                            this.shipToSelectedAddress = {};
                            event.currentTarget.setInputVal('');
                            event.currentTarget.setSelectedData(undefined);
                        } else {
                            this.shipToAddressSource = _addressAll.filter(
                                item => item.label.indexOf(event.detail) > -1
                            );
                        }
                        let _cmpAddress = this.template.querySelector(
                            '.shipToAutoCompleteAddress'
                        );
                        if (_cmpAddress && this.addressSource.length > 0) {
                            _cmpAddress.setSource(this.shipToAddressSource);
                            if (event.detail !== '') {
                                _cmpAddress.filterDataByKeyword(event.detail);
                            }
                        }
                    }
                }
            });
        }
    }

    handleCustomerSelected(event) {
        let customerSource = [...this.customerSource];
        let _selected = JSON.parse(event.detail);

        this.selectedCustomer = customerSource.find(
            item => item.value === _selected.value
        );
        // update by winfried on 2022.12.27
        // 控制GST/HST、QST两个字段可见性
        this.isCCA = this.selectedCustomer.isCCA;
        this.changeCurrencyCode;
        if (this.isInternal) {
            this.customerId = this.selectedCustomer.value;
            getPromotionCodeByCustomer({ customerId: this.customerId }).then(
                data => {
                    if (data && Array.isArray(data)) {
                        this.promotionCodeSource = data.map(item => {
                            return {
                                label: item.promotionCodeForExternal,
                                description: item.promotionName,
                            };
                        });
                    }
                },
                error => {
                    console.error(error);
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: this.label.CCM_Portal_Error,
                            message: error.body.message,
                            variant: 'error',
                        })
                    );
                }
            );
        }
    }
    /**
     * Allen, Comment on 2021/05/27
     * inside sales, search customer
     */
    handleCustomerSearchInputChange(event) {
        if (event.detail && event.detail.length >= 3) {
            getCustomer({ keyWord: event.detail }).then(
                result => {
                    if (result) {
                        let _customers = [];
                        _customers = result.map(item => {
                            return {
                                label: item.Name,
                                AccountNumber: item.AccountNumber
                                    ? item.AccountNumber
                                    : item.Name,
                                value: item.Id,
                                type: item.Type,
                                // update by winfried on 2022.12.27
                                // 控制GST/HST、QST两个字段可见性
                                isCCA: item.isCCA,
                            };
                        });
                        this.customerSource = _customers;
                    }
                },
                error => {
                    console.error(error);
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: this.label.CCM_Portal_Error,
                            message: error.body.message,
                            variant: 'error',
                        })
                    );
                }
            );
        } else {
            this.selectedCustomer = {};
        }
    }

    handlePromotionCodeSelected(event) {
        let _selected = JSON.parse(event.detail);
        this.promoCodeInput = _selected.label;
        this.promotionName = _selected.description;
        if (this.promoCodeInput !== this.promotionCode) {
            this.searchPromotion('in-customer');
        }
    }
    handlePromotionCodeSearchInputChange(event) {
        if (event.detail) {
            //
            // if(event.detail)
        } else {
            //
        }
    }

    handleAddressSelected(e) {
        let classList = e.currentTarget.classList;
        if(classList && classList.length > 0) {
            classList.forEach(item=>{
                if(item === 'shipToAutoCompleteAddress') {
                    let _address = this.shipToAddressSource.find(
                        item => item.value === JSON.parse(e.detail).value
                    );
                    this.shipToSelectedAddress = _address;
                }
                else if(item === 'autoCompleteAddress') {
                    let _address = this.addressSource.find(
                        item => item.value === JSON.parse(e.detail).value
                    );
                    this.selectedAddress = _address;
                    this.brand = _address.brand;
                }
            });
        }
    }

    handleChangeReimbursementType(event) {
        this.reimbursementType = event.detail.value;
    }

    handleWindowClick(event) {
        this.windowId = event.currentTarget.dataset.id;
        this.windows.forEach(item => {
            if (item.value === this.windowId) {
                item.selected = true;
            } else {
                item.selected = false;
            }
        });
        this.claimItemsThreshold.forEach(item => {
            let _product = this.productSourceThreshold.find(
                _p => _p.value === item.productId
            );
            if (_product) {
                let _windowPrice = _product.windowMSRP.find(
                    msrp => msrp.windowId === this.windowId
                );
                item.MSRP = _windowPrice ? _windowPrice.MSRP : 0;
            }
        });
        this.claimItemsOffering.forEach(item => {
            let _product = this.productSourceOffering.find(
                _p => _p.value === item.productId
            );
            if (_product) {
                let _windowPrice = _product.windowMSRP.find(
                    msrp => msrp.windowId === this.windowId
                );
                item.MSRP = _windowPrice ? _windowPrice.MSRP : 0;
            }
        });
    }

    handleCancelWindowClick(event) {
        this.windowId = event.currentTarget.dataset.id;
        this.windows.forEach(item => {
            if (item.value === this.windowId) {
                item.selected = false;
            }
        });
    }

    /**
     * Allen, Comment on 2021/05/27
     * scale sale price * quantity
     */
    handleSalePriceChange(event) {
        let _type = event.currentTarget.dataset.type;
        let _index = parseInt(event.currentTarget.dataset.index, 10);
    }

    //add by austin,Wholeorder计算TotalSales
    handleSaleSellthroughPriceBlur(event) {

        this.sumTotalSalesBeforeChanges = 0;
        this.claimItemsThreshold.forEach(item => {
            this.sumTotalSalesBeforeChanges = Number(this.sumTotalSalesBeforeChanges) + Number(isNaN(item.scaleTotalSales) ? 0 : Number(item.scaleTotalSales));
        });
        this.salePrice = parseFloat(event.currentTarget.value);
        let _type = event.currentTarget.dataset.type;
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _quantity = 0;
        if (
            _type === 'threshold' &&
            this.claimItemsThreshold[_index].quantity
        ) {
            _quantity = this.claimItemsThreshold[_index].quantity;
        }
        if (_type === 'offering' && this.claimItemsOffering[_index].quantity) {
            _quantity = this.claimItemsOffering[_index].quantity;
        }
        let _item = {
            salePrice: this.salePrice,
            scaleTotalSales: this.decimalPlaces(this.salePrice * _quantity),
        };
        if (_type === 'threshold') {
            Object.assign(this.claimItemsThreshold[_index], _item);
        }
        if (_type === 'offering') {
            Object.assign(this.claimItemsOffering[_index], _item);
        }
        this.totalSalesChange();
    }
    //end

    handleSalePriceBlur(event) {
        let _type = event.currentTarget.dataset.type;
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        this.salePrice = parseFloat(event.currentTarget.value);
        if (_type === 'offering') {
            let _selector =
                ".offeringTable c-ccm-autocomplete[data-index='" +
                _index +
                "']";
            if (!this.claimItemsOffering[_index].productId) {
                this.template.querySelector(_selector).reportValidity();
                event.currentTarget.value = '';
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.label.CCM_Portal_SelectProductFirst,
                        message: this.label.CCM_Portal_Pleaseselectproductfirst,
                        variant: 'warning',
                    })
                );
                return;
            }
        }

        if (_type === 'offering') {
            let _promoPrice = parseFloat(
                this.claimItemsOffering[_index].promoPrice
            );
            if (
                this.salePrice > _promoPrice + 1 ||
                this.salePrice < _promoPrice - 1
            ) {
                event.currentTarget.value = '';
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.label.CCM_Portal_SalePriceError,
                        message: this.label.CCM_Portal_SalePricewithin1ispermitted,
                    })
                );
            }
        }
        if (_type === 'threshold') {
            let _promoPrice = parseFloat(
                this.claimItemsThreshold[_index].promoPrice
            );
            if (
                this.salePrice > _promoPrice + 1 ||
                this.salePrice < _promoPrice - 1
            ) {
                event.currentTarget.value = '';
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.label.CCM_Portal_SalePriceError,
                        message: this.label.CCM_Portal_SalePricewithin1ispermitted,
                        variant: 'warning',
                    })
                );
            }
        }

        let _quantity = 0;
        if (
            _type === 'threshold' &&
            this.claimItemsThreshold[_index].quantity
        ) {
            _quantity = this.claimItemsThreshold[_index].quantity;
        }
        if (_type === 'offering' && this.claimItemsOffering[_index].quantity) {
            _quantity = this.claimItemsOffering[_index].quantity;
        }
        let _item = {
            salePrice: this.salePrice,
            scaleTotalSales: this.decimalPlaces(this.salePrice * _quantity),
        };
        if (_type === 'threshold') {
            Object.assign(this.claimItemsThreshold[_index], _item);
        }
        if (_type === 'offering') {
            Object.assign(this.claimItemsOffering[_index], _item);
        }
    }

    handleQuantityChange(event) {
        this.quantity = parseInt(event.currentTarget.value, 10);
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _type = event.currentTarget.dataset.type;
        let _salePrice = 0;
        if (_type === 'threshold') {
            if (this.claimItemsThreshold[_index].salePrice) {
                _salePrice = this.claimItemsThreshold[_index].salePrice;
            }
            let _item = {
                quantity: this.quantity,
                scaleTotalSales: this.decimalPlaces(_salePrice * this.quantity),
                scaleAmount: this.decimalPlaces(
                    this.quantity *
                        this.claimItemsThreshold[_index].chervonFundedAmount
                ),
            };
            Object.assign(this.claimItemsThreshold[_index], _item);
        }
        if (_type === 'offering') {
            if (this.claimItemsOffering[_index].salePrice) {
                _salePrice = this.claimItemsOffering[_index].salePrice;
            }
            let _item = {
                quantity: this.quantity,
                scaleTotalSales: this.decimalPlaces(_salePrice * this.quantity),
                scaleAmount: this.decimalPlaces(
                    this.quantity *
                        this.claimItemsOffering[_index].chervonFundedAmount
                ),
            };
            Object.assign(this.claimItemsOffering[_index], _item);
        }
        if (this.stWholeOrder) {
            this.totalSalesChange();
        }
    }

    //sum whole order Total Claim Amount logic, add by austin
    handleQuantityChangeWholeOrder(event) {
        this.sumTotalSalesBeforeChanges = 0;
        this.claimItemsThreshold.forEach(item => {
            this.sumTotalSalesBeforeChanges = Number(this.sumTotalSalesBeforeChanges) + Number(isNaN(item.scaleTotalSales) ? 0 : Number(item.scaleTotalSales));
        });
        this.quantity = parseInt(event.currentTarget.value, 10);
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _type = event.currentTarget.dataset.type;
        let _salePrice = 0;
        let sumThresholdScaleTotalSales = 0;
        //threshold product
        if (_type === 'threshold') {
            if (this.claimItemsThreshold[_index].salePrice) {
                _salePrice = this.claimItemsThreshold[_index].salePrice;
            }
            let _item = {
                quantity: this.quantity,
                scaleTotalSales: this.decimalPlaces(_salePrice * this.quantity),
            };
            Object.assign(this.claimItemsThreshold[_index], _item);
        }
        this.totalSalesChange();
    }

    /**
     * Promotion item Total sales change
     */
    totalSalesChange() {
        let sumThresholdScaleTotalSales = 0;
        let _promotionRule = [];
        let _promotionRuleBefore = [];
        let rules = JSON.parse(JSON.stringify(this.rules));
        let num = this.claimItemsThreshold.length;
        this.claimItemsThreshold.forEach(item => {
            item.scaleAmount = 0;
            sumThresholdScaleTotalSales = Number(sumThresholdScaleTotalSales) + Number(isNaN(item.scaleTotalSales) ? 0 : Number(item.scaleTotalSales));
        });
        rules.forEach(rule => {
            let mintotalorderamount = rule.thresholds[0].mintotalorderamount;
            let maxtotalorderamount = typeof(rule.thresholds[0].maxtotalorderamount) == 'undefined' ? Infinity : rule.thresholds[0].maxtotalorderamount;
            if (mintotalorderamount <= sumThresholdScaleTotalSales &&
                maxtotalorderamount > sumThresholdScaleTotalSales) {
                _promotionRule.push(rule);
            }
            if (mintotalorderamount <= this.sumTotalSalesBeforeChanges &&
                maxtotalorderamount > this.sumTotalSalesBeforeChanges) {
                _promotionRuleBefore.push(rule);
            }
        });
        if(_promotionRule.length != 0 && _promotionRuleBefore.length != 0){
            if(_promotionRule[0].ruleId != _promotionRuleBefore[0].ruleId && this.sumTotalSalesBeforeChanges != 0){
                this.claimItemsOffering = [];
            }
        }else if(_promotionRule.length != _promotionRuleBefore.length && _promotionRule.length == 0){
            this.claimItemsOffering = [];
        }

        this.sumTotalSalesBeforeChanges = sumThresholdScaleTotalSales;
        if (_promotionRule.length != 0) {
            //判断是否包含free goods
            let includeFreeGoods = false;
            let offerType = [];
            _promotionRule[0].offerings.forEach(offer => {
                if (_promotionRule.indexOf(offer.recordTypeApiName.value) == -1) {
                    offerType.push(offer.recordTypeApiName.value);
                }
            })
            if (offerType.length > 1) {
                includeFreeGoods = true;
            }
            if (includeFreeGoods) {//混合
                let chervonFunding = 0;
                let pOffering = [];
                _promotionRule[0].offerings.forEach(offer => {
                    if (offer.recordTypeApiName.value != 'Free_Goods') {
                        // chervonFunding = offer.ChervonFunding.value;
                        //YANKO ADD PERCENT CHERVON FUNDING
                        if(offer.ChervonFunding.value > 0){
                            this.totalFunding = offer.ChervonFunding.value;
                        }else{
                            this.totalFunding = this.decimalPlaces(sumThresholdScaleTotalSales * (offer.percentChervonFunding.value / 100)) - 0;
                        }
                    } else {
                        pOffering.push(offer);
                    }
                });
                //精度校准
                let thresholdFundingSum = 0;
                this.claimItemsThreshold.forEach(item => {
                    item.scaleAmount = this.decimalPlaces(this.totalFunding / num);
                    item.wholeOrderFnding = this.decimalPlaces(this.totalFunding);
                    thresholdFundingSum += this.decimalPlaces(this.totalFunding / num) - 0;
                });
                if (Number(this.totalFunding) != Number(thresholdFundingSum) && this.claimItemsThreshold.length > 0) {
                    this.claimItemsThreshold[0].scaleAmount = Number(this.claimItemsThreshold[0].scaleAmount) + Number(this.totalFunding - thresholdFundingSum) - 0;
                }
                //product显示
                let _productsOffering = [];
                let offeringProduct = [];
                // offeringProduct.push(this.promotionProduct.find(item => item.ruleId === _promotionRule[0].ruleId));
                this.promotionProduct.forEach(promProd => {
                    if(promProd.ruleId == _promotionRule[0].ruleId){
                        offeringProduct.push(promProd);
                    }
                })
                pOffering.forEach(freeGoodsOff => {
                    freeGoodsOff.products.forEach(product => {
                        let _product = offeringProduct.find(
                            item => item.value === product.value
                        );
                        if(_product){
                            _productsOffering.push(_product);
                        }
                    })
                });
                this.claimItemsOffering.forEach(claim => {
                    _productsOffering.forEach(product => {
                        if(claim.productId ===  product.value){
                            claim.chervonFundedAmount = product.chervonFundedAmount;
                            claim.scaleAmount = this.decimalPlaces(claim.quantity * claim.chervonFundedAmount);
                        }
                    })
                });
                this.productSourceOffering = _productsOffering;
                if (
                    _productsOffering.length > 0 &&
                    this.claimItemsOffering.length === 0
                ) {
                    this.claimItemsOffering.push({ lineNo: 1 });
                }
            } else {//非混合
                if (_promotionRule[0].offerings[0].recordTypeApiName.value == 'Free_Goods') {
                    //product显示
                    let _productsOffering = [];
                    let offeringProduct = [];
                    // offeringProduct.push(this.promotionProduct.find(item => item.ruleId === _promotionRule[0].ruleId));
                    this.promotionProduct.forEach(promProd => {
                        if(promProd.ruleId == _promotionRule[0].ruleId){
                            offeringProduct.push(promProd);
                        }
                    })
                    let offerings = JSON.parse(JSON.stringify(_promotionRule[0].offerings));
                    offerings.forEach(offer => {
                        offer.products.forEach(product => {
                            let _product = offeringProduct.find(
                                item => item.value === product.value
                            );
                            _productsOffering.push(_product);
                        });
                    })
                    this.claimItemsOffering.forEach(claim => {
                        _productsOffering.forEach(product => {
                            if(claim.productId ===  product.value){
                                claim.chervonFundedAmount = product.chervonFundedAmount;
                                claim.scaleAmount = this.decimalPlaces(claim.quantity * product.chervonFundedAmount);

                            }
                        })
                    });
                    this.productSourceOffering = _productsOffering;
                    if (
                        _productsOffering.length > 0 &&
                        this.claimItemsOffering.length === 0
                    ) {
                        this.claimItemsOffering.push({ lineNo: 1 });
                    }
                } else {
                    //add by yanko Chervon Funding Percent logic
                    if(_promotionRule[0].offerings[0].percentChervonFunding.value > 0){
                        this.totalfunding = this.decimalPlaces(sumThresholdScaleTotalSales * (_promotionRule[0].offerings[0].percentChervonFunding.value / 100)) - 0;
                    }else {
                        this.totalfunding = this.decimalPlaces(_promotionRule[0].offerings[0].ChervonFunding.value);
                    }
                    //yanko end
                    let _productsOffering = [];
                    let _claimItemsOffering = [];
                    this.productSourceOffering = _productsOffering;
                    this.claimItemsOffering = _claimItemsOffering;
                    //精度校准
                    let thresholdFundingSum = 0;
                    // let tfunding = this.decimalPlaces(_promotionRule[0].offerings[0].ChervonFunding.value);
                    this.claimItemsThreshold.forEach(item => {
                        item.scaleAmount = this.decimalPlaces(this.totalfunding / num);
                        item.wholeOrderFnding = this.totalfunding;
                        thresholdFundingSum = this.decimalPlaces(thresholdFundingSum + this.totalfunding / num) - 0;
                    });
                    if (this.decimalPlaces(thresholdFundingSum) != this.decimalPlaces(this.totalfunding) && this.claimItemsThreshold.length > 0) {
                        // this.claimItemsThreshold[0].scaleAmount = Number(this.claimItemsThreshold[0].scaleAmount) + Number((this.decimalPlaces(this.totalfunding) - this.decimalPlaces(thresholdFundingSum)).toFixed(2)) - 0;
                        this.claimItemsThreshold[0].scaleAmount = Number(this.claimItemsThreshold[0].scaleAmount) + Number((this.totalfunding - thresholdFundingSum).toFixed(2)) - 0;
                    }
                }
            }
        } else {
            let _productsOffering = [];
            let _claimItemsOffering = [];
            this.productSourceOffering = _productsOffering;
            this.claimItemsOffering = _claimItemsOffering;
        }
    }

    /**
     * Allen, Comment on 2021/05/27
     * validate inputs, save claim
     */
    handleClickSave(event) {
        let _validate = this.validateBeforeSave();

        if (_validate) {
            let _claimItemsThreshold = this.claimItemsThreshold.map(item => {
                return {
                    recordId: item.recordId,
                    salePrice: item.salePrice,
                    quantity: item.quantity,
                    promoPrice: item.promoPrice,
                    productId: item.productId,
                    MSRP: item.MSRP,
                    claimAmount: item.claimAmount,
                    chervonFundedAmount: item.chervonFundedAmount,
                    wholeOrderFunding: item.wholeOrderFnding,
                    stWholeOrder: this.stWholeOrder,
                    type: 'Threshold',
                    brand: item.brand,
                    scaleAmount:item.scaleAmount,
                    isBMSM: this.isBMSM,
                };
            });
            let _claimItemsOffering = this.claimItemsOffering.map(item => {
                return {
                    recordId: item.recordId,
                    salePrice: item.salePrice,
                    quantity: item.quantity,
                    promoPrice: item.promoPrice,
                    productId: item.productId,
                    MSRP: item.MSRP,
                    claimAmount: item.claimAmount,
                    chervonFundedAmount: item.chervonFundedAmount,
                    type: 'Offering',
                    brand: item.brand,
                    stWholeOrder: this.stWholeOrder,
                    isBMSM: this.isBMSM,
                };
            });
            let _json = {
                reimbursementType: this.reimbursementType,
                promotionWindowId: this.windowId,
                promotionId: this.promotionId,
                thresholdClaimItems: _claimItemsThreshold,
                offeringClaimItems: _claimItemsOffering,
                customerId: this.customerId,
                claimDescription: this.claimDescription,
                address: this.selectedAddress.value,
                shiptoAddress: this.shipToSelectedAddress.value,
                deleteClaimItemIds: this.deleteClaimItemIds,
                // update by winfried on 2022.10.21
                // 为CCA用户添加GST/HST、QST两个字段
                GSTOrHSTCurrency: this.GSTOrHSTCurrency,
                QSTCurrency: this.QSTCurrency,
                totalClaimAmountInclusive: this.totalClaimAmountInclusive,
                currencyCode: this.currencyCode,
            };
            if (this.recordId) {
                _json.recordId = this.recordId;
            }
            this.showSpinner = true;
            saveClaimRequest({ claimRequestStr: JSON.stringify(_json) }).then(
                data => {
                    this.showSpinner = false;
                    if (data.isClaimDateExpired){
                        this.toastClaimSubmissionPeriodEnded();
                    }
                    else if (data.recordId) {
                        this.dispatchEvent(
                            new ShowToastEvent({
                                title: this.label.CCM_Portal_Success,
                                message: this.label.CCM_Portal_Claimhasbeensaved,
                                variant: 'success',
                            })
                        );

                        this.currentStep = 2;
                        // eslint-disable-next-line @lwc/lwc/no-api-reassignments
                        this.recordId = data.recordId;

                        _claimItemsThreshold = [];
                        _claimItemsOffering = [];
                        data.thresholdClaimItems.forEach((claim, index) => {
                            let _item = {
                                lineNo: index + 1,
                                recordId: claim.recordId,
                                productId: claim.productId,
                                productName: claim.productName,
                                productCode: claim.productCode,
                                MSRP: claim.MSRP,
                                promoPrice: claim.promoPrice,
                                type: claim.type,
                                quantity: claim.quantity,
                                salePrice: claim.salePrice,
                                scaleTotalSales: claim.totalSales,
                                chervonFundedAmount: claim.chervonFundedAmount,
                                scaleAmount: claim.claimAmount,
                                brand: claim.brand,
                                displayMSRP:true,
                            };
                            _claimItemsThreshold.push(_item);
                        });
                        data.offeringClaimItems.forEach((claim, index) => {
                            let _item = {
                                lineNo: index + 1,
                                recordId: claim.recordId,
                                productId: claim.productId,
                                productName: claim.productName,
                                productCode: claim.productCode,
                                MSRP: claim.MSRP,
                                promoPrice: this.isPMAPP ? claim.promoPrice : 0,
                                type: claim.type,
                                quantity: claim.quantity,
                                salePrice: claim.salePrice,
                                scaleTotalSales: claim.totalSales,
                                chervonFundedAmount: claim.chervonFundedAmount,
                                scaleAmount: claim.claimAmount,
                                brand: claim.brand,
                            };
                            _claimItemsOffering.push(_item);
                        });
                        this.claimItemsThreshold = _claimItemsThreshold;
                        this.claimItemsOffering = _claimItemsOffering;
                    }
                },
                error => {
                    this.showSpinner = false;
                    console.error(error);
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: this.label.CCM_Portal_SaveFailed,
                            message: error.body.message,
                            variant: 'error',
                        })
                    );
                }
            );
        }
    }

    handleClickSaveClaim() {
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: 'Claim_List__c',
            },
        });
    }

    handleInvoiceView(event){
        let recordId = event.currentTarget.dataset.id;
        let url = '/apex/Invoice?invoiceID=' + recordId;
        window.open(url);
    }

    /**
     * Allen, Comment on 2021/05/27
     * submit claim, after submit redirect to detail page
     */
    handleClickSubmit(event) {
        if (this.uploadedFiles.length === 0) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_AttachmentRequired,
                    message: this.label.CCM_Portal_Pleaseuploadaattachment,
                    variant: 'warning',
                })
            );
            return;
        }
        this.showSpinner = true;
        submitClaimReqeust({ claimId: this.recordId }).then(
            data => {
                this.showSpinner = false;
                if (data.isClaimDateExpired){
                    this.toastClaimSubmissionPeriodEnded();
                }
                else if (data.recordId) {
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: this.label.CCM_Portal_Success,
                            message: this.label.CCM_Portal_Claimhasbeensubmited,
                            variant: 'success',
                        })
                    );
                    if (event.currentTarget) {
                        event.currentTarget.disabled = true;
                    }
                    this.status = 'Pending Review';

                    // eslint-disable-next-line @lwc/lwc/no-async-operation
                    setTimeout(() => {
                        if (this.isInternal) {
                            this[NavigationMixin.Navigate]({
                                type: 'standard__recordPage',
                                attributes: {
                                    recordId: data.recordId,
                                    objectApiName: 'Claim_Request__c',
                                    actionName: 'view',
                                },
                            });
                        } else {
                            this[NavigationMixin.Navigate]({
                                type: 'comm__namedPage',
                                attributes: {
                                    name: 'Sell_Through_Promotion_Claim__c',
                                },
                                state: {
                                    c__claimid: data.recordId,
                                },
                            });
                        }
                    }, 500);
                }
            },
            error => {
                this.showSpinner = false;
                console.error(error);
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.label.CCM_Portal_SubmitFailed,
                        message: error.body.message,
                        variant: 'error',
                    })
                );
            }
        );
    }

    handleHiddenAddLine(event) {
        this.showAddLine = false;
    }
    handleDeleteClaimItem(event) {
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _type = event.currentTarget.dataset.type;
        if (_type === 'threshold') {
            let deleteItems = this.claimItemsThreshold.splice(_index, 1);
            this.claimItemsThreshold.forEach((item, index) => {
                item.lineNo = index + 1;
            });
            if (this.recordId && deleteItems[0].recordId) {
                this.deleteClaimItemIds.push(deleteItems[0].recordId);
            }
        }
        if (_type === 'offering') {
            let deleteItems = this.claimItemsOffering.splice(_index, 1);
            this.claimItemsOffering.forEach((item, index) => {
                item.lineNo = index + 1;
            });
            if (this.recordId && deleteItems[0].recordId) {
                this.deleteClaimItemIds.push(deleteItems[0].recordId);
            }
        }
        if (this.stWholeOrder) {
            this.totalSalesChange();
        }
    }

    handleUploadFinished(event) {
        const uploadedFiles = event.detail.files;
        let documentIds = [];
        uploadedFiles.forEach(item => {
            documentIds.push(item.documentId);
            // eslint-disable-next-line no-restricted-globals
            item.previewUrl =
                'https://' +
                location.host +
                '/sfc/servlet.shepherd/version/renditionDownload?rendition=THUMB720BY480&versionId=' +
                item.contentVersionId;
        });
        this.uploadedFiles = this.uploadedFiles.concat(uploadedFiles);
        this.uploadedFiles.forEach((item, index) => {
            item.lineNo = index + 1;
        });

        this.showSpinner = true;
        insertDocumentLink({recordId: this.recordId, documentIds: documentIds}).then(result=>{
            this.showSpinner = false;
        }, error=>{
            this.showSpinner = false;
            console.log(error);
        });

        // this.getAllFiles({contentDocumentSet:this.uploadedFiles}).then(data=>{
        // })
    }

    handleDeleteFile(event) {
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        this.uploadedFiles.splice(_index, 1);
        let _docid = event.currentTarget.dataset.docid;
        deleteContentDocument({ cdId: _docid }).then(
            data => {
                this.uploadedFiles.forEach((item, index) => {
                    item.lineNo = index + 1;
                });
            },
            error => {
                console.error(error);
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.label.CCM_Portal_Error,
                        message: error.body.message,
                        variant: 'error',
                    })
                );
            }
        );
    }

    handleClickEditClaim(event) {
        this.isSetAddress = false;
        this.currentStep = 1;
    }

    handleClickNext(event) {
        this.currentStep = 2;
    }

    decimalPlaces(num) {
        // num = num.toString();
        // let index = num.indexOf('.');
        // if (index !== -1) {
        //     num = num.substring(0, 2 + index + 1);
        // } else {
        //     num = num.substring(0);
        // }
        // return parseFloat(num).toFixed(2);
        return Math.round(num * 100) / 100;
    }

    /**
     * Allen, Comment on 2021/05/27
     * validate requied, number min max
     * 2022-07-28: remove add all Promotion Rule Product verification
     */
    validateBeforeSave() {
        let _validate = true;
        if (this.isInternal) {
            if (
                !this.selectedCustomer ||
                JSON.stringify(this.selectedCustomer) === '{}'
            ) {
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.label.CCM_Portal_CustomerRequired,
                        message: this.label.CCM_Portal_Pleaseselectacustomer,
                        variant: 'warning',
                    })
                );
                _validate = false;
            }
        }
        this.template.querySelectorAll('lightning-input').forEach(item => {
            if (!item.reportValidity()) {
                _validate = false;
            }
        });
        this.template.querySelectorAll('c-ccm-autocomplete').forEach(item => {
            if (!item.reportValidity()) {
                _validate = false;
            }
        });
        this.template
            .querySelectorAll('c-ccm-lwc-search-address')
            .forEach(item => {
                if (!item.reportValidity()) {
                    _validate = false;
                }
            });
        this.template.querySelectorAll('lightning-textarea').forEach(item => {
            if (!item.reportValidity()) {
                _validate = false;
            }
        });
        if (_validate && !this.windowId) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_WindowRequired,
                    message: this.label.CCM_Portal_Pleaseselectawindow,
                    variant: 'warning',
                })
            );
            _validate = false;
        }
        if (
            _validate &&
            !this.selectedAddress &&
            JSON.stringify(this.selectedAddress) !== '{}'
        ) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_BillToAddressRequired,
                    message: this.label.CCM_Portal_PleaseSelectAaddress,
                    variant: 'warning',
                })
            );
            _validate = false;
        }

        if (
            _validate &&
            !this.shipToSelectedAddress &&
            JSON.stringify(this.shipToSelectedAddress) !== '{}'
        ) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: this.label.CCM_Portal_ShipToAddressRequired,
                    message: this.label.CCM_Portal_PleaseSelectAaddress,
                    variant: 'warning',
                })
            );
            _validate = false;
        }

        if (_validate && this.hasThresholdProduct) {
            if (this.claimItemsThreshold.length === 0) {
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.label.CCM_Portal_ThresholdProductRequired,
                        message: this.label.CCM_Portal_PleaseAddthresholdproduct,
                        variant: 'warning',
                    })
                );
                _validate = false;
            }
        }
        //add by asutin,验证whole order情况下的threshold
        if (_validate && this.stWholeOrder && this.hasWholeThreshold ) {
            if (this.claimItemsThreshold.length === 0) {
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.label.CCM_Portal_ThresholdProductRequired,
                        message: this.label.CCM_Portal_PleaseAddthresholdproduct,
                        variant: 'warning',
                    })
                );
                _validate = false;
            }
            this.claimItemsThreshold.forEach(threshold => {
                if (threshold.MSRP == null) {
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: this.label.CCM_Portal_MSRPError,
                            message: this.label.CCM_Portal_noMSRPTips,
                            variant: 'warning',
                        })
                    );
                    _validate = false;
                    return;
                }

            });
        }
        // end by asutin
        if (_validate && this.hasOfferingProduct) {
            if (this.claimItemsOffering.length === 0) {
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.label.CCM_Portal_OfferingProductRequired,
                        message: this.label.CCM_Portal_PleaseAddofferingproduct,
                        variant: 'warning',
                    })
                );
                _validate = false;
            }
        }
        // 25.2.12 Buy More Save More Validate
        if (_validate && this.isBMSM) {
            if (this.totalClaimAmount == 0) {
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.label.CCM_Portal_CheckTotalClaimAmount,
                        message: this.label.CCM_Portal_TotalClaimAmountNeedGreaterThan0,
                        variant: 'warning',
                    })
                );
                _validate = false;
            }
        }
        // 25.2.12 End

        /* validate threshold and offering product is meet the promotion rule;
         * 2022-07-28: remove add all Promotion Rule Product verification
         */
        /*if(_validate && this.hasThresholdProduct){

            for (let i = 0; i < this.claimItemsThreshold.length; i++) {
                let _product = this.claimItemsThreshold[i];
                // find this product in which rule
                for (let index = 0; index < this.rules.length; index++) {
                    const _rule = this.rules[index];
                    let _threshold = _rule.thresholds.find(item=>item.products.filter(prod=>prod.value === _product.productId).length>0);
                    if(_threshold){
                        _product.rule = _rule;
                        _product.threshold = _threshold;
                    }
                }
            }

            for (let i = 0; i < this.claimItemsOffering.length; i++) {
                let _product = this.claimItemsOffering[i];
                // find this product in which rule
                for (let index = 0; index < this.rules.length; index++) {
                    const _rule = this.rules[index];
                    let _offering = _rule.offerings.find(item=>item.products.filter(prod=>prod.value === _product.value).length>0);
                    if(_offering){
                        _product.rule = _rule;
                        _product.offering = _offering;
                    }
                }
            }

            let thresholdValidateRule = {};
            for (let index = 0; index < this.claimItemsThreshold.length; index++) {
                const _thresholdProduct = this.claimItemsThreshold[index];
                let _rule = _thresholdProduct.rule;
                if(thresholdValidateRule[_rule.ruleId]){
                    continue
                }
                let _threshold =  _thresholdProduct.threshold;
                // threshold in same rule
                let _thresholdOthers =  _rule.thresholds.filter(_th=>_th.thresholdId !== _threshold.thresholdId);
                // products In same threshold
                let _productsInOneThreshold = _threshold.products.filter(_p=>_p.value !== _thresholdProduct.productId);
                // products in other thresholds in same rule
                let _productsInOtherThresholds = [];
                _thresholdOthers.forEach(_th=>{
                    _productsInOtherThresholds = _productsInOtherThresholds.concat(_th.products);
                });
                let requiredProducts = [];
                requiredProducts = _productsInOneThreshold.concat(_productsInOtherThresholds);
                requiredProducts.push(_thresholdProduct);
                if(!thresholdValidateRule[_rule.ruleId]){
                    thresholdValidateRule[_rule.ruleId] = {};
                }
                thresholdValidateRule[_rule.ruleId].requiredProducts = requiredProducts;
            }
            let _keys = Object.keys(thresholdValidateRule);
            for (let index = 0; index < _keys.length; index++) {
                const _key = _keys[index];
                let _requiredProducts = thresholdValidateRule[_key].requiredProducts;
                let _addedProducts =  this.claimItemsThreshold.filter(_p=>_p.rule.ruleId===_key);
                if(_requiredProducts.length>_addedProducts.length){
                    let _requiredIds = _requiredProducts.map(item=>{
                        if(item.productId){
                            return item.productId;
                        }
                        return item.value;
                    });
                    let _addedIds = _addedProducts.map(item=>{
                        if(item.productId){
                            return item.productId;
                        }
                        return item.value;
                    });
                    let _subs = window._.xor(_requiredIds, _addedIds);
                    let _missedProduct = _requiredProducts.find(_p=>_p.productId === _subs[0] || _p.value === _subs[0])
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: 'Threshold Product Required',
                            message: `Threshold also need add "${_missedProduct.ProductCode}" product!`,
                            variant: 'warning'
                        })
                    );
                    _validate = false;
                    break;
                }
            }
        }*/
        return _validate;
    }

    navigateToFiles(event) {
        let _docId = event.currentTarget.dataset.docid;
        let _versionId = event.currentTarget.dataset.versionid;
        // this[NavigationMixin.Navigate]({
        //   type: 'standard__namedPage',
        //   attributes: {
        //       pageName: 'filePreview'
        //   },
        //   state : {
        //       recordIds: _docId,
        //       selectedRecordId: _docId
        //   }
        // })
        // eslint-disable-next-line no-restricted-globals
        // let url = 'https://'+location.host+'/sfc/servlet.shepherd/version/renditionDownload?rendition=THUMB720BY480&versionId='+_docId;
        // this[NavigationMixin.Navigate]({
        //     type: 'standard__webPage',
        //     attributes: {
        //         url: url
        //     }
        // }, false );
        const filePreviewEvent = new CustomEvent('filePreview', {
            detail: { docid: _docId },
        });
        // Fire the custom event
        this.dispatchEvent(filePreviewEvent);
    }

    // update by winfried on 2022.12.21
    // 增加GST/HST、QST两个字段
    handleGSTOrHSTChange(event) {
        this.GSTOrHSTCurrency = event.detail.value;
    }
    handleQSTChange(event) {
        this.QSTCurrency = event.detail.value;
    }
    toastClaimSubmissionPeriodEnded(){
        this.dispatchEvent(
            new ShowToastEvent({
                title: this.label.CCM_Portal_ClaimSubmissionPeriodEnded,
                message: this.label.CCM_Portal_ClaimSubmissionPeriodEndedTips,
                variant: 'warning',
            })
        );
    }
}