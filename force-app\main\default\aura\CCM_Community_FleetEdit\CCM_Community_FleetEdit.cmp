<!--
 - Created by eric wang on 3/9/2020
 -->
<aura:component controller="CCM_Community_FleetEditCtl" implements="forceCommunity:availableForAllPageTypes">

    <!-- attribute start -->
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="readonly" type="Boolean" default="true"/>
    <aura:attribute name="fleetClaim" type="Map" default="{}"/>

    <aura:attribute name="fleetProgramRule" type="Object"/>
    <aura:attribute name="kitId2WarrantyItemsInitMap" type="Map" description="kitId2WarrantyItemsInitMap"/>
    <aura:attribute name="kitId2MsrpMap" type="Map"/>
    <aura:attribute name="fakeIdList" type="List" default="[]" description="fakeIdList"/>
    <aura:attribute name="fleetClaimLoaded" type="Boolean" default="false" />

    <aura:attribute name="step" type="Integer" description="step"/>
    <!-- attribute end -->

    <!-- handler start -->
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <!-- handler end -->

    <lightning:spinner aura:id="spinner" class="slds-hide slds-is-fixed" alternativeText="Loading" size="medium" variant="brand"/>

    <section class="slds-p-around_x-small">
        <lightning:layout multipleRows="true" horizontalAlign="space">
            <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Fleetclaimnumber}:</label>
            </lightning:layoutItem>
            <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                {!v.fleetClaim.name}
            </lightning:layoutItem>
            <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Status}:</label>
            </lightning:layoutItem>
            <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                <aura:if isTrue="{!($Label.c.CCM_Portal_Language == 'fr')}">
                    {!v.fleetClaim.approvalStatusFrench}
                    <aura:set attribute="else">
                        {!v.fleetClaim.approvalStatus}
                    </aura:set>
                </aura:if>

            </lightning:layoutItem>
            <aura:renderIf isTrue="{! v.fleetClaim.approvalStatus == 'Approved'}">
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Ispaid}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    {!v.fleetClaim.isPaidLabel}
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <label class="slds-form-element__label">{!$Label.c.CCM_Portal_InvoiceNumber}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    {!v.fleetClaim.claimPackName}
                </lightning:layoutItem>
            </aura:renderIf>
        </lightning:layout><br/>
        <!-- Organization information start -->
        <article class="slds-card">
            <div class="slds-grid">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__body">
                        <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade">
                            <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                  title="{!$Label.c.CCM_Portal_OrganizationInformation}">
                                <span><strong>{!$Label.c.CCM_Portal_OrganizationInformation}</strong></span>
                             </span>
                        </h2>
                    </div>
                </header>
            </div>
            <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                <lightning:layout multipleRows="true" horizontalAlign="space">
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Sellingorganizationname}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        {!v.fleetClaim.channelCustomer.name}
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                        <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Registrationsource}:</label>
                    </lightning:layoutItem>
                    <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                        <!-- add haibo: french -->
                        <aura:if isTrue="{! ($Label.c.CCM_Portal_Language == 'fr')}">
                            {!$Label.c.CCM_Portal_BusinessPortal}
                            <aura:set attribute="else">
                                {!v.fleetClaim.channelCustomer.source}
                            </aura:set>
                        </aura:if>
                    </lightning:layoutItem>
                </lightning:layout>
            </div>
        </article><br/>
        <!-- Organization information end -->
        <aura:renderIf isTrue="{!v.step == 1}">
            
            <c:CCM_Community_FleetOwnerInfo aura:id="ownerInfo"
                                            fleetClaim="{!v.fleetClaim}"
                                            readonly="{!v.readonly}"/><br/>
            <c:CCM_Community_FleetSalesInfo aura:id="salesInfo"
                                            fleetClaim="{!v.fleetClaim}"
                                            fleetProgramRule="{!v.fleetProgramRule}"
                                            kitId2MsrpMap="{!v.kitId2MsrpMap}"
                                            readonly="{!v.readonly}"/><br/>
            <c:CCM_Community_FleetInvoiceInfo aura:id="invoiceInfo"
                                              fleetClaim="{!v.fleetClaim}"
                                              addressIdListStr="{!v.fleetClaim.addressIdListStr}"
                                              shipToAddressIdListStr="{!v.fleetClaim.shipToAddressIdListStr}"
                                              readonly="{!v.readonly}"/><br/>
        </aura:renderIf>

        <aura:renderIf isTrue="{!v.step == 2}">
            <c:CCM_Community_FleetProductInfo aura:id="productInfo"
                                              fleetClaim="{!v.fleetClaim}"
                                              readonly="{!v.readonly}"/><br/>
            <aura:if isTrue="{!v.fleetClaim.haveApprovalHistory}">
                <c:CCM_ApprovalHistory objectId="{!v.fleetClaim.id}" state="close"/>
            </aura:if>
        </aura:renderIf>
        <div class="slds-align_absolute-center slds-m-top_small slds-p-horizontal_x-large">
            <aura:renderIf isTrue="{!v.step == 1}">
                <lightning:button variant="brand"
                                  label="{!$Label.c.CCM_Portal_Next}"
                                  title="{!$Label.c.CCM_Portal_Next}"
                                  onclick="{!c.doNext}"/>
            </aura:renderIf>
            <aura:renderIf isTrue="{!v.step == 2}">
                <lightning:button variant="brand"
                                  label="{!$Label.c.CCM_Portal_Previous}"
                                  title="{!$Label.c.CCM_Portal_Previous}"
                                  onclick="{!c.doPrevious}"/>
            </aura:renderIf>

            <aura:renderIf isTrue="{!and(!empty(v.step), !v.readonly)}">
                <lightning:button variant="brand"
                                  label="{!$Label.c.CCM_Portal_Save}"
                                  title="{!$Label.c.CCM_Portal_Save}"
                                  onclick="{!c.doSave}"/>
                <aura:renderIf isTrue="{!v.step == 2}">
                    <lightning:button variant="brand"
                                      label="{!$Label.c.CCM_Portal_Submit}"
                                      title="{!$Label.c.CCM_Portal_Submit}"
                                      onclick="{!c.doSubmit}"/>
                </aura:renderIf>
            </aura:renderIf>
        </div>
    </section>
</aura:component>