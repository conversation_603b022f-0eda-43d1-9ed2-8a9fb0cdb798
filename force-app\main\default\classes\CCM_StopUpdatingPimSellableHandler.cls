/**
 * <AUTHOR>
 * @date 2021-07-20
 * @description This class is used to prevent updating Sellable status for PIM Products.
 */
public with sharing class CCM_StopUpdatingPimSellableHandler implements Triggers.Handler {
    public static Boolean boolToRun;
    public void handle() {
        // prettier-ignore
        system.debug(boolToRun);
        if (boolToRun == false) return;
        Map<Id, Product2> mapOld = (Map<Id, Product2>) Trigger.oldMap;
        RecordType rt = [SELECT Id FROM RecordType WHERE Name =: CCM_Constants.PRODUCT_PARTS_RECORD_TYPE_DEVELOPER_NAME];
        for (Product2 objP : (List<Product2>) Trigger.new) {
            
            if (CCM_Constants.PRODUCT_SOURCE_PIM.equalsIgnoreCase(objP.Source__c) && objP.Sellable__c != mapOld.get(objP.Id).Sellable__c && rt.Id != objP.RecordTypeId ) {
               
                objP.Sellable__c = mapOld.get(objP.Id).Sellable__c;
            }
        }
    }
}