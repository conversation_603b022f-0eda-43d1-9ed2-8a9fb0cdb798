const getConversationSummary = async (conversationId, chatbotId) => {
  // const apiUrl = 'https://chat.dv.copilot.livex.ai';
  const apiUrl = 'https://chat.copilot.livex.ai';
  // Validate required parameters
  if (!conversationId || !chatbotId) {
    throw new Error('Both conversationId and chatbotId are required');
  }

  // Construct the URL with query parameter
  const url = new URL(`${apiUrl}/api/v1/conversation-summary/${conversationId}`);
  url.searchParams.append('chatbot_id', chatbotId);

  const response = await fetch(url.toString(), {
    method: "GET",
    headers: {
      "Content-Type": "application/json"
    }
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  const jsonResponse = await response.json();
  return jsonResponse;
};

export { getConversationSummary };