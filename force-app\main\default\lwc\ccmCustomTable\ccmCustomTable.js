import LightningDatatable from 'lightning/datatable';
import customImageCell from './customImageCell.html';

export default class CcmCustomTable extends LightningDatatable {
    static customTypes = {
        customImage: {
            template: customImageCell,
            typeAttributes:['imageUrl']
        },
        customLink: {
            template: customLinkCell,
            typeAttributes:['label','url']
        }
    }
}