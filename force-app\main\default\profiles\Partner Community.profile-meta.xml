<?xml version='1.0' encoding='UTF-8'?>
<Profile xmlns="http://soap.sforce.com/2006/04/metadata">
    <classAccesses>
        <apexClass>CCM_Warranty_IOT_Process</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>CCM_Warranty_SyncToHerokuUtil</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>CCM_Warranty_UpdateExpirationDateUtil</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>CCM_Warranty_UpdateItemInsertedUtil</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>WarrantyItemTriggerHandle</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>CCM_Warranty_ReplacementUtil</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>CCM_Warranty_DataInitHandler</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>CCM_Warranty_ConnectToProductUtil</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>CCM_Warranty_Process_IOT_Queue</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>CCM_Warranty_RegistrationEmailUtil</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>CCM_WarrantyRegistrationEmail_AUS</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>CCM_WarrantyRegistrationEmail_Skil</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <fieldPermissions>
        <editable>false</editable>
        <field>Warranty__c.Source__c</field>
        <readable>true</readable>
    </fieldPermissions>
</Profile>
