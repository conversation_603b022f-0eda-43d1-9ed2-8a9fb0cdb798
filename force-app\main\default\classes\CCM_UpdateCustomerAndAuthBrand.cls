/**
 * <AUTHOR> @date 2023-02-21
 * @description: 当Customer Profile更新时,更新Credit Limit和所有Auth Brand的Payment term
 */
public with sharing class CCM_UpdateCustomerAndAuthBrand implements Triggers.Handler {
    public void handle() {

        Set<Id> customerIdSet = new Set<Id>();
        // Set<Id> accountIdSet = new Set<Id>();
        String currentYear  = String.valueOf(System.today().year());
        for (Customer_Profile__c objCP : (List<Customer_Profile__c>) Trigger.new) {
        	if(objCP.Payment_Behavior_Code__c == 'C' && currentYear == objCP.Effective_Year__c){
        		customerIdSet.add(objCP.Customer__c);
        	}
        }
        // for (Customer_Profile__c objCP : (List<Customer_Profile__c>) Trigger.new) {
        // 	if (String.isNotEmpty(objCP.Customer__c)) {
        //         accountIdSet.add(objCP.Customer__c);
        //     }
        // }


        List<Account> customerList = [SELECT Id, Credit_Limit__c FROM Account WHERE Id IN: customerIdSet];
        // List<Account> accountList = [SELECT Id,Website,Approval_Status__c,(SELECT Id,Effective_Year__c,Website__c FROM Customer_Profile__r ORDER BY Effective_Year__c DESC LIMIT 1) FROM Account WHERE Id IN :accountIdSet];
        // List<Account> accountUpdateList = new List<Account>();
        // for(Account acc : accountList){

        //         if(acc.Customer_Profile__r.size() > 0){
        //             acc.Website = acc.Customer_Profile__r[0].Website__c;
        //             accountUpdateList.add(acc);
        //         }

        // }
        for(Account acc : customerList){
        	acc.Credit_Limit__c = '1';
        }

        CCM_AccountSendApproval.isRun = false;
		update customerList;
        // if(accountUpdateList.size() > 0){
        //     update accountUpdateList;
        // }


        List<Sales_Program__c> authBrandList = [SELECT Id, Payment_Term__c FROM Sales_Program__c WHERE Customer__c IN: customerList];

		for(Sales_Program__c sp : authBrandList){
			sp.Payment_Term__c = 'NA032';
		}

		CCM_ProgramSendApproval.isRun = false;
		update authBrandList;


    }
}