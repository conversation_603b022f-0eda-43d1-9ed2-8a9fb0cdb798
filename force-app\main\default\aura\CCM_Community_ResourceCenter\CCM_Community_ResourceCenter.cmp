<aura:component access="global" implements="forceCommunity:availableForAllPageTypes"
                description="CCM_Community_ResourceCenter" controller = "CCM_PromotionAnnounceCtl">

    <aura:attribute name="selectedTab" type="String" default="one" />
    <aura:attribute name="isSecondTier" type="Boolean" default="true" />
    <aura:attribute name="isCCA" type="Boolean" default="false" />
    <aura:attribute name="bannerDocument" type="String" default=""/>

    <aura:handler action="{!c.doInit}" name="init" value="{!this}" />

    <lightning:tabset selectedTabId="{!v.selectedTab}" variant="vertical">
        <!-- contact -->
        <div class="contact">

            <aura:if isTrue="{!v.isSecondTier}">
                <p>{!$Label.c.CCM_Portal_DealerExclusiveServiceContacts}</p>
                <br/>
                <p>{!$Label.c.CCM_Portal_EGODealerSupport}</p>
                <p>844-346-1326</p>
                <p><lightning:formattedEmail value="<EMAIL>"  hideIcon="true"/></p>
                <br/>

                <p>{!$Label.c.CCM_Portal_PowerToolDealerSupport}</p>
                <p>877-340-1130</p>
                <p><lightning:formattedEmail value="<EMAIL>"  hideIcon="true"/></p>
                <br/>

                <p>{!$Label.c.CCM_Portal_PartsHelpdesk}</p>
                <p>877-320-1469</p>
                <p><lightning:formattedEmail value="<EMAIL>"  hideIcon="true"/></p>
                <aura:set attribute="else">
                    <p><b>{!$Label.c.CCM_Portal_ContactUs}</b></p>
                    <br/>
                    <p>{!$Label.c.CCM_Portal_PartsHelpdesk}</p>
                    <p>877-320-1469</p>
                    <p><lightning:formattedEmail value="<EMAIL>"  hideIcon="true"/></p>
                    <br/>

                    <p>{!$Label.c.CCM_Portal_InsideSales}-</p>
                    <p>888-810-1816</p>
                    <p><lightning:formattedEmail value="<EMAIL>"  hideIcon="true"/></p>
                    <br/>
                </aura:set>
            </aura:if>

            <br/>
            <br/>
            <br/>
        </div>

        <!-- User Guidence-->
        <lightning:tab label="{!$Label.c.CCM_Portal_UserGuidance}" id="one">
            <div class="slds-m-bottom_small">
                <a href="javascript:void(0);" onclick="{!c.openImageDocument}"> 
                    <img src="{!$Resource.ServiceImage}"/>
                </a>
            </div>
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                          title="{!$Label.c.CCM_Portal_UserGuidance}">
                                            <span><strong>{!$Label.c.CCM_Portal_UserGuidance}</strong></span>
                                     </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <c:CCM_Community_UserGuidenceList />
                </div>
            </article>

        </lightning:tab>
        <!-- Knowledge-->
        <lightning:tab label="{!$Label.c.CCM_Portal_KnowledgeBase}" id="KnowledgeBase">
            <div class="slds-m-bottom_small">
                <a href="javascript:void(0);" onclick="{!c.openImageDocument}"> 
                    <img src="{!$Resource.ServiceImage}"/>
                </a>
            </div>
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                          title="{!$Label.c.CCM_Portal_KnowledgeBase}">
                                            <span><strong>{!$Label.c.CCM_Portal_KnowledgeBase}</strong></span>
                                     </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <c:CCM_Community_KnowledgeList />
                </div>
            </article>
        </lightning:tab>

        <aura:if isTrue="{!v.isCCA}">
          <!-- Hand Tag-->
          <lightning:tab label="{!$Label.c.CCM_Portal_HandTag}" id="handTag">
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                          title="Hand Tag">
                                            <span><strong>{!$Label.c.CCM_Portal_HandTag}</strong></span>
                                     </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                    <c:CCM_Community_HandTagList/>
            </div>
            </article>
        </lightning:tab>
        </aura:if>

        <!-- Knowledge-->
        <lightning:tab label="{!$Label.c.CCM_Portal_SafetyRecallNotice}" id="SafetyRecallNotice">
            <div class="slds-m-bottom_small">
                <a href="javascript:void(0);" onclick="{!c.openImageDocument}"> 
                    <img src="{!$Resource.ServiceImage}"/>
                </a>
            </div>
            <article class="slds-card">
                <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                          title="{!$Label.c.CCM_Portal_SafetyRecallNotice}">
                                            <span><strong>{!$Label.c.CCM_Portal_SafetyRecallNotice}</strong></span>
                                     </span>
                            </h2>
                        </div>
                    </header>
                </div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                   <c:CCM_Community_SafetyRecallNotice />
            </div>
            </article>
        </lightning:tab>


    </lightning:tabset>


</aura:component>