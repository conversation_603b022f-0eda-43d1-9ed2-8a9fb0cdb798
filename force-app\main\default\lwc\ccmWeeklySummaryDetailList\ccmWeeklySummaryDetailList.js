import { LightningElement } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import getSummaryList from '@salesforce/apex/CCM_WeeklySummaryCtrl.getSummaryList';
import isSalesDirector from '@salesforce/apex/CCM_WeeklySummaryCtrl.getRecordData';

export default class CcmWeeklySummaryDetailList extends NavigationMixin(LightningElement) {

    data;
    originalData;
    sortedBy;
    sortDirection = 'asc';
    isSalesDirector = true;

    connectedCallback() {
        getSummaryList({'limit': ''}).then(result => {
            if(result) {
                let resultJson = JSON.parse(result);
                this.originalData = resultJson.weeklySummarys;
                this.data = [...this.originalData];
            }
        });

        isSalesDirector({'recordId': ''}).then(result=>{
            if(result) {
                let resultJson = JSON.parse(result);
                this.isSalesDirector = resultJson.isSalesDirector;
            }
        });
    }



    handleSort(event) {
        const fieldName = event.currentTarget.dataset.field;

        // Toggle sort direction if clicking the same field
        if (this.sortedBy === fieldName) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortDirection = 'asc';
            this.sortedBy = fieldName;
        }

        // Sort the data
        const cloneData = [...this.data];
        cloneData.sort(this.sortBy(fieldName, this.sortDirection === 'asc' ? 1 : -1));
        this.data = cloneData;

        // Update sort icons
        this.updateSortIcons();
    }

    sortBy(field, reverse) {
        return function (a, b) {
            let aVal = a[field] || '';
            let bVal = b[field] || '';

            // Convert to string for comparison
            aVal = aVal.toString().toLowerCase();
            bVal = bVal.toString().toLowerCase();

            return reverse * ((aVal > bVal) - (bVal > aVal));
        };
    }

    updateSortIcons() {
        // Hide all sort icons first
        const allIcons = this.template.querySelectorAll('.sort-icon');
        allIcons.forEach(icon => {
            icon.style.display = 'none';
        });

        // Show the appropriate icon for the sorted column
        if (this.sortedBy) {
            const sortedHeader = this.template.querySelector(`[data-field="${this.sortedBy}"]`);
            if (sortedHeader) {
                const iconToShow = sortedHeader.querySelector(`[data-direction="${this.sortDirection}"]`);
                if (iconToShow) {
                    iconToShow.style.display = 'inline';
                }
            }
        }
    }

    handleIconClick(event) {
        const actionType = event.target.dataset.action;
        const recordId = event.target.dataset.recordid;

        switch (actionType) {
            case 'view':
                this.directToDetailPageView(recordId);
                break;
            case 'edit':
                this.directToDetailPageEdit(recordId);
                break;
            default:
                break;
        }
    }

    directToDetailPageView(recordId) {
        let url = window.location.origin + '/lightning/n/Weekly_Summary?c__recordId=' + recordId + '&c__mode=view';
        window.open(url);
    }

    directToDetailPageEdit(recordId) {
        let url = window.location.origin + '/lightning/n/Weekly_Summary?c__recordId=' + recordId + '&c__mode=edit';
        window.open(url);
    }

    directToDetailPageNew() {
        let url = window.location.origin + '/lightning/n/Weekly_Summary?c__mode=edit';
        window.open(url);
    }
}