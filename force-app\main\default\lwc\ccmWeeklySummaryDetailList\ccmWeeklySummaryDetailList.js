import { LightningElement } from 'lwc';
import getSummaryList from '@salesforce/apex/CCM_WeeklySummaryCtrl.getSummaryList';

export default class CcmWeeklySummaryDetailList extends LightningElement {

    columns = [
        { label: 'Name', fieldName: 'yearWeek', sortable: true, cellAttributes: { alignment: 'left' }},
        {
            label: 'Created By',
            fieldName: 'createdBy',
            type: 'text',
            sortable: true,
            cellAttributes: { alignment: 'left' },
        },
        { 
            label: 'Region', 
            fieldName: 'region', 
            type: 'text', 
            sortable: true, 
            cellAttributes: { alignment: 'left' }},
        { label: 'Year', fieldName: 'year', type: 'text', sortable: true, cellAttributes: { alignment: 'left' } },
        { label: 'Month', fieldName: 'month', type: 'text', sortable: true, cellAttributes: { alignment: 'left' } },
        { label: 'Week', fieldName: 'week', type: 'text', sortable: true, cellAttributes: { alignment: 'left' } },
        { label: 'Weekly Highlights', fieldName: 'weeklyHighlights', cellAttributes: { alignment: 'left' } },
        { label: 'Competitive Info', fieldName: 'competitiveInfo', cellAttributes: { alignment: 'left' } },
        { label: 'Other', fieldName: 'other', cellAttributes: { alignment: 'left' } },
        { label: 'Next Week\'s Focus', fieldName: 'nextWeekFocus', cellAttributes: { alignment: 'left' } },
    ];

    data;

    defaultSortDirection = 'asc';
    sortDirection = 'asc';
    sortedBy;

    connectedCallback() {
        getSummaryList({'limit': ''}).then(result => {
            if(result) {
                let resultJson = JSON.parse(result);
                this.data = resultJson.weeklySummarys;
            }
        });
    }

    sortBy(field, reverse, primer) {
        const key = primer
            ? function (x) {
                  return primer(x[field]);
              }
            : function (x) {
                  return x[field];
              };

        return function (a, b) {
            a = key(a);
            b = key(b);
            return reverse * ((a > b) - (b > a));
        };
    }

    onHandleSort(event) {
        const { fieldName: sortedBy, sortDirection } = event.detail;
        const cloneData = [...this.data];

        cloneData.sort(this.sortBy(sortedBy, sortDirection === 'asc' ? 1 : -1));
        this.data = cloneData;
        this.sortDirection = sortDirection;
        this.sortedBy = sortedBy;
    }
}