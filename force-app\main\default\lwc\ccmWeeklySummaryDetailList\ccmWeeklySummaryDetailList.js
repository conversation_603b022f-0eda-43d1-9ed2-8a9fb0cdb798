import { LightningElement } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import getSummaryList from '@salesforce/apex/CCM_WeeklySummaryCtrl.getSummaryList';

export default class CcmWeeklySummaryDetailList extends NavigationMixin(LightningElement) {

    columns = [
        {
            label: 'View',
            type: 'button-icon',
            typeAttributes: {
                iconName: 'action:preview',
                name: 'view',
                title: 'View',
                variant: 'bare',
                alternativeText: 'View',
                disabled: { fieldName: 'disableView' }
            },
            cellAttributes: { alignment: 'center' }
        },
        {
            label: 'Edit',
            type: 'button-icon',
            typeAttributes: {
                iconName: 'action:new_note',
                name: 'edit',
                title: 'Edit',
                variant: 'bare',
                alternativeText: 'Edit',
                disabled: { fieldName: 'disableEdit' }
            },
            cellAttributes: { alignment: 'center' }
        },
        { label: 'Name', fieldName: 'yearWeek', sortable: true, cellAttributes: { alignment: 'left' }},
        {
            label: 'Created By',
            fieldName: 'createdBy',
            type: 'text',
            sortable: true,
            cellAttributes: { alignment: 'left' },
        },
        {
            label: 'Region',
            fieldName: 'region',
            type: 'text',
            sortable: true,
            cellAttributes: { alignment: 'left' }},
        { label: 'Year', fieldName: 'year', type: 'text', sortable: true, cellAttributes: { alignment: 'left' } },
        { label: 'Month', fieldName: 'month', type: 'text', sortable: true, cellAttributes: { alignment: 'left' } },
        { label: 'Week', fieldName: 'week', type: 'text', sortable: true, cellAttributes: { alignment: 'left' } },
        { label: 'Weekly Highlights', fieldName: 'weeklyHighlights', cellAttributes: { alignment: 'left' } },
        { label: 'Competitive Info', fieldName: 'competitiveInfo', cellAttributes: { alignment: 'left' } },
        { label: 'Other', fieldName: 'other', cellAttributes: { alignment: 'left' } },
        { label: 'Next Week\'s Focus', fieldName: 'nextWeekFocus', cellAttributes: { alignment: 'left' } },
    ];

    data;

    defaultSortDirection = 'asc';
    sortDirection = 'asc';
    sortedBy;

    connectedCallback() {
        getSummaryList({'limit': ''}).then(result => {
            if(result) {
                let resultJson = JSON.parse(result);
                // Process data to add disabled properties for buttons
                this.data = resultJson.weeklySummarys.map(row => ({
                    ...row,
                    disableView: !row.showView,
                    disableEdit: !row.showEdit
                }));
            }
        });
    }



    sortBy(field, reverse, primer) {
        const key = primer
            ? function (x) {
                  return primer(x[field]);
              }
            : function (x) {
                  return x[field];
              };

        return function (a, b) {
            a = key(a);
            b = key(b);
            return reverse * ((a > b) - (b > a));
        };
    }

    onHandleSort(event) {
        const { fieldName: sortedBy, sortDirection } = event.detail;
        const cloneData = [...this.data];

        cloneData.sort(this.sortBy(sortedBy, sortDirection === 'asc' ? 1 : -1));
        this.data = cloneData;
        this.sortDirection = sortDirection;
        this.sortedBy = sortedBy;
    }

    handleCellChange(event) {
        const actionName = event.detail.action.name;
        const row = event.detail.row;

        switch (actionName) {
            case 'view':
                this.directToDetailPageView(row.id);
                break;
            case 'edit':
                this.directToDetailPageEdit(row.id);
                break;
            default:
                break;
        }
    }

    directToDetailPageView(recordId) {
        let url = window.location.origin + '/lightning/n/Weekly_Summary?c__recordId=' + recordId + '&c__mode=view';
        window.open(url);
    }

    directToDetailPageEdit(recordId) {
        let url = window.location.origin + '/lightning/n/Weekly_Summary?c__recordId=' + recordId + '&c__mode=edit';
        window.open(url);
    }
}