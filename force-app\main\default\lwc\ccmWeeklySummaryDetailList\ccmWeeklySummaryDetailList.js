import { LightningElement } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import getSummaryList from '@salesforce/apex/CCM_WeeklySummaryCtrl.getSummaryList';

export default class CcmWeeklySummaryDetailList extends NavigationMixin(LightningElement) {

    data;

    connectedCallback() {
        getSummaryList({'limit': ''}).then(result => {
            if(result) {
                let resultJson = JSON.parse(result);
                this.data = resultJson.weeklySummarys;
            }
        });
    }



    handleIconClick(event) {
        const actionType = event.target.dataset.action;
        const recordId = event.target.dataset.recordid;

        switch (actionType) {
            case 'view':
                this.directToDetailPageView(recordId);
                break;
            case 'edit':
                this.directToDetailPageEdit(recordId);
                break;
            default:
                break;
        }
    }

    directToDetailPageView(recordId) {
        let url = window.location.origin + '/lightning/n/Weekly_Summary?c__recordId=' + recordId + '&c__mode=view';
        window.open(url);
    }

    directToDetailPageEdit(recordId) {
        let url = window.location.origin + '/lightning/n/Weekly_Summary?c__recordId=' + recordId + '&c__mode=edit';
        window.open(url);
    }
}