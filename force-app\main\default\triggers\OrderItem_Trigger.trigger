trigger OrderItem_Trigger on Order_Item__c(before insert, after insert, before update, after update, before delete, after delete) {
    new Triggers()
    .bind(Triggers.Evt.beforeinsert, new CCM_OrderItemBeforeHandler())
    .bind(Triggers.Evt.beforeupdate, new CCM_OrderItemBeforeHandler())
    .bind(Triggers.Evt.beforeinsert, new CCM_ShareOrderItemHandler())
    .bind(Triggers.Evt.beforeinsert, new CCM_OrderItemASGHandler())
    .bind(Triggers.Evt.beforeinsert, new CCM_ShareOrderItemToAddressOwner())
    .bind(Triggers.Evt.afterinsert, new CCM_ShareOrderHandler())
    .bind(Triggers.Evt.afterdelete, new CCM_ShareOrderItemDelHandler())
    .bind(Triggers.Evt.afterinsert, new CCM_PromotionActualQuantity())
    .bind(Triggers.Evt.afterupdate, new CCM_PromotionActualQuantity())
    .bind(Triggers.Evt.afterInsert, new CCM_UpdateCustomerOrderCheckbox())
    .bind(Triggers.Evt.afterinsert, new CCM_GetSGForOrderItemHandler())
    .bind(Triggers.Evt.afterinsert, new CCM_CalculateOrderDiscountAmount())
    .bind(Triggers.Evt.afterinsert, new CCM_CalculateMerchandisingAmount())

    .bind(Triggers.Evt.afterinsert, new CCM_CalculateProfileReturnShareHandler())
    .bind(Triggers.Evt.afterdelete, new CCM_CalculateProfileReturnShareHandler())
    .bind(Triggers.Evt.afterupdate, new CCM_CalculateProfileReturnShareHandler())
    .bind(Triggers.Evt.afterinsert, new CCM_UpdateBrandSeriesOnOrderHandler())
    .bind(Triggers.Evt.afterupdate, new CCM_UpdateBrandSeriesOnOrderHandler())
    .manage();
}