/**
 * Created by gluo006 on 9/2/2019.
 */
({
    doInit: function(component, event, helper){

        var selectedTabId = helper.getUrlParameter('selectedTab');
        if(selectedTabId){
            component.set("v.selectedTab", selectedTabId);
        }

        var action = component.get('c.getUserProfile');
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state == 'SUCCESS') {
                var result = response.getReturnValue();
                //二级返回false
                component.set('v.isSecondTier', result.isSecondTier);
                component.set('v.isCCA', result.isCCA);
            }
        })
        $A.enqueueAction(action);
        helper.getBannerDocument(component);
    },
    openImageDocument : function(component, event, helper){
        var recordId = component.get('v.bannerDocument');
        $A.get('e.lightning:openFiles').fire({
            recordIds: [recordId]
        });
    }
})