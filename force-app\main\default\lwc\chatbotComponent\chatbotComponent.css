.liveXAgentAssistantContainer {
    height: 65vh;
    width: 100%;
    position: relative;
    overflow: hidden;
    border: none;
}

/* Ensure the chatbot iframe (if any) fills the container */
.liveXAgentAssistantContainer iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Responsive adjustments for different screen sizes */
@media (max-width: 768px) {
    .liveXAgentAssistantContainer {
        height: 50vh; /* Smaller height on mobile devices */
    }
}

@font-face {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 300;
    src: url(https://fonts.gstatic.com/s/poppins/v20/pxiByp8kv8JHgFVrLDz8Z1xlFd2JQEk.woff2) format('woff2');
}
@font-face {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/poppins/v20/pxiEyp8kv8JHgFVrJJfecnFHGPc.woff2) format('woff2');
}
@font-face {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 500;
    src: url(https://fonts.gstatic.com/s/poppins/v20/pxiByp8kv8JHgFVrLGT9Z1xlFd2JQEk.woff2) format('woff2');
}

/* Your other component styles here */