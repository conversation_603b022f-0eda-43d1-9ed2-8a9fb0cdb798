/**
 * Created by ali375 on 2019/5/13.
 */
({
    doInit: function (component, event, helper) {
        component.set('v.isEdit', component.get('v.pageReference').attributes.actionName.toUpperCase() == 'EDIT');
        component.set('v.recordTypeId', component.get('v.pageReference').state.recordTypeId);
        if (!component.get('v.isEdit')) {
            helper.init(component);
            helper.GenratePRI(component);
            //helper.reloadData(component);
        } else {
            helper.reloadData(component);
            helper.GenratePRI(component);

        }
    },
    checkProductReturnLabel: function(component, event, helper){
        var productId = component.get('v.productObj').Id;
        var recordTypeName = component.get('v.recordType');
        if(productId &&  recordTypeName != 'Recall'){
            helper.checkReturnLabel(component);
        }
    },
    getRecommendation: function(component, event, helper){
        var recordTypeName = component.get('v.recordType');
        if(!component.get('v.isChannel') && recordTypeName != 'Recall'){
            var productId = component.get('v.productObj').Id;
            if(!productId){
                return;
            }else{
                var isST1510TSN = component.get('v.isST1510TSN');
                if(isST1510TSN == false){
                    helper.getRecommendation(component);
                }
                helper.checkReturnLabel(component);
                helper.GenratePRI(component);
                helper.GenerateSIT(component);
                helper.getRelatedTopIssue(component);
                helper.prePopulateExternalNotes(component);
            }
        }
    },
    handleTopIssueChange: function(component) {
        let topIssueSelected = component.get('v.topIssueSelected');
        let topIssueTemplateMap = component.get('v.topIssueTemplateMap');
        let template = '';
        for (const [key, value] of Object.entries(topIssueTemplateMap)) {
            if(key === topIssueSelected) {
                template = value;
            }
        }
        component.set('v.topIssueTemplate', template);
    },
    addTemplateToDescription: function(component) {
        let description = component.find('Description').get('v.value');
        let topIssueTemplate = component.get('v.topIssueTemplate');
        if(description) {
            description = description + '\n' + topIssueTemplate;
        }
        else {
            description = topIssueTemplate;
        }
        component.set('v.targetFields.Description', description);
    },
    onRecordDataUpdate: function (component, event, helper) {
        //TO DO

    },
    onClickCancel: function (component, event, helper) {
        if (component.get('v.isEdit')) {
            helper.navigateToRecord(component.get('v.recordId'));
            helper.closeActiveTab(component);
        } else {
            var ws = JSON.parse(JSON.stringify(component.get('v.pageReference'))).state.ws;
            if(ws && ws.indexOf('Case') < 0){
                var recordId = ws.split('/')[4];
                helper.navigateToRecord(recordId);
                helper.closeActiveTab(component);
            }else{
                helper.closeActiveTab(component);
            }
        }
    },
    onClickSave: function (component, event, helper) {
        helper.showEle(component, 'spinner');
        if(component.get('v.isCanSaveWithSIT')){
            $A.get("e.force:showToast").setParams({
                "title": "Warning",
                "message": "This SIT code cannot be used when length of ownership greater than 90 days",
                "type": "warning"
            }).fire();
            helper.hideEle(component, 'spinner');
            return;
        }
        var subject = component.find('subject').get('v.value');
        if(!component.get('v.isChannel')){
            var origin = component.find('origin').get('v.value');

            // var brand = component.find('brand').get('v.value');
            var country = component.find('country').get('v.value');


            var priority = component.find('priority').get('v.value');
            var status = component.find('status').get('v.value');
            var FED_EX__c = component.find('FED_EX__c').get('v.value');
        }else{
            var status = component.find('status2').get('v.value');
            var FED_EX__c = component.find('FED_EX2__c').get('v.value');
            var serialNumber = component.find('Serial_Number__c').get('v.value');
            var handleTime = component.find('Handle_Time__c').get('v.value');
            var representative = component.find('Representative__c').get('v.value');
        }


        //        var openTime = component.find('open-time').get('v.value');

        var recordTypeId = component.get('v.recordTypeId');

        switch (component.get('v.recordType')) {
            case 'General':

                if(!component.get('v.isChannel')){
                    var externalNote = null;
                    //console.log('external note ++++++ ' + externalNote);
                    var caseType = component.find('case-type').get('v.value');
                    if(!caseType) {
                        caseType = component.get('v.targetFields.Case_Type__c');
                    }
                    if(caseType == 'Service Referral'){
                        externalNote = component.find('external-note').get('v.value');
                        component.set('v.targetFields.External_Note__c', externalNote);
                    }
                    var specialTracking = component.find('special-tracking').get('v.value');
                    // if(!specialTracking) {
                    //     specialTracking = component.get('v.specialTrackingValue');
                    // }
                    var returnLabel = component.find('return-label').get('v.value');

                    // var relatedIssue = component.get('v.dualBoxValue').join(';');
                    var relatedIssue = component.get('v.selectedGenreList');
                }else{
                    var caseType = component.find('case-type2').get('v.value');
                }
                var productObj = JSON.parse(JSON.stringify(component.get('v.productObj')));
                var productId;
                if (productObj && productObj.Id) {
                    productId = productObj.Id;
                } else {
                    productId = null;
                }
                if(!component.get('v.isChannel')){
                    var warrantyNumberObj = JSON.parse(JSON.stringify(component.get('v.warrantyNumberObj')));
                    var warrantyNumberId;
                    if (warrantyNumberObj && warrantyNumberObj.Id) {
                        warrantyNumberId = warrantyNumberObj.Id;
                    } else {
                        warrantyNumberId = null;
                    }
                    component.set('v.targetFields.Warranty__c', warrantyNumberId);
                    component.set('v.targetFields.Special_Issue_Tracking__c', specialTracking === '-- None --' ? '': specialTracking);
                    component.set('v.specialTrackingValue', specialTracking);
                    component.set('v.targetFields.Return_Label__c', returnLabel);
                    component.set('v.targetFields.Product_Related_Issue__c', relatedIssue);
                    component.set("v.productRelatedIssues", relatedIssue);
                    let topIssueSelected = component.get('v.topIssueSelected');
                    component.set('v.targetFields.Top_Issue_Template__c', topIssueSelected);
                }
                component.set('v.targetFields.Case_Type__c', caseType);
                component.set('v.targetFields.ProductId', productId);


                break;
            case 'Recall':
                var recallSolution = component.get('v.recallSolution') || '';
                //                var projectCustomer = component.find('project-customer').get('v.value');
                //                var warrantyItem = component.find('warranty-item').get('v.value');
                var warrantyItemObj = JSON.parse(JSON.stringify(component.get('v.warrantyItemObj')));
                var warrantyItemId;
                if (warrantyItemObj && warrantyItemObj.Id) {
                    warrantyItemId = warrantyItemObj.Id;
                } else {
                    warrantyItemId = null;
                }
                var warrantyNumberObj = JSON.parse(JSON.stringify(component.get('v.warrantyNumberObj')));
                var warrantyNumberId;
                if (warrantyNumberObj && warrantyNumberObj.Id) {
                    warrantyNumberId = warrantyNumberObj.Id;
                } else {
                    warrantyNumberId = null;
                }
                component.set('v.targetFields.Warranty__c', warrantyNumberId);
                var externalNote = component.find('external-note').get('v.value');
                component.set('v.targetFields.External_Note__c', externalNote);
                var customerProjectObj = JSON.parse(JSON.stringify(component.get('v.customerProjectObj')));
                var customerProjectId;
                if (customerProjectObj && customerProjectObj.Id) {
                    customerProjectId = customerProjectObj.Id;
                } else {
                    customerProjectId = null;
                }

                //                var actualSolution = component.find('actual-solution').get('v.value');
                //                var recallStatus = component.find('recall-status').get('v.value');
                var returnLabelNo = component.find('return-label-number').get('v.value');
                component.set('v.targetFields.Recall_Solution__c', recallSolution);
                //                component.set('v.targetFields.Recall_Status__c', recallStatus);
                component.set('v.targetFields.Return_Lable_No__c', returnLabelNo);
                component.set('v.targetFields.Actual_Solution__c', component.get('v.actualSolution'));
                component.set('v.targetFields.Project_Customer__c', customerProjectId);
                component.set('v.targetFields.Warranty_Item__c', warrantyItemId);

                break;
        }

        component.set('v.targetFields.Fedex_Link__c', '<a href="https://www.fedex.com/apps/fedextrack/?action=track&trackingnumber=' + FED_EX__c + '&cntry_code=us&locale=en_US">' + FED_EX__c + '</a>');
        component.set('v.targetFields.Subject', subject);
        component.set('v.targetFields.Origin', origin);
        component.set('v.targetFields.Serial_Number__c', serialNumber);
        component.set('v.targetFields.Handle_Time__c', handleTime);
        component.set('v.targetFields.Representative__c', representative);
        component.set('v.targetFields.Country__c', country);
        component.set('v.targetFields.Priority', priority);
        component.set('v.targetFields.Status', status);
        //        component.set('v.targetFields.Open_Time__c', openTime);
        component.set('v.targetFields.recordTypeId', recordTypeId);

        if(!component.get('v.isChannel')){
            var attachmentURL = component.find('AttachmentURL__c').get('v.value');
            component.set('v.targetFields.AttachmentURL__c', attachmentURL);
        }

        var isValid = helper.getValidation(component);
        if (isValid) {
            if(!component.get('v.returnLabelAndProductValid')){
                helper.showEle(component, 'returnLabelModal')
                return;
            }
            //loading
            // helper.showEle(component, 'spinner');
            helper.performValidation(component);

        } else {
            //alert to show required fields
            $A.get("e.force:showToast").setParams({
                "title": "Error",
                "message": "Cannot save the data, please review error fields",
                "type": "error"
            }).fire();
            helper.hideEle(component, 'spinner');
        }


    },

    onAccountChange: function (component, event, helper) {
        if (component.get('v.recordType') === 'General') {
            var accountId = component.find('account-id').get('v.value');
            if (accountId) {
                helper.getAccountRecordTypeById(component, accountId);
            } else {
                component.set('v.brandName', 'EGO');
                component.find('brand').set('v.value', 'EGO');
            }
        }
    },
    productRequiredFieldChange: function (component, event, helper) {
        if(!component.get('v.isChannel')){
            var caseType = component.find('case-type').get('v.value');
            var brand = component.find('brand').get('v.value');
            var target = component.get('v.targetFields');
            var recordTypeName = component.get('v.recordType');
            target = JSON.parse(JSON.stringify(target));

            if (target) {
                component.set('v.targetFields.Case_Type__c', caseType);
                component.set('v.targetFields.Brand_Name__c', brand);
            }
            if(caseType == 'Warranty Order'){
                component.set('v.isProductNeeded', true);
                component.set('v.isWarrantyNeeded', true);
                if(!component.get('v.warrantyNumberObj').Id){
                    $A.get("e.force:showToast").setParams({
                        "title": "Error",
                        "message": "Please choose a Warranty",
                        "type": "error"
                    }).fire();
                }
                if(brand && brand != 'ALL' && !component.find('special-tracking').get('v.value')){
                    component.set('v.isSpecialTrackingNeeded', true);
                }
                if(!brand || brand == 'ALL'){
                    component.set('v.isSpecialTrackingNeeded', false);
                }

            }else if(caseType == 'Non-warranty Order'){
                component.set('v.isWarrantyNeeded', false);
                component.set('v.isProductNeeded', true);
                component.set('v.isSpecialTrackingNeeded', false);
            }else{
                component.set('v.isWarrantyNeeded', false);
                component.set('v.isProductNeeded', false);
                component.set('v.isSpecialTrackingNeeded', false);

            }
            if(caseType == 'Service Referral' || recordTypeName == 'Recall'){
                component.set('v.isExternalNoteNeeded', true);
                if(caseType == 'Service Referral') {
                    let preExternalNote = component.get('v.preExternalNote');
                    if(preExternalNote) {
                        component.find('external-note').set('v.value', preExternalNote);
                    }
                }
            }else{
                component.set('v.isExternalNoteNeeded', false);

            }
        }
        helper.checkSITWithPurchaseData(component);
    },
    actualSolutionChange: function (component, event, helper) {
        if(!component.get('v.isChannel')){

            var recordTypeName = component.get('v.recordType');
            var actualSolution = component.get('v.actualSolution');

            if(recordTypeName == 'Recall' && actualSolution == 'Repair'){
                component.set('v.isExternalNoteNeeded', true);
            }else{
                component.set('v.isExternalNoteNeeded', false);

            }
        }
    },
    onProjectCustomerChange: function (component, event, helper) {
        if(!component.get('v.isChannel') && component.get('v.recordType') != 'General'){
            var projectCustomerId = event.getParam('projectCustomerId');

            if (projectCustomerId) {
                helper.getRecallInformation(component, projectCustomerId);
            }
            var warrantyNumberId = event.getParam('warrantyNumberId');
            if(warrantyNumberId){
                helper.getBrandByWarranty(component);
            }
            if(component.get('v.recordType') == 'Recall' && component.get('v.targetFields.AccountId') != null){
                return;
            }
            helper.init(component);
        }
        helper.checkSITWithPurchaseData(component);
        helper.iSST1510TSN(component);
    },
    openSafeConcernWindow:function(component, event, helper){
        helper.showEle(component, 'checkModal');
        helper.hideEle(component, 'recordForms');
    },
    openRefusedServiceWindow: function (component, event, helper) {
        var isChecked = component.get('v.targetFields.Customer_Was_Refused_Service_From_Provid__c');
        if (!isChecked) {
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": "Reminder",
                "message": "be sure to list refusal location in your case description",
                "type": "success",
                "mode": "sticky"
            }).fire();
        }else{
            component.set("v.targetFields.Leader_Comments__c", '');
        }
    },
    closeSafeConcernWindow:function(component, event, helper){
        component.set('v.targetFields.Recall__c', !component.get('v.targetFields.Recall__c'))
        helper.hideEle(component, 'checkModal');
        helper.showEle(component, 'recordForms');
    },
    confirmSafeConcernWindow:function(component, event, helper){
        helper.hideEle(component, 'checkModal');
        helper.showEle(component, 'recordForms');
    },
    closeReturnLabelWindow:function(component, event, helper){
        helper.hideEle(component, 'returnLabelModal');
        helper.showEle(component, 'recordForms');
    },
    confirmReturnLabelWindow:function(component, event, helper){
        helper.hideEle(component, 'returnLabelModal');
        helper.showEle(component, 'recordForms');
    },
    searchKnowledge1 : function(component, event, helper){
        alert();
    },
    searchKnowledge :function(component, event, helper){
        console.log("Search initiated..."+JSON.stringify(component.get("v.productObj")));
        var appEvent = $A.get("e.c:KR_KnowledgeSearchEvent");
        var PRI = null;
        if(component.find("related-issue") != null){
            PRI = component.find("related-issue").get("v.value");
        }
        appEvent.setParams({
            "brandName" : component.get("v.targetFields.Brand_Name__c"),
            "ProductId" : component.get("v.productObj").Id,
            "PRI" : PRI,
            "description" : component.get("v.targetFields.Description"),
            "safelyConcern" : component.get("v.targetFields.Recall__c"),
            "type":"normal"
        });
        appEvent.fire();
    },
    handleGenreChange: function (component, event, helper) {
        //Get the Selected values
        var selectedValues = event.getParam("value");
        var oldValue = false;
        var newValue = false;
        for (let key in selectedValues) {
            if (selectedValues[key].includes('Leaking phase change material')) {
                newValue = true;
            }
        };
        for (let key in component.get("v.targetFields.Product_Related_Issue__c")) {
            if (component.get("v.targetFields.Product_Related_Issue__c")[key].includes('Leaking phase change material')) {
                oldValue = true;
            }
        }
        if(oldValue == false && newValue == true){
            // CSR 特殊的电池model提醒
            var warrantyId = component.get('v.warrantyNumberObj.Id');
            var productId = component.get('v.productObj.Id');
            if(warrantyId && productId){
                helper.showSpecialBatteryReminder(component, warrantyId, productId);
            }
        }

        //Update the Selected Values
        component.set("v.targetFields.Product_Related_Issue__c", selectedValues);
        component.set("v.productRelatedIssues", selectedValues);
    },
    checkSITWithWarranrty: function (component, event, helper){
        helper.checkSITWithPurchaseData(component);
    }
    // handleSITChange: function(component, event, helper) {
    //     let value = event.getParam('value');
    //     if(value === 'QA return') {
    //         let product = component.get('v.productObj');
    //         if(product && product['ProductCode']) {
    //             let action = component.get('c.getComments');
    //             action.setParams({'productCode': product['ProductCode']});
    //             action.setCallback(this, function(response){
    //                 let state = response.getState();
    //                 if (state === "SUCCESS"){
    //                     let result = response.getReturnValue();
    //                     if(result && result !== '') {
    //                         component.set('v.showQAReturnMessage', true);
    //                         component.set('v.qaReturnMessage', result.replace('\r\n', '<br />').replace('\n', '<br />'));
    //                     }
    //                     else {
    //                         component.set('v.showQAReturnMessage', false);
    //                     }
    //                 }
    //             });
    //             $A.enqueueAction(action);
    //         }
    //     }
    // }
})