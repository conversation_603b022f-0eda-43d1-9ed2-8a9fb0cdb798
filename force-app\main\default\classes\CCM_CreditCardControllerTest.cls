@isTest
public  class CCM_CreditCardControllerTest {
    static testMethod void testMethod1() {
        Lead theLead = Test_SalesData.createProspectData();
        theLead.Credit_Limit__c = '100';
        theLead.TaxID__c ='test';
        update theLead;

        Account customer = new Account();
        customer.Name = 'Test Customer';
        customer.AccountNumber = '*********';
        customer.TaxID__c = 'test';
        customer.Credit_Limit__c ='100';
        insert customer;

        Customer_Credit_Card__c creditCard = new Customer_Credit_Card__c(Credit_Limit_Type__c = 'test',
            Current_Payment_Term__c = 'EGO-NA002/Skil-NA016/EGO-NA002/SkilSaw-NA016/EGO-NA007/Skil-NA016',
            Current_Credit_Limit__c = 120,
            Customer_Name__c = customer.Id,
            Tax_ID__c = 'test',
            Additional_Payment_Terms__c = 'test',
            Order_Type__c = 'test',
            Calculation_Terms__c = 120,
            Address__c = 'test',
            Actual_Annual_Sales_Volume__c = 120,
            Legal_Entity__c = 'test',
            Forecasted_Annual_Sales_Volume__c = 120,
            Cluster__c = 'test',
            Purchasing_Amount_Based_On_Term__c = 120,
            Sub_Cluster__c = 'test',
            Chervon_Days_Beyond_Terms__c = 120,
            Sales_volume__c = 'test',
            Tools_Purchase_From_Chervon__c = 'test',
            Days_Beyond_Terms__c = 'test',
            Years_In_Business__c = 'test',
            Business_With_Chervon__c = 'test',
            On_Time_Payment_10_days__c = 'test',
            Are_There_Any_Current_Legal_Case__c = 'test',
            Payment_Behavior_Code__c = 'test',
            Credit_Report_Risk_Code__c = 'test',
            Score1__c = 0,
            Score2__c = 0,
            Score3__c = 0,
            Score4__c = 0,
            Score5__c = 0,
            Score6__c = 0,
            Score7__c = 0,
            Score8__c = 0,
            Score9__c = 0,
            FinalScore1__c = 0,
            FinalScore2__c = 0,
            FinalScore3__c = 0,
            FinalScore4__c = 0,
            FinalScore5__c = 0,
            FinalScore6__c = 0,
            FinalScore7__c = 0,
            FinalScore8__c = 0,
            FinalScore9__c = 0,
            Total_Score__c = 0,
            Maximum_Recommended_Limit__c = 0);
        insert creditCard;
        Test.startTest();
        Account acc = [SELECT Id FROM Account WHERE Name = 'Test Customer' LIMIT 1];
        Lead lea = [SELECT Id FROM Lead LIMIT 1];
        Customer_Credit_Card__c creditCard1 = [SELECT Id FROM Customer_Credit_Card__c LIMIT 1];
        CCM_NewCustomerProfileController.checkObjectByType(theLead.Id);
        CCM_NewCustomerProfileController.checkObjectByType(customer.Id);
        CCM_NewCustomerProfileController.getCustomerProfileField();
        CCM_NewCustomerProfileController.getCurrentUserProfile();
        CCM_NewCustomerProfileController.getSalesGroup(lea.Id,'Prospect');
        CCM_NewCustomerProfileController.getSalesGroup(acc.Id,'Customer');

        PageReference objPageReference = Page.CreditCard;
        Test.setCurrentPage(objPageReference);
        // new CCM_InvoiceDetailCtl();
        ApexPages.currentPage().getParameters().put('creditCardID', '');
        new CCM_CreditCardDetailCtl();
        CCM_CreateCustomerCreditCardController.searchData(acc.Id);
        CCM_CreateCustomerCreditCardController.searchData(lea.Id);
        CCM_CreateCustomerCreditCardController.DateResult data = new CCM_CreateCustomerCreditCardController.DateResult();
        String data1 = JSON.serialize(data);
        CCM_CreateCustomerCreditCardController.saveCreditCard(lea.Id,data1);
        BatchAndScheduleCreditCardSave updateCdbatech = new BatchAndScheduleCreditCardSave();
        Database.executeBatch(updateCdbatech, 1);
        ApexPages.currentPage().getParameters().put('creditCardID', creditCard1.Id);
        new CCM_CreditCardDetailCtl();
        Test.stopTest();
    }
}