/**
 * Created by gluo006 on 5/20/2019.
 */
({
    doInit:function(component, event, helper){
        component.set('v.currencySymbol', $A.get("$Locale.currency"));
        let caseId = component.get('v.csrCaseId');
        let isFromNewOrder = false;
        if(caseId) {
            isFromNewOrder = true;
        }
        if(!caseId) {
            //when only one recordType
            var csrCaseId = helper.getCookie('casePath').split('/view')[0].slice(18);
            component.set('v.csrCaseId', csrCaseId);
        }
        helper.showEle(component, 'spinner');
        //default the brand value as EGO
        component.set('v.defaultBrand', ['EGO', 'Hammerhead', 'Skil', 'SkilSaw','Hypertough']);
        if(component.get('v.pageReference')){
            component.set('v.isEdit', component.get('v.pageReference').attributes.actionName.toUpperCase() === 'EDIT');
            let { state } = component.get("v.pageReference"), { recordTypeId } = state || {};
            component.set("v.orderRecordTypeId", recordTypeId);
        }else{
            //warranty
            component.set('v.isEdit', false);
            component.set('v.notFromWarranty', false);
        }

        if(isFromNewOrder) {
            component.set('v.notFromWarranty', true);
        }

        if(component.get('v.caseType') == 'Warranty Order' || component.get('v.isRecallCase')){
            component.set('v.orderFields.Type', 'CNA Free Warranty Line')
        }else if(component.get('v.caseType') == 'Non-warranty Order'){
            component.set('v.orderFields.Type', 'CNA Sales Order - USD');
        }

        if (!component.get('v.isEdit')) {
            console.log('csrCaseId---> ' + csrCaseId);
            helper.init(component);
        } else {
            helper.reloadData(component);
        }
    },
    // onClickCancel: function (component, event, helper) {
    //     if (component.get('v.isEdit')) {
    //         helper.navigateToRecord(component.get('v.recordId'));
    //         helper.closeActiveTab(component);
    //     } else {
    //         var ws = JSON.parse(JSON.stringify(component.get('v.pageReference'))).state.ws;
    //         if(ws && ws.indexOf('Case') < 0){
    //             var recordId = ws.split('/')[4];
    //             helper.navigateToRecord(recordId);
    //             helper.closeActiveTab(component);
    //         }else{
    //             helper.closeActiveTab(component);
    //         }
    //     }
    // },
    toOrderItem:function(component, event, helper){
//        var orderType = component.find('orderType').get('v.value');
        var caseId = component.get('v.csrCaseId');
        var startDate = component.find('startDate').get('v.value');
        var customerPhone = component.find('customerPhone').get('v.value');
        var customerEmail = component.find('customerEmail').get('v.value');

        //var shippingStreet = component.find('shippingStreet').get('v.value');
        //var shippingCity = component.find('shippingCity').get('v.value');
        //var shippingState = component.find('shippingState').get('v.value');
        //var shippingZipCode = component.find('shippingZipCode').get('v.value');
        //var shippingCountry = component.find('shippingCountry').get('v.value');
        var description = component.find('description').get('v.value');
//        component.set('v.orderFields.Type', orderType);

        //get the address from melissa data to orderField- Added By Nitish Gyansys(SC-08)
        let address = component.get("v.address");
        component.set('v.orderFields.Shipping_Address__c', address.street);
        component.set('v.orderFields.Shipping_City__c', address.city);
        component.set('v.orderFields.Shipping_State__c', address.state);
        component.set('v.orderFields.Shipping_Zip_Code__c', address.zipCode);
        component.set('v.orderFields.Shipping_Country__c', address.country);

        component.set('v.orderFields.EffectiveDate', startDate);
        component.set('v.orderFields.Custome_Phone__c', customerPhone);
        component.set('v.orderFields.CustomerEmail__c', customerEmail);
        //component.set('v.orderFields.Shipping_Address__c', shippingStreet);
        //component.set('v.orderFields.Shipping_City__c', shippingCity);
        //component.set('v.orderFields.Shipping_State__c', shippingState);
        //component.set('v.orderFields.Shipping_Zip_Code__c', shippingZipCode);
        //component.set('v.orderFields.Shipping_Country__c', shippingCountry);
        component.set('v.orderFields.description', description);

        var isValid = helper.getValidation(component);
        if(isValid){
              if(component.get('v.caseType') == 'Warranty Order'){
                  component.set('v.orderItemFields.Product_Type__c', 'CNA Free Warranty Line');
              }else if(component.get('v.caseType') == 'Non-warranty Order'){
                  component.set('v.orderItemFields.Product_Type__c', 'CNA General Line');
                  component.set('v.orderFields.Free_Delivery__c', false)
              }
            helper.getPriceBook(component);
            helper.getGeolocation(component,caseId,address);
        }else{
            //alert to show required fields
            $A.get("e.force:showToast").setParams({
                "title": "Error",
                "message": "Cannot save the data, please review error fields",
                "type": "error"
            }).fire();
        }
    },
    toInvoice: function(component, event, helper){
        //alert("inside invoice");
        //shipping override
        var shippingOverride = component.find('shippingOverride').get('v.value');
        if(shippingOverride){
            component.set('v.orderFields.Total_Shipping_Charges__c', shippingOverride);
        }
        component.set('v.subTotal', (component.get('v.productCost') + Number(component.get('v.tax')) + Number(component.get('v.orderFields.Total_Shipping_Charges__c'))).toFixed(2))
        component.set('v.orderFields.Total_Amount__c', (component.get('v.productCost') + Number(component.get('v.tax')) + Number(component.get('v.orderFields.Total_Shipping_Charges__c'))).toFixed(2))
        helper.showEle(component, 'fourActions');
        helper.showEle(component, 'fourStep');
        helper.hideEle(component, 'lastStep');
        helper.hideEle(component, 'thirdActions');
        helper.hideEle(component, 'secondStep');
        helper.hideEle(component, 'secondActions');

    },
    backToShipping: function(component, event, helper){
        //reset
        helper.showEle(component, 'lastStep');
        helper.showEle(component, 'thirdActions');
        helper.hideEle(component, 'fourStep');
        helper.hideEle(component, 'fourActions');
    },
    handleDelete:function(component, event, helper){
       var index = Number(event.target.getAttribute('data-index'));
       var orderItemData = component.get('v.orderItemData');
       orderItemData.splice(index, 1);
       component.set('v.orderItemData', orderItemData);
    },
    backToOrder:function(component, event, helper){
        helper.showEle(component, 'firstStep');
        helper.showEle(component, 'firstActions');
        helper.hideEle(component, 'secondStep');
        helper.hideEle(component, 'secondActions');
    },
    handleProductChange:function(component, event, helper){
        //reset the price
        component.set('v.parts', '');
        component.set('v.partsVal', '');
        component.set('v.price', '');
        component.set('v.total', '');
        component.set('v.addItemDisabled', true);
        var productObj = JSON.parse(JSON.stringify(component.get('v.productObj')));
        var partsObj = JSON.parse(JSON.stringify(component.get('v.partsObj')));
        var productId;
        var partsId;
        if (productObj && productObj.Id) {
            productId = productObj.Id;
            // component.set('v.partsDisabled', false);
            // var partsId;
            if (partsObj && partsObj.Id) {
                partsId = partsObj.Id;
//                component.set('v.orderItemFields.Product_Type__c', 'CNA General Line')
            } else {
                partsId = '';
            }
        }else{
            productId = null;
            if (partsObj && partsObj.Id) {
                partsId = partsObj.Id;
            } else {
                partsId = '';
            }
            // if(component.get('v.caseType') == 'Warranty Order'){
            //     component.set('v.partsDisabled', true);
            // }
            helper.hideEle(component, 'spinnerPrice');
            component.set('v.price', '');
            component.set('v.total', '');
        }
        // 以防因网络延迟。当parts类型order不加载整机价格
        if(component.get('v.placeOrderScope') == 'parts' && partsId == ''){
            return;
        }
        helper.getPriceByProduct(component, productId, partsId);
        //alert to show required fields
        helper.calculateTotal(component, event, helper);
    },
    onPartChange:function(component, event, helper){
        var productObj = JSON.parse(JSON.stringify(component.get('v.productObj')));
        var productId;
        if (productObj && productObj.Id) {
            productId = productObj.Id;
        } else {
            productId = null;
        }
        var partsId = event.getSource().get('v.value');
        component.set('v.partsVal', partsId);
        helper.showEle(component, 'spinnerPrice');
        helper.getPriceByProduct(component, productId, partsId.split(';')[0]);
    },
    toShippingInfo:function(component, event, helper){
        if(component.get('v.isSaving')){
            return;
        }
        component.set('v.isSaving', true);
        var orderItemDataLen = component.get('v.orderItemData').length;
        if(orderItemDataLen <= 0 && component.get('v.caseType') != 'Non-warranty Order'){
            helper.showToast('Failed', 'At least one product or part needs to be selected!');
            component.set('v.isSaving', false);
            return;
        }
        //Accessory warranty control
        helper.checkCanCreatePartsOrder(component);
    },
    calculateTotal:function(component, event, helper){
        helper.calculateTotal(component);
    },
    onAddItem:function(component, event, helper){
        var caseId = component.get('v.csrCaseId');
        var productObj = JSON.parse(JSON.stringify(component.get('v.productObj')));
        var partsObj = JSON.parse(JSON.stringify(component.get('v.partsObj')));

        let placeOrderScope = component.get('v.placeOrderScope');
        if(placeOrderScope === 'parts' && !partsObj.Id) {
            $A.get("e.force:showToast").setParams({
                "title": "Error",
                "message": "Only Can Place Parts!",
                "type": "error"
            }).fire();
            return;
        }
        if(partsObj.Id != null && partsObj.Id != ''){
            var warehouse = helper.getWarehouse(component,caseId,partsObj.Id,partsObj.Name);
        }else{
            var warehouse = helper.getWarehouse(component,caseId,productObj.Id,productObj.Name);
        }
        component.set('v.addItemDisabled', true);
    },
    backToOrderItem:function(component, event, helper){
        //hide the save button to reCalculate the shipping Fee
        component.set('v.canBeSave', false);
        component.set('v.orderFields.Total_Shipping_Charges__c', '');
        component.set('v.isParts', false);
        helper.showEle(component, 'secondStep');
        helper.showEle(component, 'secondActions');
        helper.hideEle(component, 'lastStep');
        helper.hideEle(component, 'thirdActions');
    },
    onSelectShippingMethod:function(component, event, helper){
          component.set('v.orderFields.Shipping_method__c', event.getSource().get('v.value'))
    },
    checkOnRates:function(component, event, helper){
        helper.showEle(component, 'spinner');
        helper.checkOnRates(component);
    },
    onClickSave:function(component, event, helper){
        //alert('Skiping');
        helper.showEle(component, 'spinner');

        //get Order rest info
        var ShippingFee = component.find('ShippingFee').get('v.value');
        if(ShippingFee !== 0){
            ShippingFee = ShippingFee.substr(1);
        }
//        var shippingOverride = component.find('shippingOverride').get('v.value');
        var signatureRequired = component.find('signatureRequired').get('v.value');
        var trackingNumber = component.find('trackingNumber').get('v.value');
        component.set('v.orderFields.Total_Shipping_Charges__c', ShippingFee);
        component.set('v.orderFields.Signature_Required__c', signatureRequired);
        component.set('v.orderFields.Tracking_Number__c', trackingNumber);
        //shipping override
        var shippingOverride = component.find('shippingOverride').get('v.value');
        if(shippingOverride){
            component.set('v.orderFields.Total_Shipping_Charges__c', shippingOverride);
        }
        helper.onSaveRecord(component);
    },
    onClickPartsSave: function(component, event, helper){
        if(component.get('v.isSaving')){
            return;
        }
        component.set('v.isSaving', true);
        var valid = helper.getValidation(component, 'fourStep');
        var comps = component.find('tax');
        var validCommonCheck = Validator.pass(comps);
        if(valid && validCommonCheck){
            helper.checkPaymentStatus(component);
        }else{
            //alert to show required fields
            component.set('v.isSaving', false);
            $A.get("e.force:showToast").setParams({
                "title": "Error",
                "message": "Cannot save the data, please review error fields",
                "type": "error"
            }).fire();
        }
    },
    onClickCancel: function (component, event, helper) {
        if (component.get('v.isEdit')) {
            helper.navigateToRecord(component.get('v.recordId'));
        }
//         else {
//            var homeEvt = $A.get("e.force:navigateToObjectHome");
//            homeEvt.setParams({
//                "scope": "Order"
//            });
//            homeEvt.fire();
//        }
       helper.closeActiveTab(component);
    },
    onCheckStreetAddress:function(component, event, helper){
        var street = component.find('shippingStreet').get('v.value');
        if(street == '' || street == null){
            return;
        }else{
            helper.showEle(component, 'streetLoading');
            helper.checkAddress(component);
        }
    },
    onCheckCityAddress:function(component, event, helper){
        var city = component.find('shippingCity').get('v.value');
        if(city == '' || city == null){
            return;
        }else{
            helper.showEle(component, 'cityLoading');
            helper.checkAddress(component);
        }
    },
    onCheckStateAddress:function(component, event, helper){
        var state = component.find('shippingState').get('v.value');
        if(state == '' || state == null){
            return;
        }else{
            helper.showEle(component, 'stateLoading');
            helper.checkAddress(component);
        }
    },
    onCheckCountryAddress:function(component, event, helper){
        var country = component.find('shippingCountry').get('v.value');
        if(country == '' || country == null){
            return;
        }else{
            helper.showEle(component, 'countryLoading');
            helper.checkAddress(component);
        }
    },
    onGetAddressByCode:function(component, event, helper){
        var zipCode = component.find('shippingZipCode').get('v.value');
        if(zipCode == '' || zipCode == undefined || zipCode == null){
            //reset the state, city and country
            component.find('shippingCity').set('v.value', '');
            component.find('shippingState').set('v.value', '');
            component.find('shippingCountry').set('v.value', '');
            return;
        }
        helper.showEle(component, 'cityLoading');
        helper.showEle(component, 'stateLoading');
        helper.showEle(component, 'countryLoading');
        helper.getAddressByCode(component);
    },

    //update by nick 20200506
    calculateTaxTotal:function(component){
        var tax = component.get('v.tax');
        if (!tax) {
            component.set('v.tax', 0);
        }
        var partsCost = component.get('v.productCost');
        var shippingFee = component.get('v.orderFields.Total_Shipping_Charges__c');
        var subTotal = Number(partsCost) + Number(shippingFee) + Number(tax);
        component.set('v.orderFields.Total_Tax__c', component.get('v.tax'));
        component.set('v.subTotal', subTotal.toFixed(2));
    },
    updateShippingCharges : function(component, event, helper){
        let shippingFeeStr = component.find("Shipping-Fee-Invoice").get("v.value");
        let shippingFee = 0;
        if(shippingFeeStr && shippingFeeStr != component.get("v.currencySymbol")){
            shippingFee = parseFloat(shippingFeeStr.replace(component.get("v.currencySymbol"),0));
        }
        //alert(shippingFee);
        //if(shippingFee == NaN) shippingFee = 0;
        let existingShippingFee = component.get('v.orderFields.Total_Shipping_Charges__c');
        let subTotal = component.get('v.subTotal') - existingShippingFee + shippingFee;
        component.set('v.subTotal', subTotal.toFixed(2));
        component.set('v.orderFields.Total_Shipping_Charges__c', shippingFee);
        //alert(subTotal);
        //alert()

        // 重新计算tax
        var shippingOverride = component.find('shippingOverride').get('v.value');
        var taxRate = component.get('v.taxRate');
        var productCost = component.get('v.productCost');
        if(component.get('v.isIncludeFreight') &&  shippingOverride == ''){
            component.set('v.tax', ((Number(taxRate) * productCost) + (Number(taxRate) * shippingFee)).toFixed(2));
        }else{
            if(shippingOverride != 0 ){
                component.set('v.tax', ((Number(taxRate) * productCost) + (Number(taxRate) * shippingOverride)).toFixed(2));
            }else{
                component.set('v.tax', (Number(taxRate) * productCost) .toFixed(2));
            }
        }
        component.set('v.orderFields.Total_Tax__c', component.get('v.tax'));
    }

})