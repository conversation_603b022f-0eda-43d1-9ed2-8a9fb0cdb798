/**
 * Created by ali375 on 2019/5/13.
 */
({
    init: function (component) {
        var self = this;
        console.log(component.get('v.recordTypeId'))
        component.find("caseRecord").getNewRecord(
            "Case", // objectApiName
            component.get('v.recordTypeId'), // recordTypeId
            true, // skip cache
            $A.getCallback(function () {
                var rec = component.get("v.targetRecord");
                console.log(JSON.stringify(rec));
                var openTime = new Date().getFullYear() + '-' + ((new Date().getMonth() + 1).toString().length >= 2 ? new Date().getMonth() + 1 : '0' + (new Date().getMonth() + 1)) + '-' + new Date().getDate() + ' ' + ((new Date().getHours() < 10) ? '0' + new Date().getHours() : new Date().getHours()) + ':' + ((new Date().getMinutes() < 10) ? '0' + new Date().getMinutes() : new Date().getMinutes()) + ':' + (new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds());
                component.set('v.openTime', openTime);
                component.set('v.targetFields.Open_Time__c', new Date().toISOString());
                component.set('v.recordType', rec.fields.RecordType.displayValue);
                component.set('v.recordTypeId', rec.fields.RecordType.value.id);
                var error = component.get("v.targetError");
                if (error || (rec === null)) {
                    console.log("Error initializing record template: " + error);
                } else {
                    console.log("Record template initialized: " + rec.apiName);

                }
                self.getAllTabInfo(component);
                if (component.get('v.recordType') == 'Recall') {
                    return;
                }
                if (!component.get('v.isEdit')) {
                    let par = window.location.href;
                    let params = window.location.href.split('%2F');
                    if (par.indexOf('def_account_id') > -1) {
                        component.set('v.isFromCustomerToNewCase', true);
                    }
                    var accid = params[4];
                    if (accid != null && !accid.startsWith('CF')) {
                        accid = accid.replace('def_account_id=', '').replace('&', '');
                        self.checkIsCSR(component, accid, null, null);
                    }
                    var plValues = [];

                    plValues.push({
                        label: 'None',
                        value: 'None'
                    });

                    component.set("v.GenreList", plValues);

                }
                self.getSpecificModals(component);

            })
        );
        self.GenerateSIT(component);
        self.getCaseTypeOption(component);
    },
    GenratePRI: function (component) {
        if (component.get('v.productObj').Id != null && component.get('v.productObj').Id != '') {
            var action = component.get("c.getPiklistValues");
            action.setParams({
                productId: component.get('v.productObj').Id,
                caseId: component.get('v.recordId')
            })
            action.setCallback(this, function (response) {
                var state = response.getState();
                if (state === "SUCCESS") {
                    var result = JSON.parse(response.getReturnValue());
                    console.log(result.plValues); console.log(result.selectedValues);
                    var plValues = [];
                    let brand = component.find('brand').get('v.value');
                    if (brand === 'EGO') {
                        plValues.push({
                            label: 'Connect APP',
                            value: 'Connect APP'
                        });
                    }
                    for (var i = 0; i < result.plValues.length; i++) {
                        plValues.push({
                            label: result.plValues[i],
                            value: result.plValues[i]
                        });
                    }

                    component.set("v.GenreList", plValues);


                    component.set("v.selectedGenreList", result.selectedValues);
                }
            });
            $A.enqueueAction(action);
        } else {
            if (component.get('v.isEdit')) {
                var plValues = [];
                plValues.push({
                    label: "None",
                    value: "None"
                });
                component.set("v.GenreList", plValues);

            }
        }
    },
    GenerateSIT: function (component) {
        let product = component.get('v.productObj');
        let productCode = '';
        if (product && product['ProductCode']) {
            productCode = product['ProductCode'];
        }
        let serialNumbers = component.get('v.serialNumbers');
        let serialNumbersNeedToCheck = [];
        serialNumbers.forEach(item => {
            let pd = item['ProductCode'];
            if (pd.toUpperCase() === productCode.toUpperCase()) {
                serialNumbersNeedToCheck.push(item['SerialNumber']);
            }
        });
        let action = component.get('c.getSpecialIssueTracking');
        action.setParams({
            'productCode': productCode,
            'serialNumbersNeedToCheck': serialNumbersNeedToCheck,
            'warrantyId': component.get('v.warrantyNumberObj').Id
        });
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === "SUCCESS") {
                let specialTrackingValue = component.get('v.specialTrackingValue');
                let result = JSON.parse(response.getReturnValue());
                let sitOptions = result['sitOptions'];
                let options = [];
                options.push({
                    value: '',
                    label: ' -- None --'
                });
                for (const [key, value] of Object.entries(sitOptions)) {
                    let isSelected = false;
                    if (specialTrackingValue && specialTrackingValue === value) {
                        isSelected = true;
                    }
                    options.push({
                        value: key,
                        label: key,
                        selected: isSelected
                    });
                }
                component.set('v.specialTrackingOptions', options);
            }
        });
        $A.enqueueAction(action);
    },
    getRelatedTopIssue: function (component) {
        let product = component.get('v.productObj');
        let productCode = '';
        if (product && product['ProductCode']) {
            productCode = product['ProductCode'];
        }
        if (productCode) {
            let action = component.get('c.getRelatedTopIssues');
            action.setParams({ 'productCode': productCode });
            action.setCallback(this, function (response) {
                let state = response.getState();
                if (state === "SUCCESS") {
                    let result = JSON.parse(response.getReturnValue());
                    let topIssueTemplateMap = {};
                    let topIssueOptions = [];
                    topIssueOptions.push({
                        value: '',
                        label: ' -- None --'
                    });
                    let topIssueTemplate = '';
                    let topIssueSelected = component.get('v.topIssueSelected');
                    result.forEach(item => {
                        let topIssue = '';
                        let templateContent = '';
                        for (const [key, value] of Object.entries(item)) {
                            if (key === 'Top_Issue__c') {
                                topIssue = value;
                            }
                            else if (key === 'Template__c') {
                                templateContent = value;
                            }
                        }
                        topIssueTemplateMap[topIssue] = templateContent;
                        let isSelected = false;
                        if (topIssueSelected && topIssueSelected === topIssue) {
                            isSelected = true;
                        }
                        topIssueOptions.push({
                            value: topIssue,
                            label: topIssue,
                            selected: isSelected
                        });
                    });


                    component.set('v.topIssueTemplateMap', topIssueTemplateMap);
                    component.set('v.topIssueOptions', topIssueOptions);
                    component.set('v.topIssueTemplate', topIssueTemplate);

                    if (result && result.length > 0) {
                        component.set('v.showTopIssue', true);
                    }
                    else {
                        component.set('v.showTopIssue', false);
                    }
                }
            });
            $A.enqueueAction(action);
        }
    },
    getCaseTypeOption: function(component) {
        let action = component.get('c.getCaseTypeOptions');
        let rec = component.get("v.targetRecord");
        let caseId = undefined;
        if(rec) {
            caseId = rec.Id;
        }
        action.setParams({'caseId': caseId});
        action.setCallback(this, function (response){
            let state = response.getState();
            if(state === "SUCCESS") {
                let target = component.get('v.targetFields');
                target = JSON.parse(JSON.stringify(target));
                let caseType = component.get('v.targetFields.Case_Type__c');

                let result = JSON.parse(response.getReturnValue());
                let caseTypeOptions = result['caseTypeOptions'];
                let options = [];
                options.push({
                    value: '',
                    label: ' -- None --'
                });
                for (const [key, value] of Object.entries(caseTypeOptions)) {
                    let isSelected = false;
                    if (caseType && caseType === value) {
                        isSelected = true;
                    }
                    options.push({
                        value: key,
                        label: key,
                        selected: isSelected
                    });
                }
                component.set('v.caseTypeOptions', options);
            }
        });
        $A.enqueueAction(action);
    },
    getAllTabInfo: function (component) {
        var self = this;
        var workspace = component.find("workspace");
        var openTabs = [];
        var accId = '', warrantyId = '', hasWarranty = false;
        workspace.getAllTabInfo().then(function (tabInfo) {
            for (var i = 0; i < tabInfo.length; i++) {
                var currentTab = tabInfo[i];
                if (currentTab.focused) {
                    component.set('v.focusedTabId', currentTab.recordId);
                    if (currentTab.subtabs.length > 0) {
                        for (var j = 0; j < currentTab.subtabs.length; j++) {
                            if (currentTab.subtabs[j].url.indexOf('Warranty') > -1) {
                                hasWarranty = true;
                            }
                            openTabs.push(currentTab.subtabs[j].recordId);
                            //if(currentTab.subtabs[j].focused && currentTab.subtabs[j].title.indexOf('New Case') >= 0){
                            //var additionParams = currentTab.subtabs[j].pageReference.state.additionalParams;
                            //if(additionParams){
                            //if account_id then create the case directly from account
                            if (currentTab.iconAlt == 'Account') {
                                accId = currentTab.recordId;

                            }

                            // 修改case关联的warranty。从多个小Tab的点击创建case的时候，获取点击页面的warranty的15位ID
                            if(currentTab.subtabs[j].focused){
                                const URLParamMap = new Map();
                                const searchParams = new URLSearchParams(currentTab.subtabs[j].url);
                                for (const [key, value] of searchParams) {
                                    URLParamMap.set(key, value);
                                }
                                if(URLParamMap.has('additionalParams') && URLParamMap.get('additionalParams').indexOf('W-') >-1){
                                    const URLParams = URLParamMap.get('additionalParams').split('=');
                                    warrantyId = URLParams[URLParams.length -1].split('&')[0];
                                    console.log(warrantyId);
                                }
                            }

                            // if (currentTab.subtabs[j].title.indexOf('W-') > -1) {
                            //     // if create from warranty
                            //     warrantyId = currentTab.subtabs[j].recordId;
                            // }
                            // }
                            // }
                        }
                        if (accId == '' && warrantyId == '') {
                            if (currentTab.url.indexOf('Account') > -1) {
                                accId = currentTab.recordId;
                            } else {
                                warrantyId = currentTab.recordId;
                            }
                        }
                    }
                }
            }
            //all the open tab
            component.set('v.openTabs', openTabs);

            if (!component.get('v.isEdit')) {
                self.checkIsCSR(component, accId, warrantyId, hasWarranty);
                if (warrantyId) {
                    self.getSerialNumbers(component, warrantyId);
                }
            } else {
                self.checkIsCSR(component);
            }

            let caseId = component.get('v.recordId');
            self.caseReminder(component, caseId, warrantyId);
        })
    },
    checkReturnLabel: function (component) {
        var self = this;
        var returnLabel = component.find('return-label').get('v.value');
        if (!returnLabel) {
            return;
        }
        var action = component.get("c.returnLabelAndBatteryMessage");
        action.setParams({
            productId: component.get('v.productObj').Id,
            returnLabel: component.find('return-label').get('v.value')
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                if (result.Message) {
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Error",
                        "message": result.Message,
                        "type": "error"
                    }).fire();
                    self.hideEle(component, 'spinner');
                    component.find('return-label').set('v.value', '')
                    component.set('v.targetFields.Return_Label__c', '')
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].message,
                    "type": "error"
                }).fire();
                self.hideEle(component, "spinner");
            }
        });
        $A.enqueueAction(action);
    },
    checkIsCSR: function (component, accId, warrantyId, hasWarranty) {
        var self = this;
        var action = component.get("c.init");
        action.setParams({
            accId: accId,
            warId: warrantyId,
            hasWarranty: hasWarranty
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                component.set('v.isCSR', result.isCSRUser);
                //init the customer and warranty
                if (result.AccId) {
                    component.set('v.targetFields.AccountId', result.AccId);
                }
                if (result.WarId) {
                    component.set('v.warrantyNumberObj', { Name: result.WarName, Id: result.WarId });

                    //                   component.set('v.targetFields.Warranty__c', result.WarId);
                }
                if (result.ConId) {
                    component.set('v.targetFields.ContactId', result.ConId);
                }
                if (result.BrandName) {
                    component.set('v.targetFields.Brand_Name__c', result.BrandName);
                }
                if (result.isChannel) {
                    component.set('v.isChannel', result.isChannel);
                }
            }
        });
        $A.enqueueAction(action);
    },
    reloadData: function (component) {
        var self = this;
        component.find("caseRecord").reloadRecord(
            true,
            $A.getCallback(function () {
                var rec = component.get("v.targetRecord");
                console.log(JSON.stringify(rec));

                if (rec.recordTypeInfo && rec.recordTypeInfo.name) {
                    component.set('v.recordType', rec.recordTypeInfo.name);
                    component.set('v.recordTypeId', rec.recordTypeInfo.recordTypeId);
                } else {
                    component.set('v.recordType', 'General');
                }
                if (rec.fields.Warranty__r && rec.fields.Warranty__c.value) {
                    component.set('v.warrantyNumberObj', { Name: rec.fields.Warranty__r.displayValue, Id: rec.fields.Warranty__c.value });
                }
                if (rec.fields.Product && rec.fields.ProductId.value) {
                    component.set('v.productObj', { Name: rec.fields.Product.displayValue, Id: rec.fields.ProductId.value, ProductCode: rec.fields.Product_Code__c.value });
                    component.find("productLoader").reloadRecord(true,
                        $A.getCallback(function (){
                            console.log(JSON.stringify(component.get("v.targetError2")));
                            var temp = component.find("productLoader").get("v.targetFields");
                            console.log(JSON.stringify(temp));
                            component.set('v.productObj.Repairable__c', temp.Repairable__c);
                        }));
                }
                if (rec.fields.Open_Time__c) {
                    component.set('v.openTime', rec.fields.Open_Time__c.displayValue);
                }
                if (rec.fields.Id) {
                    component.set('v.recordId', rec.fields.Id.value);
                }
                //if edit initial the solution value as Recall_Solution__c
                if (rec.recordTypeInfo.name == 'Recall') {
                    if (rec.fields.Recall_Solution__c.value) {
                        component.set('v.recallSolution', rec.fields.Recall_Solution__c.value)
                    }
                    component.set('v.actualSolution', rec.fields.Actual_Solution__c.value)
                    if (rec.fields.Actual_Solution__c.value == 'Repair') {
                        component.set('v.isExternalNoteNeeded', true);
                    }
                    if (rec.fields.Warranty_Item__r && rec.fields.Warranty_Item__c.value) {
                        component.set('v.warrantyItemObj', { Name: rec.fields.Warranty_Item__r.displayValue, Id: rec.fields.Warranty_Item__c.value });
                    }
                    if (rec.fields.Project_Customer__r && rec.fields.Project_Customer__c.value) {
                        component.set('v.customerProjectObj', { Name: rec.fields.Project_Customer__r.displayValue, Id: rec.fields.Project_Customer__c.value });
                    }
                } else {
                    if (rec.fields.Case_Type__c.value == 'Warranty Order') {
                        component.set('v.isProductNeeded', true);
                        if (rec.fields.Brand_Name__c.value && rec.fields.Brand_Name__c.value !== 'ALL') {
                            component.set('v.isSpecialTrackingNeeded', true);
                        }
                    }
                    if (rec.fields.Case_Type__c.value == 'Service Referral') {
                        component.set('v.isExternalNoteNeeded', true);
                    }
                }

                var error = component.get("v.targetError");
                if (error || (rec === null)) {
                    console.log("Error initializing record template: " + error);
                } else {
                    console.log("Record template initialized: " + rec.apiName);
                }
                self.getAllTabInfo(component);
                var recordType = component.get('v.recordType');
                if (recordType != 'Recall') {
                    self.checkIsCSR(component, rec.fields.AccountId.value, null, null);
                    if (rec.fields.Fedex_Link__c.value) {
                        component.find('FED_EX__c').set('v.value', rec.fields.Fedex_Link__c.value.split('>')[1].replace(/[^0-9]/ig, ""));

                    }

                    //if(component.get('v.isChannel')){
                    if (rec.fields.Serial_Number__c.value) {
                        component.set('v.serialnumber', rec.fields.Serial_Number__c.value);
                    }
                    if (rec.fields.Handle_Time__c.value) {
                        component.set('v.handletime', rec.fields.Handle_Time__c.value);
                    }
                    if (rec.fields.Representative__c.value) {
                        component.set('v.representative', rec.fields.Representative__c.value);
                    }
                    if (rec.fields.Fedex_Link__c.value) {
                        component.set('v.fedex2', rec.fields.Fedex_Link__c.value.split('>')[1].replace(/[^0-9]/ig, ""));
                    }
                    if (rec.fields.Case_Type__c.value) {
                        //component.set('v.casetype', rec.fields.Case_Type2__c.value);
                        component.set('v.casetype2', rec.fields.Case_Type__c.value);
                    }
                    //}

                    if (rec.fields.Product_Related_Issue__c.value) {
                        component.set('v.selectedGenreList', rec.fields.Product_Related_Issue__c.value);
                    }
                    if (rec.fields.Special_Issue_Tracking__c.value) {
                        component.set('v.specialTrackingValue', rec.fields.Special_Issue_Tracking__c.value);
                        component.set('v.originalSepcialTrackingValue', rec.fields.Special_Issue_Tracking__c.value);
                    }
                    self.GenerateSIT(component);
                    self.getCaseTypeOption(component);
                }

                self.getSpecificModals(component);
            })
        );


    },
    getRecommendation: function (component) {
        var self = this;
        var action = component.get("c.showRecommendation");
        action.setParams({
            productId: component.get('v.productObj').Id,
            warrantyId: component.get('v.warrantyNumberObj').Id
        })
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = JSON.parse(response.getReturnValue());
                if (result.qaReturnMessage) {
                    var toastEvt = $A.get("e.force:showToast");
                        toastEvt.setParams({
                            "title": "QA Return Reminder",
                            "message": "This item is on the QA list. Check QA Return Recommendation field to determine next action.",
                            "type": "success",
                            "mode": "sticky"
                        }).fire();
                    component.set('v.qaReturnMessage', result.qaReturnMessage.replace('\r\n', '<br />').replace('\n', '<br />'));
                }
                else {
                    component.set('v.qaReturnMessage', '');
                }
                component.set('v.targetFields.Recommendation__c', result.Message);
            }
        });
        $A.enqueueAction(action);
    },
    getBrandByWarranty: function (component) {
        var action = component.get("c.getWarrantyFindBrand");
        action.setParams({
            warrantyId: component.get('v.warrantyNumberObj.Id')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                component.set('v.targetFields.Brand_Name__c', result);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": "Error get account record Id",
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    iSST1510TSN: function (component) {
        var action = component.get("c.iSST1510TSN");
        action.setParams({
            warrantyId: component.get('v.warrantyNumberObj.Id')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                if(result == true){
                    component.set('v.isST1510TSN', true);
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": "Info",
                        "message": "If customer is calling with a battery issue you must replace both the battery and trimmer. See the recommendation field below for more information.",
                        "type": "info",
                        "mode": "sticky"
                    }).fire();
                    component.set('v.targetFields.Recommendation__c', 'If a thermal event occurred, follow safety concern guideline. \nIf no thermal event, CSR should direct customer to recycle battery and call back string trimmer via QA return. \nCSR will communicate to the customer that the failed battery appears to be a result of a trimmer issue, therefore we will be replacing both.');    
                }else{
                    component.set('v.isST1510TSN', false);
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": "Error get warranty Id",
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    onSaveRecord: function (component) {
        var self = this;
        let action = component.get('c.checkQAReturnUseLeft');
        let specialIssueTracking = component.get('v.specialTrackingValue');
        let selectedIssues = [];
        if (specialIssueTracking) {
            selectedIssues.push(specialIssueTracking);
        }
        let product = component.get('v.productObj');
        product = JSON.parse(JSON.stringify(product));
        if(product && product['ProductCode']) {
            action.setParams({'productCode': product['ProductCode'], 'selectedIssues': selectedIssues.join(',')});
            action.setCallback(this, function(response){
                let state = response.getState();
                if (state === 'SUCCESS') {
                    let errMsg = response.getReturnValue();
                    if (errMsg && errMsg !== '') {
                        var resultsToast = $A.get("e.force:showToast");
                        resultsToast.setParams({
                            "title": "Error",
                            "message": errMsg,
                            "type": "error"
                        }).fire();
                    }
                    else {
                        component.find("caseRecord").saveRecord(function (saveResult) {
                            var resultsToast = $A.get("e.force:showToast");
                            if (saveResult.state === "SUCCESS" || saveResult.state === "DRAFT") {
                                // Success! Prepare a toast UI message
                                if (component.get('v.isEdit')) {
                                    resultsToast.setParams({
                                        "title": "Record Updated",
                                        "message": "The case was updated.",
                                        "type": "success"
                                    }).fire();
                                } else {
                                    resultsToast.setParams({
                                        "title": "Record Created",
                                        "message": "The new case was created.",
                                        "type": "success"
                                    }).fire();
                                }
                                self.focusCaseTab(component, saveResult.recordId);

                                // update qa return rule
                                let specialIssueTracking = component.get('v.specialTrackingValue');
                                let selectedIssues = [];
                                if (specialIssueTracking) {
                                    selectedIssues.push(specialIssueTracking);
                                }

                                let product = component.get('v.productObj');
                                let originalSepcialTrackingValue = component.get('v.originalSepcialTrackingValue');
                                if(product && product['ProductCode']) {
                                    let action = component.get('c.updateReturnRule');
                                    action.setParams({'productCode': product['ProductCode'], 'selectedIssues': selectedIssues.join(','), 'originalIssues': originalSepcialTrackingValue});
                                    action.setCallback(this, function(response){
                                    });

                                    $A.enqueueAction(action);
                                }
                            } else if (saveResult.state === "INCOMPLETE") {
                                console.log("User is offline, device doesn't support drafts.");
                                resultsToast.setParams({
                                    "title": "Error",
                                    "message": "User is offline, device doesn't support drafts.",
                                    "type": "error"
                                }).fire();
                                self.hideEle(component, 'spinner');
                            } else if (saveResult.state === "ERROR") {
                                console.log('Problem saving contact, error: ' +
                                    JSON.stringify(saveResult.error));
                                resultsToast.setParams({
                                    "title": "Error",
                                    "message": saveResult.error[0].message,
                                    "type": "error"
                                }).fire();
                                self.hideEle(component, 'spinner');
                            } else {
                                console.log('Unknown problem, state: ' + saveResult.state +
                                    ', error: ' + JSON.stringify(saveResult.error));
                                resultsToast.setParams({
                                    "title": "Error",
                                    "message": 'Unknown problem, state: ' + saveResult.state +
                                        ', error: ' + JSON.stringify(saveResult.error),
                                    "type": "error"
                                }).fire();
                                self.hideEle(component, 'spinner');
                            }

                        });
                    }
                }
            });
            $A.enqueueAction(action);
        }
        else {
            component.find("caseRecord").saveRecord(function (saveResult) {
                var resultsToast = $A.get("e.force:showToast");
                if (saveResult.state === "SUCCESS" || saveResult.state === "DRAFT") {
                    // Success! Prepare a toast UI message
                    if (component.get('v.isEdit')) {
                        resultsToast.setParams({
                            "title": "Record Updated",
                            "message": "The case was updated.",
                            "type": "success"
                        }).fire();
                    } else {
                        resultsToast.setParams({
                            "title": "Record Created",
                            "message": "The new case was created.",
                            "type": "success"
                        }).fire();
                    }
                    self.focusCaseTab(component, saveResult.recordId);

                    // update qa return rule
                    let specialIssueTracking = component.get('v.specialTrackingValue');
                    let selectedIssues = [];
                    if(specialIssueTracking) {
                        selectedIssues.push(specialIssueTracking);
                    }
                    let originalSepcialTrackingValue = component.get('v.originalSepcialTrackingValue');
                    let product = component.get('v.productObj');
                    if(product && product['ProductCode']) {
                        let action = component.get('c.updateReturnRule');
                        action.setParams({'productCode': product['ProductCode'], 'selectedIssues': selectedIssues.join(','), 'originalIssues': originalSepcialTrackingValue});
                        action.setCallback(this, function(response){
                        });

                        $A.enqueueAction(action);
                    }
                } else if (saveResult.state === "INCOMPLETE") {
                    console.log("User is offline, device doesn't support drafts.");
                    resultsToast.setParams({
                        "title": "Error",
                        "message": "User is offline, device doesn't support drafts.",
                        "type": "error"
                    }).fire();
                    self.hideEle(component, 'spinner');
                } else if (saveResult.state === "ERROR") {
                    console.log('Problem saving contact, error: ' +
                        JSON.stringify(saveResult.error));
                    resultsToast.setParams({
                        "title": "Error",
                        "message": saveResult.error[0].message,
                        "type": "error"
                    }).fire();
                    self.hideEle(component, 'spinner');
                } else {
                    console.log('Unknown problem, state: ' + saveResult.state +
                        ', error: ' + JSON.stringify(saveResult.error));
                    resultsToast.setParams({
                        "title": "Error",
                        "message": 'Unknown problem, state: ' + saveResult.state +
                        ', error: ' + JSON.stringify(saveResult.error),
                        "type": "error"
                    }).fire();
                    self.hideEle(component, 'spinner');
                }

            });
        }
        // component.find("caseRecord").saveRecord(function (saveResult) {
        //     var resultsToast = $A.get("e.force:showToast");
        //     if (saveResult.state === "SUCCESS" || saveResult.state === "DRAFT") {
        //         // Success! Prepare a toast UI message
        //         if (component.get('v.isEdit')) {
        //             resultsToast.setParams({
        //                 "title": "Record Updated",
        //                 "message": "The case was updated.",
        //                 "type": "success"
        //             }).fire();
        //         } else {
        //             resultsToast.setParams({
        //                 "title": "Record Created",
        //                 "message": "The new case was created.",
        //                 "type": "success"
        //             }).fire();
        //         }
        //         self.focusCaseTab(component, saveResult.recordId);

        //         // update qa return rule
        //         let specialIssueTracking = component.get('v.specialTrackingValue');
        //         let selectedIssues = [];
        //         if(specialIssueTracking) {
        //             selectedIssues.push(specialIssueTracking);
        //         }
        //         let product = component.get('v.productObj');
        //         if(product && product['ProductCode']) {
        //             let action = component.get('c.updateReturnRule');
        //             action.setParams({'productCode': product['ProductCode'], 'selectedIssues': selectedIssues.join(',')});
        //             action.setCallback(this, function(response){
        //             });

        //             $A.enqueueAction(action);
        //         }
        //     } else if (saveResult.state === "INCOMPLETE") {
        //         console.log("User is offline, device doesn't support drafts.");
        //         resultsToast.setParams({
        //             "title": "Error",
        //             "message": "User is offline, device doesn't support drafts.",
        //             "type": "error"
        //         }).fire();
        //         self.hideEle(component, 'spinner');
        //     } else if (saveResult.state === "ERROR") {
        //         console.log('Problem saving contact, error: ' +
        //             JSON.stringify(saveResult.error));
        //         resultsToast.setParams({
        //             "title": "Error",
        //             "message": saveResult.error[0].message,
        //             "type": "error"
        //         }).fire();
        //         self.hideEle(component, 'spinner');
        //     } else {
        //         console.log('Unknown problem, state: ' + saveResult.state +
        //             ', error: ' + JSON.stringify(saveResult.error));
        //         resultsToast.setParams({
        //             "title": "Error",
        //             "message": 'Unknown problem, state: ' + saveResult.state +
        //             ', error: ' + JSON.stringify(saveResult.error),
        //             "type": "error"
        //         }).fire();
        //         self.hideEle(component, 'spinner');
        //     }

        // });
    },
    navigateToRecord: function (id) {
        var navEvt = $A.get("e.force:navigateToSObject");
        navEvt.setParams({
            "recordId": id,
            "slideDevName": "related"
        });
        navEvt.fire();
    },
    getAccountRecordTypeById: function (component, accountId) {
        var action = component.get("c.changeCustomer");
        action.setParams({
            accountId: accountId
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                component.set('v.brandName', result);
                component.find('brand').set('v.value', result);
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": "Error get account record Id",
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    getValidation: function (component) {
        var valid = true;
        var recordType = component.get('v.recordType');
        var caseType = null;
        var brand = null;


        if (recordType === 'General') {
            if (!component.get('v.isChannel')) {
                caseType = component.find('case-type').get('v.value');
                brand = component.find('brand').get('v.value');
            } else {
                caseType = component.find('case-type2').get('v.value');
                brand = component.find('brand2').get('v.value');
            }
            if (!component.get('v.isChannel')) {
                valid = valid && this.getElementRequiredError(component, 'origin');
                valid = valid && this.getElementRequiredError(component, 'account-id');
                valid = valid && this.getElementRequiredError(component, 'status');
            }
            if (!component.get('v.isChannel')) {
                valid = valid && this.getElementRequiredError(component, 'brand');
                valid = valid && this.getElementRequiredError(component, 'case-type');
                valid = valid && this.getElementRequiredError(component, 'related-issue');
                valid = valid && this.getElementRequiredError(component, 'special-tracking');
                valid = valid && this.getElementRequiredError(component, 'external-note');
                valid = valid && this.getElementRequiredError(component, 'Description');

            } else {
                valid = valid && this.getElementRequiredError(component, 'brand2');
                valid = valid && this.getElementRequiredError(component, 'case-type2');
                valid = valid && this.getElementRequiredError(component, 'status2');
                valid = valid && this.getElementRequiredError(component, 'Description');
            }

            if (!component.get('v.isChannel')) {
                if (component.get("v.showDetectionForSpecificModal")) {
                    let element = component.find('detection');
                    var requiredText = component.find('detection-error-required');
                    if (!component.get("v.targetFields.Detection__c")) {
                        $A.util.addClass(element, 'field-error');
                        if (requiredText) {
                            $A.util.removeClass(requiredText, 'slds-hide');
                        }
                        valid = valid && false;
                    } else {
                        $A.util.removeClass(element, 'field-error');
                        if (requiredText) {
                            $A.util.addClass(requiredText, 'slds-hide');
                        }
                        valid = valid && true;
                    }
                }
            }

        } else if (recordType === 'Recall') {
            valid = valid && this.getElementRequiredError(component, 'Description');
            valid = valid && this.getElementRequiredError(component, 'customerProjectObj', 'lookup');
            valid = valid && this.getElementRequiredError(component, 'warrantyItemObj', 'lookup');
            valid = valid && this.getElementRequiredError(component, 'actual-solution');
        }
        return valid;
    },
    getElementRequiredError: function (component, ele, fieldType) {

        var element = component.find(ele);
        var caseType = null;
        var brand = null;
        var recordType = component.get('v.recordType');
        var actualSoluntion = null;
        if (recordType === 'General') {
            if (!component.get('v.isChannel')) {
                caseType = component.find('case-type').get('v.value');
                if(!caseType) {
                    caseType = component.get('v.targetFields.Case_Type__c');
                }
                brand = component.find('brand').get('v.value');
            } else {
                caseType = component.find('case-type2').get('v.value');
                brand = component.find('brand2').get('v.value');
            }
        } else {
            actualSoluntion = component.get('v.actualSolution');
        }

        var requiredText = component.find(ele + '-error-required');
        var val = '';
        if (ele == 'external-note') {
            //check External Note when case type is Service Referral
            if (!component.get('v.isChannel') && (caseType == 'Service Referral' || (recordType == 'Recall' && actualSoluntion == 'Repair'))) {
                val = element.get('v.value');
            } else {
                val = 'NotRequired';
            }
        } else if (ele == 'special-tracking') {
            //check special issue tracking when Warranty Order and brand not null not equal to ALL
            if (!component.get('v.isChannel') && caseType == 'Warranty Order' && brand && brand != 'ALL') {
                val = element.get('v.value');
                if (!val) {
                    val = component.get('v.specialTrackingValue');
                }
                if (val == '-- None --') {
                    val = '';
                }
            } else {
                val = 'NotRequired';
            }
        } else if (ele == 'actual-solution') {
            val = component.get('v.targetFields.Actual_Solution__c');
        } else if (fieldType == 'lookup') {
            val = component.get('v.' + ele).Id
        }else if (ele == 'case-type') {
            val = caseType;
        } else {
            val = element.get('v.value');
        }

        var valid = !!val;
        if (ele == 'related-issue' && valid) {
            //check special issue tracking when Warranty Order and brand not null not equal to ALL
            if (component.get('v.selectedGenreList').length > 0) {
                valid = true;
            } else {
                valid = false;
            }
        }
        /*if(component.get('v.recordType') == 'Recall'){
            valid = true;
        }*/
        if (valid) {
            $A.util.removeClass(element, 'field-error');
            if (requiredText) {
                $A.util.addClass(requiredText, 'slds-hide');
            }
        } else {
            $A.util.addClass(element, 'field-error');
            if (requiredText) {
                $A.util.removeClass(requiredText, 'slds-hide');
            }
        }
        return valid;

    },
    performValidation: function (component) {
        var self = this;
        var action = component.get("c.saveCase");
        var status1 = null;
        var serialNumber = null;
        var handleTime = null;
        var representative = null;
        var Product_Related_Issue = '';

        if (!component.get('v.isChannel')) {
            status1 = component.find('status').get('v.value');
            Product_Related_Issue = JSON.stringify(component.get('v.selectedGenreList'));
        } else {
            status1 = component.find('status2').get('v.value');
            serialNumber = component.find('Serial_Number__c').get('v.value');
            handleTime = component.find('Handle_Time__c').get('v.value');
            representative = component.find('Representative__c').get('v.value');

        }
        var param = {
            Id: component.get('v.recordId') || null,
            RecordTypeId: component.get('v.recordTypeId') || null,
            Warranty_Item__c: component.get('v.targetFields.Warranty_Item__c') || null,
            Case_Type__c: component.get('v.targetFields.Case_Type__c') || null,
            ProductId: component.get('v.targetFields.ProductId') || null,
            Warranty__c: component.get('v.targetFields.Warranty__c') || null,
            Project_Customer__c: component.get('v.targetFields.Project_Customer__c') || null,
            Status: status1,
            FED_EX__c: component.get('v.targetFields.FED_EX__c') || null,
            Serial_Number__c: serialNumber,
            Handle_Time__c: handleTime,
            Representative__c: representative,
            Product_Related_Issue__c: Product_Related_Issue,
            AttachmentURL__c: component.get('v.targetFields.AttachmentURL__c'),

        };
        action.setParams({
            details: JSON.stringify(param),
            isFromCustomerToNewCase: component.get('v.isFromCustomerToNewCase')
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            var toastEvt = $A.get("e.force:showToast");
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                if (result) {
                    if (result == 'receipt') {
                        toastEvt.setParams({
                            "title": "Error",
                            "message": "Service Referral cases cannot be saved with receipt in pending status.  Please update Receipt Information on the warranty before saving the Service Referral case.",
                            "type": "error",
                            "mode": "sticky"
                        });
                        toastEvt.fire();
                        self.hideEle(component, 'spinner');
                    } else if (component.get('v.recordType') == 'Recall') {
                        result = JSON.parse(result);
                        if (result.Project__c) {
                            component.set('v.targetFields.Project__c', result.Project__c)
                        }
                        if (result.Warranty__c) {
                            component.set('v.targetFields.Warranty__c', result.Warranty__c)
                        }
                        if (result.ProductId) {
                            component.set('v.targetFields.ProductId', result.ProductId)
                        }
                        if (result.Brand) {
                            component.set('v.targetFields.Brand_Name__c', result.Brand)
                        }
                        this.onSaveRecord(component);
                    } else {
                        toastEvt.setParams({
                            "title": "Error",
                            "message": result,
                            "type": "error"
                        }).fire();
                    }
                } else {
                    if (component.get('v.targetFields.Case_Type__c') == 'Service Referral') {
                        var externalNote = component.find('external-note').get('v.value');
                        component.set('v.targetFields.Extenal_Note__c', externalNote);

                    }
                    if (Product_Related_Issue != null) {
                        Product_Related_Issue = self.fixSFDCString(Product_Related_Issue, 'Product_Related_Issue__c');
                        component.set('v.targetFields.Product_Related_Issue__c', Product_Related_Issue);
                    }
                    this.onSaveRecord(component);
                }


            } else {
                toastEvt.setParams({
                    "title": "Error",
                    "message": "Error get account record Id",
                    "type": "error"
                }).fire();
                self.hideEle(component, 'spinner');
            }
        });
        $A.enqueueAction(action);
    },
    getRecallInformation: function (component, id) {
        var action = component.get("c.changeRecallSolution");
        action.setParams({
            projectCustomerId: id
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();
                if (result) {
                    component.set('v.recallSolution', result);
                    component.set('v.targetFields.Recall_Solution__c', result);
                    component.set('v.targetFields.Actual_Solution__c', result);
                } else {
                    component.set('v.recallSolution', '');
                    component.set('v.targetFields.Recall_Solution__c', '');
                    component.set('v.targetFields.Actual_Solution__c', '');
                }
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": "Error get account record Id",
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    },
    closeActiveTab: function (component) {
        var workspaceAPI = component.find("workspace");
        var focusedTabInfo = workspaceAPI.getFocusedTabInfo();
        if (focusedTabInfo && focusedTabInfo != undefined) {
            workspaceAPI.getFocusedTabInfo().then(function (response) {
                var focusedTabId = response.tabId;
                workspaceAPI.closeTab({ tabId: focusedTabId });
            }).catch(function (error) {
                    console.log(error);
                });
        }
    },
    focusCaseTab: function (component, id) {
        var self = this;
        var workspaceAPI = component.find("workspace");
        //        if(!component.get('v.isEdit')){
        if (component.get('v.focusedTabId')) {
            workspaceAPI.getFocusedTabInfo().then(function (response) {
                var focusedTabId = response.tabId;
                workspaceAPI.openTab({
                    recordId: component.get('v.focusedTabId'),
                    focus: true
                }).then(function (response1) {
                    workspaceAPI.refreshTab({
                        tabId: response1,
                        includeAllSubtabs: false
                    });
                    var openTabs = component.get('v.openTabs');
                    if (openTabs) {
                        for (var i = 0; i < openTabs.length; i++) {
                            workspaceAPI.openSubtab({
                                parentTabId: response1,
                                recordId: openTabs[i],
                                focus: true
                            });
                        }
                    }
                    workspaceAPI.openSubtab({
                        parentTabId: response1,
                        recordId: id,
                        focus: true
                    })
                    workspaceAPI.closeTab({ tabId: focusedTabId });
                })
            })
        } else {
            self.navigateToRecord(id);
            self.closeActiveTab(component);
        }
        //        }else{
        //            workspaceAPI.getFocusedTabInfo().then(function (response) {
        //                var focusedTabId = response.tabId;
        //                workspaceAPI.closeTab({tabId: focusedTabId});
        //            })
        //        }

    },
    showEle: function (component, ele) {
        $A.util.removeClass(
            component.find(ele),
            "slds-hide"
        );
    },
    hideEle: function (component, ele) {
        $A.util.addClass(
            component.find(ele),
            "slds-hide"
        );
    },
    fixSFDCString: function (formJSON, elementName) {
        if (Array.isArray(formJSON[elementName])) {
            var brokenArray = formJSON[elementName];
            var fixedArray = brokenArray.join(";");

            delete formJSON[elementName];
            formJSON[elementName] = fixedArray;
        }
        return formJSON;
    },
    onChange: function (cmp, event) {
        // Retrieve an array of the selected options
        var selectedOptionValue = event.getParam("value");
    },
    getSpecificModals: function (cmp) {
        let action = cmp.get("c.querySpecificModals");
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === "SUCCESS") {
                let result = response.getReturnValue();
                result = JSON.parse(result);
                cmp.set("v.modals", result.modals);
                cmp.set("v.detectionMap", result.detectionMap);
                let detections = [];
                Object.keys(result.detectionMap).forEach(item => {
                    let detection = {
                        name: item,
                        selected: false
                    };
                    if (item === "5 LED flash green and red alternately" || item === "5 LED shine solid green for 10s") {
                        detections.splice(0, 0, detection);
                    }
                    else {
                        detections.push(detection);
                    }
                });
                cmp.set("v.detections", detections);
            }
        });
        $A.enqueueAction(action);
    },
    showDetections: function (cmp) {
        let isExcludeExist = false;
        if (cmp.get("v.selectedGenreList")) {
            const exclude_issues = ["Missing Contents", "Smoking / Melting", "Not applicable"];
            cmp.get("v.selectedGenreList").forEach(item => {
                let exist = exclude_issues.includes(item);
                if (exist) {
                    isExcludeExist = true;
                }
            });
        }
        let isProductIssueSelected = cmp.get("v.selectedGenreList") || undefined && !isExcludeExist;
        let productCode = (cmp.get("v.productObj") || { "ProductCode": "" })["ProductCode"];
        let modals = JSON.stringify(cmp.get("v.modals"));
        if (modals.indexOf(productCode) !== -1 && isProductIssueSelected && isProductIssueSelected.length > 0) {
            cmp.set("v.showDetectionForSpecificModal", true);
        }
        else {
            cmp.set("v.showDetectionForSpecificModal", false);
        }
    },
    getSerialNumbers: function (component, warrantyId) {
        let self = this;
        let action = component.get('c.getSerialNumbers');
        action.setParams({
            warrantyId: warrantyId
        });
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === "SUCCESS") {
                let result = JSON.parse(response.getReturnValue());
                component.set('v.serialNumbers', result);
            }
        });
        $A.enqueueAction(action);
    },
    showSpecialBatteryReminder: function (component, warrantyId, productId) {
        let self = this;
        let action = component.get('c.showSpecialBatteryReminder');
        action.setParams({
            warrantyId: warrantyId,
            productId: productId
        });
        action.setCallback(this, function (response) {
            let state = response.getState();
            if (state === "SUCCESS") {
                let result = JSON.parse(response.getReturnValue());
                var toastEvt = $A.get("e.force:showToast");
                if(result.reminder == 'EGO'){
                    toastEvt.setParams({
                        "title": "Info",
                        "message": "Tell the customer to keep the battery for 10 days after sending the pictures in case we request a return. If not contacted, they can recycle it.",
                        "type": "info",
                        "mode": "sticky"
                    }).fire();
                }else if(result.reminder == 'FLEX'){
                    toastEvt.setParams({
                        "title": "Info",
                        "message": "Tell the customer to keep the battery (SN newer than XX23300XXXXXX) for 10 days after sending the pictures in case we request a return. If not contacted, they can recycle it.",
                        "type": "info",
                        "mode": "sticky"
                    }).fire();
                }else if(result.reminder == 'Skil'){
                    toastEvt.setParams({
                        "title": "Info",
                        "message": "Tell the customer to keep the battery (SN newer than 331XXXXXX) for 10 days after sending the pictures in case we request a return. If not contacted, they can recycle it.",
                        "type": "info",
                        "mode": "sticky"
                    }).fire();
                }
            }
        });
        $A.enqueueAction(action);
    },

    prePopulateExternalNotes: function(component) {
        let productId = component.get('v.productObj').Id;
        let action = component.get('c.getExternalNotes');
        action.setParams({'productId': productId});
        action.setCallback(this, function(response){
            let state = response.getState();
            if(state === 'SUCCESS') {
                let result = response.getReturnValue();
                if(result) {
                    component.set('v.preExternalNote', result);
                }
            }
        });
        $A.enqueueAction(action);
    },

    caseReminder: function(component, caseId, warrantyId) {
        let action = component.get('c.caseReminder');
        action.setParams({'caseId': caseId, 'warrantyId': warrantyId});
        action.setCallback(this, function(response){
            let state = response.getState();
            if(state == "SUCCESS"){
                // show reminder
                let message = response.getReturnValue();
                if(message) {
                    let toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        title : 'Reminder',
                        message: message,
                        type: 'Success',
                        mode: 'sticky'
                    });
                    toastEvent.fire();
                }
            }
        });
        $A.enqueueAction(action);
    },

    // 当today-purchaseDate > 90天，不可以选SIT的newly
    checkSITWithPurchaseData: function(component){
        var specialTrackingComponent = component.find('special-tracking');
        var specialTracking;
        if(specialTrackingComponent){
            specialTracking = specialTrackingComponent.get('v.value');
        }else{
            return;
        }
        var warrantyId = component.get('v.warrantyNumberObj.Id');  
        if(component.get('v.targetFields.Case_Type__c') == 'Warranty Order' && warrantyId != null && specialTracking == 'Newly purchased lead approved'){
            component.set('v.isCanSaveWithSIT', true);
        } else {
            return;
        }
        let action = component.get('c.getPurchaseData');
        action.setParams({warrantyId: warrantyId});
        action.setCallback(this, function(response){
            var state = response.getState();
            var toastEvt = $A.get("e.force:showToast");
            if (state === "SUCCESS") {
                var result =  JSON.parse(response.getReturnValue());
                if(result){
                    var purchaseDate = new Date(result.Purchase_Date__c);
                    var today = new Date();
                    var timeDiff = today - purchaseDate;
                    var daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
                    if(daysDiff > 90){
                        component.set('v.isCanSaveWithSIT', true);
                        toastEvt.setParams({
                            "title": "Warning",
                            "message": "This SIT code cannot be used when length of ownership greater than 90 days",
                            "type": "warning"
                        }).fire();
                    } else {
                        component.set('v.isCanSaveWithSIT', false);
                    }
                } else {
                    toastEvt.setParams({
                        "title": "Error",
                        "message": "Error get purchase Date",
                        "type": "error"
                    }).fire();
                }
            } else {
                toastEvt.setParams({
                    "title": "Error",
                    "message": "Error get purchase Date",
                    "type": "error"
                }).fire();
            }
        });
        $A.enqueueAction(action);
    }
})