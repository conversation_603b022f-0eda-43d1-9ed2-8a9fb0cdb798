({
    doInit: function(component, event, helper){
        var poTypeOptions = [ {'label': 'Order', 'value': 'Place_Order'},
                      {'label': 'Parts Order', 'value': 'Place_Parts_Order'}
                     ];
        component.set('v.poTypeOptions', poTypeOptions);

        var columns = [
            {
                label: $A.get("$Label.c.CCM_Action"),
                width: '80px',
                tdStyle: 'text-align: left',
                children:[
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${id}",
                        variant:"bare",
                        iconName:"utility:preview",
                        alternativeText:"View",
                        onclick: component.getReference('c.doView')
                    }
                },
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${id}",
                        variant:"bare",
                        iconName:"utility:edit",
                        alternativeText:"Edit",
                        class: "${editStyleCss}",
                        onclick: component.getReference("c.doEdit")
                    }
                },
                {
                    type: "lightning:buttonIcon",
                    attributes:{
                        value: "${id}",
                        variant:"bare",
                        iconName:"utility:delete",
                        alternativeText:"Delete",
                        class: "${deleteStyleCss}",
                        onclick: component.getReference("c.doDelete")
                    }
                }]
            },
            {label: 'Customer Name', fieldName:'customerName'},
            {label: 'Account Number', fieldName:'accountNumber'},
            {label: 'Purchase Order Type', fieldName: 'orderType'},
            /*{label: 'Order Number', fieldName:'orderNumber'},*/
            {label: 'PO Number', fieldName: 'poNumber'},
            {label: 'Customer PO Number', fieldName: 'customerPONumber'},
            {label: 'Brand', fieldName: 'brand'},
            {label: 'Purchase Order Status', fieldName: 'status'},
            {label: 'Total Amount',
                children:[
                {
                    type: "lightning:formattedNumber",
                    attributes:{
                        value: "${totalPrice}",
                        currencyCode: "${currencyIsoCode}",
                        currencyDisplayAs:"code",
                        style:"currency"
                    }
                }]
            },
           /* {label: 'Submit Date',
                children:[
                {
                    type: "lightning:formattedDateTime",
                    attributes:{
                        value: "${submitDate}",
                        year : "numeric",
                        month: "numeric",
                        day: "numeric",
                        timeZone:"UTC"
                    }
                }]
            },*/
            {label: 'SO Creation Status', fieldName: 'syncStatus'},
            {label: 'Submit Date', fieldName: 'submitDate'},
            {label: 'Created By', fieldName: 'createdBy'}];

        component.set('v.columns',columns);

        component.set('v.filterObj',{});
        component.set('v.filterObj',{brand:[]});
        component.set('v.filterObj.poRecordType', 'Place_Order');

        console.log("BooleanA_LOCK_ORDER_FUNC_TMP: ", Boolean($A.get("$Label.c.A_LOCK_ORDER_FUNC_TMP")));
        // 临时关闭订单功能
        component.set("v.blLockOrderFuncTmp", $A.get("$Label.c.A_LOCK_ORDER_FUNC_TMP") == 'True');
        let currentpageNumber = sessionStorage.getItem('currentpageNumber');
        if(currentpageNumber != null && currentpageNumber != 'undefined'){
            component.set("v.pageNumber", currentpageNumber);
            sessionStorage.removeItem('currentpageNumber');
        }

        helper.getObjectRecords(component, event, helper, true);
    },
    handleSearch : function(component, event, helper){
        component.set('v.pageNumber', 1);
        helper.getObjectRecords(component, event, helper, false);
    },
    doReset : function(component, event, helper){
        component.set("v.filterObj", {});
        component.set('v.filterObj',{brand:[]});
        component.set("v.value", '');
        helper.getObjectRecords(component, event, helper, false);
    },
    pageChange: function(component, event, helper) {
        var pageNumber = event.getParam("pageNumber");
        component.set("v.pageNumber",pageNumber);
        helper.getObjectRecords(component, event, helper, false);
        event.stopPropagation();
    },
    pageCountChange : function(component, event, helper){
        var pageCount = event.getParam("pageCount");
        component.set("v.pageCount",pageCount);
        component.set("v.pageNumber", 1);
        helper.getObjectRecords(component, event, helper, false);
        event.stopPropagation();
    },
    doView : function(component, event, helper){
        var recordId = event.getSource().get('v.value');
        helper.getObjectInfo(component,event, helper, recordId, false);
    },
    doEdit : function(component, event, helper){
        var recordId = event.getSource().get('v.value');
        helper.getObjectInfo(component,event, helper, recordId, true);
    },
    doDelete : function(component, event, helper){
        var recordId = event.getSource().get('v.value');
        var action = component.get("c.deleteOrderInfo");
        var currentpageNumber = component.get("v.pageNumber");
        sessionStorage.setItem('currentpageNumber', currentpageNumber);
        action.setParams({
            "recordId": recordId,
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = response.getReturnValue();
                if (results === 'Success'){
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Success!",
                        "message": "The order has been deleted successfully.",
                        "type": "success"
                    });
                    toastEvent.fire();

                    var refreshEvt = $A.get('e.force:refreshView');
                    refreshEvt.fire();
                }else{
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "The order has been deleted Fail, Please contact your system administrator.",
                        "type": "error"
                    });
                    toastEvent.fire();
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },
    doDelegate : function(component, event, helper){
        var url = window.location.origin + '/lightning/n/Quotation_Info_Edit';
        window.open(url,'_self');
    },
    doDelegateParts : function(component, event, helper){
        var url = window.location.origin + '/lightning/n/ServiceQuotation';
        window.open(url,'_self');
    }
})