/**
 * <AUTHOR>
 * @date 2021-08-23
 * @description This class is used to default the Expected Delivery Date for a Purchase Order generated through Portal to Order Submission Date.
 */
public with sharing class CCM_FillExpectedDeliveryDateHandler implements Triggers.Handler {
    public static Boolean boolToRun = true;
    public void handle() {
        // prettier-ignore
        if (boolToRun != true) return;
        switch on Trigger.operationType {
            when BEFORE_INSERT, BEFORE_UPDATE {
                for (Purchase_Order__c objPO : (List<Purchase_Order__c>) Trigger.new) {
                    if (objPO.RecordTypeId == CCM_Constants.PURCHASE_ORDER_RECORD_TYPE_PLACE_ORDER_ID) {
                        // objPO.Expected_Delivery_Date__c = objPO.Submit_Date__c?.date();
                        // objPO.Is_Delegate__c == false &&
                        if(objPO.Shipping_By__c != null && objPO.BillTo__c != null && objPO.ORG_ID__c != 'CCA'){
                            List<Address_With_Program__c> awp = [SELECT Id,Program__c,Program__r.Freight_Term__c FROM Address_With_Program__c WHERE Id = :objPO.BillTo__c];
                            if(objPO.Shipping_By__c == 'Customer'){
                                objPO.Freight_Term__c = 'COLLECT';
                            }else if(objPO.Shipping_By__c == 'Third Party Billing'){//& awp.Program__r.Freight_Term__c == 'THIRD_PARTY'
                            objPO.Freight_Term__c = 'THIRD_PARTY';
                            }else if(objPO.Shipping_By__c == 'Chervon'){
                                if(awp.size() > 0){
                                    objPO.Freight_Term__c = awp[0].Program__r.Freight_Term__c;
                                }
                            }
                        }
                    }
                }
                Set<String> purchaseOrderIdSet = new Set<String>();
                Set<String> purchasePartsOrderIdSet = new Set<String>();
                if(Trigger.isInsert){
                    for (Purchase_Order__c objPO : (List<Purchase_Order__c>) Trigger.new) {
                        if (objPO.RecordTypeId == CCM_Constants.PURCHASE_ORDER_RECORD_TYPE_PLACE_ORDER_ID) {
                            if(objPO.Status__c == 'Submitted'){
                                purchaseOrderIdSet.add(objPO.Id);
                            }
                        }else{
                            if(objPO.Status__c == 'Submitted'){
                                purchasePartsOrderIdSet.add(objPO.Id);
                            }
                        }
                    }
                    List<Purchase_Order_Item__c> purchaseOrderItemList = new List<Purchase_Order_Item__c>();
                    purchaseOrderItemList =  [SELECT Id,List_Price_Date__c FROM Purchase_Order_Item__c WHERE Purchase_Order__c IN :purchaseOrderIdSet];
                    if(purchaseOrderItemList.size() > 0){
                        for(Purchase_Order_Item__c pi : purchaseOrderItemList){
                            pi.List_Price_Date__c = Date.valueOf(System.today());
                        }
                        update purchaseOrderItemList;
                    }
                }else{
                    for (Purchase_Order__c objPO : (List<Purchase_Order__c>) Trigger.new) {
                        Purchase_Order__c oldPurchaseOrder = (Purchase_Order__c) Trigger.oldMap.get(objPO.Id);
                        if (objPO.RecordTypeId == CCM_Constants.PURCHASE_ORDER_RECORD_TYPE_PLACE_ORDER_ID) {
                            if(objPO.Status__c == 'Submitted' && objPO.Status__c != oldPurchaseOrder.Status__c){
                                purchaseOrderIdSet.add(objPO.Id);
                            }
                        }else{
                            if(objPO.Status__c == 'Submitted' && objPO.Status__c != oldPurchaseOrder.Status__c){
                                purchasePartsOrderIdSet.add(objPO.Id);
                            }
                        }
                    }
                    if(purchaseOrderIdSet.size() > 0){
                        CCM_SelectListPriceAndDateBatch selectListPriceBatch = new CCM_SelectListPriceAndDateBatch(purchaseOrderIdSet);
                        Database.executeBatch(selectListPriceBatch,2);
                    }
                }

                if(purchasePartsOrderIdSet.size() > 0){
                    CCM_SelectListPriceAndDateBatch selectListPriceBatch = new CCM_SelectListPriceAndDateBatch(purchasePartsOrderIdSet);
                    Database.executeBatch(selectListPriceBatch,2);
                }
            }
        }
    }
}
