<template>
    <div class="slds-card" style="max-height: 500px; overflow-y: auto;">
        <div>
            <lightning-button name="new" label="New Weekly Summary" variant="brand" style="position: relative; top:10px; right:-68%;" onclick={directToDetailPageNew}></lightning-button>
        </div>
        <div>
            <h2 style="font-weight: bold">Weekly Summary</h2>
            <div class="slds-grid slds-wrap" style="padding-top: 15px">
                <div class="slds-col slds-size_3-of-12"></div>
                <div class="slds-col slds-size_2-of-12" style="font-weight: bold;">Week</div>
                <div class="slds-col slds-size_7-of-12" style="font-weight: bold;">Summary</div>
                <template for:each={weeklySummarys} for:item="summary">
                    <div key={summary.id} class="slds-col slds-size_3-of-12" style="display:flex; line-height: 40px">
                        <template if:true={summary.showView}>
                            <lightning-icon style="margin-right:5px" class="iconpoint" icon-name="action:preview" alternative-text="View" title="View" size='xx-small' data-recordid={summary.id} onclick={directToDetailPageView}></lightning-icon>
                        </template>
                        <template if:true={summary.showEdit}>
                            <lightning-icon style="margin-right:5px" class="iconpoint" icon-name="action:new_note" alternative-text="Edit" title="Edit" size='xx-small' data-recordid={summary.id} onclick={directToDetailPageEdit}></lightning-icon>
                        </template>
                    </div>
                    <div key={summary.id} class="slds-col slds-size_2-of-12" style="line-height: 40px;">{summary.week}</div>
                    <div key={summary.id} class="slds-col slds-size_7-of-12" style="line-height: 40px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{summary.weeklyHighlights}</div>
                </template>    
            </div>
        </div>
        <div class="slds-m-top_small slds-m-bottom_medium slds-align_absolute-center">
            <span class="ViewAll" onclick={directToList}>View All</span>
        </div>
    </div>
</template>