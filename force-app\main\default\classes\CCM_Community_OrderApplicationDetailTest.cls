@isTest
private class CCM_Community_OrderApplicationDetailTest {
    @testSetup static void testSetup() {
        CCM_DealerLocationUpdateOfOrderHandler.isRun = false;
        CCM_LeadAutoAssignmentHandler.isRun = false;
        CCM_Lead_Validation_Rules_Handler.isRun = false;
        CCM_SyncSalesProgramInfo.isRun = false;
        CCM_SyncDelearNumToCampaign.isRun = false;
        Account_SyncToShopify obj = new Account_SyncToShopify();
        Account_SyncToShopify.functionName.add('Account');
        CCM_SharingUtil.isSharingOnly = true;
        CCM_SharePurchaseOrdersToAddressOwner.isRun = false;
        CCM_FillExpectedDeliveryDateHandler.boolToRun = false;
        CCM_AddressMergeAgencies.isRun = false;
        CCM_AddressAccessHandler.isRun = false;
        CCM_AddressAccessPlusHandler.isRun = false;
        CCM_AddressBatchHandler.isRun = false;
        CCM_DataSharingForAddressOwner.isRun = false;
        CCM_AddressStateValidation.isRun = false;
        CCM_PoASGHandler.isRun = false;
        CCM_SharePurchaseOrdersToAddressOwner.isRun = false;
        CCM_FillExpectedDeliveryDateHandler.boolToRun = false;
        Account acc = new Account( Name = 'Community');
        acc.Name = 'testacc01';
        acc.AccountNumber = '14355';
        acc.TaxID__c = 'test';
        acc.PaymentMethod__c = 'CHECK';
        acc.RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Account').get('Channel');
        insert acc;

        Agency__c ag = new Agency__c();
        ag.Name = 'TESTAG01';
        ag = (Agency__c)CCM_TESTDataUtil.createSobject(ag, '');
        //Purchase Order
        Purchase_Order__c po = new Purchase_Order__c();
        po.Sales_Agency__c = ag.Id;
        po.Customer__c = acc.Id;
        po.Status__c = 'Submitted';
        po.Sync_Status__c = 'In Processing';
        po.Expected_Delivery_Date__c = Date.today();
        po.Freight_Fee__c = 10;
        po.Product_Price__c = 100000;
        po.Freight_Term__c = 'COLLECT';
        po.Payment_Term__c = 'NA001';
        po = (Purchase_Order__c)CCM_TESTDataUtil.createSobject(po, 'Place_Order');

        Purchase_Order__c po1 = new Purchase_Order__c();
        po1.Customer__c = acc.id;
        po1.Product_Price__c = 100000;
        po1.Is_Alternative_Address__c = true;
        po1 = (Purchase_Order__c)CCM_TESTDataUtil.createSobject(po1,'Place_Order');

        Purchase_Order_Item__c poItem1 = new Purchase_Order_Item__c();
        poItem1.Purchase_Order__c = po1.Id;
        poItem1 = (Purchase_Order_Item__c)CCM_TESTDataUtil.createSobject(poItem1,'');

        Purchase_Order_Item__c poItem2 = new Purchase_Order_Item__c();
        poItem2.Purchase_Order__c = po.Id;
        poItem2 = (Purchase_Order_Item__c)CCM_TESTDataUtil.createSobject(poItem2,'');


        Lead theLead = new Lead(
            LastName = 'TestLead001',
            Company = 'TestCompanyName',
            User_Type__c = 'Enterprise',
            Invoicing_Method__c = 'MAIL',
            Email = '<EMAIL>',
            Credit_Limit__c = '1',
            Street = 'test street',
            City = 'New York',
            State = 'NY',
            Country = 'US',
            Sales_Group__c = 'SG01',
            PostalCode = '11111',
            Intended_Brand__c = 'EGO',
            Distributor_or_Dealer__c = 'Direct Dealer'
        );
        insert theLead;

        Contact con =  Test_SalesData.createContact(theLead.Id, null);
        CCM_ShippingAddressSendApproval.isRun = false;
        Account_Address__c address = new Account_Address__c();
        address.Approval_Status__c = 'Approved';
        address.Customer__c = acc.Id;
        address = (Account_Address__c)CCM_TESTDataUtil.createSobject(address, 'Dropship_Shipping_Address');

        Address_With_Program__c awp = new Address_With_Program__c();
        awp.Account_Address__c = address.Id;
        awp = (Address_With_Program__c)CCM_TESTDataUtil.createSobject(awp,'');

        Order ord = new Order(
            EffectiveDate = Date.today(),
            Status = 'Draft',
            BillTo__c = awp.Id,
            ShipTo__c = awp.Id,
            Purchase_Order__c = po.Id,
            Name = 'testord01',
            Customer__c = con.Id,
            AccountId = acc.Id,
            Sales_Agency__c = ag.Id,
            RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Order').get('Actived_Order')
        );
        insert ord;

        // Order_Item__c oItem = new Order_Item__c();
        // oItem.Order__c = ord.Id;
        // oItem = (Order_Item__c)CCM_TESTDataUtil.createSobject(oItem,'');

        Shipment__c ship = new Shipment__c();
        ship.Order__c = ord.Id;
        ship = (Shipment__c)CCM_TESTDataUtil.createSobject(ship,'');

        Shipment_Item__c shipItem = new Shipment_Item__c();
        shipItem.Shipment__c = ship.Id;
        shipItem = (Shipment_Item__c)CCM_TESTDataUtil.createSobject(shipItem,'');

        Invoice__c inv = new Invoice__c();
        inv.Order__c = ord.Id;
        inv = (Invoice__c)CCM_TESTDataUtil.createSobject(inv,'');
    }
    static testMethod void testMethod1() {
        test.startTest();
        Purchase_Order__c po01 = [SELECT Id FROM Purchase_Order__c LIMIT 1];
        Order ord01 = [SELECT Id FROM Order LIMIT 1];
        CCM_Community_OrderApplicationDetailCtl.getData(po01.Id);
        CCM_Community_OrderApplicationDetailCtl.getData(ord01.Id);
        CCM_Community_OrderApplicationDetailCtl.sendEmail(po01.Id);
        CCM_Community_OrderApplicationDetailCtl.OrderData od1 = new CCM_Community_OrderApplicationDetailCtl.OrderData();
        od1.customerCurrency = '100';
        List<CCM_Community_OrderApplicationDetailCtl.OrderItemData> cod2 = new List<CCM_Community_OrderApplicationDetailCtl.OrderItemData>();
        CCM_Community_OrderApplicationDetailCtl.OrderItemData oid1 = new CCM_Community_OrderApplicationDetailCtl.OrderItemData();
        oid1.listPrice = 111;
        cod2.add(oid1);
        CCM_Community_OrderApplicationDetailCtl.saveData(po01.Id, JSON.serialize(od1), JSON.serialize(cod2));
        CCM_Community_OrderApplicationDetailCtl.calculateFreightFee(JSON.serialize(od1), JSON.serialize(cod2));
        CCM_Community_OrderApplicationDetailCtl.ShipmentItemData osd = new CCM_Community_OrderApplicationDetailCtl.ShipmentItemData();
        osd.itemQtyInOrder = 11;
        osd.itemQtyInShipment = 10;
        osd.upcCode = '001';
        osd.productName = 'test';
        osd.productModelNum = '100';
        CCM_Community_OrderApplicationDetailCtl.InvoiceData invd = new CCM_Community_OrderApplicationDetailCtl.InvoiceData();
        invd.invoiceNum = '100';
        CCM_Community_OrderApplicationDetailCtl.syncToEBS(po01.Id);
        test.stopTest();
    }

    static testmethod void testgetObjectName() {
        test.startTest();
        Purchase_Order__c po01 = [SELECT Id FROM Purchase_Order__c LIMIT 1];
        CCM_Community_OrderApplicationDetailCtl.getObjectName(po01.Id);
        test.stopTest();
    }

    static testMethod void testMethod2() {
        test.startTest();
        Purchase_Order__c po01 = [SELECT Id FROM Purchase_Order__c LIMIT 1];
        po01.Is_Alternative_Address__c = true;
        po01.ORG_ID__c = 'CCA';
        PO01.Is_Delegate__c = true;
        Order ord01 = [SELECT Id FROM Order LIMIT 1];
        CCM_Community_OrderApplicationDetailCtl.getData(po01.Id);
        CCM_Community_OrderApplicationDetailCtl.getData(ord01.Id);
        CCM_Community_OrderApplicationDetailCtl.OrderData od1 = new CCM_Community_OrderApplicationDetailCtl.OrderData();
        od1.customerCurrency = '100';
        od1.isAlternativeAddress = true;
        od1.orgCode = 'CNA';
        od1.productPrice = 3000.00;
        od1.freightTargetFee = 50.00;
        od1.additionalShipAddressStreet = 'testStreet';
        od1.additionalShipAddressCity = 'testCity';
        od1.additionalShipAddressCountry = 'testCountry';
        od1.additionalShipAddressProvince = 'testProvince';
        od1.additionalShipAddressPostCode = 'testCode';
        od1.additionalContactName = 'Test Contact';
        od1.additionalContactPhone = '12345678911';
        od1.additionalContactEmail = '<EMAIL>';
        List<CCM_Community_OrderApplicationDetailCtl.OrderItemData> cod2 = new List<CCM_Community_OrderApplicationDetailCtl.OrderItemData>();
        CCM_Community_OrderApplicationDetailCtl.OrderItemData oid1 = new CCM_Community_OrderApplicationDetailCtl.OrderItemData();
        oid1.listPrice = 111;
        cod2.add(oid1);
        CCM_Community_OrderApplicationDetailCtl.saveData(po01.Id, JSON.serialize(od1), JSON.serialize(cod2));
        CCM_Community_OrderApplicationDetailCtl.calculateFreightFee(JSON.serialize(od1), JSON.serialize(cod2));
        CCM_Community_OrderApplicationDetailCtl.ShipmentItemData osd = new CCM_Community_OrderApplicationDetailCtl.ShipmentItemData();
        osd.itemQtyInOrder = 11;
        osd.itemQtyInShipment = 10;
        osd.upcCode = '001';
        osd.productName = 'test';
        osd.productModelNum = '100';
        CCM_Community_OrderApplicationDetailCtl.InvoiceData invd = new CCM_Community_OrderApplicationDetailCtl.InvoiceData();
        invd.invoiceNum = '100';
        CCM_Community_OrderApplicationDetailCtl.syncToEBS(po01.Id);
        test.stopTest();
    }

    @IsTest
    static void testMethod3(){
        Test.startTest();
        CCM_SharingUtil.isSharingOnly = true;
        Purchase_Order__c po01 = [SELECT Id FROM Purchase_Order__c LIMIT 1];
        po01.ORG_ID__c = 'CCA';
        po01.Is_Alternative_Address__c = true;
        po01.Additional_Shipping_Province__c = 'ON';
        update po01;
        Order ord01 = [SELECT Id FROM Order LIMIT 1];
        CCM_Community_OrderApplicationDetailCtl.getPurchasePartsOrder(po01.Id);
        CCM_Community_OrderApplicationDetailCtl.getOrderPartsInfo(ord01.Id);

        Account acc = new Account( Name = 'Community');
        acc.Name = 'testacc01';
        acc.AccountNumber = '14355';
        acc.TaxID__c = 'test';
        acc.PaymentMethod__c = 'CHECK';
        acc.RecordTypeId = CCM_Contants.CHANNEL_RECORDTYPEID;
        insert acc;

        Purchase_Order__c po = new Purchase_Order__c();
        po.Order_Type__c = 'CNA Sales Order - USD';
        po.Customer__c = acc.Id;
        po.Status__c = 'Submitted';
        po.Sync_Status__c = 'In Processing';
        po.Expected_Delivery_Date__c = Date.today();
        po.Freight_Term__c = 'COLLECT';
        po.Payment_Term__c = 'NA001';
        po.Freight_Fee__c = 10;
        po.Product_Price__c = 100;
        po.Freight_Fee_Waived__c = 10;
        po.Extra_Freight_Fee_To_Be_Waived__c = 10;
        po.Handling_Fee__c = 10;
        po.Discount_Amount__c = 10;
        po.Surcharge_Amount__c = 10;
        po.Is_Alternative_Address__c = true;
        po.Additional_Shipping_Province__c = 'MB';
        insert po;

        Purchase_Order_Item__c poItem1 = new Purchase_Order_Item__c();
        poItem1.Purchase_Order__c = po.Id;
        insert poItem1;
        CCM_Community_OrderApplicationDetailCtl.caculateTotalTax(po);
        po.Additional_Shipping_Province__c = 'XYZ';
        CCM_Community_OrderApplicationDetailCtl.caculateTotalTax(po);

        Test.stopTest();
    }
}
