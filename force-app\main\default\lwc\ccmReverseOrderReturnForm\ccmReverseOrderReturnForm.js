import { LightningElement, track, wire } from 'lwc';
import { CurrentPageReference } from 'lightning/navigation';
import getReverseOrderInfo from "@salesforce/apex/CCM_ReverseOrderInfoCtl.getReverseOrderInfo";
import Reverse_Order_Caution from '@salesforce/resourceUrl/Reverse_Order_Caution';

// 导入 Custom Label
import CCM_Portal_PrintSaveAsPDF from "@salesforce/label/c.CCM_Portal_PrintSaveAsPDF";
import CCM_Portal_ReturnForm from "@salesforce/label/c.CCM_Portal_ReturnForm";
import CCM_Portal_IncludecopyofReturnFormineachcarton from "@salesforce/label/c.CCM_Portal_IncludecopyofReturnFormineachcarton";
import CCM_Portal_RequestNumber from "@salesforce/label/c.CCM_Portal_RequestNumber";
import CCM_Portal_ReturnFormDate from "@salesforce/label/c.CCM_Portal_ReturnFormDate";
import CCM_Portal_CustomerCode from "@salesforce/label/c.CCM_Portal_CustomerCode";
import CCM_Portal_CustomerName from "@salesforce/label/c.CCM_Portal_CustomerName";
import CCM_Portal_BillTo from "@salesforce/label/c.CCM_Portal_BillTo";
import CCM_Portal_ASNNumber from "@salesforce/label/c.CCM_Portal_ASNNumber";
import CCM_Portal_CustomerPO from "@salesforce/label/c.CCM_Portal_CustomerPO";
import CCM_Portal_ShipFrom from "@salesforce/label/c.CCM_Portal_ShipFrom";
import CCM_Portal_SHIPPINGINSTRUCTIONS from "@salesforce/label/c.CCM_Portal_SHIPPINGINSTRUCTIONS";
import CCM_Portal_PleaseincludeacopyofthisRGAineachpackagesent from "@salesforce/label/c.CCM_Portal_PleaseincludeacopyofthisRGAineachpackagesent";
import CCM_Portal_ShipTo from "@salesforce/label/c.CCM_Portal_ShipTo";
import CCM_Portal_Line from "@salesforce/label/c.CCM_Portal_Line";
import CCM_Portal_Model from "@salesforce/label/c.CCM_Portal_Model";
import CCM_Portal_Description from "@salesforce/label/c.CCM_Portal_Description";
import CCM_Portal_Brand from "@salesforce/label/c.CCM_Portal_Brand";
import CCM_Portal_Unit from "@salesforce/label/c.CCM_Portal_Unit";
import CCM_Portal_Quantity from "@salesforce/label/c.CCM_Portal_Quantity";
import CCM_Portal_DamagedinShipment from "@salesforce/label/c.CCM_Portal_DamagedinShipment";
import CCM_Portal_Damagedinshipmentproducts from "@salesforce/label/c.CCM_Portal_Damagedinshipmentproducts";
import CCM_Portal_OverageProductsnotorderedbutreceived from "@salesforce/label/c.CCM_Portal_OverageProductsnotorderedbutreceived";
import CCM_Portal_pickUpTips from "@salesforce/label/c.CCM_Portal_pickUpTips";
import CCM_Portal_sendrequestTips from "@salesforce/label/c.CCM_Portal_sendrequestTips";
import CCM_Portal_productpalletTips from "@salesforce/label/c.CCM_Portal_productpalletTips";


export default class CcmReverseOrderReturnForm extends LightningElement {
    // 定义 label
    @track label = {
        CCM_Portal_PrintSaveAsPDF,
        CCM_Portal_ReturnForm,
        CCM_Portal_IncludecopyofReturnFormineachcarton,
        CCM_Portal_RequestNumber,
        CCM_Portal_ReturnFormDate,
        CCM_Portal_CustomerCode,
        CCM_Portal_CustomerName,
        CCM_Portal_BillTo,
        CCM_Portal_ASNNumber,
        CCM_Portal_CustomerPO,
        CCM_Portal_ShipFrom,
        CCM_Portal_SHIPPINGINSTRUCTIONS,
        CCM_Portal_PleaseincludeacopyofthisRGAineachpackagesent,
        CCM_Portal_ShipTo,
        CCM_Portal_Line,
        CCM_Portal_Model,
        CCM_Portal_Description,
        CCM_Portal_Brand,
        CCM_Portal_Unit,
        CCM_Portal_Quantity,
        CCM_Portal_DamagedinShipment,
        CCM_Portal_Damagedinshipmentproducts,
        CCM_Portal_OverageProductsnotorderedbutreceived,
        CCM_Portal_pickUpTips,
        CCM_Portal_sendrequestTips,
        CCM_Portal_productpalletTips
    };
    cautionImgUrl = Reverse_Order_Caution;

    requeatReason_damaged = this.label.CCM_Portal_DamagedinShipment;

    recordId = '';
    @track
    reverseOrder = {};
    @track
    selectedBillingAddress = {};
    @track
    selectedShippingAddress = {};
    @track
    itemsReturn = [];
    @track
    showASNNumber = false;

    @track
    itemsReturnMap = new Map();

    contactEmail = '';

    @wire(CurrentPageReference)
    setCurrentPageReference(currentPageReference) {
        if (currentPageReference.state) {
            const _state = currentPageReference.state;
            if (_state.c__reverseorderid) {
                // eslint-disable-next-line @lwc/lwc/no-api-reassignments
                this.recordId = _state.c__reverseorderid;
            }
        }
    }

    get isNA() {
        if(this.reverseOrder) {
            if(this.reverseOrder.orgCode == 'CNA') {
                return true;
            }
            return false;
        }
        return true;
    }

    connectedCallback() {
        if (!this.connected) {

            this.connected = true;

            if (this.recordId) {
                getReverseOrderInfo({ reverseOrderId: this.recordId }).then(result => {
                    console.log(result);
                    if (result.isSuccess) {
                        document.title = `Return Form${this.label.CCM_Portal_Unit} ${result.returnData.requestNumber}`;
                        this.reverseOrder = result.returnData;
                        if(this.reverseOrder.orgCode == 'CCA') {
                            this.contactEmail = '<EMAIL>';
                        }
                        else {
                            this.contactEmail = '<EMAIL>.<NAME_EMAIL>';
                        }
                        this.orderNumber = this.reverseOrder.originalOrderNumberInEBS;
                        this.customerName = this.reverseOrder.customerName;
                        this.customerNumber = this.reverseOrder.customerNumber;
                        this.customerId = result.returnData.customerId;
                        this.orderId = result.returnData.orderId;
                        this.selectedRequestReason = result.returnData.externalReturnReason;
                        this.approvalStatus = result.returnData.approvalStatus;
                        this.reverseOrderRequestStatus = result.returnData.reverseOrderRequestStatus;
                        this.isAlternativeAddress = result.returnData.IsAlternativeAddress;
                        this.returnFreightFee = result.returnData.returnFreightFee;
                        this.selectedOPSResult = result.returnData.opsResult;
                        this.opsRemark = result.returnData.opsRemark;
                        this.customerContactEmail = result.returnData.customerContactEmail;
                        this.createdById = result.returnData.createdById;
                        this.insideSalesId = result.returnData.insideSalesId;
                        if (this.reverseOrder.externalReturnReason == 'Damaged in Shipment' || this.reverseOrder.externalReturnReason == 'Customer Refusal - No reason provided') {
                            this.showASNNumber = this.reverseOrder.returnOrderNumberInEBS != '' && this.reverseOrder.returnOrderNumberInEBS != null;
                        }
                        
                        console.log(this.showASNNumber);
                        if (this.isAlternativeAddress) {
                            this.alternativeShipping.street = result.returnData.shippingAddress.address1;
                            this.alternativeShipping.city = result.returnData.shippingAddress.city;
                            this.alternativeShipping.state = result.returnData.shippingAddress.state;
                            this.alternativeShipping.country = result.returnData.shippingAddress.country;
                            this.alternativeShipping.postalCode = result.returnData.shippingAddress.postalCode;
                            this.alternativeShipping.contactEmail = result.returnData.shippingAddress.contactEmail;
                            this.alternativeShipping.contactName = result.returnData.shippingAddress.contactName;
                            this.alternativeShipping.contactPhone = result.returnData.shippingAddress.contactPhone;
                        }

                        if(this.reverseOrder.orgCode == 'CCA') {
                            this.label.CCM_Portal_pickUpTips = this.label.CCM_Portal_pickUpTips.replace('{contactEmail}', '<EMAIL>');
                            this.label.CCM_Portal_sendrequestTips = this.label.CCM_Portal_sendrequestTips.replace('{contactEmail}', '<EMAIL>');
                        }
                        else {
                            this.label.CCM_Portal_pickUpTips = this.label.CCM_Portal_pickUpTips.replace('{contactEmail}', '<EMAIL>.<NAME_EMAIL>');
                            this.label.CCM_Portal_sendrequestTips = this.label.CCM_Portal_sendrequestTips.replace('{contactEmail}', '<EMAIL>.<NAME_EMAIL>');
                        }

                        let productItems = [];
                        if (result.returnData.reverseOrderProductItems && result.returnData.reverseOrderProductItems.length > 0) {
                            this.reverseOrderProductItems = result.returnData.reverseOrderProductItems;
                            productItems = result.returnData.reverseOrderProductItems.map(item => {
                                return {
                                    label: item.itemNumber,
                                    // value: item.recordId,
                                    value: item.itemNumber,
                                    modelNumber: item.modelNumber
                                }
                            })

                        }

                        if (result.returnData.invoiceNumbers && result.returnData.invoiceNumbers.length > 0) {
                            this.invoiceNumbers = result.returnData.invoiceNumbers;
                            this.invoiceNumbers.forEach(item => {
                                item.recordUrl = '/' + item.recordId;
                            })
                        }

                        if (result.returnData.takeItems && result.returnData.takeItems.length > 0) {
                            this.nextStep1 = result.returnData.takeItems[0].nextStepAction;
                            this.itemsTake = result.returnData.takeItems;
                            this.itemsTake.forEach((item, index) => {
                                item.lineNo = index + 1;
                                let _images = result.returnData.itemImages.filter(image => {
                                    return image.itemId === item.recordId;
                                });
                                _images.forEach((image, index) => {
                                    image.lineNo = index + 1;
                                });
                                item.uploadedFiles = _images;
                                item.hasPictures = _images.length > 0;
                            })
                        }

                        if (result.returnData.returnItems && result.returnData.returnItems.length > 0) {
                            this.nextStep2 = result.returnData.returnItems[0].nextStepAction;
                            this.itemsReturn = result.returnData.returnItems;
                            this.itemsReturn.forEach((item, index) => {
                                item.lineNo = index + 1;
                                item.itemNoRequired = this.itemNoRequired;
                                let _images = result.returnData.itemImages.filter(image => {
                                    return image.itemId === item.recordId;
                                });
                                _images.forEach((image, index) => {
                                    image.lineNo = index + 1;
                                });
                                item.uploadedFiles = _images;
                                item.hasPictures = _images.length > 0;
                            })

                            // group items by selling warehouse
                            this.itemsReturnMap = this.groupBy(this.itemsReturn,(item)=>{
                                return item.sellingWarehouse;
                            })
                        }
                        this.reverseOrderAttachments = result.returnData.attachments;
                        this.reverseOrderAttachments.forEach((image, index) => {
                            image.lineNo = index + 1;
                        });
                        this.selectedBillingAddress = result.returnData.billingAddress;
                        this.selectedBillingAddress.street = result.returnData.billingAddress.address1 ? result.returnData.billingAddress.address1 + ' ' : '' + result.returnData.billingAddress.address2 ? result.returnData.billingAddress.address2 : '';
                        this.selectedShippingAddress = result.returnData.shippingAddress;
                        this.selectedShippingAddress.street = result.returnData.shippingAddress.address1 ? result.returnData.shippingAddress.address1 + ' ' : '' + result.returnData.shippingAddress.address2 ? result.returnData.shippingAddress.address2 : '';

                        if (this.selectedRequestReason.toLowerCase() === this.requeatReason_damaged.toLocaleLowerCase()) {
                            this.overageHrader = this.label.CCM_Portal_Damagedinshipmentproducts;
                        } else {
                            this.overageHrader = this.label.CCM_Portal_OverageProductsnotorderedbutreceived;
                        }
                    }
                })
            }
        }
    }
    printMe() {
        let url = window.location.origin + '/apex/CcmReverseOrderReturnFormVF?id=' + this.recordId;
        window.open(url, '_blank');
    }

    groupBy(list,fn){
        let groups = {};
        list.forEach(item=>{
            let key = fn(item);
            let group = groups[key] ? groups[key] : [];
            group.push(item);
            groups[key] = group;
        })

        return Object.keys(groups).map(key=>{
            let obj = {
                warehouse: key,
                data: groups[key] 
            }
            return obj;
        })
    }
}