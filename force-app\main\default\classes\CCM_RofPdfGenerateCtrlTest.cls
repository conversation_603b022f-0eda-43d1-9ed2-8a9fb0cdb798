/**
 * @Author: <PERSON><PERSON>
 * @Description: Test class for CCM_RofPdfGenerateCtrl
 */
@IsTest
public class CCM_RofPdfGenerateCtrlTest {
    @TestSetup
    static void makeData(){
        Test.startTest();
        CCM_SharingUtil.isSharingOnly = true;
        CCM_SalesOrgValidationHandler.isRun = false;
        CCM_SharingUtil.isSharingCalculation = true;
        CCM_ContactUpdateHandler.isRun = false;
        CCM_StopUpdatingPimSellableHandler.boolToRun = false;
        CCM_UpaftCtrl.inFutureContext = true;
        CCM_TaxUpdateOrderItemHandler.boolToRun = false;

        // customer info
        String recordId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName = 'Channel' LIMIT 1].Id;
        Account account = new Account();
        account.Name = 'TestCustomer';
        account.Distributor_or_Dealer__c = '2nd Tier Dealer';
        account.RecordTypeId = recordId;
        account.TaxID__c = 'testaccount';
        account.AccountNumber = '12345';
        account.ORG_Code__c = 'CNA';
        insert account;

        Purchase_Order__c objPO = new Purchase_Order__c(Customer__c = account.Id);
        insert objPO;

        Account_Address__c add = new Account_Address__c();
        add.Customer__c = account.Id;
        add.X2nd_Tier_Dealer__c = account.Id;
        add.Active__c = true;
        add.Approval_Status__c = 'Approved';
        add.Address1__c = 'aaa';
        add.City__c = 'CA';
        add.State__c = 'CA';
        add.Country__c = 'US';
        add.Postal_Code__c = '123123';
        insert add;

        Sales_Program__c salesProgram = new Sales_Program__c();
        salesProgram.Customer__c = account.Id;
        salesProgram.Approval_Status__c = 'Approved';
        //salesProgram.Price_Book__c = pb.Id;
        salesProgram.Authorized_Brand_Name_To_Oracle__c = 'EGO_2020_Test';
        salesProgram.RecordTypeId = CCM_Contants.SALES_STANDARD_PROGRAM_RECORDTYPEID;
        salesProgram.Brands__c = 'EGO';
        salesProgram.ORG_Code__c = 'CNA';
        salesProgram.Payment_Term__c = 'NA001';
        salesProgram.Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.';
        insert salesProgram;

        Address_With_Program__c addPro = new Address_With_Program__c();
        addPro.Account_Address__c = add.Id;
        addPro.Program__c = salesProgram.Id;
        insert addPro;

        //Order Info
        Order o = new Order();
        o.AccountId = account.Id;
        o.Order_Number__c = 'yyy';
        o.Order_Status__c = 'Partial Shipment';
        o.BillTo__c = addPro.Id;
        o.ShipTo__c = addPro.Id;
        o.Purchase_Order__c = objPO.Id;
        //o.Price_Book__c = pb.Id;
        o.PO_Number__c = 'CCC-11111';
        o.Org_Code__c = 'CNA';

        o.Shipping_Country__c = 'US';
        o.Shipping_State__c = 'GA';
        o.Shipping_City__c = 'Alpharetta';
        o.Shipping_Address__c = 'main street';
        o.EffectiveDate = Date.today();
        o.Status = 'Draft';
        insert o;

        //Order Item Info
        Order_Item__c oi = new Order_Item__c();
        oi.Order__c = o.Id;
        oi.Selling_Warehouse__c = '111';
        //insert oi;

        

        //Reverse Order Info
        Reverse_Order_Request__c reverseOrderRequest = new Reverse_Order_Request__c();
        reverseOrderRequest.Customer__c = account.Id;
        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Submitted_Date__c = Date.newInstance(2021, 07, 20);
        reverseOrderRequest.Order__c = o.Id;
        reverseOrderRequest.Customer_Contact_Email__c = '<EMAIL>;<EMAIL>';

        reverseOrderRequest.Name = 'PDFTestReverseOrder';
        reverseOrderRequest.Billing_Address__c = add.Id;
        reverseOrderRequest.Shipping_Address__c = add.Id;
        reverseOrderRequest.Additional_Shipping_Street__c = 'SL';
        reverseOrderRequest.Additional_Shipping_Street2__c = 'YY';
        reverseOrderRequest.Additional_Shipping_Province__c = 'CA';
        reverseOrderRequest.Additional_Shipping_City__c = 'NY';
        reverseOrderRequest.Additional_Shipping_Country__c = 'NA';
        reverseOrderRequest.Additional_Shipping_Postal_Code__c = '111111';
        reverseOrderRequest.External_Return_Reason__c = 'Damaged in Shipment';
        insert reverseOrderRequest;

        //Reverse Order Item Info
        Reverse_Order_Item__c roi = new Reverse_Order_Item__c();
        roi.Reverse_Order_Request__c = reverseOrderRequest.Id;
        roi.Order_Product_Type__c = 'Overage';
        roi.Brand__c = 'EGO';
        insert roi;

    }

    @IsTest
    static void CCM_RofPdfGenerateCtrlTest() {
        Reverse_Order_Request__c ror = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];

        PageReference pageRef = Page.CcmReverseOrderReturnFormVF;
        Test.setCurrentPage(pageRef);
        ApexPages.Standardcontroller sc = new ApexPages.Standardcontroller(ror);
        ApexPages.currentPage().getParameters().put('Id',ror.id);

        Test.StartTest();
        CCM_RofPdfGenerateCtrl pdfCtl = new CCM_RofPdfGenerateCtrl();
        //CCM_RofPdfGenerateCtrl.generateROFPdf(ror.Id);
        Test.stopTest();
    }
    @IsTest
    static void CCM_RofPdfGenerateCtrlTest2() {
        Reverse_Order_Request__c ror = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];

        PageReference pageRef = Page.CcmReverseOrderReturnFormVF;
        Test.setCurrentPage(pageRef);
        ApexPages.Standardcontroller sc = new ApexPages.Standardcontroller(ror);
        ApexPages.currentPage().getParameters().put('Id',ror.id);

        Test.StartTest();
        //CCM_RofPdfGenerateCtrl pdfCtl = new CCM_RofPdfGenerateCtrl();
        CCM_RofPdfGenerateCtrl.generateROFPdf(ror.Id);
        Test.stopTest();
    }
    
    @IsTest
    static void CCM_RofPdfGenerateCtrlTest3() {
        Reverse_Order_Request__c ror = [SELECT Id FROM Reverse_Order_Request__c LIMIT 1];
		ror.Is_Alternative_Address__c = true;
        update ror;
        
        PageReference pageRef = Page.CcmReverseOrderReturnFormVF;
        Test.setCurrentPage(pageRef);
        ApexPages.Standardcontroller sc = new ApexPages.Standardcontroller(ror);
        ApexPages.currentPage().getParameters().put('Id',ror.id);

        Test.StartTest();
        CCM_RofPdfGenerateCtrl pdfCtl = new CCM_RofPdfGenerateCtrl();
        Test.stopTest();
    }
}