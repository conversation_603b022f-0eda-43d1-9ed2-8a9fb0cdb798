<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Tip</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Help_Text__c</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Basic Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Address1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Address2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>City__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>State__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Country__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Postal_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Store_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Address_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>x2nd_Tier_Dealer_for_Sales__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>X2nd_Tier_Dealer__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ORG_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Address1_In_French__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Address2_In_French__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Customer__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Active__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EGO_Agency__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FLEX_Agency__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SKIL_SKILSAW_Agency__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Store_Location__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Shipping Contacts</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Contact__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Contact2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Contact4__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Contact3__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Contact5__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Approval_Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Sales_Agencies__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sales_Director__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Sales_Managers__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Custom Links</label>
        <layoutColumns />
        <layoutColumns />
        <layoutColumns />
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account_Address__c.New_Store_Location</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Submit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Clone</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedLists>
        <fields>CreatedDate</fields>
        <fields>StepStatus</fields>
        <fields>OriginalActor</fields>
        <fields>Actor</fields>
        <fields>Comments</fields>
        <relatedList>RelatedProcessHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Address_With_Program__c.Account_Address__c</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>TASK.WHAT_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>TASK.CLOSED</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>Activity.Account_Address__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Warranty_Return_Claim__c.Billing_Address__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Warranty_Return_Claim__c.Shipping_Address__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Warranty_Return_Claim__c.Store_Address__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Warranty_Return_Claim__c.Store_Location__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Warranty_Return_Claim__c.Warehouse_Location__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h0h00000bWLq7</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>