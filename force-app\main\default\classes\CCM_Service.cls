/**
 * @description       :
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 07-04-2024
 * @last modified by  : <EMAIL>
**/
global without sharing class CCM_Service {
    //created by 0702
    public static String pushCustInfo(List<String> quoteIdlist) {
        List<String> paramStrlist = new List<String>();
        String querystr =
            'select ORG_Code__c, Customer_SF_ID__c,AccountNumber,' +
            'Customer_Cluster__c,Customer_Sub_Cluster__c,' +
            'Ship_Complete__c,Shipment_Priority__c,' +
            'Sales_Channel__c,Name,Invoicing_Method__c,Risk_Code__c,' +
            'Customer_Oracle_ID__c,Status__c,Credit_Limit__c,CurrencyIsoCode ' +
            'from Account ' +
            'where id in :quoteIdlist';

        String endPointName = 'chervon_odata_uat';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String endpoint = MeIS.End_Point__c + Label.CCM_pushCustomerInfo;
        String HeaderKey = 'Basic ' + MeIS.Key__c;

        List<SObject> sObjlist = Database.query(querystr);
        for (SObject obj : sObjlist) {
            Map<String, String> paramMap = new Map<String, String>();
            Account acc = (Account) obj;
            System.debug(LoggingLevel.INFO, '*** acc.Id: ' + acc.Id);
            paramMap.put(
                'CUSTOMER_HEADER_ID',
                'Salesforce-' +
                acc.Id +
                '-' +
                system.now().getTime()
            );
            paramMap.put('Customer_SF_ID__c', acc.Customer_SF_ID__c);
            paramMap.put('Customer_Oracle_ID__c', acc.Customer_Oracle_ID__c);
            paramMap.put('AccountNumber', acc.AccountNumber);
            paramMap.put('Customer_Cluster__c', acc.Customer_Cluster__c);
            paramMap.put(
                'Customer_Sub_Cluster__c',
                acc.Customer_Sub_Cluster__c
            );
            paramMap.put('Ship_Complete__c', acc.Ship_Complete__c);
            paramMap.put('Shipment_Priority__c', acc.Shipment_Priority__c);
            paramMap.put('Sales_Channel__c', acc.Sales_Channel__c);
            paramMap.put('Name', acc.Name);
            paramMap.put(
                'Cerdit__c',
                acc.Credit_Limit__c == null
                    ? '0'
                    : String.valueOf(acc.Credit_Limit__c)
            );
            if( acc.Credit_Limit__c == 'N/A'){
                paramMap.put('Cerdit__c','');
            }
            paramMap.put('Risk_Code__c', acc.Risk_Code__c);
            paramMap.put('Account_Type__c', 'External');
            paramMap.put('Currency', acc.CurrencyIsoCode);
            paramMap.put('Invoicing_Method__c', acc.Invoicing_Method__c);
            paramMap.put('Status__c', acc.Status__c);
            paramMap.put('Attribute1', '');
            paramMap.put('Attribute2', '');
            paramMap.put('Attribute3', '');
            paramMap.put('Attribute4', '');
            paramMap.put('Attribute5', '');
            paramMap.put('Attribute6', '');
            paramMap.put('Attribute7', '');
            paramMap.put('Attribute8', '');
            paramMap.put('Attribute9', '');
            paramMap.put('Attribute10', '');
            String paramStr = Json.serialize(paramMap);
            paramStrlist.add(paramStr);


        }
        CCM_ServiceCallout.pushCallout(paramStrlist, endpoint,'POST',HeaderKey,false);
        return null;
    }

    public static String pushAddressInfo(List<String> quoteIdlist) {
        List<String> approveMaplist = new List<String>();
        approveMaplist.add('');
        approveMaplist.add('Approved');
        List<String> paramStrlist = new List<String>();
        String querystr =
            'select Id,Customer_Line_SF_ID__c,Customer_Line_Oracle_ID__c,' +
            'Account_Address__r.Customer__r.Customer_SF_ID__c,' +
            'Account_Address__r.Customer__r.Customer_Oracle_ID__c,' +
            'Account_Address__r.Customer__r.Name,Account_Address__r.Email_for_Invoicing__c,' +
            'Account_Address__r.Customer__r.Distributor_or_Dealer__c,Account_Address__r.Name,' +
            'Account_Address__r.ORG_ID__c,Account_Address__r.RecordType.DeveloperName,' +
            'Account_Address__r.Primary__c,Account_Address__r.Store_Number__c,' +
            'Program__r.Sales_Group__c,Account_Address__r.Sales_Rep__c,' +
            'Account_Address__r.Address1__c,Account_Address__r.Address2__c,' +
            'Account_Address__r.City__c,Account_Address__r.State__c,' +
            'Account_Address__r.Postal_Code__c,Account_Address__r.Country__c,' +
            'Account_Address__r.Ship_VIA__c,Program__r.Brands__c,' +
            'Program__r.Payment_Term__c,Program__r.Freight_Term__c,Program__r.Order_Type__c,' +
            'Program__r.Trade_Discount__c,Program__r.Warehouse_Discount__c,Program__r.Price_Book__c,Program__r.Price_Book__r.Name,' +
            'Program__r.Authorized_Brand_Name_To_Oracle__c,' +
            'Program__r.Payment_Discount__c,Program__r.Co_Op_Discount__c,' +
            'Program__r.Volume_Rebate__c,Program__r.Growth_Rebate__c,' +
            'Program__r.Freight_Allowance__c,Program__r.Discount1__c,' +
            'Program__r.Discount2__c,Program__r.Discount3__c,Special_Dropship_Address__c,' +
            'Program__r.New_Store_Allowance__c,Status__c,Account_Address__r.Sales_Agency__c,' +
            'Account_Address__r.Sales_Agency__r.Code__c, Account_Address__r.Sales_Agency__r.Alias_Formula__c ' +
            'from Address_With_Program__c where id in :quoteIdlist ' +
            'And Account_Address__r.Approval_Status__c in :approveMaplist ' +
            'And Program__r.Approval_Status__c in :approveMaplist';

        String endPointName = 'chervon_odata_uat';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String endpoint = MeIS.End_Point__c + Label.CCM_pushAddressInfo;
        String HeaderKey = 'Basic ' + MeIS.Key__c;

        List<SObject> sObjlist = Database.query(querystr);
        Map<String, String> DropShipFreigtTermMap = Util.getDropShipFreightTermMap2();
        Map<String, String> specialDropShipFreigtTermMap = Util.getSpecialDropShipFreightTermMap();
        Map<String, String> specialDropShipPricebookMap = Util.getSpecialDropShipPricebookName();
        for (SObject obj : sObjlist) {
            Map<String, String> paramMap = new Map<String, String>();
            Address_With_Program__c address = (Address_With_Program__c) obj;
            System.debug(LoggingLevel.INFO, '*** address.Id: ' + address.Id);
            paramMap.put(
                'CUSTOMER_LINE_ID',
                'Salesforce-' +
                address.Id +
                '-' +
                system.now().getTime()
            );
            paramMap.put(
                'Customer_Line_SF_ID__c',
                address.Customer_Line_SF_ID__c
            );
            paramMap.put(
                'Customer_Line_Oracle_ID__c',
                address.Customer_Line_Oracle_ID__c
            );
            paramMap.put(
                'Customer_SF_ID__c',
                address.Account_Address__r.Customer__r.Customer_SF_ID__c
            );
            paramMap.put(
                'Customer_Oracle_ID__c',
                address.Account_Address__r.Customer__r.Customer_Oracle_ID__c
            );
            paramMap.put('Region__c', address.Account_Address__r.ORG_ID__c);
            String billFlag = 'false';
            String shipFlag = 'false';
            String b = address.Program__r.Brands__c == null
                ? ''
                : address.Program__r.Brands__c;
            String add1 = '';
            String shipmethod = '';
            if (
                address.Account_Address__r.RecordType.DeveloperName ==
                'Billing_Address'
            ) {
                billFlag = 'true';
                //add1 = b + '-' + address.Account_Address__r.Customer__r.Name;
                add1 = address.Program__r.Authorized_Brand_Name_To_Oracle__c;
                if (
                    address.Account_Address__r.Customer__r.Distributor_or_Dealer__c ==
                    'Distributor' ||
                    address.Account_Address__r.Customer__r.Distributor_or_Dealer__c ==
                    'Co-op Distributor'
                ) {
                    add1 = add1 + ' warehouse';
                }
                shipmethod = 'Ground Freight';
            }
            if (
                address.Account_Address__r.RecordType.DeveloperName ==
                'Shipping_Address'
            ) {
                shipFlag = 'true';
                add1 = address.Account_Address__r.Name;
            }
            if (
                address.Account_Address__r.RecordType.DeveloperName ==
                'Dropship_Billing_Address'
            ) {
                billFlag = 'true';
                add1 = address.Program__r.Authorized_Brand_Name_To_Oracle__c;
                //add1 = b + '-' + address.Account_Address__r.Customer__r.Name;
                add1 = add1 + ' store & others';
                shipmethod = 'Ground Freight';
            }
            if (
                address.Account_Address__r.RecordType.DeveloperName ==
                'Dropship_Shipping_Address'
            ) {
                shipFlag = 'true';
                add1 = address.Account_Address__r.Name;
            }
            paramMap.put('BILLTO_FLAG__C', billFlag);
            paramMap.put('SHIPTO_FLAG__C', shipFlag);
            paramMap.put(
                'Primary__c',
                String.valueOf(address.Account_Address__r.Primary__c)
            );
            paramMap.put(
                'Email_for_Invoice__c',
                address.Account_Address__r.Email_for_Invoicing__c
            );
            paramMap.put(
                'Store_Number__c',
                address.Account_Address__r.Store_Number__c
            );
            paramMap.put('Sales_Group__c', address.Program__r.Sales_Group__c);
            //update by nick ********
            if (address.Account_Address__r.Sales_Agency__c != null && address.Account_Address__r.Sales_Agency__r.Code__c != 'No Sales Credit') {
                paramMap.put('Sales_Rep__c', address.Account_Address__r.Sales_Agency__r.Code__c);
            } else {
                paramMap.put('Sales_Rep__c', null);
            }

            paramMap.put('Address1__c', add1);
            paramMap.put('Address2__c', address.Account_Address__r.Address1__c);
            paramMap.put('Address3__c', address.Account_Address__r.Address2__c);
            paramMap.put('City__c', address.Account_Address__r.City__c);
            paramMap.put('State__c', '');
            if (address.Account_Address__r.ORG_ID__c != 'CCA') {
                paramMap.put('State__c', address.Account_Address__r.State__c);
            }
            paramMap.put('Postal_Code__c', address.Account_Address__r.Postal_Code__c);
            paramMap.put('Country__c', address.Account_Address__r.Country__c);
            paramMap.put('Ship_VIA__c', shipmethod);
            String brandName = address.Program__r.Brands__c;
            if (String.isNotBlank(brandName)) {
                brandName = brandName.toUpperCase();
            }
            paramMap.put('Brand__c', brandName);
            paramMap.put('Payment_Term__c', address.Program__r.Payment_Term__c);
            String fTerm = address.Program__r.Freight_Term__c;
            if (
                address.Account_Address__r.RecordType.DeveloperName ==
                'Dropship_Billing_Address'
            ) {
                //update by nick ********:special dropship will get price list and freight term from "Price And Terms Reference"
                if (address.Special_Dropship_Address__c == true) {
                    fTerm = specialDropShipFreigtTermMap.get(address.Id) != null
                        ? specialDropShipFreigtTermMap.get(address.Id)
                        : fTerm;
                } else {
                    fTerm = DropShipFreigtTermMap.get(brandName) != null
                        ? DropShipFreigtTermMap.get(brandName)
                        : fTerm;
                }
            }
            paramMap.put('Freight_Term__c', String.isEmpty(fTerm) ? '' : fTerm);
            paramMap.put('Order_Type__c', address.Program__r.Order_Type__c);
            String pricelist = '';
            if (address.Program__r.Price_Book__c == null) {
                pricelist =
                    address.Account_Address__r.Customer__r.Name + ' Price List';
            } else {
                pricelist = address.Program__r.Price_Book__r.Name;
            }
            if (
                address.Account_Address__r.RecordType.DeveloperName ==
                'Dropship_Billing_Address'
            ) {
                if (address.Special_Dropship_Address__c == true) {
                    //update by nick ********: special dripship will get price book from 'Price And Terms Reference' which Special_Dropship_Address__c is true
                    pricelist = specialDropShipPricebookMap.get(address.Id);
                } else {
                    pricelist = Util.getDropShipPricebookName(brandName, address.Account_Address__r.ORG_ID__c);
                }
            }
            paramMap.put('Price_List__c', pricelist);
            paramMap.put(
                'Trade_Discount__c',
                address.Program__r.Trade_Discount__c == null
                    ? '0'
                    : String.valueOf(address.Program__r.Trade_Discount__c)
            );
            paramMap.put(
                'Warehouse_Discount__c',
                address.Program__r.Warehouse_Discount__c == null
                    ? '0'
                    : String.valueOf(address.Program__r.Warehouse_Discount__c)
            );
            paramMap.put(
                'Payment_Discount__c',
                address.Program__r.Payment_Discount__c == null
                    ? '0'
                    : String.valueOf(address.Program__r.Payment_Discount__c)
            );
            paramMap.put(
                'Co_Op_Discount__c',
                address.Program__r.Co_Op_Discount__c == null
                    ? '0'
                    : String.valueOf(address.Program__r.Co_Op_Discount__c)
            );
            paramMap.put(
                'Volume_Rebate__c',
                address.Program__r.Volume_Rebate__c == null
                    ? '0'
                    : String.valueOf(address.Program__r.Volume_Rebate__c)
            );
            paramMap.put(
                'Growth_Rebate__c',
                address.Program__r.Growth_Rebate__c == null
                    ? '0'
                    : String.valueOf(address.Program__r.Growth_Rebate__c)
            );
            paramMap.put(
                'Freight_Allowance__c',
                address.Program__r.Freight_Allowance__c == null
                    ? '0'
                    : String.valueOf(address.Program__r.Freight_Allowance__c)
            );
            paramMap.put(
                'Discount1__c',
                address.Program__r.Discount1__c == null
                    ? '0'
                    : String.valueOf(address.Program__r.Discount1__c)
            );
            paramMap.put(
                'Discount2__c',
                address.Program__r.Discount2__c == null
                    ? '0'
                    : String.valueOf(address.Program__r.Discount2__c)
            );
            paramMap.put(
                'Discount3__c',
                address.Program__r.Discount3__c == null
                    ? '0'
                    : String.valueOf(address.Program__r.Discount3__c)
            );
            paramMap.put(
                'New_Store_Allowance__c',
                String.valueOf(address.Program__r.New_Store_Allowance__c)
            );
            paramMap.put('Status__c', address.Status__c);

            paramMap.put('Attribute1', '');
            if (address.Account_Address__r.ORG_ID__c == 'CCA') {
                paramMap.put('Attribute1', address.Account_Address__r.State__c);
            }
            paramMap.put('Attribute2', '');
            paramMap.put('Attribute3', '');
            paramMap.put('Attribute4', '');
            paramMap.put('Attribute5', '');
            paramMap.put('Attribute6', '');
            paramMap.put('Attribute7', '');
            paramMap.put('Attribute8', '');
            paramMap.put('Attribute9', '');
            paramMap.put('Attribute10', '');
            String paramStr = Json.serialize(paramMap);
            paramStrlist.add(paramStr);
        }
        System.debug(LoggingLevel.INFO, '*** endpoint: ' + endpoint);
        CCM_ServiceCallout.pushCallout(
            paramStrlist,
            endpoint,
            'POST',
            HeaderKey,
            false
        );
        return null;
    }

    public static String pushContactInfo(List<String> quoteIdlist) {
        List<String> paramStrlist = new List<String>();
        String querystr =
            'select Contact_SF_ID__c,Status__c,' +
            'Contact_Oracle_ID__c,Contact__r.FirstName,' +
            'Contact__r.LastName,Contact__r.MobilePhone,' +
            'Contact__r.Phone,Contact__r.Fax,Contact__r.Email,' +
            'Address_With_Program__r.Customer_Line_SF_ID__c,' +
            'Address_With_Program__r.Customer_Line_Oracle_ID__c ' +
            'from Address_Program_with_Contact__c ' +
            'where Id in :quoteIdlist And Contact__c != null';

        String endPointName = 'chervon_odata_uat';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String endpoint = MeIS.End_Point__c + Label.CCM_pushContactInfo;
        String HeaderKey = 'Basic ' + MeIS.Key__c;

        List<SObject> sObjlist = Database.query(querystr);
        for (SObject obj : sObjlist) {
            Map<String, String> paramMap = new Map<String, String>();
            Address_Program_with_Contact__c con = (Address_Program_with_Contact__c) obj;
            System.debug(
                LoggingLevel.INFO,
                '*** Address_Program_with_Contact__c.Id: ' + con.Id
            );
            paramMap.put(
                'CONTACT_ID',
                'Salesforce-' +
                con.Id +
                '-' +
                system.now().getTime()
            );
            paramMap.put('Contact_SF_ID__c', con.Contact_SF_ID__c);
            paramMap.put('Contact_Oracle_ID__c', con.Contact_Oracle_ID__c);
            paramMap.put(
                'Customer_Line_SF_ID__c',
                con.Address_With_Program__r.Customer_Line_SF_ID__c
            );
            paramMap.put(
                'Customer_Line_Oracle_ID_c',
                con.Address_With_Program__r.Customer_Line_Oracle_ID__c
            );
            paramMap.put('FirstName', con.Contact__r.FirstName);
            paramMap.put('LastName', con.Contact__r.LastName);
            paramMap.put('MobilePhone', con.Contact__r.MobilePhone);
            paramMap.put('Phone', con.Contact__r.Phone);
            paramMap.put('Fax', con.Contact__r.Fax);
            paramMap.put('Email', con.Contact__r.Email);
            paramMap.put('Status__c', con.Status__c);
            paramMap.put('Attrubute1', '');
            paramMap.put('Attrubute2', '');
            paramMap.put('Attrubute3', '');
            paramMap.put('Attrubute4', '');
            paramMap.put('Attrubute5', '');
            paramMap.put('Attrubute6', '');
            paramMap.put('Attrubute7', '');
            paramMap.put('Attrubute8', '');
            paramMap.put('Attrubute9', '');
            paramMap.put('Attrubute10', '');
            String paramStr = Json.serialize(paramMap);
            paramStrlist.add(paramStr);
        }
        CCM_ServiceCallout.pushCallout(
            paramStrlist,
            endpoint,
            'POST',
            HeaderKey,
            false
        );
        return null;
    }

    public static String pushCustomerInfoBundle(
        String quoteId,
        Boolean onlyinsert
    ) {
        List<String> approveMaplist = new List<String>();
        approveMaplist.add('');
        approveMaplist.add('Approved');
        List<String> paramStrlist = new List<String>();
        Map<String, Object> paramMap = new Map<String, Object>();
        List<Map<String, String>> accMaplist = new List<Map<String, String>>();
        List<Map<String, String>> addMaplist = new List<Map<String, String>>();
        List<Map<String, String>> conMaplist = new List<Map<String, String>>();

        String endPointName = 'chervon_odata_uat';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String endpoint = MeIS.End_Point__c + Label.CCM_pushCustomerBundleInfo;
        String HeaderKey = 'Basic ' + MeIS.Key__c;

        String accquerystr =
            'select Customer_SF_ID__c,AccountNumber,' +
            'Customer_Cluster__c,Customer_Sub_Cluster__c,' +
            'Ship_Complete__c,Shipment_Priority__c,' +
            'Sales_Channel__c,Name,Invoicing_Method__c,Risk_Code__c,' +
            'Customer_Oracle_ID__c,Status__c,Credit_Limit__c,CurrencyIsoCode ' +
            'from Account ' +
            'where id = :quoteId';

        /*if(onlyinsert){
            accquerystr = accquerystr + ' And Customer_Oracle_ID__c = null';
        }*/
        List<Account> acclist = Database.query(accquerystr);
        if (acclist.size() > 0) {
            Map<String, String> accMap = new Map<String, String>();
            Account acc = acclist.get(0);
            paramMap.put('customerId', acc.Customer_SF_ID__c);
            accMap.put(
                'CUSTOMER_HEADER_ID',
                'Salesforce-' +
                acc.Id +
                '-' +
                system.now().getTime()
            );
            accMap.put('Customer_SF_ID__c', acc.Customer_SF_ID__c);
            accMap.put('Customer_Oracle_ID__c', acc.Customer_Oracle_ID__c);
            accMap.put('AccountNumber', acc.AccountNumber);
            accMap.put('Customer_Cluster__c', acc.Customer_Cluster__c);
            accMap.put('Customer_Sub_Cluster__c', acc.Customer_Sub_Cluster__c);
            accMap.put('Ship_Complete__c', acc.Ship_Complete__c);
            accMap.put('Shipment_Priority__c', acc.Shipment_Priority__c);
            accMap.put('Sales_Channel__c', acc.Sales_Channel__c);
            accMap.put('Name', acc.Name);
            accMap.put(
                'Cerdit__c',
                acc.Credit_Limit__c == null
                    ? '0'
                    : String.valueOf(acc.Credit_Limit__c)
            );
            if( acc.Credit_Limit__c == 'N/A'){
                paramMap.put('Cerdit__c','');
            }
            accMap.put('Risk_Code__c', acc.Risk_Code__c);
            accMap.put('Account_Type__c', 'External');
            accMap.put('Currency', acc.CurrencyIsoCode);
            accMap.put('Invoicing_Method__c', acc.Invoicing_Method__c);
            accMap.put('Status__c', acc.Status__c);
            accMaplist.add(accMap);

            String addquerystr =
                'select Id,Customer_Line_SF_ID__c,Customer_Line_Oracle_ID__c,' +
                'Account_Address__r.Customer__r.Customer_SF_ID__c,' +
                'Account_Address__r.Customer__r.Customer_Oracle_ID__c,' +
                'Account_Address__r.Customer__r.Name,Account_Address__r.Email_for_Invoicing__c,' +
                'Account_Address__r.Customer__r.Distributor_or_Dealer__c,Account_Address__r.Name,' +
                'Account_Address__r.ORG_ID__c,Account_Address__r.RecordType.DeveloperName,' +
                'Account_Address__r.Primary__c,Account_Address__r.Store_Number__c,' +
                'Program__r.Sales_Group__c,Account_Address__r.Sales_Rep__c,' +
                'Account_Address__r.Address1__c,Account_Address__r.Address2__c,' +
                'Account_Address__r.City__c,Account_Address__r.State__c,' +
                'Account_Address__r.Postal_Code__c,Account_Address__r.Country__c,' +
                'Account_Address__r.Ship_VIA__c,Program__r.Brands__c,' +
                'Program__r.Payment_Term__c,Program__r.Freight_Term__c,Program__r.Order_Type__c,' +
                'Account_Address__r.Sales_Agency__r.Code__c,' +
                'Program__r.Trade_Discount__c,Program__r.Warehouse_Discount__c,Program__r.Price_Book__c,Program__r.Price_Book__r.Name,' +
                'Program__r.Payment_Discount__c,Program__r.Co_Op_Discount__c,' +
                'Program__r.Volume_Rebate__c,Program__r.Growth_Rebate__c,' +
                'Program__r.Freight_Allowance__c,Program__r.Discount1__c,' +
                'Program__r.Authorized_Brand_Name_To_Oracle__c,' +
                'Program__r.Discount2__c,Program__r.Discount3__c,Special_Dropship_Address__c,' +
                'Program__r.New_Store_Allowance__c,Status__c ' +
                'from Address_With_Program__c where Account_Address__r.Customer__c = :quoteId ' +
                'And Account_Address__r.Approval_Status__c in :approveMaplist ' +
                'And Program__r.Approval_Status__c in :approveMaplist';
            if (onlyinsert) {
                addquerystr =
                    addquerystr + ' And Customer_Line_Oracle_ID__c = null';
            }
            List<Address_With_Program__c> addlist = Database.query(addquerystr);
            System.debug(LoggingLevel.INFO, '*** addlist: ' + addlist);
            System.debug(LoggingLevel.INFO, '*** addquerystr: ' + addquerystr);
            List<Id> addIdlist = new List<Id>();
            Map<String, String> DropShipFreigtTermMap = Util.getDropShipFreightTermMap2();
            Map<String, String> specialDropShipFreigtTermMap = Util.getSpecialDropShipFreightTermMap();
            Map<String, String> specialDropShipPricebookMap = Util.getSpecialDropShipPricebookName();
            for (Address_With_Program__c address : addlist) {
                addIdlist.add(address.Id);
                Map<String, String> addMap = new Map<String, String>();
                addMap.put(
                    'CUSTOMER_LINE_ID',
                    'Salesforce-' +
                    address.Id +
                    '-' +
                    system.now().getTime()
                );
                addMap.put(
                    'Customer_Line_SF_ID__c',
                    address.Customer_Line_SF_ID__c
                );
                addMap.put(
                    'Customer_Line_Oracle_ID__c',
                    address.Customer_Line_Oracle_ID__c
                );
                addMap.put(
                    'Customer_SF_ID__c',
                    address.Account_Address__r.Customer__r.Customer_SF_ID__c
                );
                addMap.put(
                    'Customer_Oracle_ID__c',
                    address.Account_Address__r.Customer__r.Customer_Oracle_ID__c
                );
                addMap.put('Region__c', address.Account_Address__r.ORG_ID__c);
                String billFlag = 'false';
                String shipFlag = 'false';
                String b = address.Program__r.Brands__c == null
                    ? ''
                    : address.Program__r.Brands__c;
                String add1 = '';
                String shipmethod = '';
                if (
                    address.Account_Address__r.RecordType.DeveloperName ==
                    'Billing_Address'
                ) {
                    billFlag = 'true';
                    //add1 = b + '-' + address.Account_Address__r.Customer__r.Name;
                    add1 = address.Program__r.Authorized_Brand_Name_To_Oracle__c;
                    if (
                        address.Account_Address__r.Customer__r.Distributor_or_Dealer__c ==
                        'Distributor' ||
                        address.Account_Address__r.Customer__r.Distributor_or_Dealer__c ==
                        'Co-op Distributor'
                    ) {
                        add1 = add1 + ' warehouse';
                    }
                    shipmethod = 'Ground Freight';
                }
                if (
                    address.Account_Address__r.RecordType.DeveloperName ==
                    'Shipping_Address'
                ) {
                    shipFlag = 'true';
                    add1 = address.Account_Address__r.Name;
                }
                if (
                    address.Account_Address__r.RecordType.DeveloperName ==
                    'Dropship_Billing_Address'
                ) {
                    billFlag = 'true';
                    //add1 = b + '-' + address.Account_Address__r.Customer__r.Name;
                    add1 = address.Program__r.Authorized_Brand_Name_To_Oracle__c;
                    add1 = add1 + ' store & others';
                    shipmethod = 'Ground Freight';
                }
                if (
                    address.Account_Address__r.RecordType.DeveloperName ==
                    'Dropship_Shipping_Address'
                ) {
                    shipFlag = 'true';
                    add1 = address.Account_Address__r.Name;
                }
                addMap.put('BILLTO_FLAG__C', billFlag);
                addMap.put('SHIPTO_FLAG__C', shipFlag);
                addMap.put(
                    'Primary__c',
                    String.valueOf(address.Account_Address__r.Primary__c)
                );
                addMap.put(
                    'Email_for_Invoice__c',
                    address.Account_Address__r.Email_for_Invoicing__c
                );
                addMap.put(
                    'Store_Number__c',
                    address.Account_Address__r.Store_Number__c
                );
                addMap.put('Sales_Group__c', address.Program__r.Sales_Group__c);
                addMap.put(
                    'Sales_Rep__c',
                    address.Account_Address__r.Sales_Agency__r.Code__c
                );
                addMap.put('Address1__c', add1);
                addMap.put(
                    'Address2__c',
                    address.Account_Address__r.Address1__c
                );
                addMap.put(
                    'Address3__c',
                    address.Account_Address__r.Address2__c
                );
                addMap.put('City__c', address.Account_Address__r.City__c);
                addMap.put('State__c', address.Account_Address__r.State__c);
                addMap.put(
                    'Postal_Code__c',
                    address.Account_Address__r.Postal_Code__c
                );
                addMap.put('Country__c', address.Account_Address__r.Country__c);
                addMap.put('Ship_VIA__c', shipmethod);
                String brandName = address.Program__r.Brands__c;
                if (String.isNotBlank(brandName)) {
                    brandName = brandName.toUpperCase();
                }
                addMap.put('Brand__c', brandName);
                addMap.put(
                    'Payment_Term__c',
                    address.Program__r.Payment_Term__c
                );
                String fTerm = address.Program__r.Freight_Term__c;
                if (
                    address.Account_Address__r.RecordType.DeveloperName ==
                    'Dropship_Billing_Address'
                ) {
                    //update by nick ********:special dropship will get price list and freight term from "Price And Terms Reference"
                    if (address.Special_Dropship_Address__c == true) {
                        fTerm = specialDropShipFreigtTermMap.get(address.Id) !=
                            null
                            ? specialDropShipFreigtTermMap.get(address.Id)
                            : fTerm;
                    } else {
                        fTerm = DropShipFreigtTermMap.get(brandName) != null
                            ? DropShipFreigtTermMap.get(brandName)
                            : fTerm;
                    }
                }
                addMap.put('Freight_Term__c', fTerm);
                addMap.put('Order_Type__c', address.Program__r.Order_Type__c);
                String pricelist = '';
                if (address.Program__r.Price_Book__c == null) {
                    pricelist =
                        address.Account_Address__r.Customer__r.Name +
                        ' Price List';
                } else {
                    pricelist = address.Program__r.Price_Book__r.Name;
                }
                if (
                    address.Account_Address__r.RecordType.DeveloperName ==
                    'Dropship_Billing_Address'
                ) {
                    if (address.Special_Dropship_Address__c == true) {
                        //update by nick ********: special dripship will get price book from 'Price And Terms Reference' which Special_Dropship_Address__c is true
                        pricelist = specialDropShipPricebookMap.get(address.Id);
                    } else {
                        pricelist = Util.getDropShipPricebookName(brandName, address.Account_Address__r.ORG_ID__c);
                    }
                }
                addMap.put('Price_List__c', pricelist);
                addMap.put(
                    'Trade_Discount__c',
                    address.Program__r.Trade_Discount__c == null
                        ? '0'
                        : String.valueOf(address.Program__r.Trade_Discount__c)
                );
                addMap.put(
                    'Warehouse_Discount__c',
                    address.Program__r.Warehouse_Discount__c == null
                        ? '0'
                        : String.valueOf(
                              address.Program__r.Warehouse_Discount__c
                          )
                );
                addMap.put(
                    'Payment_Discount__c',
                    address.Program__r.Payment_Discount__c == null
                        ? '0'
                        : String.valueOf(address.Program__r.Payment_Discount__c)
                );
                addMap.put(
                    'Co_Op_Discount__c',
                    address.Program__r.Co_Op_Discount__c == null
                        ? '0'
                        : String.valueOf(address.Program__r.Co_Op_Discount__c)
                );
                addMap.put(
                    'Volume_Rebate__c',
                    address.Program__r.Volume_Rebate__c == null
                        ? '0'
                        : String.valueOf(address.Program__r.Volume_Rebate__c)
                );
                addMap.put(
                    'Growth_Rebate__c',
                    address.Program__r.Growth_Rebate__c == null
                        ? '0'
                        : String.valueOf(address.Program__r.Growth_Rebate__c)
                );
                addMap.put(
                    'Freight_Allowance__c',
                    address.Program__r.Freight_Allowance__c == null
                        ? '0'
                        : String.valueOf(
                              address.Program__r.Freight_Allowance__c
                          )
                );
                addMap.put(
                    'Discount1__c',
                    address.Program__r.Discount1__c == null
                        ? '0'
                        : String.valueOf(address.Program__r.Discount1__c)
                );
                addMap.put(
                    'Discount2__c',
                    address.Program__r.Discount2__c == null
                        ? '0'
                        : String.valueOf(address.Program__r.Discount2__c)
                );
                addMap.put(
                    'Discount3__c',
                    address.Program__r.Discount3__c == null
                        ? '0'
                        : String.valueOf(address.Program__r.Discount3__c)
                );
                addMap.put(
                    'New_Store_Allowance__c',
                    String.valueOf(address.Program__r.New_Store_Allowance__c)
                );
                addMap.put('Status__c', address.Status__c);
                addMaplist.add(addMap);
            }
            System.debug(
                LoggingLevel.INFO,
                '*** addMaplist: ' + JSON.serialize(addMaplist)
            );
            if (addIdlist.size() > 0) {
                String conquerystr =
                    'select Contact_SF_ID__c,Status__c,' +
                    'Contact_Oracle_ID__c,Contact__r.FirstName,' +
                    'Contact__r.LastName,Contact__r.MobilePhone,' +
                    'Contact__r.Phone,Contact__r.Fax,Contact__r.Email,' +
                    'Address_With_Program__r.Customer_Line_SF_ID__c,' +
                    'Address_With_Program__r.Customer_Line_Oracle_ID__c ' +
                    'from Address_Program_with_Contact__c ' +
                    'where Address_With_Program__c in :addIdlist And Contact__c != null';
                if (onlyinsert) {
                    conquerystr =
                        conquerystr + ' And Contact_Oracle_ID__c = null';
                }
                List<Address_Program_with_Contact__c> conlist = Database.query(
                    conquerystr
                );
                for (Address_Program_with_Contact__c con : conlist) {
                    Map<String, String> conMap = new Map<String, String>();
                    conMap.put(
                        'CONTACT_ID',
                        'Salesforce-' +
                        con.Id +
                        '-' +
                        system.now().getTime()
                    );
                    conMap.put('Contact_SF_ID__c', con.Contact_SF_ID__c);
                    conMap.put(
                        'Contact_Oracle_ID__c',
                        con.Contact_Oracle_ID__c
                    );
                    conMap.put(
                        'Customer_Line_SF_ID__c',
                        con.Address_With_Program__r.Customer_Line_SF_ID__c
                    );
                    conMap.put(
                        'Customer_Line_Oracle_ID_c',
                        con.Address_With_Program__r.Customer_Line_Oracle_ID__c
                    );
                    conMap.put('FirstName', con.Contact__r.FirstName);
                    conMap.put('LastName', con.Contact__r.LastName);
                    conMap.put('MobilePhone', con.Contact__r.MobilePhone);
                    conMap.put('Phone', con.Contact__r.Phone);
                    conMap.put('Fax', con.Contact__r.Fax);
                    conMap.put('Email', con.Contact__r.Email);
                    conMap.put('Status__c', con.Status__c);
                    conMaplist.add(conMap);
                }
            }
        }
        if (onlyinsert) {
            accMaplist = new List<Map<String, String>>();
        }
        paramMap.put('CustomerInfo', accMaplist);
        paramMap.put('AddressInfo', addMaplist);
        paramMap.put('ContactInfo', conMaplist);
        String paramStr = Json.serialize(paramMap);
        paramStrlist.add(paramStr);
        Boolean emailFlag = true;
        if (onlyinsert) {
            emailFlag = false;
        }
        //System.debug(LoggingLevel.INFO, '*** paramStrlist: ' + Json.serialize(paramStrlist));
        System.debug(LoggingLevel.INFO, '*** endpoint: ' + endpoint);
        CCM_ServiceCallout.pushCallout(
            paramStrlist,
            endpoint,
            'POST',
            HeaderKey,
            emailFlag
        );
        return null;
    }

    //StageII
    //get kit info
    public static String getKitFromPIM() {
        String endPoint = 'http://*************:1512/rest/V1.0/list/Article/byAssortment?assortment=\'SFDC_Kits_NA\'&pageSize=-1&fields=';
        String fieldsName = 'Article.SupplierAID,ArticleLang.DescriptionShort(9,1),ArticleLang.Name(9,1)';
        fieldsName += ',ArticleLang.DescriptionLong(9,1)';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Height(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Width(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Depth(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.GrossWeightInclPackage(imperial,unit_132919738582802)';
        fieldsName += ',ArticleAttributeValue.Value("Tool Height (inch)",9,DEFAULT),ArticleAttributeValue.Value("Tool Length (inch)",9,DEFAULT)';
        fieldsName += ',ArticleAttributeValue.Value("Tool Weight (pound)",9,DEFAULT),ArticleAttributeValue.Value("Tool Width (inch)",9,DEFAULT)';
        fieldsName += ',Article.ManufactureType';
        fieldsName += ',ArticlePackagingDetails.WeightUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.HeightUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.WidthUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.DepthUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.GrossWeightInclPackagingUnit(imperial,unit_132919738582802)';
        fieldsName += ',Article.CurrentStatus,Article.LastModified';
        fieldsName += ',Article.MaterialNumber,Article.SellableFlag';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,Unit_523181549830005)';
        fieldsName += ',ArticleLang.DescriptionShort(12,1)';//Add by vince 20240704
        endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        String endPointName = 'chervon_pim';
        // MeIntegration_Setting__mdt MeIS = [
        //     SELECT End_Point__c, Key__c
        //     FROM MeIntegration_Setting__mdt
        //     WHERE DeveloperName = :endPointName
        //     LIMIT 1
        // ];
        // String headerToken = MeIS.Key__c;
        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    // get kit info
    public static String getComboKitFromPIM() {
        String endPoint = 'http://*************:1512/rest/V1.0/list/Article/byAssortment?assortment=\'SFDC_ComboKit_NA\'&pageSize=-1&fields=';
        String fieldsName = 'Article.SupplierAID,ArticleLang.DescriptionShort(9,1),ArticleLang.Name(9,1)';
        fieldsName += ',ArticleLang.DescriptionLong(9,1)';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Height(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Width(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Depth(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.GrossWeightInclPackage(imperial,unit_132919738582802)';
        fieldsName += ',ArticleAttributeValue.Value("Tool Height (inch)",9,DEFAULT),ArticleAttributeValue.Value("Tool Length (inch)",9,DEFAULT)';
        fieldsName += ',ArticleAttributeValue.Value("Tool Weight (pound)",9,DEFAULT),ArticleAttributeValue.Value("Tool Width (inch)",9,DEFAULT)';
        fieldsName += ',Article.ManufactureType';
        fieldsName += ',ArticlePackagingDetails.WeightUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.HeightUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.WidthUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.DepthUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.GrossWeightInclPackagingUnit(imperial,unit_132919738582802)';
        fieldsName += ',Article.CurrentStatus,Article.LastModified';
        fieldsName += ',Article.MaterialNumber,Article.SellableFlag';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,Unit_523181549830005)';
        fieldsName += ',ArticleLang.DescriptionShort(12,1)';//Add by vince 20240704
        endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        String endPointName = 'chervon_pim';
        // MeIntegration_Setting__mdt MeIS = [
        //     SELECT End_Point__c, Key__c
        //     FROM MeIntegration_Setting__mdt
        //     WHERE DeveloperName = :endPointName
        //     LIMIT 1
        // ];
        // String headerToken = MeIS.Key__c;
        // String HeaderKey = 'Basic ' + headerToken;
        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    //get product info
    public static String getProductFromPIM() {
        String endPoint = 'http://*************:1512/rest/V1.0/list/Article/byAssortment?assortment=\'SFDC_Models_NA\'&pageSize=-1&fields=';
        String fieldsName = 'Article.SupplierAID,ArticleLang.DescriptionShort(9,1),ArticleLang.Name(9,1)';
        fieldsName += ',ArticleLang.DescriptionLong(9,1)';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Height(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Width(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Depth(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.GrossWeightInclPackage(imperial,unit_132919738582802)';
        fieldsName += ',ArticleAttributeValue.Value("Tool Height (inch)",9,DEFAULT),ArticleAttributeValue.Value("Tool Length (inch)",9,DEFAULT)';
        fieldsName += ',ArticleAttributeValue.Value("Tool Weight (pound)",9,DEFAULT),ArticleAttributeValue.Value("Tool Width (inch)",9,DEFAULT)';
        fieldsName += ',Article.ManufactureType';
        fieldsName += ',ArticlePackagingDetails.WeightUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.HeightUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.WidthUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.DepthUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.GrossWeightInclPackagingUnit(imperial,unit_132919738582802)';
        fieldsName += ',Article.CurrentStatus,Article.LastModified';
        fieldsName += ',Article.MaterialNumber,Article.SellableFlag';
        endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        String endPointName = 'chervon_pim';
        // MeIntegration_Setting__mdt MeIS = [
        //     SELECT End_Point__c, Key__c
        //     FROM MeIntegration_Setting__mdt
        //     WHERE DeveloperName = :endPointName
        //     LIMIT 1
        // ];
        // String headerToken = MeIS.Key__c;
        // String HeaderKey = 'Basic ' + headerToken;
        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    // get product info
    public static String getAccessoriesProductFromPIM() {
        String endPoint = 'http://*************:1512/rest/V1.0/list/Article/byAssortment?assortment=\'SFDC_Accessories_NA\'&pageSize=-1&fields=';
        String fieldsName = 'Article.SupplierAID,ArticleLang.DescriptionShort(9,1),ArticleLang.Name(9,1)';
        fieldsName += ',ArticleLang.DescriptionLong(9,1)';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Height(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Width(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Depth(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.GrossWeightInclPackage(imperial,unit_132919738582802)';
        fieldsName += ',ArticleAttributeValue.Value("Tool Height (inch)",9,DEFAULT),ArticleAttributeValue.Value("Tool Length (inch)",9,DEFAULT)';
        fieldsName += ',ArticleAttributeValue.Value("Tool Weight (pound)",9,DEFAULT),ArticleAttributeValue.Value("Tool Width (inch)",9,DEFAULT)';
        fieldsName += ',Article.ManufactureType';
        fieldsName += ',ArticlePackagingDetails.WeightUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.HeightUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.WidthUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.DepthUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.GrossWeightInclPackagingUnit(imperial,unit_132919738582802)';
        fieldsName += ',Article.CurrentStatus,Article.LastModified';
        fieldsName += ',Article.MaterialNumber,Article.SellableFlag';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,Unit_523181549830005)';
        //Yanko
        fieldsName += ',ArticleAdditionalFields1.MOQ';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,IPSP)';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,IPKB)';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,IPCB)';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,IPC)';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,IPK)';
        //Yanko
        fieldsName += ',ArticleLang.DescriptionShort(12,1)';//Add by vince 20240704
        endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        String endPointName = 'chervon_pim';
        // MeIntegration_Setting__mdt MeIS = [
        //     SELECT End_Point__c, Key__c
        //     FROM MeIntegration_Setting__mdt
        //     WHERE DeveloperName = :endPointName
        //     LIMIT 1
        // ];
        // String headerToken = MeIS.Key__c;
        // String HeaderKey = 'Basic ' + headerToken;
        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    // get product BrandSeries info
    public static String getBrandSeriesFromPIM() {
        String endPoint = 'http://*************:1512/rest/V1.0/list/Article/ArticleAdditionalFields2/byAssortment?assortment=1403&pageSize=-1&fields=ArticleAdditionalFields2.PLMBrandSeries';
        String endPointName = 'chervon_pim';
        String credential = getPIMCredential(endPointName);
        String Headerkey = 'Basic ' + Encodingutil.base64Encode(Blob.valueOf('pwc_rest:123$%^'));
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(null, endPoint, 'GET', HeaderKey);
        return res.getBody();
    }

    // get product info
    public static String getToolsOnlyProductFromPIM() {
        String endPoint = 'http://*************:1512/rest/V1.0/list/Article/byAssortment?assortment=%27ArticleAssortment_1656526334004070%27&pageSize=-1&fields=';
        String fieldsName = 'Article.SupplierAID,ArticleLang.DescriptionShort(9,1),ArticleLang.Name(9,1)';
        fieldsName += ',ArticleLang.DescriptionLong(9,1)';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Height(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Width(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.Depth(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.GrossWeightInclPackage(imperial,unit_132919738582802)';
        fieldsName += ',ArticleAttributeValue.Value("Tool Height (inch)",9,DEFAULT),ArticleAttributeValue.Value("Tool Length (inch)",9,DEFAULT)';
        fieldsName += ',ArticleAttributeValue.Value("Tool Weight (pound)",9,DEFAULT),ArticleAttributeValue.Value("Tool Width (inch)",9,DEFAULT)';
        fieldsName += ',Article.ManufactureType';
        fieldsName += ',ArticlePackagingDetails.WeightUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.HeightUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.WidthUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.DepthUnit(imperial,unit_132919738582802)';
        fieldsName += ',ArticlePackagingDetails.GrossWeightInclPackagingUnit(imperial,unit_132919738582802)';
        fieldsName += ',Article.CurrentStatus,Article.LastModified';
        fieldsName += ',Article.MaterialNumber,Article.SellableFlag';
        fieldsName += ',ArticlePackagingDetails.Quantity(imperial,Unit_523181549830005)';
        fieldsName += ',ArticleLang.DescriptionShort(12,1)';//Add by vince 20240704
        endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        String endPointName = 'chervon_pim';
        // MeIntegration_Setting__mdt MeIS = [
        //     SELECT End_Point__c, Key__c
        //     FROM MeIntegration_Setting__mdt
        //     WHERE DeveloperName = :endPointName
        //     LIMIT 1
        // ];
        // String headerToken = MeIS.Key__c;
        // String HeaderKey = 'Basic ' + headerToken;
        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    // get parts info
    public static String getPartFromPIM(
        String startIndex,
        String pageSize,
        Boolean doInit
    ) {
        // SFDC Spareparts NA => 304
        String endPoint =
            'http://*************:1512/rest/V1.0/list/Article/byAssortment?assortment=304&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'Article.SupplierAID,ArticleLang.DescriptionShort(9,1),ArticleLang.Name(9,1)';
            fieldsName += ',ArticleLang.DescriptionLong(9,1),Article.ManufactureType';
            fieldsName += ',ArticleAttributeValue.Value("Wearing Parts",9,DEFAULT),ArticleAttributeValue.Value("Warranty Period for Wearing Parts (Commercial Purpose)",9,DEFAULT),ArticleAttributeValue.Value("Warranty Period for Wearing Parts (Residential Purpose)",9,DEFAULT)';
            fieldsName += ',Article.SellableFlag';
            fieldsName += ',ArticleLang.DescriptionShort(12,1)';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        String endPointName = 'chervon_pim';
        // MeIntegration_Setting__mdt MeIS = [
        //     SELECT End_Point__c, Key__c
        //     FROM MeIntegration_Setting__mdt
        //     WHERE DeveloperName = :endPointName
        //     LIMIT 1
        // ];
        // String headerToken = MeIS.Key__c;
        // String HeaderKey = 'Basic ' + headerToken;
        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    //get the relationship between kit and product
    public static String getKitsAndProducts(
        String startIndex,
        String pageSize,
        Boolean doInit
    ) {
        String endPoint =
            'http://*************:1512/rest/V1.0/list/Article/ArticleComponent/byAssortment?assortment=\'SFDC_Kits_NA\'&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleComponent.RefArticleIdentifier,ArticleComponent.Quantity';
            fieldsName += ',ArticleComponent.DisplayOrder';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        String endPointName = 'chervon_pim';

        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    // get relationship between kit and product
    public static String getComboKitsAndProducts(
        String startIndex,
        String pageSize,
        Boolean doInit
    ) {
        String endPoint =
            'http://*************:1512/rest/V1.0/list/Article/ArticleComponent/byAssortment?assortment=\'SFDC_ComboKit_NA\'&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleComponent.RefArticleIdentifier,ArticleComponent.Quantity';
            fieldsName += ',ArticleComponent.DisplayOrder';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        String endPointName = 'chervon_pim';

        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    //get the relationship between product and part
    public static String getProductsAndParts(
        String startIndex,
        String pageSize,
        Boolean doInit
    ) {
        String endPoint =
            'http://*************:1512/rest/V1.0/list/Article/ArticleReference/byAssortment?assortment=\'SFDC_Models_NA\'&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleReference.Type,ArticleReference.ReferencedArticle';
            fieldsName += ',ArticleReference.Quantity,ArticleReference.ExplosionID';
            fieldsName += ',ArticleReference.Repairable,ArticleReference.EstimatedRepairTime';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        String endPointName = 'chervon_pim';

        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    //get the relationship between product and part
    public static String getAccessoriesAndParts(
        String startIndex,
        String pageSize,
        Boolean doInit
    ) {
        // SFDC_Accessories_NA => 502
        String endPoint =
            'http://*************:1512/rest/V1.0/list/Article/ArticleReference/byAssortment?assortment=502&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleReference.Type,ArticleReference.ReferencedArticle';
            fieldsName += ',ArticleReference.Quantity,ArticleReference.ExplosionID';
            fieldsName += ',ArticleReference.Repairable,ArticleReference.EstimatedRepairTime';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        String endPointName = 'chervon_pim';

        String credential = getPIMCredential(endPointName);
       String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    // get the relationship product and part
    public static String getToolsOnlyAndParts(
        String startIndex,
        String pageSize,
        Boolean doInit
    ) {
        // SFDC_ToolsOnly_NA => 1403
        String endPoint =
            'http://*************:1512/rest/V1.0/list/Article/ArticleReference/byAssortment?assortment=1403&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleReference.Type,ArticleReference.ReferencedArticle';
            fieldsName += ',ArticleReference.Quantity,ArticleReference.ExplosionID';
            fieldsName += ',ArticleReference.Repairable,ArticleReference.EstimatedRepairTime';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        String endPointName = 'chervon_pim';

        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    // get the relationship between product andDiagram
    public static String getProductsAndDiagram(
        String startIndex,
        String pageSize,
        Boolean doInit
    ) {
        String endPoint =
            'http://*************:1512/rest/V1.0/list/Article/ArticleMediaAssetMap/byAssortment?assortment=\'SFDC_Models_NA\'&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleMediaAssetDocument.Identifier';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAssetDocumentAttributes.FilenameHMM(9,originalimage)';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Priority';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAssetLog.ModificationDate(HPM)';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Version';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Page';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        endPoint = endPoint + '&qualificationFilter=mediaAssetType(expdia)';
        String endPointName = 'chervon_pim';

        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    //get the relationship between product and Diagram
    public static String getAccessoriesProductsAndDiagram(
        String startIndex,
        String pageSize,
        Boolean doInit
    ) {
        // SFDC_Accessories_NA => 502
        String endPoint =
            'http://*************:1512/rest/V1.0/list/Article/ArticleMediaAssetMap/byAssortment?assortment=502&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleMediaAssetDocument.Identifier';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAssetDocumentAttributes.FilenameHMM(9,originalimage)';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Priority';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAssetLog.ModificationDate(HPM)';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Version';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Page';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        endPoint = endPoint + '&qualificationFilter=mediaAssetType(expdia)';
        String endPointName = 'chervon_pim';

        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    // get the  relationship between product and Diagram
    public static String getToolsOnlyAndDiagram(
        String startIndex,
        String pageSize,
        Boolean doInit
    ) {
        // SFDC_ToolsOnly_NA => 1403
        String endPoint =
            'http://*************:1512/rest/V1.0/list/Article/ArticleMediaAssetMap/byAssortment?assortment=1403&pageSize=' +
            pageSize;
        endPoint += '&startIndex=' + startIndex;
        if (!doInit) {
            endPoint += '&fields=';
            String fieldsName = 'ArticleMediaAssetDocument.Identifier';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAssetDocumentAttributes.FilenameHMM(9,originalimage)';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Priority';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAssetLog.ModificationDate(HPM)';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Version';
            fieldsName += ',ArticleMediaAssetMap.MediaAsset->MediaAsset.Page';
            endPoint = endPoint + EncodingUtil.urlEncode(fieldsName, 'UTF-8');
        }
        endPoint = endPoint + '&qualificationFilter=mediaAssetType(expdia)';
        String endPointName = 'chervon_pim';

        String credential = getPIMCredential(endPointName);
        String HeaderKey = 'Basic ' + credential;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res.getBody();
    }

    public static HttpResponse getProductsAndDiagram(String DiagramID) {
        String endPoint =
            'http://*************:1512/rest/V1.0/media/originalimage/' +
            DiagramID;
        String endPointName = 'chervon_pim';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String HeaderKey = 'Basic ' + headerToken;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        return res;
    }

    // push order info
    public static String pushOrderInfo(String quoteId) {
        List<String> paramStrlist = new List<String>();
        Boolean isHaveaddress = false;
        String querystr =
            'select id,Name,Customer__r.AccountNumber,Customer_PO_Num__c,Sales_Agency__c,' +
            'Shipping_By__c,BillTo__c,'+
            'Customer__c, Customer__r.Distributor_or_Dealer__c,' +
            'Order_Type__c,Price_List__c,Sales_Rep__c,CreatedDate,Sales_Agency__r.Alias_Formula__c,Sales_Agency__r.Code__c,' +
            'CurrencyIsoCode,Payment_Term__c,Freight_Term__c,RecordType.DeveloperName,' +
            'Shipping_Method__c,Sales_channel__c,Shipping_priority__c,Is_Alternative_Address__c,' +
            'Notes__c,Expected_Delivery_Date__c,Delivery_Supplier__c,BillTo_OracleID__c,' +
            'Customer_Freight_Account__c,Handling_Fee__c,ShipTo_OracleID__c,Shipping_Address_Name__c,' +
            'Org_Code__c,Additional_Shipping_Street__c,Additional_Shipping_City__c,' +
            'Additional_Shipping_Province__c,Additional_Shipping_Country__c,Discount_Amount__c,' +
            'Additional_Shipping_Postal_Code__c,Customer__r.Name,Additional_Contact_Phone__c,' +
            'Additional_Contact_Name__c,Actual_Freight_Fee__c,Additional_Shipping_Street2__c,' +
            'Payment_Term_Promotion_Code__c,' +
            'ShipTo_Name__c,State__c,Address_Line1__c,Zip_Code__c,Address_Line2__c,Country__c,City__c,Phone_Number__c,'+
            'Whole_Order_Promotion_Code__c,' +
            'ORG_ID__c,Credit_Authorization__c,' +
            'Surcharge_Amount__c,Is_Delegate__c,CreatedBy.Name,' +
            '(select id,List_Price_Date__c,List_Price__c,Name,Product__r.ProductCode,Quantity__c,' +
            'Product__r.RecordType.DeveloperName,' +
            'CreatedDate,Line_Type__c,Unit_Price__c,Ship_Date__c,Price_Book__r.Name,' +
            'Surcharge_Amount__c,' +
            'Whole_Order_Promotion__r.Promotion_Code_For_External__c,Promotion__r.Promotion_Code_For_External__c ' +
            'from Purchase_Order_Items__r) ' +
            'from Purchase_Order__c ' +
            'where Id = :quoteId';
        SObject obj = Database.query(querystr);
        Map<String, Object> paramMap = new Map<String, Object>();
        Purchase_Order__c po = (Purchase_Order__c) obj;
        List<Address_With_Program__c> awp = [SELECT Id,Program__c,Program__r.Freight_Term__c FROM Address_With_Program__c WHERE Id = :po.BillTo__c];
        if(po.ShipTo_Name__c != null || po.State__c != null || po.Address_Line1__c != null || po.Zip_Code__c != null || po.Address_Line2__c != null || po.Country__c != null || po.City__c != null || po.Phone_Number__c != null){
            isHaveaddress = true;
        }
        String strAccountNumber;
        if ('2nd Tier Dealer'.equals(po.Customer__r.Distributor_or_Dealer__c)) {
            for (Account_Address__c objShipTo : [
                SELECT Customer__r.AccountNumber
                FROM Account_Address__c
                WHERE
                    X2nd_Tier_Dealer__c = :po.Customer__c
                    AND (RecordType_Name__c = 'Shipping_Address'
                    OR RecordType_Name__c = 'Dropship_Shipping_Address')
                    AND Approval_Status__c = 'Approved'
                    AND Active__c = TRUE
                LIMIT 1
            ]) {
                strAccountNumber = objShipTo.Customer__r.AccountNumber;
            }
        }

        paramMap.put('Salesforce_PurchaseOrder_Number', po.Name);
        paramMap.put('PurchaseOrder_Flag', po.RecordType.DeveloperName);
        paramMap.put(
            'Customer',
            String.isBlank(strAccountNumber)
                ? po.Customer__r.AccountNumber
                : strAccountNumber
        );
        paramMap.put('PO_Number', po.Customer_PO_Num__c);
        paramMap.put('ShipTo', po.ShipTo_OracleID__c);
        paramMap.put('BillTo', po.BillTo_OracleID__c);
        paramMap.put('Order_Type', po.Order_Type__c);
        paramMap.put('Price_List', po.Price_List__c);
        //<CS/: Modified by Abby on 2020/1/15 /CS>
        paramMap.put('Sales_Rep', po.Sales_Agency__r.Code__c);
        //<CE/: Modified by Abby on 2020/1/15 /CE>
        paramMap.put('Date_Order', po.CreatedDate.format('yyyy-MM-dd'));
        paramMap.put('CurrencyCode', po.CurrencyIsoCode);
        // Todo
        paramMap.put('Payment_Term', po.Payment_Term__c);
        if(po.ORG_ID__c == 'CCA'){
            paramMap.put('Freight_Term', po.Freight_Term__c);
        }else{
            if(po.Shipping_By__c == 'Customer'){
                paramMap.put('Freight_Term', 'COLLECT');
            }else if(po.Shipping_By__c == 'Third Party Billing'){
                paramMap.put('Freight_Term', 'THIRD_PARTY');
            }else if(po.Shipping_By__c == 'Chervon'){
                if(awp.size() > 0){
                    paramMap.put('Freight_Term', awp[0].Program__r.Freight_Term__c);
                }
            }
        }

        paramMap.put('Shipping_Method', po.Shipping_Method__c);
        paramMap.put('Sales_Channel', po.Sales_channel__c);
        paramMap.put('Shipping_Priority', po.Shipping_priority__c);
        paramMap.put('Notes', po.Notes__c);
        paramMap.put('Expected_Delivery_Date', po.Expected_Delivery_Date__c);
        if (po.Delivery_Supplier__c == 'FedEx') {
            paramMap.put('Carrier_Code', 'FDEG');
        } else if (po.Delivery_Supplier__c == 'UPS') {
            paramMap.put('Carrier_Code', po.Delivery_Supplier__c);
        } else {
            paramMap.put('Carrier_Code', '');
        }
        paramMap.put(
            'Customer_Freight_Account',
            po.Customer_Freight_Account__c
        );
        paramMap.put(
            'Org_Code',
            String.isEmpty(po.Org_Code__c)
                ? CCM_Constants.ORG_CODE_CNA
                : po.ORG_Code__c
        );
        paramMap.put('Handling_Fee', po.Handling_Fee__c);
        paramMap.put('Freight_Fee', po.Actual_Freight_Fee__c);
        if(isHaveaddress){
            paramMap.put('Dropship_Name', po.ShipTo_Name__c);
        }else if (po.Is_Alternative_Address__c) {
            paramMap.put('Dropship_Name', po.Additional_Contact_Name__c);
        } else {
            paramMap.put('Dropship_Name', po.Shipping_Address_Name__c);
        }
        if(isHaveaddress){
            paramMap.put('Dropship_Address1', po.Address_Line1__c);
            paramMap.put('Dropship_Address2', po.Address_Line2__c);
            paramMap.put('Telephone_Number', po.Phone_Number__c);
            paramMap.put('Dropship_Country', po.Country__c);
            paramMap.put('Dropship_ZIP', po.Zip_Code__c);
            paramMap.put('Dropship_State', po.State__c);
            paramMap.put('Dropship_City', po.City__c);
        }else{
            paramMap.put('Dropship_Address1', po.Additional_Shipping_Street__c);
            paramMap.put('Dropship_Address2', po.Additional_Shipping_Street2__c);
            paramMap.put('Telephone_Number', po.Additional_Contact_Phone__c);
            paramMap.put('Dropship_Country', po.Additional_Shipping_Country__c);
            paramMap.put('Dropship_ZIP', po.Additional_Shipping_Postal_Code__c);
            paramMap.put('Dropship_State', po.Additional_Shipping_Province__c);
            paramMap.put('Dropship_City', po.Additional_Shipping_City__c);
        }
        // to do
        // if Is Delegate, set full name to External User, else use CreatedBy.Name
        if(po.Is_Delegate__c) {
            paramMap.put('create_by', po.CreatedBy.Name);
        }
        else {
            paramMap.put('create_by', 'External User');
        }

        //Modified by John Jiang 2020-04-21 : this field is none in seeburger
        //paramMap.put('Discount_Percent', '10%');
        paramMap.put('Attribute1', '');
        paramMap.put('Attribute2', '');
        paramMap.put('Attribute3', '');
        paramMap.put('Attribute4', '');
        paramMap.put('Attribute5', '');
        if (String.isNotBlank(po.ORG_ID__c)) {
            paramMap.put('ORG_Code', po.ORG_ID__c);
        }
        if (String.isNotBlank(po.Payment_Term_Promotion_Code__c)) {
            paramMap.put('Attribute3', po.Payment_Term_Promotion_Code__c); // push promotion code to ebs
        }
        if (String.isNotBlank(po.Whole_Order_Promotion_Code__c)) {
            paramMap.put('Attribute4', po.Whole_Order_Promotion_Code__c); // push promotion code to ebs
        }
        if (
            String.isBlank(po.Whole_Order_Promotion_Code__c) &&
            po.Customer__r.AccountNumber == 'B10127'
        ) {
            paramMap.put('Attribute4', 'AQ promo'); // push promotion code to ebs
        }

        //Surcharge Amount
        if (po.Surcharge_Amount__c != null) {
            paramMap.put('Attribute6', po.Surcharge_Amount__c);
        }
        paramMap.put('Attribute7', po.Credit_Authorization__c);

        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
        List<Purchase_Order_Item__c> itemlist = po.Purchase_Order_Items__r;
        if (itemlist != null && itemlist.size() > 0) {
            for (Purchase_Order_Item__c item : itemlist) {
                if (
                    item.Product__r.RecordType.DeveloperName ==
                    'Amware_Merchandising'
                ) {
                    continue;
                }
                Map<String, Object> itemMap = new Map<String, Object>();
                itemMap.put('Salesforce_PurchaseOrderLine_Number', item.Name);
                itemMap.put('Product', item.Product__r.ProductCode);
                itemMap.put('Order_Quantity', item.Quantity__c);
                itemMap.put('Line_Price_List', item.Price_Book__r.Name);
                itemMap.put('Price', item.Unit_Price__c);
                itemMap.put(
                    'Request_Date',
                    item.CreatedDate.format('yyyy-MM-dd')
                );
                itemMap.put('Ship_Date', item.Ship_Date__c);
                itemMap.put('Line_Type', item.Line_Type__c);
                itemMap.put('Line_Attribute1', '');
                itemMap.put('Line_Attribute2', '');
                itemMap.put('Line_Attribute3', '');
                if (String.isNotBlank(item.Promotion__r.Promotion_Code_For_External__c)) {
                    itemMap.put('Line_Attribute3', item.Promotion__r.Promotion_Code_For_External__c);
                }
                //line surcharge amount
                itemMap.put('Line_Attribute4', item.Surcharge_Amount__c);
                itemMap.put('Line_Attribute5', '');
                itemMap.put('sf_pricing_date', item.List_Price_Date__c);
                itemMap.put('sf_list_price', item.List_Price__c);
                itemMaplist.add(itemMap);
            }
        }
        paramMap.put('OrderLine', itemMaplist);
        String paramStr = Json.serialize(paramMap);
        paramStrlist.add(paramStr);
        System.debug(LoggingLevel.INFO, '*** paramStr: ' + paramStr);
        String endPointName = 'chervon_order_oms';
        MeIntegration_Setting__mdt MeIS = [SELECT End_Point__c, Key__c FROM MeIntegration_Setting__mdt WHERE DeveloperName = :endPointName LIMIT 1];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c;
        String HeaderKey = 'Basic ' + headerToken;
        SyncRes Sres = new SyncRes();
        if (!Test.isRunningTest()) {
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey);
            Sres = (SyncRes) JSON.deserialize(res.getBody(), SyncRes.class);
            Util.logIntegration('outdata_fg_so' + '_' + po.Customer_PO_Num__c, 'outdata', 'post', 'null', paramStr, JSON.serialize(Sres));
        }

        return Sres.ReturnCode;
    }

    public static String pushOrderInfo(
        String quoteId,
        String orderFlag,
        String accountNumber,
        String billTo,
        String shipTo,
        String orgCode,
        String freightFee,
        String TaxCode
    ) {
        List<String> paramStrlist = new List<String>();
        String querystr =
            'Select Id,Type,EffectiveDate,Pricebook2.ERP_PriceList_Name__c,' +
            'Pricebook2.ExternalID__c,Account.Name,Shipping_Address__c,PO_No__c,' +
            'Shipping_City__c,Billing_State__c,Shipping_Zip_Code__c,CurrencyIsoCode,' +
            'Account.Phone,Shipping_Country__c,Price_Book__r.Name,CreatedDate,' +
            'Shipping_State__c,Custome_Phone__c, Case__c, Case__r.RecordType.Name, Case__r.Product_Related_Issue__c, Case__r.Special_Issue_Tracking__c, Case__r.Product.ProductCode,' +
            '(Select Id,OrderId, Storage_Name__c, PricebookEntry.Product2.ProductCode,' +
            'PricebookEntry.Product2.Item_Number__c,isParts__c,' +
            'Quantity,UnitPrice,Schedule_Shipping_Datetime__c,' +
            'Product_Type__c, Posted__c,CreatedDate ' +
            'from OrderItems) ' +
            'from Order ' +
            'where Id = :quoteId';
        System.debug(LoggingLevel.INFO, '*** querystr: ' + querystr);
        SObject obj = Database.query(querystr);

        Map<String, Object> paramMap = new Map<String, Object>();
        Order od = (Order) obj;

        paramMap.put('Salesforce_PurchaseOrder_Number', od.Id);
        paramMap.put('PurchaseOrder_Flag', orderFlag);
        paramMap.put('Customer', accountNumber);
        paramMap.put('PO_Number', od.PO_No__c);
        paramMap.put('ShipTo', shipTo);
        paramMap.put('BillTo', billTo);
        paramMap.put('Order_Type', od.Type);
        paramMap.put('Price_List', od.Price_Book__r.Name);
        paramMap.put(
            'Date_Order',
            String.valueof(Date.valueof(od.CreatedDate))
        );
        paramMap.put('CurrencyCode', od.CurrencyIsoCode);
        paramMap.put('TaxCode', TaxCode);

        paramMap.put('Notes', 'TOC');

        paramMap.put('Org_Code', orgCode);
        paramMap.put('Freight_Fee', freightFee);
        paramMap.put('Dropship_Name', od.Account.name);
        paramMap.put('Dropship_Address1', od.Shipping_Address__c);
        paramMap.put('Dropship_Address2', '');
        paramMap.put('Telephone_Number', od.Custome_Phone__c);
        paramMap.put('Dropship_Country', od.Shipping_Country__c ?. toUpperCase());
        paramMap.put('Dropship_ZIP', od.Shipping_Zip_Code__c);
        paramMap.put('Dropship_State', od.Shipping_State__c);
        paramMap.put('Dropship_City', od.Shipping_City__c);
        paramMap.put('Attribute1', '');
        paramMap.put('Attribute2', '');
        paramMap.put('Attribute3', '');
        paramMap.put('Attribute4', '');
        paramMap.put('Attribute5', '');
        paramMap.put('create_by', 'External User');
        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
        List<OrderItem> itemlist = od.OrderItems;
        String priNeedVirtualCustomer = System.Label.PRI_Of_Virtual_Customer;
        List<String> pris = priNeedVirtualCustomer.split(';');
        if (itemlist != null && itemlist.size() > 0) {
            for (OrderItem item : itemlist) {
                Map<String, Object> itemMap = new Map<String, Object>();
                itemMap.put('Salesforce_PurchaseOrderLine_Number', item.Id);
                itemMap.put(
                    'Product',
                    item.PricebookEntry.Product2.ProductCode
                );
                itemMap.put('Order_Quantity', item.Quantity);
                itemMap.put('Line_Price_List', od.Price_Book__r.Name);
                itemMap.put('Price', item.UnitPrice);
                itemMap.put(
                    'Request_Date',
                    String.valueof(Date.valueof(item.CreatedDate))
                );
                itemMap.put(
                    'Ship_Date',
                    String.valueof(
                        Date.valueof(item.Schedule_Shipping_Datetime__c)
                    )
                );
                itemMap.put('Line_Type', item.Product_Type__c);
                itemMap.put('Line_Attribute1', '');
                itemMap.put('Line_Attribute2', '');
                itemMap.put('Line_Attribute3', '');
                itemMap.put('Line_Attribute4', '');
                itemMap.put('Line_Attribute5', '');

                // 如果是Warranty Parts Order，划分成本中心
                if(od.type == 'CNA Sample and Warranty Order'){
                    paramMap.put('Attribute2', 'Service Replacement Order');
                    if (item.Storage_Name__c.contains('CNA')) {
                        if(od.Case__c != null){
                            if(od.Case__r.RecordType.Name == 'Recall'){
                                paramMap.put('Attribute10', 'Y');
                                paramMap.put('Customer', 'B11780');
                                paramMap.put('Dropship_Name', od.Account.Name);
                                paramMap.put('ShipTo', '1343105');
                                paramMap.put('BillTo', '1343102');

                                if(od.Case__r.Product.ProductCode == 'BY8708-00') {
                                    paramMap.put('Customer', 'B12391');
                                    paramMap.put('BillTo', '1583160');
                                    paramMap.put('ShipTo', '1583169');
                                }
                            }
                            else {
                                List<String> issueList = new List<String>();
                                if(String.isNotBlank(od.Case__r.Product_Related_Issue__c)) {
                                    issueList.add(od.Case__r.Product_Related_Issue__c);
                                }
                                if(String.isNotBlank(od.Case__r.Special_Issue_Tracking__c)) {
                                    issueList.add(od.Case__r.Special_Issue_Tracking__c);
                                }

                                if(!issueList.isEmpty()) {
                                    String issues = String.join(issueList, ',');
                                    for(String pri : pris) {
                                        if(issues.contains(pri)) {
                                            paramMap.put('Customer', 'B12100');
                                            paramMap.put('ShipTo', '1459100');
                                            paramMap.put('BillTo', '1459099');
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // else {
                    //     if(od.Case__c != null){
                    //         if(od.Case__r.RecordType.Name == 'Recall'){
                    //             paramMap.put('Attribute10', 'Y');
                    //             paramMap.put('Customer', 'B11781');
                    //             paramMap.put('Dropship_Name', od.Account.Name);
                    //             paramMap.put('ShipTo', '1344099');
                    //             paramMap.put('BillTo', '1343103');

                    //             if(od.Case__r.Product.ProductCode == 'BY8708-00') {
                    //                 paramMap.put('Customer', 'B12391');
                    //                 paramMap.put('BillTo', '1583156');
                    //                 paramMap.put('ShipTo', '1583168');
                    //             }
                    //         }
                    //         else {
                    //             List<String> issueList = new List<String>();
                    //             if(String.isNotBlank(od.Case__r.Product_Related_Issue__c)) {
                    //                 issueList.add(od.Case__r.Product_Related_Issue__c);
                    //             }
                    //             if(String.isNotBlank(od.Case__r.Special_Issue_Tracking__c)) {
                    //                 issueList.add(od.Case__r.Special_Issue_Tracking__c);
                    //             }
                    //             if(!issueList.isEmpty()) {
                    //                 String issues = String.join(issueList, ',');
                    //                 for(String pri : pris) {
                    //                     if(issues.contains(pri)) {
                    //                         paramMap.put('Customer', 'B12083');
                    //                         paramMap.put('ShipTo', '1450101');
                    //                         paramMap.put('BillTo', '1450100');
                    //                     }
                    //                 }
                    //             }
                    //         }
                    //     }
                    // }
                }
                itemMaplist.add(itemMap);
            }
        }
        paramMap.put('OrderLine', itemMaplist);
        String paramStr = Json.serialize(paramMap);
        paramStrlist.add(paramStr);
        System.debug(LoggingLevel.INFO, '*** paramStr: ' + paramStr);
        String endPointName = 'chervon_order_oms';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c;
        String HeaderKey = 'Basic ' + headerToken;
        HttpResponse objHttpResponse = Test.isRunningTest()
            ? new HttpResponse()
            : CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey);
        Util.logIntegration('outdata_parts_replacementorder' + '_' + od.PO_No__c, 'outdata', 'post', 'null', paramStr, JSON.serialize(objHttpResponse.getBody()));
        return logSyncingResult(quoteId, paramStr, objHttpResponse);
    }
    @TestVisible
    private static String logSyncingResult(
        Id idOrder,
        String strParam,
        HttpResponse objHttpResponse
    ) {
        Boolean boolErrorOccurred =
            objHttpResponse
                ?.getBody()
                ?.contains(
                    Label.CCM_Interface_Error_During_Claim_Pack_Sending
                ) == true;
        Boolean boolFailedInSyncing = false;
        Boolean boolEmailSwitcherOn = Label.CCM_Order_Syncing_Error_Notification_Email_Switcher.equalsIgnoreCase(
            'on'
        );
        Messaging.SingleEmailMessage objEmail;
        List<Log__c> lstLog = new List<Log__c>();
        List<OrgWideEmailAddress> lstOrgWideEmailAddress;
        SyncRes objResponseWrapper;
        try {
            objResponseWrapper = (SyncRes) JSON.deserialize(objHttpResponse.getBody(), SyncRes.class);
            // prettier-ignore
            if (Test.isRunningTest()) throw new CCM_CustomException('test');
        } catch (Exception objE) {
            lstLog.add(
                new Log__c(
                    ApexName__c = 'CCM_Service',
                    Error_Message__c = objE.getMessage() + '\n' + objE.getStackTraceString(),
                    Method__c = 'logSyncingResult',
                    Name = 'Failed in parsing the JSON response during syncing Order ' + idOrder,
                    ReqParam__c = strParam,
                    ResParam__c = objHttpResponse.getBody()
                )
            );
        }
        boolFailedInSyncing =
            objResponseWrapper == null ||
            objResponseWrapper.ReturnCode == 'F';
        lstLog.add(
            new Log__c(
                ApexName__c = 'CCM_Service',
                Method__c = 'logSyncingResult',
                Name = (boolErrorOccurred == false && boolFailedInSyncing == false ? 'Succeeded' : 'Failed') + ' in syncing Order ' + idOrder,
                ReqParam__c = strParam,
                ResParam__c = objHttpResponse?.getBody()
            )
        );
        insert lstLog;
        if (boolEmailSwitcherOn == true &&
            (boolErrorOccurred == true ||
            boolFailedInSyncing == true)) {
            lstOrgWideEmailAddress = [SELECT Id FROM OrgWideEmailAddress WHERE Address = :Label.CCM_Order_Syncing_Error_Notification_Email_From LIMIT 1];
            // prettier-ignore
            if (lstOrgWideEmailAddress.isEmpty()) return objResponseWrapper?.ReturnCode;
            objEmail = new Messaging.SingleEmailMessage();
            objEmail.setOrgWideEmailAddressId(lstOrgWideEmailAddress[0].Id);
            objEmail.setToAddresses(Label.CCM_Order_Syncing_Error_Notification_Email_List.split(CCM_Constants.SEMICOLON_REGEXP_SPACE_IGNORED));
            objEmail.setSubject(
                Label.CCM_Order_Syncing_Error_Notification_Email_Subject
            );
            objEmail.setPlainTextBody(
                String.format(
                    Label.CCM_Order_Syncing_Error_Notification_Email_Body,
                    new List<String>{
                        idOrder,
                        Datetime.now().format(CCM_Constants.DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
                        Url.getSalesforceBaseUrl().toExternalForm() +
                        '/' +
                        lstLog[lstLog.size() - 1].Id
                    }
                )
            );
            // prettier-ignore
            if (!Test.isRunningTest()) Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{objEmail});
        }
        return objResponseWrapper?.ReturnCode;
    }

    //2020.11.25 qiulin Deng : restart the interface to send service order info
    // parts
    @future(callout=true)
    public static void pushServiceReplacementOrderInfo(String quoteId) {
        List<String> paramStrlist = new List<String>();
        String querystr =
            'Select Id,Type,EffectiveDate,Pricebook2.ERP_PriceList_Name__c,' +
            'Pricebook2.ExternalID__c,Account.Name,Shipping_Address__c,PO_No__c,' +
            'Shipping_City__c,Billing_State__c,Shipping_Zip_Code__c,CurrencyIsoCode,' +
            'Account.Phone,Shipping_Country__c,Price_Book__r.Name,CreatedDate,' +
            'Shipping_State__c,Custome_Phone__c,Case__r.RecordType.Name, Case__r.Product.ProductCode, ActivatedBy.Name,' +
            'Total_Tax__c,Case__c,Case__r.Product_Related_Issue__c,Case__r.Special_Issue_Tracking__c,Account.Site_Origin__pc,' +
            '(Select Id,OrderId, PricebookEntry.Product2.ProductCode,' +
            'PricebookEntry.Product2.Item_Number__c,isParts__c,' +
            'Quantity,UnitPrice,Schedule_Shipping_Datetime__c,' +
            'Product_Type__c, Posted__c,CreatedDate, Storage_Name__c,' +
            'PricebookEntry.Product2.Brand_Name__c ' +
            'from OrderItems) ' +
            'from Order ' +
            'where Id = :quoteId';
        System.debug(LoggingLevel.INFO, '*** querystr: ' + querystr);
        SObject obj = Database.query(querystr);

        Map<String, Object> paramMap = new Map<String, Object>();
        Order od = (Order) obj;

        paramMap.put('Salesforce_PurchaseOrder_Number', od.Id);
        paramMap.put('CRM_HEADER_ID', od.Id);
        paramMap.put('PurchaseOrder_Flag', 'Place_Order');
        paramMap.put('Customer', '9998');
        paramMap.put('PO_Number', od.PO_No__c);
        paramMap.put('ShipTo', '13140');
        paramMap.put('BillTo', '13138');
        paramMap.put('Price_List', od.Pricebook2.ERP_PriceList_Name__c);
        paramMap.put('Date_Order', String.valueof(Date.valueof(od.CreatedDate)));
        paramMap.put('CurrencyCode', 'USD');
        paramMap.put('create_by', od.ActivatedBy.Name);
        if (od.Total_Tax__c == 0) {
            paramMap.put('TaxCode', '0');
        } else {
            if (od.Total_Tax__c != null) {
                paramMap.put('TaxCode', String.valueOf(od.Total_Tax__c * 100));
            } else {
                paramMap.put('TaxCode', '');
            }
        }
        paramMap.put('Notes', '');
        paramMap.put('Freight_Fee', '0');
        paramMap.put('Dropship_Name', od.Account.name);
        paramMap.put('Dropship_Address1', od.Shipping_Address__c);
        paramMap.put('Dropship_Address2', '');
        paramMap.put('Telephone_Number', od.Custome_Phone__c);
        paramMap.put('Dropship_Country', od.Shipping_Country__c ?. toUpperCase());
        paramMap.put('Dropship_ZIP', od.Shipping_Zip_Code__c);
        paramMap.put('Dropship_State', od.Shipping_State__c);
        paramMap.put('Dropship_City', od.Shipping_City__c);
        paramMap.put('Attribute1', '');
        paramMap.put('Attribute2', 'Service Replacement Order');
        paramMap.put('Attribute3', '');
        paramMap.put('Attribute4', '');
        paramMap.put('Attribute5', '');

        paramMap.put('CREATION_DATE', System.now());
        paramMap.put('CURRENCY_CODE', 'USD');
        paramMap.put('PROCESS_STATUS', 'W');
        paramMap.put('CARRIER_CODE', 'Fedex');
        paramMap.put('CARRIER_NAME', 'Fedex');
        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
        List<OrderItem> itemlist = od.OrderItems;
        String priNeedVirtualCustomer = System.Label.PRI_Of_Virtual_Customer;
        List<String> pris = priNeedVirtualCustomer.split(';');
        if (itemlist != null && itemlist.size() > 0) {
            for (OrderItem item : itemlist) {
                Map<String, Object> itemMap = new Map<String, Object>();
                itemMap.put('Salesforce_PurchaseOrderLine_Number', item.Id);
                itemMap.put(
                    'Product',
                    item.PricebookEntry.Product2.Item_Number__c
                );
                itemMap.put('Order_Quantity', item.Quantity);
                itemMap.put('Line_Price_List', od.Price_Book__r.Name);
                itemMap.put('Price', item.UnitPrice);
                itemMap.put('Request_Date', String.valueof(Date.valueof(item.CreatedDate)));
                itemMap.put('Ship_Date', String.valueof(Date.valueof(item.Schedule_Shipping_Datetime__c)));
                itemMap.put('Line_Type', item.Product_Type__c);
                if (item.isParts__c) {
                    itemMap.put('Line_Attribute1', '配件');
                } else {
                    itemMap.put('Line_Attribute1', '整机');
                }
                itemMap.put('Line_Attribute2', '');
                itemMap.put('Line_Attribute3', '');
                itemMap.put('Line_Attribute4', '');
                itemMap.put('Line_Attribute5', item.Storage_Name__c);
                paramMap.put('Attribute10', 'N');
                if (item.Storage_Name__c.contains('CNA')) {
                    paramMap.put('Org_Code', 'CNA');
                    paramMap.put('Order_Type', 'CNA Sample and Warranty Order');
                    if(od.Case__c != null){
                        if(od.Case__r.RecordType.Name == 'Recall'){
                            paramMap.put('Attribute10', 'Y');
                            paramMap.put('Customer', 'B11780');
                            paramMap.put('Dropship_Name', od.Account.Name);
                            paramMap.put('ShipTo', '1343105');
                            paramMap.put('BillTo', '1343102');

                            if(od.Case__r.Product.ProductCode == 'BY8708-00') {
                                paramMap.put('Customer', 'B12391');
                                paramMap.put('BillTo', '1583160');
                                paramMap.put('ShipTo', '1583169');
                            }
                        }
                        else {
                            List<String> issueList = new List<String>();
                            if(String.isNotBlank(od.Case__r.Product_Related_Issue__c)) {
                                issueList.add(od.Case__r.Product_Related_Issue__c);
                            }
                            if(String.isNotBlank(od.Case__r.Special_Issue_Tracking__c)) {
                                issueList.add(od.Case__r.Special_Issue_Tracking__c);
                            }

                            if(!issueList.isEmpty()) {
                                String issues = String.join(issueList, ',');
                                for(String pri : pris) {
                                    if(issues.contains(pri)) {
                                        paramMap.put('Customer', 'B12100');
                                        paramMap.put('ShipTo', '1459100');
                                        paramMap.put('BillTo', '1459099');
                                    }
                                }
                            }
                        }
                    }
                } else {
                    paramMap.put('Org_Code', 'CCA');
                    paramMap.put('Order_Type', 'CA Sample and Warranty Order');

                    if (
                        item.PricebookEntry.Product2.Brand_Name__c == 'EGO' ||
                        item.PricebookEntry.Product2.Brand_Name__c == 'Flex'
                    ) {
                        paramMap.put('Customer', '9995');
                        paramMap.put('ShipTo', '160592');
                        paramMap.put('BillTo', '160586');
                    } else if (
                        item.PricebookEntry.Product2.Brand_Name__c == 'Skil' ||
                        item.PricebookEntry.Product2.Brand_Name__c == 'SkilSaw'
                    ) {
                        paramMap.put('Customer', '9993');
                        paramMap.put('ShipTo', '160588');
                        paramMap.put('BillTo', '160582');
                    }
                    if(od.Case__c != null){
                        if(od.Case__r.RecordType.Name == 'Recall'){
                            paramMap.put('Attribute10', 'Y');
                            paramMap.put('Customer', 'B11781');
                            paramMap.put('Dropship_Name', od.Account.Name);
                            paramMap.put('ShipTo', '1344099');
                            paramMap.put('BillTo', '1343103');

                            if(od.Case__r.Product.ProductCode == 'BY8708-00') {
                                paramMap.put('Customer', 'B12391');
                                paramMap.put('BillTo', '1583156');
                                paramMap.put('ShipTo', '1583168');
                            }
                        }
                        else {
                            List<String> issueList = new List<String>();
                            if(String.isNotBlank(od.Case__r.Product_Related_Issue__c)) {
                                issueList.add(od.Case__r.Product_Related_Issue__c);
                            }
                            if(String.isNotBlank(od.Case__r.Special_Issue_Tracking__c)) {
                                issueList.add(od.Case__r.Special_Issue_Tracking__c);
                            }
                            if(!issueList.isEmpty()) {
                                String issues = String.join(issueList, ',');
                                for(String pri : pris) {
                                    if(issues.contains(pri)) {
                                        paramMap.put('Customer', 'B12083');
                                        paramMap.put('ShipTo', '1450101');
                                        paramMap.put('BillTo', '1450100');
                                    }
                                }
                            }
                        }
                    }
                }
                itemMaplist.add(itemMap);
            }
        }
        paramMap.put('OrderLine', itemMaplist);
        String paramStr = Json.serialize(paramMap);
        paramStrlist.add(paramStr);
        System.debug(LoggingLevel.INFO, '*** paramStr: ' + paramStr);

        String endPointName = 'chervon_order_oms';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c;
        String HeaderKey = 'Basic ' + headerToken;
        HttpResponse objHttpResponse = Test.isRunningTest()
            ? new HttpResponse()
            : CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey);
        Util.logIntegration('outdata_fg_replacementorder' + '_' + od.PO_No__c, 'outdata', 'post', 'null', paramStr, JSON.serialize(objHttpResponse.getBody()));
        logSyncingResult(quoteId, paramStr, objHttpResponse);
    }

    //2021.3.23 Ljh push claim information to ebs - added on 2021-05-05
    @future(callout=true)
    public static void pushPromotionClaimInfo(Set<Id> claimRequestIdSet) {
        List<String> paramStrlist = new List<String>();
        String querystr =
            'Select Id,Name,Customer__r.AccountNumber,Customer__r.Name,Last_Approval_Date__c, Submit_Date__c, Total_Claim_Amount__c,CurrencyIsoCode, Ship_To__r.Customer_Line_Oracle_ID__c, ' +
            'Billing_Address_With_Authorized_Brand__r.Customer_Line_Oracle_ID__c,Promotion_Code__r.Name,Promotion_Code_Label__c,Promotion_Code__r.Promotion_Code_For_External__c,' +
            'Sent_to_EBS__c,Sent_Time__c,Sync_Message__c,Sync_Status__c,' +
            '(Select Id,Name,Amount__c,Brand__c,GL_Code__c,' +
            'Cost_Center__c From Charge_Credit_Accounts__r),' +
            'Customer__r.org_code__c,GSTOrHST_Currency__c,QST_Currency__c, Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__r.AccountNumber, CreatedBy.Profile.Name ' +
            'From Claim_Request__c ' +
            'Where Id IN :claimRequestIdSet';

        List<Claim_Request__c> crList = Database.query(querystr);

        List<Map<String, Object>> paramMaplist = new List<Map<String, Object>>();
        List<String> lstCCA2ndTierDealerProfile = new List<String>{
            'Partner Community 2nd Tier Dealer Sales for CA',
            'Partner Community 2nd Tier Dealer Service for CA',
            'Partner Community 2nd Tier Dealer Sales&Service for CA'
        };
        // get claim information, contains claim header and lines
        if (crList != null && crList.size() > 0) {
            for (Claim_Request__c cr : crList) {
                Map<String, Object> paramMap = new Map<String, Object>();
                paramMap.put('Salesforce_Number', cr.Name);
                paramMap.put('DataType', 'P_CLAIM');
                paramMap.put('CurrencyCode', cr.CurrencyIsoCode);
                paramMap.put('Date', String.valueof(Date.valueof(cr.Last_Approval_Date__c)));
                paramMap.put('BillTo', cr.Billing_Address_With_Authorized_Brand__r.Customer_Line_Oracle_ID__c);
                if (lstCCA2ndTierDealerProfile.contains(cr.CreatedBy.Profile.Name)) {
                    paramMap.put('Customer', cr.Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__r.AccountNumber);
                } else {
                    paramMap.put('Customer', cr.Customer__r.AccountNumber);
                }
                //paramMap.put('Org_Code', 'CNA');
                paramMap.put('Amount', cr.Total_Claim_Amount__c);
                paramMap.put('Cost_Center', '');
                paramMap.put('Attribute1', cr.Ship_To__r.Customer_Line_Oracle_ID__c);
                paramMap.put('Attribute2', '');
                paramMap.put('Attribute3', '');
                paramMap.put('Attribute4', '');
                paramMap.put('Attribute5', '');
                if (cr.Customer__r.org_code__c == 'CCA') {
                    paramMap.put('Org_Code', 'CCA');
                    // paramMap.put('GSTOrHST_Currency', cr.GSTOrHST_Currency__c);
                    // paramMap.put('QST_Currency', cr.QST_Currency__c);
                } else if (cr.Customer__r.org_code__c == 'CNA') {
                    paramMap.put('Org_Code', 'CNA');
                }
                List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
                List<Charge_Credit_Account__c> itemlist = cr.Charge_Credit_Accounts__r;
                if (itemlist != null && itemlist.size() > 0) {
                    for (Charge_Credit_Account__c item : itemlist) {
                        Map<String, Object> itemMap = new Map<String, Object>();
                        itemMap.put('Salesforce_Line_Number', item.Name);
                        itemMap.put('Amount', item.Amount__c);
                        itemMap.put('Brand', item.Brand__c);
                        itemMap.put('GL_Code', item.GL_Code__c);
                        itemMap.put('Cost_Center', item.Cost_Center__c);
                        itemMap.put('Claim_Item_No', cr.Promotion_Code__r.Promotion_Code_For_External__c + '-' + cr.Promotion_Code__r.Name);
                        itemMap.put('Line_Attribute1', '');
                        itemMap.put('Line_Attribute2', '');
                        itemMap.put('Line_Attribute3', '');
                        itemMap.put('Line_Attribute4', '');
                        itemMap.put('Line_Attribute5', '');
                        itemMaplist.add(itemMap);
                    }
                }
                paramMap.put('Item', itemMaplist);
                paramMaplist.add(paramMap);
            }
        }

        String paramStr = Json.serialize(paramMaplist);
        paramStrlist.add(paramStr);
        System.debug(LoggingLevel.INFO, '*** paramStr: ' + paramStr);
        String endPointName = 'chervon_seeburger_uat';
        MeIntegration_Setting__mdt MeIS = [SELECT End_Point__c, Key__c FROM MeIntegration_Setting__mdt WHERE DeveloperName = :endPointName LIMIT 1];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + 'credit_memo';
        String HeaderKey = 'Basic ' + headerToken;
        SyncResult Sres = new SyncResult();
        if (!Test.isRunningTest()) {
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey); // callout
            Sres = (SyncResult) JSON.deserialize(
                res.getBody(),
                SyncResult.class
            );
        }
        // update sync status
        if (Sres.Process_Status == 'Success') {
            for (Claim_Request__c cr : crList) {
                cr.Sync_Status__c = 'Success';
                cr.Sync_Message__c = '';

                try {
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    List<String> sendTo = new List<String>();
                    String orgId = UserInfo.getOrganizationId();
                    Organization org = [SELECT Id, IsSandbox FROM Organization WHERE Id = :orgId];
                    if (org.IsSandbox) {
                        sendTo.add('<EMAIL>');
                    }
                    String orgCode = '';
                    if (cr.Customer__r.org_code__c == 'CCA') {
                        orgCode = 'CAD';
                        email.setSubject('Review of CA Sell-through Credit Memo');
                        sendTo.add('<EMAIL>');
                    } else {
                        orgCode = 'USD';
                        email.setSubject('Review of US Sell-through Credit Memo');
                        sendTo.add('<EMAIL>');
                        sendTo.add('<EMAIL>');
                    }
                    String line1 = 'Dear Approver,\n';
                    String line2 = ' Credit memo for customer ' + cr.Customer__r.AccountNumber + ' ' + cr.Customer__r.Name + ', claim# ' + cr.Name + ' for ' + orgCode + cr.Total_Claim_Amount__c + ' has been submitted to you for review.  Please login to Oracle to process the credit memo coding to complete processing to customer.  Thank you.';
                    String body = line1 + line2;
                    email.setHtmlBody(body);
                    email.setToAddresses(sendTo);
                    // email.setPlainTextBody( body );
                    Messaging.SendEmailResult[] r = Messaging.sendEmail(
                        new List<Messaging.SingleEmailMessage>{ email }
                    );
                } catch (Exception e) {
                    System.debug('CCM_Service.pushPromotionClaimInfo>>Err:' + e.getMessage() + e.getLineNumber());
                }
            }
        } else if (Sres.Process_Status == 'Fail') {
            for (Claim_Request__c cr : crList) {
                cr.Sync_Status__c = 'Failed';
                cr.Sync_Message__c = Sres.Process_Result[0].Error_Detail;
            }
        } else {
            for (Claim_Request__c cr : crList) {
                cr.Sync_Status__c = 'Wait';
                cr.Sync_Message__c = '';
            }
        }
        update crList; // update sync status
    }

    public static void pushClaimInfo(List<String> quoteIdlist) {
        String querystr =
            'select id,Name,Service_Partner__r.AccountNumber,' +
            'BillTo__r.Customer_Line_Oracle_ID__c,Total__c,' +
            'Last_Approval_Date__c,Org_Code__c,CurrencyIsoCode,GST__c,HSt__c,QST__c,PST__c ' +
            'from Warranty_Claim__c ' +
            'where id in :quoteIdlist';
        List<SObject> sObjlist = Database.query(querystr);
        for (SObject obj : sObjlist) {
            Map<String, Object> paramMap = new Map<String, Object>();
            Warranty_Claim__c wc = (Warranty_Claim__c) obj;
            paramMap.put('Salesforce_Number', wc.Name);
            paramMap.put('Customer', wc.Service_Partner__r.AccountNumber);
            paramMap.put('BillTo', wc.BillTo__r.Customer_Line_Oracle_ID__c);
            paramMap.put('Amount', wc.Total__c);
            paramMap.put('Date', wc.Last_Approval_Date__c);
            paramMap.put('CurrencyCode', wc.CurrencyIsoCode);
            paramMap.put('Org_Code', wc.Org_Code__c);
            paramMap.put('Cost_Center', '');
            paramMap.put('DataType', 'CLAIM');
            paramMap.put('Attribute1', '');
            if(CCM_Constants.ORG_CODE_CCA == wc.Org_Code__c) {
                paramMap.put('Attribute2', wc.GST__c);
                paramMap.put('Attribute3', wc.HST__c);
                paramMap.put('Attribute4', wc.QST__c);
                paramMap.put('Attribute5', wc.PST__c);
            }
            else {
                paramMap.put('Attribute2', '');
                paramMap.put('Attribute3', '');
                paramMap.put('Attribute4', '');
            }

            paramMap.put('Attribute5', '');
            String paramStr = Json.serialize(paramMap);
            System.debug(LoggingLevel.INFO, '*** paramStr: ' + paramStr);
            String endPointName = 'chervon_ClaimAndPaypal';
            MeIntegration_Setting__mdt MeIS = [
                SELECT End_Point__c, Key__c
                FROM MeIntegration_Setting__mdt
                WHERE DeveloperName = :endPointName
                LIMIT 1
            ];
            String headerToken = MeIS.Key__c;
            String endPoint = MeIS.End_Point__c;
            String HeaderKey = 'Basic ' + headerToken;
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
                paramStr,
                endPoint,
                'POST',
                HeaderKey
            );
        }
    }

    // push Paypal
    public static String pushPaypalInfo(
        String TransactionId,
        String OrderNumber,
        String Amount,
        String CurrencyCode,
        String orgCode,
        String PayDate
    ) {
        /*String querystr = 'select id,Name,Service_Partner__r.AccountNumber,'
                        + 'BillTo__r.Customer_Line_Oracle_ID__c,Total__c,'
                        + 'Last_Approval_Date__c,Org_Code__c,CurrencyIsoCode '
                        + 'from Warranty_Claim__c '
                        + 'where id in :quoteIdlist';
        List<SObject> sObjlist = Database.query(querystr);
        for(SObject obj : sObjlist){*/
        Map<String, Object> paramMap = new Map<String, Object>();
        paramMap.put('Salesforce_Number', TransactionId);
        paramMap.put('Order', OrderNumber);
        paramMap.put('Amount', Amount);
        paramMap.put('Date', PayDate);
        paramMap.put('CurrencyCode', CurrencyCode);
        paramMap.put('Org_Code', orgCode);
        paramMap.put('DataType', 'PAYPAL');
        paramMap.put('Attribute1', '');
        paramMap.put('Attribute2', '');
        paramMap.put('Attribute3', '');
        paramMap.put('Attribute4', '');
        paramMap.put('Attribute5', '');
        String paramStr = Json.serialize(paramMap);
        System.debug(LoggingLevel.INFO, '*** paramStr: ' + paramStr);
        String endPointName = 'chervon_ClaimAndPaypal';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c;
        String HeaderKey = 'Basic ' + headerToken;
        SyncRes Sres = new SyncRes();
        if (!Test.isRunningTest()) {
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey);
            Sres = (SyncRes) JSON.deserialize(res.getBody(), SyncRes.class);
        }

        return Sres.ReturnCode;
        /*}*/
    }
    /**
     * push co-op Program Claim Infomation
     */
    @future(callout=true)
    public static void pushCoOpClaimInfo(List<Id> coOpClaimId) {
        List<String> paramStrlist = new List<String>();
        String querystr =
            'SELECT Id,Name,Reference_Invoice_Number__c,RecordType.DeveloperName,Customer__r.AccountNumber,BillTo_OracleID__c,Total_Claim_Amount__c,Last_Approval_Date__c,CurrencyIsoCode,ShipTo_OracleID__c,ORG_Code__c,' +
            '(SELECT Id,Name,Amount__c,Brand_Code__c,GL_Code__c,Cost_Center__c,Description__c FROM Co_Op_Charge_Credit_Account__r)' +
            'FROM Co_Op_Claim__c WHERE Id IN :coOpClaimId';
        List<Co_Op_Claim__c> claimList = Database.query(querystr);
        List<Map<String, Object>> paramMaplist = new List<Map<String, Object>>();
        for (Co_Op_Claim__c coOpClaim : claimList) {
            String attribute2 = '';
            Map<String, Object> paramMap = new Map<String, Object>();
            if (coOpClaim.ORG_Code__c == 'CCA') {
                if (
                    coOpClaim.RecordType.DeveloperName.equals(
                        CCM_CoOpUtil.CLAIM_RECORD_TYPE_CREDIT_MEMO
                    )
                ) {
                    attribute2 = 'CA_External_CM';
                } else if (
                    coOpClaim.RecordType.DeveloperName.equals(
                        CCM_CoOpUtil.CLAIM_RECORD_TYPE_DEDUCTION
                    )
                ) {
                    attribute2 = 'CA_Internal_CM';
                }
            } else {
                if (
                    coOpClaim.RecordType.DeveloperName.equals(
                        CCM_CoOpUtil.CLAIM_RECORD_TYPE_CREDIT_MEMO
                    )
                ) {
                    attribute2 = 'CNA_External_CM';
                } else if (
                    coOpClaim.RecordType.DeveloperName.equals(
                        CCM_CoOpUtil.CLAIM_RECORD_TYPE_DEDUCTION
                    )
                ) {
                    attribute2 = 'CNA_Internal_CM';
                }
            }

            paramMap.put(
                'Salesforce_Number',
                coOpClaim.Name +
                (String.isEmpty(coOpClaim.Reference_Invoice_Number__c)
                    ? ''
                    : '-' + coOpClaim.Reference_Invoice_Number__c)
            );
            paramMap.put('Customer', coOpClaim.Customer__r.AccountNumber);
            paramMap.put('BillTo', coOpClaim.BillTo_OracleID__c);
            paramMap.put('Amount', coOpClaim.Total_Claim_Amount__c);
            paramMap.put('Date', coOpClaim.Last_Approval_Date__c);
            paramMap.put('CurrencyCode', coOpClaim.CurrencyIsoCode);
            paramMap.put(
                'Org_Code',
                coOpClaim.ORG_Code__c == null
                    ? CCM_Constants.ORG_CODE_CNA
                    : coOpClaim.ORG_Code__c
            );
            paramMap.put('Cost_Center', '');
            paramMap.put('DataType', 'CO_CLAIM');
            paramMap.put('Attribute1', coOpClaim.ShipTo_OracleID__c);
            paramMap.put('Attribute2', attribute2);
            paramMap.put('Attribute3', '');
            paramMap.put('Attribute4', '');
            paramMap.put('Attribute5', '');
            paramMap.put('Attribute6', '');
            paramMap.put('Attribute7', '');
            paramMap.put('Attribute8', '');
            paramMap.put('Attribute9', '');
            paramMap.put('Attribute10', '');
            List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();
            List<Co_Op_Charge_Credit_Account__c> itemlist = coOpClaim.Co_Op_Charge_Credit_Account__r;
            for (
                Co_Op_Charge_Credit_Account__c coOpChargeCreditAccount : itemlist
            ) {
                Map<String, Object> itemMap = new Map<String, Object>();
                itemMap.put(
                    'Salesforce_Line_Number',
                    coOpChargeCreditAccount.Name
                );
                itemMap.put('Amount', coOpChargeCreditAccount.Amount__c);
                itemMap.put('Brand', coOpChargeCreditAccount.Brand_Code__c);
                itemMap.put('GL_Code', coOpChargeCreditAccount.GL_Code__c);
                itemMap.put(
                    'Cost_Center',
                    coOpChargeCreditAccount.Cost_Center__c
                );
                itemMap.put(
                    'Claim_Item_No',
                    coOpChargeCreditAccount.Description__c
                );
                itemMap.put('Line_Attribute1', '');
                itemMap.put('Line_Attribute2', '');
                itemMap.put('Line_Attribute3', '');
                itemMap.put('Line_Attribute4', '');
                itemMap.put('Line_Attribute5', '');
                itemMap.put('Line_Attribute6', '');
                itemMap.put('Line_Attribute7', '');
                itemMap.put('Line_Attribute8', '');
                itemMap.put('Line_Attribute9', '');
                itemMap.put('Line_Attribute10', '');
                itemMaplist.add(itemMap);
            }
            paramMap.put('Item', itemMaplist);
            paramMaplist.add(paramMap);
        }
        String paramStr = JSON.serialize(paramMaplist);
        paramStrlist.add(paramStr);
        System.debug(LoggingLevel.INFO, '*** paramStr: ' + paramStr);
        String endPointName = 'chervon_seeburger_uat';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + 'credit_memo';
        String HeaderKey = 'Basic ' + headerToken;

        SyncResult Sres = new SyncResult();
        Integer statusCode = 200;

        if (!Test.isRunningTest()) {
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey);
            statusCode = res.getStatusCode();
            Sres = (SyncResult) JSON.deserialize(
                res.getBody(),
                SyncResult.class
            );
        }
        // update sync status
        if (Sres.Process_Status == 'Success') {
            for (Co_Op_Claim__c coOpClaim : claimList) {
                coOpClaim.Sync_Status__c = 'Success';
                coOpClaim.Sync_Message__c = '';
            }
        } else if (Sres.Process_Status == 'Fail') {
            for (Co_Op_Claim__c coOpClaim : claimList) {
                coOpClaim.Sync_Status__c = 'Failed';
                coOpClaim.Sync_Message__c =
                    Sres.Process_Result[0].Error_Message +
                    Sres.Process_Result[0].Error_Detail;
            }
        } else {
            if (!Test.isRunningTest()) {
                for (Co_Op_Claim__c cr : claimList) {
                    cr.Sync_Status__c = 'Wait';
                    cr.Sync_Message__c =
                        Sres.Process_Result[0].Error_Message +
                        Sres.Process_Result[0].Error_Detail;
                }
            }
        }
        update claimList; // update sync status
    }

    /**
     * @Description: push reverse order request and item info
     *
     * Shortage => OPS Confirmed
     * Wrong Product => Shortage => OPS Confirmed
     * Damanged In Shipment => OPS Confirmed / OPS not confirmed
     * Customer Refusal with no reason => approved
     * Order Entry Error by Sales => approved
     */
    @future(callout=true)
    public static void sendReverseOrderRequest(Set<String> reverseOrderIds) {
        String queryStr =
            'SELECT Id, ' +
            ' Name, ORG_Code__c, ' +
            ' Reverse_Order_Request_Number__c, ' +
            ' Customer__r.AccountNumber, ' +
            ' Reverse_Order_Request_Start_Date__c, ' +
            ' CurrencyIsoCode, ' +
            ' BillTo__c, ' +
            ' BillTo__r.Program__c, ' +
            ' BillTo__r.Program__r.Freight_Term__c, ' +
            ' BillTo__r.Program__r.Payment_Term__c, ' +
            ' BillTo_OracleID__c, ' +
            ' ShipTo_OracleID__c, ' +
            ' Order_Type__c, ' +
            ' Reverse_Order_Type__c, ' +
            ' Price_List__r.Price_Book_OracleID__c, ' +
            ' Price_List__r.Name, ' +
            ' Order__r.Sales_Agency__r.Code__c, ' +
            ' Order__r.Shipping_Method__c, ' +
            ' Order__r.Org_Code__c, ' +
            ' Additional_Contact_Name__c, ' +
            ' Additional_Shipping_Street__c, ' +
            ' Additional_Shipping_Street2__c, ' +
            ' Additional_Contact_Phone__c, ' +
            ' Additional_Shipping_Country__c, ' +
            ' Additional_Shipping_Postal_Code__c, ' +
            ' Additional_Shipping_Province__c, ' +
            ' Additional_Shipping_City__c, ' +
            ' Return_Freight_Fee__c, ' +
            ' Order__r.Order_Number__c, ' +
            ' Original_Customer_PO_Number__c, ' +
            ' Original_Invoice__r.Invoice_Number__c, ' +
            ' CreatedBy.Name, CreatedBy.Profile.UserLicense.Name, ' +
            ' (SELECT Name, ' +
            ' Item_Number__c, ' +
            ' Qty__c, ' +
            ' Next_Step_Action__c, ' +
            ' Price_Book__r.Price_Book_OracleID__c, ' +
            ' Price_Book__r.Name, ' +
            ' Product2__r.ProductCode, ' +
            ' Invoice_Price__c, ' +
            ' Order_Product_Type__c, ' +
            ' Internal_Return_Reason__c, ' +
            ' Original_Order_Number_in_EBS__c, ' +
            ' Warehouse_Return_Number__c, ' +
            ' Order_Item__r.Selling_Warehouse__c, ' +
            ' Order_Item__r.OrderLine_OracleID__c ' +
            ' FROM Reverse_Order_Items__r) ' +
            ' FROM Reverse_Order_Request__c ' +
            ' WHERE Id IN :reverseOrderIds ' +
            ' AND (  (Confirmed_by_OPS__c LIKE \'OPS confirmed%\' AND Reverse_Order_Type__c IN (\'Shortage\', \'Wrong Product\')) ' +
            ' OR  (Reverse_Order_Type__c IN (\'CustomerRefusal with no reason\', \'Order Entry Error by Sales\') AND Approval_Status__c = \'Approved\') ' +
            ' OR Reverse_Order_Type__c = \'Damaged In Shipment\' ' +
            ')';

        List<SObject> objList = Database.query(queryStr);
        if (objList.size() == 0) {
            return;
        }
        List<Map<String, Object>> mapList = new List<Map<String, Object>>();
        Map<String, Object> paramMap = new Map<String, Object>();
        Reverse_Order_Request__c reverse = (Reverse_Order_Request__c) objList[0];
        Boolean isDamaged = false;
        if(reverse.Reverse_Order_Type__c == 'Damaged in Shipment' && reverse.ORG_Code__c != 'CCA') {
            isDamaged = true;
        }
        paramMap.put(
            'Salesforce_PurchaseOrder_Number',
            reverse.Reverse_Order_Request_Number__c
        );
        paramMap.put('PurchaseOrder_Flag', 'Place_Reverse_Order');
        paramMap.put('Customer', reverse.Customer__r.AccountNumber);
        paramMap.put('PO_Number', reverse.Reverse_Order_Request_Number__c);
        paramMap.put('BillTo', reverse.BillTo_OracleID__c);
        paramMap.put('ShipTo', reverse.ShipTo_OracleID__c);
        if (reverse.ORG_Code__c == CCM_Constants.ORG_CODE_CCA) {
            paramMap.put('Order_Type', 'CA Return - Sales & Inv.Adj.');
        } else {
            paramMap.put('Order_Type', 'CNA Return - Sales & Inv.Adj.');
        }
        paramMap.put('Price_List', reverse.Price_List__r.Name);
        paramMap.put('Sales_Rep', reverse.Order__r.Sales_Agency__r.Code__c); // Sales Rep Code
        paramMap.put('Date_Order', '');
        paramMap.put('CurrencyCode', reverse.CurrencyIsoCode);
        paramMap.put(
            'Payment_Term',
            String.isNotEmpty(reverse.BillTo__c)
                ? (String.isNotEmpty(reverse.BillTo__r.Program__c)
                      ? reverse.BillTo__r.Program__r.Payment_Term__c
                      : '')
                : ''
        );
        paramMap.put(
            'Freight_Term',
            String.isNotEmpty(reverse.BillTo__c)
                ? (String.isNotEmpty(reverse.BillTo__r.Program__c)
                      ? reverse.BillTo__r.Program__r.Freight_Term__c
                      : '')
                : ''
        );
        // paramMap.put('Shipping_Method', reverse.Order__r.Shipping_Method__c);
        paramMap.put('Sales_Channel', '');
        paramMap.put('Shipping_Priority', '');
        paramMap.put('Notes', '');
        paramMap.put('Expected_Delivery_Date', '');
        paramMap.put('Carrier_Code', '');
        paramMap.put('Org_Code', reverse.Org_Code__c);
        paramMap.put('Dropship_Name', reverse.Additional_Contact_Name__c);
        paramMap.put(
            'Dropship_Address1',
            reverse.Additional_Shipping_Street__c
        );
        paramMap.put(
            'Dropship_Address2',
            reverse.Additional_Shipping_Street2__c
        );
        paramMap.put('Telephone_Number', reverse.Additional_Contact_Phone__c);
        paramMap.put(
            'Dropship_Country',
            reverse.Additional_Shipping_Country__c
        );
        paramMap.put(
            'Dropship_ZIP',
            reverse.Additional_Shipping_Postal_Code__c
        );
        paramMap.put('Dropship_State', reverse.Additional_Shipping_Province__c);
        paramMap.put('Dropship_City', reverse.Additional_Shipping_City__c);
        if(reverse.CreatedBy.Profile.UserLicense.Name == 'Salesforce') {
            paramMap.put('create_by', reverse.CreatedBy.Name);
        }
        else {
            paramMap.put('create_by', 'External User');
        }
        paramMap.put('Attribute1', reverse.Original_Customer_PO_Number__c);
        paramMap.put('Attribute2', '');
        paramMap.put('Attribute3', '');
        paramMap.put('Attribute4', '');
        paramMap.put('Attribute5', reverse.Order__r.Order_Number__c);
        paramMap.put('Attribute6', '');
        paramMap.put('Attribute7', '');
        paramMap.put(
            'Attribute8',
            reverse.Original_Invoice__r.Invoice_Number__c
        );
        paramMap.put('Attribute9', '');
        paramMap.put('Attribute10', '');

        List<Map<String, Object>> itemList = new List<Map<String, Object>>();

        for (Reverse_Order_Item__c item : reverse.Reverse_Order_Items__r) {
            if (
                reverse.Reverse_Order_Type__c == 'Wrong Product' &&
                item.Order_Product_Type__c != 'Shortage'
            ) {
                continue;
            }
            Map<String, Object> itemParamMap = new Map<String, Object>();
            itemParamMap.put('Salesforce_PurchaseOrderLine_Number', item.Name);
            itemParamMap.put('Product', item.Product2__r.ProductCode);
            itemParamMap.put('Order_Quantity', item.Qty__c);
            itemParamMap.put('Line_Price_List', item.Price_Book__r.Name);
            itemParamMap.put('Price', item.Invoice_Price__c);
            itemParamMap.put(
                'Request_Date',
                reverse.Reverse_Order_Request_Start_Date__c
            );
            itemParamMap.put('Ship_Date', '');
            if (reverse.ORG_Code__c == CCM_Constants.ORG_CODE_CCA) {
                itemParamMap.put('Line_Type', 'CA Return Credit with Receipt');
            } else {
                itemParamMap.put('Line_Type', 'CNA Return Credit with Receipt');
            }

            itemParamMap.put(
                'Line_Attribute1',
                item.Internal_Return_Reason__c != null
                    ? item.Internal_Return_Reason__c.toUpperCase()
                    : ''
            );
            itemParamMap.put(
                'Line_Attribute2',
                item.Original_Order_Number_in_EBS__c
            );
            itemParamMap.put('origin_cus_po_no_line', reverse.Original_Customer_PO_Number__c);
            itemParamMap.put('Line_Attribute3', '');
            itemParamMap.put('Line_Attribute4', '');
            itemParamMap.put(
                'Line_Attribute5',
                item.Order_Item__r.Selling_Warehouse__c
            );
            if(isDamaged) {
                itemParamMap.put('Line_Attribute5', 'CNA27');
            }
            itemParamMap.put(
                'Line_Attribute6',
                item.Order_Item__r.OrderLine_OracleID__c
            );
            itemParamMap.put('Line_Attribute7', '');
            itemParamMap.put('Line_Attribute8', '');
            itemParamMap.put('Line_Attribute9', '');
            itemParamMap.put('Line_Attribute10', '');
            itemList.add(itemParamMap);
        }

        // add freight fee as a line item
        if (
            reverse.Return_Freight_Fee__c != null &&
            reverse.Return_Freight_Fee__c != 0
        ) {
            Map<String, Object> freightFeeMap = new Map<String, Object>();
            freightFeeMap.put('Salesforce_PurchaseOrderLine_Number', '');
            freightFeeMap.put('Product', 'Freight');
            freightFeeMap.put('Order_Quantity', reverse.Return_Freight_Fee__c);
            freightFeeMap.put('Line_Price_List', '');
            freightFeeMap.put('Price', '1');
            freightFeeMap.put('Request_Date', '');
            freightFeeMap.put('Ship_Date', '');
            freightFeeMap.put('Line_Type', '');
            freightFeeMap.put(
                'Line_Attribute1',
                reverse.Reverse_Order_Items__r[0].Internal_Return_Reason__c !=
                    null
                    ? reverse.Reverse_Order_Items__r[0]
                          .Internal_Return_Reason__c.toUpperCase()
                    : ''
            );
            freightFeeMap.put('Line_Attribute2', '');
            freightFeeMap.put('Line_Attribute3', '');
            freightFeeMap.put('Line_Attribute4', '');
            freightFeeMap.put('Line_Attribute5', '');
            freightFeeMap.put('Line_Attribute6', '');
            freightFeeMap.put('Line_Attribute7', '');
            freightFeeMap.put('Line_Attribute8', '');
            freightFeeMap.put('Line_Attribute9', '');
            freightFeeMap.put('Line_Attribute10', '');
            itemList.add(freightFeeMap);
        }

        paramMap.put('OrderLine', itemList);
        String paramStr = JSON.serialize(paramMap);
        String endPointName = 'chervon_order_oms';
        MeIntegration_Setting__mdt MeIS = [SELECT End_Point__c, Key__c FROM MeIntegration_Setting__mdt WHERE DeveloperName = :endPointName];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c;
        String HeaderKey = 'Basic ' + headerToken;
        SyncResult sres = new SyncResult();

        if (!Test.isRunningTest()) {
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey);
            sres = (SyncResult) JSON.deserialize(res.getBody(), SyncResult.class);
            Util.logIntegration('outdata_reverseorder' + '_' + reverse.Reverse_Order_Request_Number__c, 'outdata', 'post', 'null', paramStr, JSON.serialize(sres));
        }
        // update sync status
        if (sres.Process_Status == 'Success' || sres.ReturnCode == 'S') {
            reverse.Sync_Status__c = 'Success';
            reverse.Sync_Message__c = '';
        } else if (Sres.Process_Status == 'Fail' || sres.ReturnCode == 'F') {
            reverse.Sync_Status__c = 'Failed';
            reverse.Sync_Message__c = String.isNotEmpty(sres.Process_Status)
                ? Sres.Process_Result[0].Error_Message +
                  Sres.Process_Result[0].Error_Detail
                : sres.ReturnMessage;
        }
        update reverse;
    }

    /**
     * @Description: push OPS - not confirmed reverse order request and item info
     *
     * Shortage => OPS not confirmed
     * Overage => Buy => OPS not confirmed
     * Wrong Product => OPS not confirmed => Shortage
     * Wrong Product => Buy => OPS not confirmed => Overage
     */
    @future(callout=true)
    public static void pushNotConfirmedReverseOrderRequestInfo(
        Set<String> reverseOrderIds
    ) {
        String queryStr =
            'SELECT Id, ' +
            ' ORG_Code__c, ' +
            ' Name, ' +
            ' Auto_Number__c, ' +
            ' Reverse_Order_Request_Number__c, ' +
            ' Customer__r.AccountNumber, ' +
            ' BillTo_OracleID__c, ' +
            ' ShipTo_OracleID__c, ' +
            ' Subtotal__c, ' +
            ' Reverse_Order_Type__c, ' +
            ' Return_Form_Date__c,' +
            ' CurrencyIsoCode, ' +
            ' Order__r.Shipping_Method__c, ' +
            ' Description__c, ' +
            ' Original_Customer_PO_Number__c, ' +
            ' Original_Invoice__r.Invoice_Number__c, ' +
            ' Return_Freight_Fee__c, ' +
            ' (SELECT Id, ' +
            ' Name, ' +
            ' QTY__c, ' +
            ' Amount__c, ' +
            ' Brand_Code__c, ' +
            ' GL_Code__c, ' +
            ' Cost_Center__c, ' +
            ' Reverse_Order_Item__r.Order_Product_Type__c, ' +
            ' Reverse_Order_Request__c, ' +
            ' Reverse_Order_Item__r.Product2__r.SF_Description__c, ' +
            ' Description__c ' +
            ' FROM Reverse_Order_Charge_Credit_Accounts__r) ' +
            ' FROM Reverse_Order_Request__c ' +
            ' WHERE Id IN :reverseOrderIds ' +
            ' AND Confirmed_by_OPS__c = \'OPS not confirmed\' ' +
            ' AND Reverse_Order_Type__c IN (\'Shortage\', \'Overage\', \'Wrong Product\')';

        List<Reverse_Order_Request__c> reverseList = Database.query(queryStr);
        if (reverseList.size() == 0) {
            return;
        }

        List<Map<String, Object>> mapList = new List<Map<String, Object>>();
        Map<String, Object> paramMap = new Map<String, Object>();
        for (Reverse_Order_Request__c reverse : reverseList) {
            if (reverse.Reverse_Order_Type__c != 'Wrong Product') {
                paramMap.put('Salesforce_Number', reverse.Auto_Number__c);
                paramMap.put('Customer', reverse.Customer__r.AccountNumber);
                paramMap.put('BillTo', reverse.BillTo_OracleID__c);
                paramMap.put('Amount', reverse.Subtotal__c);
                paramMap.put('Date', reverse.Return_Form_Date__c);
                paramMap.put('CurrencyCode', reverse.CurrencyIsoCode);
                paramMap.put('Org_Code', reverse.ORG_Code__c);
                paramMap.put('Cost_Center', '');
                paramMap.put('DataType', 'RS_CLAIM');
                paramMap.put('Attribute1', reverse.ShipTo_OracleID__c);
                if (reverse.ORG_Code__c == CCM_Constants.ORG_CODE_CCA) {
                    if (reverse.Reverse_Order_Type__c == 'Shortage') {
                        paramMap.put('Attribute2', 'CA_External_CM');
                    }
                    if (reverse.Reverse_Order_Type__c == 'Overage') {
                        paramMap.put('Attribute2', 'CA_Invoice');
                    }
                } else {
                    if (reverse.Reverse_Order_Type__c == 'Shortage') {
                        paramMap.put('Attribute2', 'CNA_External_CM');
                    }
                    if (reverse.Reverse_Order_Type__c == 'Overage') {
                        paramMap.put('Attribute2', 'CNA_Invoice');
                    }
                }

                paramMap.put(
                    'Attribute3',
                    reverse.Original_Customer_PO_Number__c
                );
                paramMap.put(
                    'Attribute4',
                    reverse.Original_Invoice__r.Invoice_Number__c
                );
                paramMap.put('Attribute5', '');
                paramMap.put(
                    'Attribute6',
                    (reverse.Return_Freight_Fee__c != null ||
                        reverse.Return_Freight_Fee__c != 0)
                        ? String.valueOf(reverse.Return_Freight_Fee__c)
                        : ''
                );
                paramMap.put('Attribute7', '');
                paramMap.put('Attribute8', '');
                paramMap.put('Attribute9', '');
                paramMap.put('Attribute10', '');

                List<Map<String, Object>> itemList = new List<Map<String, Object>>();

                for (
                    Reverse_Order_Charge_Credit_Account__c item : reverse.Reverse_Order_Charge_Credit_Accounts__r
                ) {
                    Map<String, Object> itemParamMap = new Map<String, Object>();
                    itemParamMap.put('Salesforce_Line_Number', item.Name);
                    itemParamMap.put('Amount', item.Amount__c);
                    itemParamMap.put('Brand', item.Brand_Code__c);
                    itemParamMap.put('GL_Code', item.GL_Code__c);
                    itemParamMap.put('Cost_Center', item.Cost_Center__c);
                    itemParamMap.put('Quantity', item.QTY__c);
                    itemParamMap.put(
                        'Claim_Item_No',
                        item.Reverse_Order_Item__r.Product2__r.SF_Description__c
                    );
                    itemParamMap.put('Line_Attribute1', item.Name);
                    itemParamMap.put('Line_Attribute2', '');
                    itemParamMap.put('Line_Attribute3', '');
                    itemParamMap.put('Line_Attribute4', '');
                    itemParamMap.put('Line_Attribute5', '');
                    itemList.add(itemParamMap);
                }
                paramMap.put('Item', itemList);
                mapList.add(paramMap);
            } else {
                Map<String, Object> shortageParamMap = new Map<String, Object>();
                shortageParamMap.put(
                    'Salesforce_Number',
                    reverse.Auto_Number__c + '-s'
                );
                shortageParamMap.put(
                    'Customer',
                    reverse.Customer__r.AccountNumber
                );
                shortageParamMap.put('BillTo', reverse.BillTo_OracleID__c);
                shortageParamMap.put('Amount', reverse.Subtotal__c);
                shortageParamMap.put('Date', reverse.Return_Form_Date__c);
                shortageParamMap.put('CurrencyCode', reverse.CurrencyIsoCode);
                shortageParamMap.put('Org_Code', reverse.ORG_Code__c);
                shortageParamMap.put('Cost_Center', '');
                shortageParamMap.put('DataType', 'RS_CLAIM');
                shortageParamMap.put('Attribute1', reverse.ShipTo_OracleID__c);
                if (reverse.ORG_Code__c == CCM_Constants.ORG_CODE_CCA) {
                    shortageParamMap.put('Attribute2', 'CA_External_CM');
                } else {
                    shortageParamMap.put('Attribute2', 'CNA_External_CM');
                }

                shortageParamMap.put(
                    'Attribute3',
                    reverse.Original_Customer_PO_Number__c
                );
                shortageParamMap.put(
                    'Attribute4',
                    reverse.Original_Invoice__r.Invoice_Number__c
                );
                shortageParamMap.put('Attribute5', '');
                shortageParamMap.put('Attribute6', '');
                shortageParamMap.put('Attribute7', '');
                shortageParamMap.put('Attribute8', '');
                shortageParamMap.put('Attribute9', '');
                shortageParamMap.put('Attribute10', '');
                List<Map<String, Object>> shortageItemList = new List<Map<String, Object>>();

                for (
                    Reverse_Order_Charge_Credit_Account__c item : reverse.Reverse_Order_Charge_Credit_Accounts__r
                ) {
                    Map<String, Object> itemParamMap = new Map<String, Object>();
                    if (item.Reverse_Order_Item__r.Order_Product_Type__c == 'Shortage') {
                        itemParamMap.put('Salesforce_Line_Number', item.Name);
                        itemParamMap.put('Amount', item.Amount__c);
                        itemParamMap.put('Brand', item.Brand_Code__c);
                        itemParamMap.put('GL_Code', item.GL_Code__c);
                        itemParamMap.put('Cost_Center', item.Cost_Center__c);
                        itemParamMap.put('Quantity', item.QTY__c);
                        itemParamMap.put(
                            'Claim_Item_No',
                            item.Reverse_Order_Item__r.Product2__r.SF_Description__c
                        );
                        itemParamMap.put('Line_Attribute1', item.Name);
                        itemParamMap.put('Line_Attribute2', '');
                        itemParamMap.put('Line_Attribute3', '');
                        itemParamMap.put('Line_Attribute4', '');
                        itemParamMap.put('Line_Attribute5', '');
                        shortageItemList.add(itemParamMap);
                    }
                }
                shortageParamMap.put('Item', shortageItemList);
                mapList.add(shortageParamMap);

                Map<String, Object> overageparamMap = new Map<String, Object>();
                overageparamMap.put('Salesforce_Number', reverse.Auto_Number__c + '-o');
                overageparamMap.put('Customer', reverse.Customer__r.AccountNumber);
                overageparamMap.put('BillTo', reverse.BillTo_OracleID__c);
                overageparamMap.put('Amount', reverse.Subtotal__c);
                overageparamMap.put('Date', reverse.Return_Form_Date__c);
                overageparamMap.put('CurrencyCode', reverse.CurrencyIsoCode);
                overageparamMap.put('Org_Code', reverse.ORG_Code__c);
                overageparamMap.put('Cost_Center', '');
                overageparamMap.put('DataType', 'RS_CLAIM');
                overageparamMap.put('Attribute1', reverse.ShipTo_OracleID__c);
                if (reverse.ORG_Code__c == CCM_Constants.ORG_CODE_CCA) {
                    overageparamMap.put('Attribute2', 'CA_Invoice');
                } else {
                    overageparamMap.put('Attribute2', 'CNA_Invoice');
                }
                overageparamMap.put('Attribute3', reverse.Original_Customer_PO_Number__c);
                overageparamMap.put('Attribute4', reverse.Original_Invoice__r.Invoice_Number__c);
                overageparamMap.put('Attribute5', '');
                overageparamMap.put('Attribute6', '');
                overageparamMap.put('Attribute7', '');
                overageparamMap.put('Attribute8', '');
                overageparamMap.put('Attribute9', '');
                overageparamMap.put('Attribute10', '');

                List<Map<String, Object>> overageItemList = new List<Map<String, Object>>();
                for (Reverse_Order_Charge_Credit_Account__c item : reverse.Reverse_Order_Charge_Credit_Accounts__r) {
                    Map<String, Object> itemParamMap = new Map<String, Object>();
                    if (item.Reverse_Order_Item__r.Order_Product_Type__c == 'Overage') {
                        itemParamMap.put('Salesforce_Line_Number', item.Name);
                        itemParamMap.put('Amount', item.Amount__c);
                        itemParamMap.put('Brand', item.Brand_Code__c);
                        itemParamMap.put('GL_Code', item.GL_Code__c);
                        itemParamMap.put('Cost_Center', item.Cost_Center__c);
                        itemParamMap.put('Quantity', item.QTY__c);
                        itemParamMap.put('Claim_Item_No', item.Reverse_Order_Item__r.Product2__r.SF_Description__c);
                        itemParamMap.put('Line_Attribute1', item.Name);
                        itemParamMap.put('Line_Attribute2', '');
                        itemParamMap.put('Line_Attribute3', '');
                        itemParamMap.put('Line_Attribute4', '');
                        itemParamMap.put('Line_Attribute5', '');
                        overageItemList.add(itemParamMap);
                    }
                }
                overageparamMap.put('Item', overageItemList);
                mapList.add(overageparamMap);
            }
        }

        String paramStr = JSON.serialize(mapList);
        System.debug('*** paramStr: ' + paramStr);
        String endPointName = 'chervon_seeburger_uat';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
        ];
        String headerToken = MeIS.Key__c;
        // String endPoint = MeIS.End_Point__c;
        String endPoint = MeIS.End_Point__c + 'credit_memo';
        String HeaderKey = 'Basic ' + headerToken;
        SyncResult Sres = new SyncResult();

        if (!Test.isRunningTest()) {
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey);
            sres = (SyncResult) JSON.deserialize(
                res.getBody(),
                SyncResult.class
            );
        }
        // update sync status
        if (sres.Process_Status == 'Success' || sres.ReturnCode == 'S') {
            for (Reverse_Order_Request__c reverse : reverseList) {
                reverse.Sync_Status__c = 'Success';
                reverse.Sync_Message__c = '';
            }
        } else if (sres.Process_Status == 'Fail' || sres.ReturnCode == 'F') {
            for (Reverse_Order_Request__c reverse : reverseList) {
                reverse.Sync_Status__c = 'Failed';
                reverse.Sync_Message__c = String.isNotEmpty(sres.Process_Status)
                    ? (sres.Process_Result[0].Error_Message +
                      sres.Process_Result[0].Error_Detail)
                    : sres.ReturnMessage;
            }
        }
        update reverseList;
    }

    //https://npo505.saas.contentserv.com/admin/rest/smart/preset/72?ContextIDs=4271&ContextClass=PdmArticle&Format=pdfreactor&Language=1&CSSLink=
    public static String getContentServID(String ModelNumber) {
        String endPointName = 'Chervon_ProductLine';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + ModelNumber;
        String HeaderKey = 'Basic ' + headerToken;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(
            null,
            endPoint,
            'GET',
            HeaderKey
        );
        Map<String, Object> InfoObj = (Map<String, Object>) JSON.deserializeUntyped(
            res.getBody()
        );
        Object ProductObjs = (Object) InfoObj.get('Product');
        Map<String, Object> InfoObj2 = (Map<String, Object>) ProductObjs;
        Object ContentID = InfoObj2 == null ? null : InfoObj2.get('ID');
        String ContentIDstr = ContentID == null
            ? ''
            : String.valueOf(ContentID);
        return ContentIDstr;
    }

    public static String getContentServProductline(String ModelNumber, String brand) {
        // String contentID = getContentServID(ModelNumber);
        // String URL = 'https://npo505.saas.contentserv.com/admin/rest/smart/preset/119?ContextIDs=';
        // URL = URL + contentID;
        // URL =
        //     URL +
        //     '&ContextClass=PdmArticle&Format=pdfreactor&Language=1&CSSLink=';
        String URL = '';
        if(String.isNotBlank(brand)) {
            Map<String, String> brandParamMap = new Map<String, String> {
                'EGO' => 'EGO NA',
                'FLEX' => 'FLEX NA',
                'SKIL' => 'SKIL NA'
            };
            String brandParam = brandParamMap.get(brand.toUpperCase());
            String urlformat = 'https://infoportal.chervon.com.cn/pdf/generate?model-number={0}&brand={1}&template-id=DatasheetGlobal&language=English';
            Boolean isSandBox = Util.isSandboxBasedOnUser();
            if(isSandBox) {
                urlformat = 'https://infoportal-qas.chervon.com.cn/pdf/generate?model-number={0}&brand={1}&template-id=DatasheetGlobal&language=English';
            }
            URL = String.format(urlformat, new List<String>{ModelNumber, brandParam});
        }
        return URL;
    }


    public static String getContentServSession(String endPointName, String ctsWebsite) {
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c, userName__c, password__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c;
        String ctsUser = MeIS.userName__c;
        String ctsPassword = MeIS.password__c;
        endPoint = endPoint + ctsUser;
        endPoint = endPoint + '&ctsPassword=';
        endPoint = endPoint + ctsPassword;
        endPoint = endPoint + '&ctsWebsite=';
        endPoint = endPoint + ctsWebsite;
        String HeaderKey = 'Basic ' + headerToken;
        HttpResponse res = CCM_ServiceCallout.getDataViaHttp(null, endPoint, 'GET', HeaderKey);
        Map<String, Object> InfoObj = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
        Object TicketObjs = (Object) InfoObj.get('Ticket');
        Map<String, Object> InfoObj2 = (Map<String, Object>) TicketObjs;
        Object ContentSession = InfoObj2.get('Parameter');
        String ContentSessionStr = ContentSession == null ? '' : String.valueOf(ContentSession);
        return ContentSessionStr;
    }

    public static String getEGOHomelink() {
        //String ContentSessionStr = getContentServSession('DistributorEGONA','Cherv0n!@#','https://ego-us.saas.contentserv.com');
        // String ContentSessionStr = getContentServSession(
        //     'Chervon_EGO',
        //     'https://ego-us.saas.contentserv.com'
        // );
        // String URL =
        //     'https://ego-us.saas.contentserv.com/admin/rest/deepsearch/?t=3' +
        //     ContentSessionStr;
        String URL = '';
        Boolean isSandBox = Util.isSandboxBasedOnUser();
        if(isSandBox) {
            URL = 'https://infoportal-qas.chervon.com.cn/en_US/ego-na/products?e_code=65fc533afaaaa76c932eaea3';
        }
        else {
            URL = 'https://infoportal.chervon.com.cn/en_US/ego-na/products?e_code=65f3ee9af2818959834a8f40';
        }
        return URL;
    }

    public static String getSKILHomelink() {
        //String ContentSessionStr = getContentServSession('DistributorSKILNA','Cherv0n!@#','https://skil-us.saas.contentserv.com');
        // String ContentSessionStr = getContentServSession(
        //     'Chervon_SKIL',
        //     'https://skil-us.saas.contentserv.com'
        // );
        // String URL =
        //     'https://skil-us.saas.contentserv.com/admin/rest/deepsearch/?t=2' +
        //     ContentSessionStr;
        // return URL;
        String URL = '';
        Boolean isSandBox = Util.isSandboxBasedOnUser();
        if(isSandBox) {
            URL = 'https://infoportal-qas.chervon.com.cn/en_US/skil-na/products?e_code=65fc533afaaaa76c932eaea3';
        }
        else {
            URL = 'https://infoportal.chervon.com.cn/en_US/skil-na/products?e_code=65f3ee9af2818959834a8f40';
        }
        return URL;
    }

    public static String getFLEXHomelink() {
        //String ContentSessionStr = getContentServSession('DistributorFLEXNA','Cherv0n!@#','https://flex-na.saas.contentserv.com');
        // String ContentSessionStr = getContentServSession(
        //     'Chervon_FLEX',
        //     'https://flex-na.saas.contentserv.com'
        // );
        // String URL =
        //     'https://flex-na.saas.contentserv.com/admin/rest/deepsearch/?t=8' +
        //     ContentSessionStr;
        // return URL;
        String URL = '';
        Boolean isSandBox = Util.isSandboxBasedOnUser();
        if(isSandBox) {
            URL = 'https://infoportal-qas.chervon.com.cn/en_US/flex/products?e_code=65fc533afaaaa76c932eaea3';
        }
        else {
            URL = 'https://infoportal.chervon.com.cn/en_US/flex/products?e_code=65f3ee9af2818959834a8f40';
        }
        return URL;
    }

    public static Boolean isValid(String stringValue, Schema.SObjectType sObjectType) {
        Id sObjectId;
        if (isId(stringValue))
            sObjectId = (Id) stringValue;
        return isValid(sObjectId, sObjectType);
    }

    public static Boolean isValid(Id sObjectId, Schema.SObjectType sObjectType) {
        return !(sObjectId == null ||
        sObjectId.getSObjectType() != sObjectType);
    }

    public static Boolean isId(String stringValue) {
        return stringValue instanceof Id;
    }

    public static String objectOfId(String stringValue) {
        String res = '';
        Id sObjectId;
        if (isId(stringValue)) {
            sObjectId = (Id) stringValue;
            res = String.valueOf(sObjectId.getSObjectType());
        } else {
            res = '不是有效的Id';
        }
        return res;
    }

    //2021.3.12 Ljh :push approved Promotion Code to ebs
    public static String pushPromotionCode(List<Promotion2__c> promotionList) {
        List<String> paramStrlist = new List<String>();
        String querystr =
            'SELECT Id, Name, ORG_Code__c, Recordtype.Developername, RecordType.Name, (SELECT Start_Date__c, End_Date__c, Claim_End_Date__c, Promotion__c, Promotion__r.Promotion_Code_For_External__c' +
            ' FROM Promotion_Windows__r WHERE Promotion_Window_Approval_Status__c = \'Approved\' AND LastModifiedDate >= YESTERDAY LIMIT 1) ' +
            ' FROM Promotion2__c WHERE Id IN :promotionList';

        System.debug(LoggingLevel.INFO, '*** querystr: ' + querystr);

        promotionList = Database.query(querystr); // get promotion codes

        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();

        SyncRes Sres = new SyncRes();
        // generate request json
        if (promotionList != null && promotionList.size() > 0) {
            for (Promotion2__c pm : promotionList) {
                for (Promotion_Window__c pw : pm.Promotion_Windows__r) {
                    Map<String, Object> paramMap = new Map<String, Object>();
                    paramMap.put('Promo_Code', pw.Promotion__r.Promotion_Code_For_External__c);
                    paramMap.put('Start_Date', String.valueof(Date.valueof(pw.Start_Date__c)));
                    paramMap.put('End_Date', pm.Recordtype.Developername == 'Sell_Through_Promotion' ? String.valueof(Date.valueof(pw.Claim_End_Date__c)) : String.valueof(Date.valueof(pw.End_Date__c)));
                    paramMap.put('Attribute1', pm.ORG_Code__c);
                    paramMap.put('Attribute2', pm.Name);
                    paramMap.put('Attribute3', pm.Recordtype.Name);
                    paramMap.put('Attribute4', '');
                    paramMap.put('Attribute5', '');
                    paramMap.put('Attribute6', '');
                    paramMap.put('Attribute7', '');
                    paramMap.put('Attribute8', '');
                    paramMap.put('Attribute9', '');
                    paramMap.put('Attribute10', '');
                    itemMaplist.add(paramMap);
                }
            }
            String paramStr = Json.serialize(itemMaplist);
            paramStrlist.add(paramStr);
            System.debug(LoggingLevel.INFO, '*** paramStr: ' + paramStr);

            String endPointName = 'chervon_seeburger_uat';
            MeIntegration_Setting__mdt MeIS = [
                SELECT End_Point__c, Key__c
                FROM MeIntegration_Setting__mdt
                WHERE DeveloperName = :endPointName
                LIMIT 1
            ];
            String headerToken = MeIS.Key__c;
            String endPoint = MeIS.End_Point__c + 'PromotionCodeInfo';
            String HeaderKey = 'Basic ' + headerToken;
            if (!Test.isRunningTest()) {
                HttpResponse res = CCM_ServiceCallout.getDataViaHttp( // callout
                    paramStr,
                    endPoint,
                    'POST',
                    HeaderKey
                );
                Sres = (SyncRes) JSON.deserialize(res.getBody(), SyncRes.class);
                Util.logIntegration('outdata_promotioncode', 'outdata', 'post', 'null', paramStr, JSON.serialize(Sres));
            }
        }
        return Sres.ReturnCode;
    }

    @future(callout=true)
    public static void pushPromotionCode(Set<String> promotionIds) {
        List<String> paramStrlist = new List<String>();
        String querystr =
            'SELECT Id,Name, ORG_Code__c, Recordtype.Developername,Recordtype.Name,(SELECT Start_Date__c, End_Date__c, Claim_End_Date__c, Promotion__c, Promotion__r.Promotion_Code_For_External__c' +
            ' FROM Promotion_Windows__r WHERE Promotion_Window_Approval_Status__c = \'Approved\' AND LastModifiedDate >= YESTERDAY LIMIT 1) ' +
            ' FROM Promotion2__c WHERE Id IN :promotionIds';

        System.debug(LoggingLevel.INFO, '*** querystr: ' + querystr);

        List<Promotion2__c> promotionList = Database.query(querystr); // get promotion codes

        List<Map<String, Object>> itemMaplist = new List<Map<String, Object>>();

        SyncRes Sres = new SyncRes();
        // generate request json
        if (promotionList != null && promotionList.size() > 0) {
            for (Promotion2__c pm : promotionList) {
                for (Promotion_Window__c pw : pm.Promotion_Windows__r) {
                    Map<String, Object> paramMap = new Map<String, Object>();
                    paramMap.put('Promo_Code', pw.Promotion__r.Promotion_Code_For_External__c);
                    paramMap.put('Start_Date', String.valueof(Date.valueof(pw.Start_Date__c)));
                    paramMap.put('End_Date', pm.Recordtype.Developername == 'Sell_Through_Promotion' ? String.valueof(Date.valueof(pw.Claim_End_Date__c)) : String.valueof(Date.valueof(pw.End_Date__c)));
                    paramMap.put('Attribute1', pm.ORG_Code__c);
                    paramMap.put('Attribute2', pm.Name);
                    paramMap.put('Attribute3', pm.Recordtype.Name);
                    paramMap.put('Attribute4', '');
                    paramMap.put('Attribute5', '');
                    paramMap.put('Attribute6', '');
                    paramMap.put('Attribute7', '');
                    paramMap.put('Attribute8', '');
                    paramMap.put('Attribute9', '');
                    paramMap.put('Attribute10', '');
                    itemMaplist.add(paramMap);
                }
            }
            String paramStr = Json.serialize(itemMaplist);
            paramStrlist.add(paramStr);
            System.debug(LoggingLevel.INFO, '*** paramStr: ' + paramStr);

            String endPointName = 'chervon_seeburger_uat';
            MeIntegration_Setting__mdt MeIS = [
                SELECT End_Point__c, Key__c
                FROM MeIntegration_Setting__mdt
                WHERE DeveloperName = :endPointName
                LIMIT 1
            ];
            String headerToken = MeIS.Key__c;
            String endPoint = MeIS.End_Point__c + 'PromotionCodeInfo';
            String HeaderKey = 'Basic ' + headerToken;
            if (!Test.isRunningTest()) {
                HttpResponse res = CCM_ServiceCallout.getDataViaHttp( // callout
                    paramStr,
                    endPoint,
                    'POST',
                    HeaderKey
                );
                Util.logIntegration('outdata_promotioncode', 'outdata', 'post', 'null', paramStr, JSON.serialize(res.getBody()));
            }
        }
    }

    @future(callout=true)
    public static void pushPaymentInfo(String paymentId) {
        String queryStr =
            'SELECT Id, OwnerId, Name, CurrencyIsoCode, CreatedDate, CreatedById, CreatedBy.TimeZoneSidKey, ' +
            'Customer__c, ' +
            'Customer__r.AccountNumber, ' +
            'Total_Amount__c, ' +
            'Actual_Paid_Amount__c, ' +
            'Result__c, ' +
            'Pay_Method__c, ' +
            'Transaction_ID__c, ' +
            'BillTo__r.Customer_Line_Oracle_ID__c, ' +
            'Billing_Address__c, ' +
            'ORG_Code__c, ' +
            'Payment_Date__c, ' +
            'Cash_Discount_Gross_Amount__c, ' +
            'Total_Credit_Memo_Amount__c, ' +
            'Remark__c, ' +
            'First_Pay_By_Paypal__c, ' +
            '(SELECT Id, CreatedDate, ' +
            'Invoice__c, ' +
            'Invoice__r.Invoice_Number__c, ' +
            'Invoice__r.Invoice_Type__c, ' +
            'Order__c, ' +
            'Invoice_Type__c, ' +
            'Accounting_Balance__c, ' +
            'Accounting_Balance__r.Accounting_OracleID__c, ' +
            'Cash_Discount_Amount__c, ' +
            'Total_Amount__c, ' +
            'Actual_Paid_Amount__c, ' +
            'Payment_Information__c, ' +
            'Payment_Information_Item_accounting__c, ' +
            'Has_No_Sequence__c ' +
            'FROM Payment_Information_Items__r) ' +
            'FROM Payment_Information__c WHERE Id = :paymentId';

        List<SObject> objList = Database.query(queryStr);
        if (objList.size() == 0) {
            return;
        }
        List<Map<String, Object>> mapList = new List<Map<String, Object>>();
        Map<String, Object> paramMap = new Map<String, Object>();
        Payment_Information__c pay = (Payment_Information__c) objList[0];
        paramMap.put('Receipt_Number', pay.Name);
        paramMap.put('Gl_Date', pay.Payment_Date__c);
        //Update for CCA，cca only use [Lock Box Canada] - Zoe 2024-8-20
        if(pay.ORG_Code__c =='CCA' && (pay.Pay_Method__c == 'Credit Card' || pay.Pay_Method__c == 'Debit Card')){
            paramMap.put('Receipt_Method', 'Lock Box Canada'); // Lock Box Canada
        } else if (pay.Pay_Method__c == 'Credit Card' || pay.Pay_Method__c == 'Debit Card') {
            paramMap.put('Receipt_Method', 'Credit Card_Old Second'); // Lock box、
        } else if (pay.Pay_Method__c == 'ACH') {
            paramMap.put('Receipt_Method', 'Lock Box'); // Lock box、Credit Card Harris Bank
        } else if (pay.Pay_Method__c == 'PAD') {
            paramMap.put('Receipt_Method', 'Lock Box Canada'); // Lock Box Canada
        }
        paramMap.put('Payment_Transaction_Id', pay.Id);
        paramMap.put('Payment_Method', pay.Pay_Method__c); // Credit Card、Debit Card、ACH
        paramMap.put('Receipt_Amount', pay.Actual_Paid_Amount__c);
        if (pay.Cash_Discount_Gross_Amount__c != 0 && pay.Cash_Discount_Gross_Amount__c != null) {
            paramMap.put('Cash_Discount_Gross_Amount', pay.Cash_Discount_Gross_Amount__c.abs());
        } else {
            paramMap.put('Cash_Discount_Gross_Amount', '');
        }
        paramMap.put('Invoice_Total_Net_Amount', pay.Total_Amount__c);
        paramMap.put('Total_Credit_Memo_Amount', pay.Total_Credit_Memo_Amount__c);
        paramMap.put('Account_Number', pay.Customer__r.AccountNumber);
        paramMap.put('Bill_To', pay.BillTo__r.Customer_Line_Oracle_ID__c);
        paramMap.put('Org_Code', pay.ORG_Code__c);
        paramMap.put('Currency_Code', pay.CurrencyIsoCode);
        if (pay.First_Pay_By_Paypal__c) {
            paramMap.put('New_Invoice_Flag', 'Y');
        } else {
            paramMap.put('New_Invoice_Flag', 'N');
        }
        paramMap.put('Attribute1', '');
        paramMap.put('Attribute2', '');
        paramMap.put('Attribute3', '');
        paramMap.put('Attribute4', '');
        paramMap.put('Attribute5', '');
        paramMap.put('Attribute6', '');
        paramMap.put('Attribute7', '');
        paramMap.put('Attribute8', '');
        paramMap.put('Attribute9', '');
        paramMap.put('Attribute10', '');

        List<Map<String, Object>> itemList = new List<Map<String, Object>>();
        Set<Id> itemIds = new Set<Id>();
        for (Payment_Information_Item__c item : pay.Payment_Information_Items__r) {
            itemIds.add(item.id);
        }

        Map<String, List<Payment_Information_Item__c>> sequenseItem = new Map<String, List<Payment_Information_Item__c>>();

        for (Payment_Information_Item__c each : [SELECT Id,
                                                        Invoice__c,
                                                        Invoice__r.Invoice_Number__c,
                                                        Invoice__r.Invoice_Type__c,
                                                        Invoice_Type__c,
                                                        Accounting_Balance__c,
                                                        Accounting_Balance__r.Accounting_OracleID__c,
                                                        Actual_Paid_Amount__c,
                                                        Cash_Discount_Amount__c,
                                                        Payment_Information_Item_accounting__c,
                                                        Total_Amount__c
                                                    FROM Payment_Information_Item__c
                                                    WHERE Payment_Information_Item_accounting__c IN :itemIds]) {
            if (!sequenseItem.containsKey(each.Payment_Information_Item_accounting__c)) {
                List<Payment_Information_Item__c> accountingList = new List<Payment_Information_Item__c>();
                accountingList.add(each);
                sequenseItem.put(each.Payment_Information_Item_accounting__c, accountingList);
            } else {
                sequenseItem.get(each.Payment_Information_Item_accounting__c).add(each);
            }
        }

        for (Payment_Information_Item__c item : pay.Payment_Information_Items__r) {
            if (sequenseItem.containsKey(item.Id)) {
                for (Payment_Information_Item__c sequense : sequenseItem.get(item.Id)) {
                    Map<String, Object> itemParamMap = new Map<String, Object>();
                    itemParamMap.put('Invoice_Number', item.Invoice__r.Invoice_Number__c);
                    itemParamMap.put('Apply_Date', pay.CreatedDate.format('yyyy-MM-dd', pay.CreatedBy.TimeZoneSidKey)); // 核销日期
                    itemParamMap.put('GL_Date', pay.Payment_Date__c); // 支付日期
                    itemParamMap.put('Accounting_OracleID',sequense.Accounting_Balance__r.Accounting_OracleID__c);
                    itemParamMap.put('Total_Invoice_Amount', sequense.Total_Amount__c);
                    itemParamMap.put('Invoice_Type', item.Invoice__r.Invoice_type__c);
                    if (sequense.Cash_Discount_Amount__c != 0 && sequense.Cash_Discount_Amount__c != null) {
                        itemParamMap.put('Cash_Discount_Amount', sequense.Cash_Discount_Amount__c.abs());
                    } else {
                        itemParamMap.put('Cash_Discount_Amount', '');
                    }
                    itemParamMap.put('Actual_Apply_Amount', sequense.Actual_Paid_Amount__c);
                    itemParamMap.put('Line_Attribute1', '');
                    itemParamMap.put('Line_Attribute2', '');
                    itemParamMap.put('Line_Attribute3', '');
                    itemParamMap.put('Line_Attribute4', '');
                    itemParamMap.put('Line_Attribute5', '');
                    itemParamMap.put('Line_Attribute6', '');
                    itemParamMap.put('Line_Attribute7', '');
                    itemParamMap.put('Line_Attribute8', '');
                    itemParamMap.put('Line_Attribute9', '');
                    itemParamMap.put('Line_Attribute10', '');
                    itemList.add(itemParamMap);
                }
            } else {
                Map<String, Object> itemParamMap = new Map<String, Object>();
                itemParamMap.put('Invoice_Number', item.Invoice__r.Invoice_Number__c);
                itemParamMap.put('Apply_Date', pay.CreatedDate.format('yyyy-MM-dd', pay.CreatedBy.TimeZoneSidKey)); // 核销日期
                itemParamMap.put('GL_Date', pay.Payment_Date__c); // 支付日期
                itemParamMap.put('Accounting_OracleID', item.Accounting_Balance__r.Accounting_OracleID__c);
                itemParamMap.put('Total_Invoice_Amount', item.Total_Amount__c);
                itemParamMap.put('Invoice_Type', item.Invoice__r.Invoice_type__c);
                if (item.Cash_Discount_Amount__c != 0 && item.Cash_Discount_Amount__c != null) {
                    itemParamMap.put('Cash_Discount_Amount', item.Cash_Discount_Amount__c.abs());
                } else {
                    itemParamMap.put('Cash_Discount_Amount', '');
                }
                itemParamMap.put('Actual_Apply_Amount', item.Actual_Paid_Amount__c);
                itemParamMap.put('Line_Attribute1', '');
                itemParamMap.put('Line_Attribute2', '');
                itemParamMap.put('Line_Attribute3', '');
                itemParamMap.put('Line_Attribute4', '');
                itemParamMap.put('Line_Attribute5', '');
                itemParamMap.put('Line_Attribute6', '');
                itemParamMap.put('Line_Attribute7', '');
                itemParamMap.put('Line_Attribute8', '');
                itemParamMap.put('Line_Attribute9', '');
                itemParamMap.put('Line_Attribute10', '');
                itemList.add(itemParamMap);
            }
        }
        paramMap.put('PayLine', itemList);
        String mdtName = 'PayPal_Sandbox';
        String orgId = UserInfo.getOrganizationId();
        Organization org = [
            SELECT Id, IsSandbox
            FROM Organization
            WHERE Id = :orgId
        ];
        if (!org.IsSandbox) {
            mdtName = 'PayPal_Production';
        }
        String paramStr = JSON.serialize(paramMap);
        String endPointName = 'chervon_seeburger_uat';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + 'onlinepayment';
        String HeaderKey = 'Basic ' + headerToken;
        SyncResult sres = new SyncResult();

        if (!Test.isRunningTest()) {
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey);
            sres = (SyncResult) JSON.deserialize(res.getBody(), SyncResult.class);
        }
        Log__c pushPaymentLog = new Log__c(
            ApexName__c = 'CCM_Service',
            Method__c = 'pushPaymentInfo',
            Name = 'CCM_Service.pushPaymentInfo: ' + ' at ' + Datetime.now().format(CCM_Constants.DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
            ReqParam__c = paramStr,
            ResParam__c = JSON.serialize(sres),
            Error_Message__c = sres.Process_Result[0].Error_Detail
        );
        insert pushPaymentLog;
        if (sres.Process_Status != 'Success') {
            pay.Result__c = 'Pending';
            update pay;
            try {
                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                List<String> sendTo = new List<String>();
                List<String> emailForAR = new List<String>();
                if(pay.ORG_Code__c =='CCA'){
                    emailForAR = Label.Email_CAAR.split(';');
                }else{
                    emailForAR = Label.Email_AR.split(';');
                }
                sendTo.addAll(emailForAR);
                email.setCharset('UTF-8');
                email.setSubject('Push to Oracle');
                String line1 = 'Dear Customer,<br>';
                String line2 = '    We are sorry to inform you that your Payment ID = ' + pay.Name +' failed to be processed in Oracle.The reason is '+sres.Process_Result[0].Error_Detail+'. Please check it in Oracle and handle it manually. Thank you!<br>';
                String body = line1 + line2;
                email.setHtmlBody(body);
                email.setToAddresses(sendTo);
                // email.setPlainTextBody( body );
                Messaging.SendEmailResult[] r = Messaging.sendEmail(
                    new List<Messaging.SingleEmailMessage>{ email }
                );
            } catch (Exception e) {
                System.debug('CCM_Service.pushPaymentInfo>>Err:' + e.getMessage() + e.getLineNumber());
            }
        } else {
            pay.Result__c = 'Success';
            update pay;
        }
    }
    @future(callout=true)
    public static void pushBeforePaymentInfo(String paymentId,Boolean boolIsSuccess) {
        String queryStr =
            'SELECT Id, OwnerId, Name, CurrencyIsoCode, CreatedDate, CreatedById, CreatedBy.TimeZoneSidKey, ' +
            'Customer__c, ' +
            'Customer__r.AccountNumber, ' +
            'Total_Amount__c, ' +
            'Actual_Paid_Amount__c, ' +
            'Result__c, ' +
            'Pay_Method__c, ' +
            'Transaction_ID__c, ' +
            'BillTo__r.Customer_Line_Oracle_ID__c, ' +
            'Billing_Address__c, ' +
            'ORG_Code__c, ' +
            'Payment_Date__c, ' +
            'Cash_Discount_Gross_Amount__c, ' +
            'Total_Credit_Memo_Amount__c, ' +
            'Remark__c, ' +
            'First_Pay_By_Paypal__c, ' +
            '(SELECT Id, CreatedDate, ' +
            'Invoice__c, ' +
            'Invoice__r.Invoice_Number__c, ' +
            'Invoice__r.Invoice_Type__c, ' +
            'Order__c, ' +
            'Invoice_Type__c, ' +
            'Accounting_Balance__c, ' +
            'Accounting_Balance__r.Accounting_OracleID__c, ' +
            'Cash_Discount_Amount__c, ' +
            'Total_Amount__c, ' +
            'Actual_Paid_Amount__c, ' +
            'Payment_Information__c, ' +
            'Payment_Information_Item_accounting__c, ' +
            'Has_No_Sequence__c ' +
            'FROM Payment_Information_Items__r) ' +
            'FROM Payment_Information__c WHERE Id = :paymentId';

        List<SObject> objList = Database.query(queryStr);
        if (objList.size() == 0) {
            return;
        }
        List<Map<String, Object>> mapList = new List<Map<String, Object>>();
        Map<Id, String> invoiceMap = new Map<Id, String>();
        Map<String, Object> paramMap = new Map<String, Object>();
        Payment_Information__c pay = (Payment_Information__c) objList[0];
        List<Map<String, Object>> itemList = new List<Map<String, Object>>();
        Set<Id> itemIds = new Set<Id>();
        Set<Id> invoiceIds = new Set<Id>();
        for (Payment_Information_Item__c item : pay.Payment_Information_Items__r) {
            itemIds.add(item.id);
        }

        Map<String, List<Payment_Information_Item__c>> sequenseItem = new Map<String, List<Payment_Information_Item__c>>();

        for (Payment_Information_Item__c each : [SELECT Id,
                                                        Invoice__c,
                                                        Invoice__r.Invoice_Number__c,
                                                        Invoice__r.Invoice_Type__c,
                                                        Invoice_Type__c,
                                                        Accounting_Balance__c,
                                                        Accounting_Balance__r.Accounting_OracleID__c,
                                                        Actual_Paid_Amount__c,
                                                        Cash_Discount_Amount__c,
                                                        Payment_Information_Item_accounting__c,
                                                        Total_Amount__c
                                                    FROM Payment_Information_Item__c
                                                    WHERE Payment_Information_Item_accounting__c IN :itemIds]) {
            if (!sequenseItem.containsKey(each.Payment_Information_Item_accounting__c)) {
                List<Payment_Information_Item__c> accountingList = new List<Payment_Information_Item__c>();
                accountingList.add(each);
                sequenseItem.put(each.Payment_Information_Item_accounting__c, accountingList);
            } else {
                sequenseItem.get(each.Payment_Information_Item_accounting__c).add(each);
            }
        }

        for (Payment_Information_Item__c item : pay.Payment_Information_Items__r) {
            if(!sequenseItem.containsKey(item.id)){
                invoiceIds.add(Item.Invoice__c);
            }
        }
        for(Accounting_Balance__c ab : [SELECT Id,Accounting_OracleID__c,Invoice__c FROM Accounting_Balance__c WHERE Invoice__c IN :invoiceIds]){
            if(!invoiceMap.containsKey(ab.Invoice__c)){
                invoiceMap.put(ab.Invoice__c, ab.Accounting_OracleID__c);
            }
        }

        for (Payment_Information_Item__c item : pay.Payment_Information_Items__r) {
            if (sequenseItem.containsKey(item.Id)) {
                for (Payment_Information_Item__c sequense : sequenseItem.get(item.Id)) {
                    Map<String, Object> itemParamMap = new Map<String, Object>();
                    itemParamMap.put('Receipt_Number', pay.Name);
                    itemParamMap.put('Apply_Date', pay.CreatedDate);
                    if(pay.ORG_Code__c =='CCA' && (pay.Pay_Method__c == 'Credit Card' || pay.Pay_Method__c == 'Debit Card')){
                        itemParamMap.put('Receipt_Method', 'Lock Box Canada'); // Lock Box Canada
                    } else if (pay.Pay_Method__c == 'Credit Card' || pay.Pay_Method__c == 'Debit Card') {
                        itemParamMap.put('Receipt_Method', 'Credit Card_Old Second'); // Lock box、
                    } else if (pay.Pay_Method__c == 'ACH') {
                        itemParamMap.put('Receipt_Method', 'Lock Box'); // Lock box、Credit Card Harris Bank
                    } else if (pay.Pay_Method__c == 'PAD') {
                        itemParamMap.put('Receipt_Method', 'Lock Box Canada'); // Lock Box Canada
                    }
                    itemParamMap.put('Payment_Method', pay.Pay_Method__c); // Credit Card、Debit Card、ACH
                    itemParamMap.put('Receipt_Amount', pay.Actual_Paid_Amount__c);
                    itemParamMap.put('Invoice_Number', item.Invoice__r.Invoice_Number__c);
                    if(!Test.isRunningTest()){
                        itemParamMap.put('Accounting_OracleID',Integer.valueOf(sequense.Accounting_Balance__r.Accounting_OracleID__c));
                    }else{
                        itemParamMap.put('Accounting_OracleID',14534);
                    }
                    itemParamMap.put('Total_Invoice_Amount', sequense.Total_Amount__c);
                    itemParamMap.put('Invoice_Type', item.Invoice__r.Invoice_type__c);
                    if (sequense.Cash_Discount_Amount__c != 0 && sequense.Cash_Discount_Amount__c != null) {
                        itemParamMap.put('Cash_Discount_Amount', Integer.valueOf(sequense.Cash_Discount_Amount__c.abs()));
                    } else {
                        itemParamMap.put('Cash_Discount_Amount', 0);
                    }
                    itemParamMap.put('Actual_Apply_Amount', sequense.Actual_Paid_Amount__c);
                    itemParamMap.put('Account_Number', pay.Customer__r.AccountNumber);
                    itemParamMap.put('Bill_To', pay.BillTo__r.Customer_Line_Oracle_ID__c);
                    itemParamMap.put('Org_Code', pay.ORG_Code__c);
                    itemParamMap.put('Currency_Code', pay.CurrencyIsoCode);
                    if(boolIsSuccess){
                        itemParamMap.put('Payment_Status', 'PAID');
                    }else{
                        itemParamMap.put('Payment_Status', 'FAILED');
                    }
                    itemList.add(itemParamMap);
                }
            } else {
                Map<String, Object> itemParamMap = new Map<String, Object>();
                itemParamMap.put('Receipt_Number', pay.Name);
                itemParamMap.put('Apply_Date', pay.CreatedDate);
                if (pay.Pay_Method__c == 'Credit Card' || pay.Pay_Method__c == 'Debit Card') {
                    itemParamMap.put('Receipt_Method', 'Credit Card_Old Second'); // Lock box、
                } else if (pay.Pay_Method__c == 'ACH') {
                    itemParamMap.put('Receipt_Method', 'Lock Box'); // Lock box、Credit Card Harris Bank
                } else if (pay.Pay_Method__c == 'PAD') {
                    itemParamMap.put('Receipt_Method', 'Lock Box Canada'); // Lock Box Canada
                }
                itemParamMap.put('Payment_Method', pay.Pay_Method__c); // Credit Card、Debit Card、ACH
                itemParamMap.put('Receipt_Amount', pay.Actual_Paid_Amount__c);
                itemParamMap.put('Invoice_Number', item.Invoice__r.Invoice_Number__c);
                if(!Test.isRunningTest()){
                    itemParamMap.put('Accounting_OracleID', Integer.valueOf(invoiceMap.get(item.Invoice__c)));
                }else{
                     itemParamMap.put('Accounting_OracleID', 1234);
                }
                itemParamMap.put('Total_Invoice_Amount', item.Total_Amount__c);
                itemParamMap.put('Invoice_Type', item.Invoice__r.Invoice_type__c);
                if (item.Cash_Discount_Amount__c != 0 && item.Cash_Discount_Amount__c != null) {
                    itemParamMap.put('Cash_Discount_Amount', Integer.valueOf(item.Cash_Discount_Amount__c.abs()));
                } else {
                    itemParamMap.put('Cash_Discount_Amount', 0);
                }
                itemParamMap.put('Actual_Apply_Amount', item.Actual_Paid_Amount__c);
                itemParamMap.put('Account_Number', pay.Customer__r.AccountNumber);
                itemParamMap.put('Bill_To', pay.BillTo__r.Customer_Line_Oracle_ID__c);
                itemParamMap.put('Org_Code', pay.ORG_Code__c);
                itemParamMap.put('Currency_Code', pay.CurrencyIsoCode);
                 if(boolIsSuccess){
                    itemParamMap.put('Payment_Status', 'PAID');
                }else{
                    itemParamMap.put('Payment_Status', 'FAILED');
                }
                itemList.add(itemParamMap);
            }
        }
        String paramStr = JSON.serialize(itemList);
        String endPointName = 'chervon_seeburger_uat';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + 'dunningpayment';
        String HeaderKey = 'Basic ' + headerToken;
        SyncResult sres = new SyncResult();
        String resStatus ='';
        String resMessage ='';
        if (!Test.isRunningTest()) {
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey);
            // sres = (SyncResult) JSON.deserialize(res.getBody(), SyncResult.class);
            Integer statusCode = res.getStatusCode();
            resMessage = res.getBody();
            if(statusCode == 200){
                resStatus = 'Process_Status:Success';
            }else{
                resStatus = 'Process_Status:Failed';
            }

        }
        Log__c pushPaymentLog = new Log__c(
            ApexName__c = 'CCM_Service',
            Method__c = 'pushBeforePaymentInfo',
            Name = 'CCM_Service.pushBeforePaymentInfo: ' + ' at ' + Datetime.now().format(CCM_Constants.DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
            ReqParam__c = paramStr,
            ResParam__c = resStatus,
            Error_Message__c = resMessage
        );
        insert pushPaymentLog;
        if (resStatus != 'Process_Status:Success') {
            try {
                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                List<String> sendTo = new List<String>();
                List<String> emailForAR = new List<String>();
                if(pay.ORG_Code__c =='CCA'){
                    emailForAR = Label.Email_CAAR.split(';');
                }else{
                    emailForAR = Label.Email_AR.split(';');
                }
                sendTo.addAll(emailForAR);
                email.setCharset('UTF-8');
                email.setSubject('Push to Oracle');
                String line1 = 'Dear Customer,<br>';
                String line2 = '    We are sorry to inform you that your Payment ID = ' + pay.Name +' failed to be processed in Oracle.Please check it in Oracle and handle it manually. Thank you!<br>';
                String body = line1 + line2;
                email.setHtmlBody(body);
                email.setToAddresses(sendTo);
                // email.setPlainTextBody( body );
                Messaging.SendEmailResult[] r = Messaging.sendEmail(
                    new List<Messaging.SingleEmailMessage>{ email }
                );
            } catch (Exception e) {
                System.debug('CCM_Service.pushPaymentInfo>>Err:' + e.getMessage() + e.getLineNumber());
            }
        }
    }

    public static void pushPaymentInfoInBatch(String paymentId) {
        String queryStr =
            'SELECT Id, OwnerId, Name, CurrencyIsoCode, CreatedDate, CreatedById, CreatedBy.TimeZoneSidKey, ' +
            'Customer__c, ' +
            'Customer__r.AccountNumber, ' +
            'Total_Amount__c, ' +
            'Actual_Paid_Amount__c, ' +
            'Result__c, ' +
            'Pay_Method__c, ' +
            'Transaction_ID__c, ' +
            'BillTo__r.Customer_Line_Oracle_ID__c, ' +
            'Billing_Address__c, ' +
            'ORG_Code__c, ' +
            'Payment_Date__c, ' +
            'Cash_Discount_Gross_Amount__c, ' +
            'Total_Credit_Memo_Amount__c, ' +
            'Remark__c, ' +
            'First_Pay_By_Paypal__c, ' +
            '(SELECT Id, CreatedDate, ' +
            'Invoice__c, ' +
            'Invoice__r.Invoice_Number__c, ' +
            'Invoice__r.Invoice_Type__c, ' +
            'Order__c, ' +
            'Invoice_Type__c, ' +
            'Accounting_Balance__c, ' +
            'Accounting_Balance__r.Accounting_OracleID__c, ' +
            'Cash_Discount_Amount__c, ' +
            'Total_Amount__c, ' +
            'Actual_Paid_Amount__c, ' +
            'Payment_Information__c, ' +
            'Payment_Information_Item_accounting__c, ' +
            'Has_No_Sequence__c ' +
            'FROM Payment_Information_Items__r) ' +
            'FROM Payment_Information__c WHERE Id = :paymentId';

        List<SObject> objList = Database.query(queryStr);
        if (objList.size() == 0) {
            return;
        }
        List<Map<String, Object>> mapList = new List<Map<String, Object>>();
        Map<String, Object> paramMap = new Map<String, Object>();
        Payment_Information__c pay = (Payment_Information__c) objList[0];
        paramMap.put('Receipt_Number', pay.Name);
        paramMap.put('Gl_Date', pay.Payment_Date__c);
        if (pay.Pay_Method__c == 'Credit Card' || pay.Pay_Method__c == 'Debit Card') {
            paramMap.put('Receipt_Method', 'Credit Card_Old Second'); // Lock box、
        } else if (pay.Pay_Method__c == 'ACH') {
            paramMap.put('Receipt_Method', 'Lock Box'); // Lock box、Credit Card Harris Bank
        } else if (pay.Pay_Method__c == 'PAD') {
            paramMap.put('Receipt_Method', 'Lock Box Canada'); // Lock Box Canada
        }
        paramMap.put('Payment_Transaction_Id', pay.Id);
        paramMap.put('Payment_Method', pay.Pay_Method__c); // Credit Card、Debit Card、ACH
        paramMap.put('Receipt_Amount', pay.Actual_Paid_Amount__c);
        if (pay.Cash_Discount_Gross_Amount__c != 0 && pay.Cash_Discount_Gross_Amount__c != null) {
            paramMap.put('Cash_Discount_Gross_Amount', pay.Cash_Discount_Gross_Amount__c.abs());
        } else {
            paramMap.put('Cash_Discount_Gross_Amount', '');
        }
        paramMap.put('Invoice_Total_Net_Amount', pay.Total_Amount__c);
        paramMap.put('Total_Credit_Memo_Amount', pay.Total_Credit_Memo_Amount__c);
        paramMap.put('Account_Number', pay.Customer__r.AccountNumber);
        paramMap.put('Bill_To', pay.BillTo__r.Customer_Line_Oracle_ID__c);
        paramMap.put('Org_Code', pay.ORG_Code__c);
        paramMap.put('Currency_Code', pay.CurrencyIsoCode);
        if (pay.First_Pay_By_Paypal__c) {
            paramMap.put('New_Invoice_Flag', 'Y');
        } else {
            paramMap.put('New_Invoice_Flag', 'N');
        }
        paramMap.put('Attribute1', '');
        paramMap.put('Attribute2', '');
        paramMap.put('Attribute3', '');
        paramMap.put('Attribute4', '');
        paramMap.put('Attribute5', '');
        paramMap.put('Attribute6', '');
        paramMap.put('Attribute7', '');
        paramMap.put('Attribute8', '');
        paramMap.put('Attribute9', '');
        paramMap.put('Attribute10', '');

        List<Map<String, Object>> itemList = new List<Map<String, Object>>();
        Set<Id> itemIds = new Set<Id>();
        for (Payment_Information_Item__c item : pay.Payment_Information_Items__r) {
            itemIds.add(item.id);
        }

        Map<String, List<Payment_Information_Item__c>> sequenseItem = new Map<String, List<Payment_Information_Item__c>>();

        for (Payment_Information_Item__c each : [SELECT Id,
                                                        Invoice__c,
                                                        Invoice__r.Invoice_Number__c,
                                                        Invoice__r.Invoice_Type__c,
                                                        Invoice_Type__c,
                                                        Accounting_Balance__c,
                                                        Accounting_Balance__r.Accounting_OracleID__c,
                                                        Actual_Paid_Amount__c,
                                                        Cash_Discount_Amount__c,
                                                        Payment_Information_Item_accounting__c,
                                                        Total_Amount__c
                                                    FROM Payment_Information_Item__c
                                                    WHERE Payment_Information_Item_accounting__c IN :itemIds]) {
            if (!sequenseItem.containsKey(each.Payment_Information_Item_accounting__c)) {
                List<Payment_Information_Item__c> accountingList = new List<Payment_Information_Item__c>();
                accountingList.add(each);
                sequenseItem.put(each.Payment_Information_Item_accounting__c, accountingList);
            } else {
                sequenseItem.get(each.Payment_Information_Item_accounting__c).add(each);
            }
        }

        for (Payment_Information_Item__c item : pay.Payment_Information_Items__r) {
            if (sequenseItem.containsKey(item.Id)) {
                for (Payment_Information_Item__c sequense : sequenseItem.get(item.Id)) {
                    Map<String, Object> itemParamMap = new Map<String, Object>();
                    itemParamMap.put('Invoice_Number', item.Invoice__r.Invoice_Number__c);
                    itemParamMap.put('Apply_Date', pay.CreatedDate.format('yyyy-MM-dd', pay.CreatedBy.TimeZoneSidKey)); // 核销日期
                    itemParamMap.put('GL_Date', pay.Payment_Date__c); // 支付日期
                    itemParamMap.put('Accounting_OracleID',sequense.Accounting_Balance__r.Accounting_OracleID__c);
                    itemParamMap.put('Total_Invoice_Amount', sequense.Total_Amount__c);
                    itemParamMap.put('Invoice_Type', item.Invoice__r.Invoice_type__c);
                    if (sequense.Cash_Discount_Amount__c != 0 && sequense.Cash_Discount_Amount__c != null) {
                        itemParamMap.put('Cash_Discount_Amount', sequense.Cash_Discount_Amount__c.abs());
                    } else {
                        itemParamMap.put('Cash_Discount_Amount', '');
                    }
                    itemParamMap.put('Actual_Apply_Amount', sequense.Actual_Paid_Amount__c);
                    itemParamMap.put('Line_Attribute1', '');
                    itemParamMap.put('Line_Attribute2', '');
                    itemParamMap.put('Line_Attribute3', '');
                    itemParamMap.put('Line_Attribute4', '');
                    itemParamMap.put('Line_Attribute5', '');
                    itemParamMap.put('Line_Attribute6', '');
                    itemParamMap.put('Line_Attribute7', '');
                    itemParamMap.put('Line_Attribute8', '');
                    itemParamMap.put('Line_Attribute9', '');
                    itemParamMap.put('Line_Attribute10', '');
                    itemList.add(itemParamMap);
                }
            } else {
                Map<String, Object> itemParamMap = new Map<String, Object>();
                itemParamMap.put('Invoice_Number', item.Invoice__r.Invoice_Number__c);
                itemParamMap.put('Apply_Date', pay.CreatedDate.format('yyyy-MM-dd', pay.CreatedBy.TimeZoneSidKey)); // 核销日期
                itemParamMap.put('GL_Date', pay.Payment_Date__c); // 支付日期
                itemParamMap.put('Accounting_OracleID', item.Accounting_Balance__r.Accounting_OracleID__c);
                itemParamMap.put('Total_Invoice_Amount', item.Total_Amount__c);
                itemParamMap.put('Invoice_Type', item.Invoice__r.Invoice_type__c);
                if (item.Cash_Discount_Amount__c != 0 && item.Cash_Discount_Amount__c != null) {
                    itemParamMap.put('Cash_Discount_Amount', item.Cash_Discount_Amount__c.abs());
                } else {
                    itemParamMap.put('Cash_Discount_Amount', '');
                }
                itemParamMap.put('Actual_Apply_Amount', item.Actual_Paid_Amount__c);
                itemParamMap.put('Line_Attribute1', '');
                itemParamMap.put('Line_Attribute2', '');
                itemParamMap.put('Line_Attribute3', '');
                itemParamMap.put('Line_Attribute4', '');
                itemParamMap.put('Line_Attribute5', '');
                itemParamMap.put('Line_Attribute6', '');
                itemParamMap.put('Line_Attribute7', '');
                itemParamMap.put('Line_Attribute8', '');
                itemParamMap.put('Line_Attribute9', '');
                itemParamMap.put('Line_Attribute10', '');
                itemList.add(itemParamMap);
            }
        }
        paramMap.put('PayLine', itemList);
        String mdtName = 'PayPal_Sandbox';
        String orgId = UserInfo.getOrganizationId();
        Organization org = [
            SELECT Id, IsSandbox
            FROM Organization
            WHERE Id = :orgId
        ];
        if (!org.IsSandbox) {
            mdtName = 'PayPal_Production';
        }
        String paramStr = JSON.serialize(paramMap);
        String endPointName = 'chervon_seeburger_uat';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + 'onlinepayment';
        String HeaderKey = 'Basic ' + headerToken;
        SyncResult sres = new SyncResult();

        if (!Test.isRunningTest()) {
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST', HeaderKey);
            sres = (SyncResult) JSON.deserialize(res.getBody(), SyncResult.class);
        }
        Log__c pushPaymentLog = new Log__c(
            ApexName__c = 'CCM_Service',
            Method__c = 'pushPaymentInfo',
            Name = 'CCM_Service.pushPaymentInfo: ' + ' at ' + Datetime.now().format(CCM_Constants.DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
            ReqParam__c = paramStr,
            ResParam__c = JSON.serialize(sres),
            Error_Message__c = sres.Process_Result[0].Error_Detail
        );
        insert pushPaymentLog;
        if (sres.Process_Status != 'Success') {
            pay.Result__c = 'Pending';
            update pay;
            try {
                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                List<String> sendTo = new List<String>();
                List<String> emailForAR = new List<String>();
                if(pay.ORG_Code__c =='CCA'){
                    emailForAR = Label.Email_CAAR.split(';');
                }else{
                    emailForAR = Label.Email_AR.split(';');
                }
                sendTo.addAll(emailForAR);
                email.setCharset('UTF-8');
                email.setSubject('Push to Oracle');
                String line1 = 'Dear Customer,<br>';
                String line2 = '    We are sorry to inform you that your Payment ID = ' + pay.Name +' failed to be processed in Oracle.The reason is '+sres.Process_Result[0].Error_Detail+'. Please check it in Oracle and handle it manually. Thank you!<br>';
                String body = line1 + line2;
                email.setHtmlBody(body);
                email.setToAddresses(sendTo);
                // email.setPlainTextBody( body );
                Messaging.SendEmailResult[] r = Messaging.sendEmail(
                    new List<Messaging.SingleEmailMessage>{ email }
                );
            } catch (Exception e) {
                System.debug('CCM_Service.pushPaymentInfo>>Err:' + e.getMessage() + e.getLineNumber());
            }
        } else {
            pay.Result__c = 'Success';
            update pay;
        }
    }

    @future(callout=true)
    public static void pushWarrantyReturn(Set<String> returnIds) {
        String queryStr = 'SELECT Id, Name, CurrencyIsoCode, BillTo__c, ShipTo__c, BillTo__r.Customer_Line_Oracle_ID__c, ShipTo__r.Customer_Line_Oracle_ID__c, '
                        + ' Additional_Contact_Email__c, Additional_Contact_Name__c, '
                        + ' Additional_Contact_Phone__c, Additional_Shipping_City__c, '
                        + ' Additional_Shipping_Country__c, Additional_Shipping_Postal_Code__c, '
                        + ' Additional_Shipping_Province__c, Additional_Shipping_Street2__c, Additional_Shipping_Street__c, '
                        + ' Additional_Store_City__c, Additional_Store_Contact_Email__c, Additional_Store_Contact_Name__c, '
                        + ' Additional_Store_Contact_Phone__c, Additional_Store_Country__c, Additional_Store_Postal_Code__c, '
                        + ' Additional_Store_Province__c, Additional_Store_Street2__c, Additional_Store_Street__c, Approval_Status__c, '
                        + ' Contact_Email_Address__c, Contact_Name__c, '
                        + ' Contact_Phone_Number__c, Customer_Reference_Number__c, Customer__c, Customer__r.AccountNumber, '
                        + ' Debit_Memo_Number__c, End_Date__c, Is_Alternative_Address__c, '
                        + ' Is_Alternative_Store_Address__c, Notes__c, Order__c, Payment_Method__c, '
                        + ' Return_Goods_Status__c, Shipping_Address__c, '
                        + ' Start_Date__c, Store_Address__c, Store_Location__c, Warehouse_Location__c, '
                        + ' Warranty_Return_Request_No__c, '
                        + ' Store_Location__r.Address1__c, Store_Location__r.Address2__c, Store_Location__r.City__c, Store_Location__r.Name, '
                        + ' Store_Location__r.Contact__c, Store_Location__r.Country__c, Store_Location__r.Postal_Code__c, '
                        + ' Store_Location__r.State__c, Store_Location__r.Store_Number__c, Store_Location__r.Dropship_Contact2__c, Store_Location__r.Dropship_Contact3__c, '
                        + ' Store_Location__r.Dropship_Contact4__c, Store_Location__r.Dropship_Contact5__c, Store_Location__r.Dropship_Contact__c,'
                        + ' Warranty_Return_Claim_Start_Date__c, '
                        + ' BillTo__r.Program__r.Payment_Term__c, BillTo__r.Program__c, BillTo__r.Program__r.Freight_Term__c, '
                        + ' (SELECT Id, Name, DIF_RTV__c, Price_Book__r.Name, Return_Reason__c, '
                        + ' Order_Item__r.Price_Book__r.Name, Order_Item__r.Selling_Warehouse__c, Order_Item__r.Order__r.PO_Number__c, Order_Item__r.Order__r.Order_Number__c, Order_Item__r.OrderLine_OracleID__c, Invoice_Price__c, Model__c, Quantity__c, '
                        + ' Model__r.ProductCode, Warranty_Return_Claim__c, Subtotal__c FROM Warranty_Return_Claim_Item__r) '
                        + ' FROM Warranty_Return_Claim__c WHERE Id IN :returnIds AND Is_Deleted__c = false';
        List<Warranty_Return_Claim__c> returnList = Database.query(queryStr);

        List<Map<String, Object>> mapList = new List<Map<String, Object>>();
        Map<String, Object> paramMap = new Map<String, Object>();
        Warranty_Return_Claim__c warrantyReturn = (Warranty_Return_Claim__c) returnList[0];

        Set<String> productCodes = new Set<String>();
        for (Warranty_Return_Claim_Item__c item : warrantyReturn.Warranty_Return_Claim_Item__r) {
            productCodes.add(item.Model__r.ProductCode);
        }
        Map<String, PricebookEntry> productPriceMap = Util.getActivePirceBookEntrySales(warrantyReturn.Customer__c, productCodes);

        paramMap.put('Salesforce_PurchaseOrder_Number', warrantyReturn.Name);
        paramMap.put('PO_Number', warrantyReturn.Name);
        paramMap.put('Customer', warrantyReturn.Customer__r.AccountNumber);
        paramMap.put('BillTo', warrantyReturn.BillTo__r.Customer_Line_Oracle_ID__c);
        paramMap.put('ShipTo', warrantyReturn.ShipTo__r.Customer_Line_Oracle_ID__c);
        paramMap.put('PurchaseOrder_Flag', 'Place_Reverse_Order');
        paramMap.put('Order_Type', 'CNA Return Order - Warranty');
        paramMap.put('CurrencyCode', warrantyReturn.CurrencyIsoCode);
        paramMap.put('Org_Code', 'CNA');
        paramMap.put('Date_Order', '');

        // shipping location
        paramMap.put('Dropship_Name ', warrantyReturn.Additional_Contact_Name__c);
        paramMap.put('Dropship_Address1', warrantyReturn.Additional_Shipping_Street__c);
        paramMap.put('Dropship_Address2', warrantyReturn.Additional_Shipping_Street2__c);
        paramMap.put('Telephone_Number', warrantyReturn.Additional_Contact_Phone__c);
        paramMap.put('Dropship_Country', warrantyReturn.Additional_Shipping_Country__c);
        paramMap.put('Dropship_ZIP', warrantyReturn.Additional_Shipping_Postal_Code__c);
        paramMap.put('Dropship_State', warrantyReturn.Additional_Shipping_Province__c);
        paramMap.put('Dropship_City', warrantyReturn.Additional_Shipping_City__c);

        // store location
        paramMap.put('Dropship_Name ', warrantyReturn.Store_Location__r.Name);
        paramMap.put('Dropship_Address1', warrantyReturn.Store_Location__r.Address1__c);
        paramMap.put('Dropship_Address2', warrantyReturn.Store_Location__r.Address2__c);
        paramMap.put('Telephone_Number', '');
        paramMap.put('Dropship_Country', warrantyReturn.Store_Location__r.Country__c);
        paramMap.put('Dropship_ZIP', warrantyReturn.Store_Location__r.Postal_Code__c);
        paramMap.put('Dropship_State', warrantyReturn.Store_Location__r.State__c);
        paramMap.put('Dropship_City', warrantyReturn.Store_Location__r.City__c);

        paramMap.put('Payment_Term', String.isNotEmpty(warrantyReturn.BillTo__c) ? (String.isNotEmpty(warrantyReturn.BillTo__r.Program__c) ? warrantyReturn.BillTo__r.Program__r.Payment_Term__c : '') : '');
        paramMap.put('Freight_Term', String.isNotEmpty(warrantyReturn.BillTo__c) ? (String.isNotEmpty(warrantyReturn.BillTo__r.Program__c) ? warrantyReturn.BillTo__r.Program__r.Freight_Term__c : '') : '');
        paramMap.put('Attribute1', '');
        paramMap.put('Attribute2', '');
        paramMap.put('Attribute3', '');
        paramMap.put('Attribute4', '');
        paramMap.put('Attribute5', '');
        paramMap.put('Attribute6', '');
        paramMap.put('Attribute7', warrantyReturn.Customer_Reference_Number__c);
        paramMap.put('Attribute8', '');
        paramMap.put('Attribute9', '');
        paramMap.put('Attribute10', '');

        List<Map<String, Object>> itemList = new List<Map<String, Object>>();

        for (Warranty_Return_Claim_Item__c item : warrantyReturn.Warranty_Return_Claim_Item__r) {
            Map<String, Object> itemParamMap = new Map<String, Object>();
            itemParamMap.put('Salesforce_PurchaseOrderLine_Number', item.Name);
            itemParamMap.put('Product',item.Model__r.ProductCode);
            itemParamMap.put('Line_Price_List', item.Order_Item__r.Price_Book__r.Name);
            itemParamMap.put('Price', item.Invoice_Price__c);
            itemParamMap.put('Order_Quantity', item.Quantity__c);
            if (productPriceMap.get(item.Model__r.ProductCode)!=null) {
                itemParamMap.put('Line_Price_List', productPriceMap.get(item.Model__r.ProductCode).Pricebook2.Name);
                itemParamMap.put('Price_List_Price', productPriceMap.get(item.Model__r.ProductCode).UnitPrice); //价格册价格
            }
            itemParamMap.put('Line_Type', 'CNA Return Credit with Receipt');
            itemParamMap.put('DIF_RTV', item.DIF_RTV__c);
            itemParamMap.put('Line_Attribute1', item.Return_Reason__c);
            itemParamMap.put('origin_cus_po_no_line', item.Order_Item__r.Order__r.PO_Number__c);
            itemParamMap.put('Line_Attribute6', item.Order_Item__r.OrderLine_OracleID__c);
            itemParamMap.put('Line_Attribute2', item.Order_Item__r.Order__r.Order_Number__c);
            itemParamMap.put('Line_Attribute3', '');
            itemParamMap.put('Line_Attribute4', '');
            itemParamMap.put('Line_Attribute5', 'CNA03');
            itemParamMap.put('Line_Attribute7', '');
            itemParamMap.put('Line_Attribute8', '');
            itemParamMap.put('Line_Attribute9', '');
            itemParamMap.put('Line_Attribute10', '');
            itemList.add(itemParamMap);
        }

        paramMap.put('OrderLine', itemList);
        String paramStr = Json.serialize(paramMap);
        String endPointName = 'chervon_order_oms';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c;
        String HeaderKey = 'Basic ' + headerToken;
        SyncRes Sres = new SyncRes();
        if (!Test.isRunningTest()) {
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST',HeaderKey);
            Sres = (SyncRes) JSON.deserialize(res.getBody(), SyncRes.class);
            Util.logIntegration('outdata_warrantyorder' + '_' + warrantyReturn.Name, 'outdata', 'post', 'null', paramStr, JSON.serialize(Sres));
        }
    }

    public static String getPIMCredential(String settingName) {
        List<MeIntegration_Setting__mdt> MeIS = [
            SELECT End_Point__c, Key__c, userName__c, password__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :settingName
        ];
        if(!MeIS.isEmpty()) {
            return MeIS[0].Key__c;
        }
        return '';
    }

    @future(callout=true)
    public static void pushSampleOrder(String sampleId) {
        String sql = 'SELECT Id, Name, CreatedDate, CreatedBy.Name, '
                        + ' CurrencyIsoCode, '
                        + ' Demo_Free_Goods_Program__c, '
                        + ' Demo_Free_Goods_Program__r.Name, '
                        + ' Demo_Free_Goods_Program__r.Start_Date__c, '
                        + ' Demo_Free_Goods_Program__r.End_Date__c, '
                        + ' Demo_Free_Goods_Program__r.Minimum_Order_Quantity__c, '
                        + ' Demo_Free_Goods_Program__r.Maximum_Order_Quantity__c, '
                        + ' Demo_Free_Goods_Program__r.Brand__c, '
                        + ' Demo_Free_Goods_Program__r.Program_Code__c, '
                        + ' Submit_Time__c, '
                        + ' Contact_Name__c, '
                        + ' Contact_Phone__c, '
                        + ' Contact_Email__c, '
                        + ' Street__c, '
                        + ' City__c, '
                        + ' State__c, '
                        + ' Order_Status__c, '
                        + ' Sample_Order_Number__c, '
                        + ' Zip_Code__c, '
                        + ' Comments__c, '
                        + ' Expected_Delivery_Date__c, '
                        + ' Rush_Charge_Cost_Centre__c, '
                        + ' Requestor_Email__c, '
                        + ' Requester_Phone__c, '
                        + ' Task_Owner__c, '
                        + ' Task_Owner__r.Name, '
                        + ' Total_Amount__c, '
                        + ' (SELECT Id, Name, '
                                + ' Product__c, '
                                + ' Product__r.ProductCode, '
                                + ' Product__r.SF_Description__c, '
                                + ' Product__r.Brand_Name__c, '
                                + ' Quantity__c, '
                                + ' MSRP_Price__c, '
                                + ' Invoice_Price__c, '
                                + ' Price_Book__c, '
                                + ' Price_Book__r.Name, '
                                + ' Subtotal__c, '
                                + ' Demo_Plan__c, '
                                + ' Demo_Plan_Item__c, '
                                + ' Demo_Plan__r.OwnerId, '
                                + ' Demo_Plan__r.Owner.Name, '
                                + ' Demo_Plan__r.Name, '
                                + ' Demo_Plan__r.Demo_Plan_Name__c, '
                                + ' Demo_Plan__r.Demo_Free_Goods_Program__c, '
                                + ' Demo_Plan__r.Account_Number__c, '
                                + ' Demo_Plan__r.Date__c, '
                                + ' Demo_Plan__r.Status__c, '
                                + ' Demo_Plan__r.Store_Location_Id__c, '
                                + ' Demo_Plan__r.Free_Sample_Planned__c, '
                                + ' Demo_Plan__r.Edit_Reason__c, '
                                + ' Demo_Plan__r.Customer__c, '
                                + ' Demo_Plan__r.Customer__r.Name, '
                                + ' Demo_Plan__r.Store_Location__c, '
                                + ' Demo_Plan__r.Store_Location__r.Name, '
                                + ' Demo_Plan__r.Used_For_Sample_Order__c '
                                + ' FROM Sample_Order_Items__r) '
                        + ' FROM Sample_Order__c WHERE isDeleted = false AND Id = :sampleId';

        List<Sample_Order__c> sampleOrderList = Database.query(sql);
        Sample_Order__c sampleOrder = sampleOrderList[0];


        Map<String, object> paramMap = new Map<String, Object>();
        String expectedDeliveryDate = sampleOrder.Expected_Delivery_Date__c != null ? sampleOrder.Expected_Delivery_Date__c.year() + '-' + sampleOrder.Expected_Delivery_Date__c.month() + '-' + sampleOrder.Expected_Delivery_Date__c.day() : '';
        paramMap.put('Order_Type', 'CNA Sample Order Only');
        paramMap.put('Date_Order', sampleOrder.Submit_Time__c.format('yyyy-MM-dd'));
        paramMap.put('Expected_Delivery_Date', expectedDeliveryDate);
        paramMap.put('PO_Number', sampleOrder.Sample_Order_Number__c);
        paramMap.put('Salesforce_PurchaseOrder_Number', sampleOrder.Name);
        paramMap.put('Customer','9997');
        paramMap.put('Dropship_Address1', sampleOrder.Street__c);
        paramMap.put('Dropship_City', sampleOrder.City__c);
        paramMap.put('Dropship_State', sampleOrder.State__c);
        paramMap.put('Dropship_Country', 'US');
        paramMap.put('Dropship_ZIP', sampleOrder.Zip_Code__c);
        paramMap.put('Dropship_Name', sampleOrder.Contact_Name__c);
        paramMap.put('Telephone_Number', sampleOrder.Contact_Phone__c);
        paramMap.put('Notes', sampleOrder.Comments__c);
        paramMap.put('Org_Code', 'CNA');
        paramMap.put('CARRIER_NAME', '');
        paramMap.put('CARRIER_CODE', '');
        paramMap.put('PROCESS_STATUS', '');
        paramMap.put('CURRENCY_CODE', sampleOrder.CurrencyIsoCode);
        paramMap.put('CREATION_DATE', sampleOrder.CreatedDate.format('yyyy-MM-dd'));
        paramMap.put('Freight_Fee', '');
        paramMap.put('Handling_Fee', '');
        paramMap.put('CurrencyCode', 'USD');
        paramMap.put('Price_List', 'Warranty and Sample price list');
        paramMap.put('Payment_Term', 'NA999');
        paramMap.put('ShipTo', '13136');
        paramMap.put('BillTo', '13134');
        paramMap.put('PurchaseOrder_Flag', 'Place_Order');
        paramMap.put('CRM_HEADER_ID', '');
        paramMap.put('Sales_Person', '');
        paramMap.put('TaxCode', '');
        paramMap.put('Shipping_Method', '');
        paramMap.put('Shipping_Priority', '2');
        paramMap.put('Attribute1', '');
        paramMap.put('Attribute2', '');
        paramMap.put('Attribute3', '');
        paramMap.put('Attribute4', '');
        paramMap.put('Attribute5', '');
        paramMap.put('Attribute6', '');
        paramMap.put('Attribute7', '');
        paramMap.put('Attribute8', '');
        paramMap.put('Attribute9', '');
        paramMap.put('Attribute10', '');

        List<Map<String, Object>> itemList = new List<Map<String, Object>>();

        for (Sample_Order_Item__c item : sampleOrder.Sample_Order_Items__r) {
            Map<String, Object> itemParamMap = new Map<String, Object>();
            itemParamMap.put('Salesforce_PurchaseOrderLine_Number', item.Name);
            itemParamMap.put('Product', item.Product__r.ProductCode);
            itemParamMap.put('Order_Quantity', item.Quantity__c);

            itemParamMap.put('Price', item.Invoice_Price__c);
            itemParamMap.put('Line_Price_List', item.Price_Book__r.Name);
            itemParamMap.put('Price_List_Price', item.MSRP_Price__c);

            itemParamMap.put('Warehouse_Ship_Window_From', expectedDeliveryDate);
            itemParamMap.put('Warehouse_Ship_Window_To', expectedDeliveryDate);
            itemParamMap.put('Customer_Deliery_Request_Date', expectedDeliveryDate);
            itemParamMap.put('Customer_Deliery_Deadline_Date', expectedDeliveryDate);
            itemParamMap.put('Request_Date', sampleOrder.Submit_Time__c.format('yyyy-MM-dd'));
            itemParamMap.put('Line_Type', 'CNA Sample Free of Charge');
            itemParamMap.put('Subinventory', item.Product__r.Brand_Name__c == 'EGO' ? 'Chino' : (item.Product__r.Brand_Name__c == 'SKIL' ? 'Olive' : ''));
            itemParamMap.put('Subcharge', '');
            itemParamMap.put('Line_Attribute1', '');
            itemParamMap.put('Line_Attribute2', '');
            itemParamMap.put('Line_Attribute3', '');
            itemParamMap.put('Line_Attribute4', '');
            itemParamMap.put('Line_Attribute5', '');
            itemParamMap.put('Line_Attribute6', '');
            itemParamMap.put('Line_Attribute7', '');
            itemParamMap.put('Line_Attribute8', '');
            itemParamMap.put('Line_Attribute9', '');
            itemParamMap.put('Line_Attribute10', '');
            itemList.add(itemParamMap);
        }

        paramMap.put('OrderLine', itemList);
        String paramStr = Json.serialize(paramMap);
        String endPointName = 'chervon_order_oms';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
            LIMIT 1
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c;
        String HeaderKey = 'Basic ' + headerToken;
        SyncRes Sres = new SyncRes();
        if (!Test.isRunningTest()) {
            HttpResponse res = CCM_ServiceCallout.getDataViaHttp(paramStr, endPoint, 'POST',HeaderKey);
            Sres = (SyncRes) JSON.deserialize(res.getBody(), SyncRes.class);
            Util.logIntegration('outdata_sampleorder' + '_' + sampleOrder.Sample_Order_Number__c, 'outdata', 'post', 'null', paramStr, JSON.serialize(Sres));
        }
    }



    public class SyncRes {
        public String ReturnCode;
        public String ReturnMessage;
    }

    public class SyncResult {
        public String ReturnCode;
        public String ReturnMessage;
        public String Process_Status;
        public List<ProcessResult> Process_Result;
    }

    public class ProcessResult {
        public String SFDC_Id;
        public String Oracle_Id;
        public String Error_Message;
        public String Error_Detail;
    }
}