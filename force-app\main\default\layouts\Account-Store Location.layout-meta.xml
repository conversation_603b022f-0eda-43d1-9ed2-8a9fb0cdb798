<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>AccountHierarchy</excludeButtons>
    <excludeButtons>CreateAccountChannel</excludeButtons>
    <excludeButtons>CreateCallList</excludeButtons>
    <excludeButtons>CreatePartnerChannelQuickAction</excludeButtons>
    <excludeButtons>CreateSurveyInvitation</excludeButtons>
    <excludeButtons>Delete</excludeButtons>
    <excludeButtons>DisableCustomerPortalAccount</excludeButtons>
    <excludeButtons>DisablePartnerPortalAccount</excludeButtons>
    <excludeButtons>IncludeOffline</excludeButtons>
    <excludeButtons>LoginToNetworkAsPersonUser</excludeButtons>
    <excludeButtons>LoginToPortalAsPersonUser</excludeButtons>
    <excludeButtons>PartnerScorecard</excludeButtons>
    <excludeButtons>PrintableView</excludeButtons>
    <excludeButtons>RecordShareHierarchy</excludeButtons>
    <excludeButtons>SendSurveyInvitation</excludeButtons>
    <excludeButtons>Share</excludeButtons>
    <excludeButtons>StartOutboundConversation</excludeButtons>
    <excludeButtons>XClean</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Store Basic Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Store_Address__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Related_Entity__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ACE_Network__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Buying_Group__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Mobile__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Website</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Store_Number2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Conversion_Reason__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Closed__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Abbv_Of_Related_Entity__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ORG_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EGO_Store_Rank__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>X2nd_Tier_Store__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SKIL_Store_Rank__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EGO_Last_Year_POS_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>County__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Related_Entity_Prospect__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Email__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Contact_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Fax</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Store_Number_Comment__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Conversion_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Close_Reason__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>StoreNumber_In_SF__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Store Labels</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Selling_Brands_Customer__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Selling_Categories_Customer__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Selling_Rider_Customer__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Service_Categories__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Service_Rider__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Selling_Brands__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Selling_Categories__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Selling_Rider__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>EGO-Store Purchase (Direct)</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EGO_This_Year_DirectF_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EGO_Direct_YoY_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EGO_Last_Year_DirectF_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EGO_Last_Year_Direct_Amount_F__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>EGO-Store Purchase (Warehouse)</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EGO_This_Year_Retail_Purcha_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EGO_Retail_Purchase_YoY_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EGO_Last_Year_Retail_Purcha_YTD_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>EGO-Store Sales (POS)</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EGO_This_Year_POS_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EGO_POS_YoY_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EGO_Last_Year_POS_YTD_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>SKIL-Store Purchase (Direct)</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SKIL_This_Year_DirectF_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SKIL_Direct_YoY_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SKIL_Last_Year_DirectF_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SKIL_Last_Year_Direct_Amount_F__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>SKIL-Store Purchase (Warehouse)</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SKIL_This_Year_Retail_Purch_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SKIL_Retail_Purchase_YoY_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SKIL_Last_Year_Retail_Purch_YTD_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>SKIL-Store Sales (POS)</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SKIL_This_Year_POS_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SKIL_POS_YoY_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SKIL_Last_Year_POS_YTD_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>FLEX-Store Purchase (Direct)</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FLEX_This_Year_DirectF_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FLEX_Direct_YoY_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FLEX_Last_Year_DirectF_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FLEX_Last_Year_Direct_Amount_F__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>FLEX-Store Purchase (Warehouse)</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FLEX_This_Year_Retail_Purch_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FLEX_Retail_Purchase_YoY_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FLEX_Last_Year_Retail_Purch_YTD_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>FLEX-Store Sales (POS)</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FLEX_This_Year_POS_YTD_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FLEX_POS_YoY_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FLEX_Last_Year_POS_YTD_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ParentId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Log_a_Call</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentNote</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeRecordType</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.LinkPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.PollPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.QuestionPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeOwnerOne</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>10</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.Take_Survey</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>11</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>FeedItem.MobileSmartActions</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedLists>
        <fields>ORDERS.ORDER_NUMBER</fields>
        <fields>ORDERS.STATUS</fields>
        <fields>ORDERS.EFFECTIVE_DATE</fields>
        <fields>CONTRACT.CONTRACT_NUMBER</fields>
        <fields>ORDERS.TOTAL_AMOUNT</fields>
        <relatedList>RelatedOrderList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>CONTACT.TITLE</fields>
        <fields>CONTACT.EMAIL</fields>
        <fields>CONTACT.PHONE1</fields>
        <fields>Functional_Role__c</fields>
        <relatedList>RelatedContactList</relatedList>
    </relatedLists>
    <relatedLists>
        <customButtons>New_Address</customButtons>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>RECORDTYPE</fields>
        <fields>Address1__c</fields>
        <fields>Customer__c</fields>
        <fields>Postal_Code__c</fields>
        <fields>City__c</fields>
        <fields>Country__c</fields>
        <fields>Account_Number__c</fields>
        <relatedList>Account_Address__c.Store_Location__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>X2nd_Tier_Dealer__c</fields>
        <fields>Brand__c</fields>
        <fields>Model__c</fields>
        <fields>Category__c</fields>
        <fields>This_Year_POS_YTD_Amount__c</fields>
        <fields>Last_Year_POS_YTD_Amount__c</fields>
        <fields>POS_Amount_YoY_Percent__c</fields>
        <fields>This_Year_POS_YTD_Qty__c</fields>
        <fields>Last_Year_POS_YTD_Qty__c</fields>
        <relatedList>Store_Location_Summary__c.Store_Location__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>ORDERS.ORDER_NUMBER</fields>
        <fields>SALES.ACCOUNT.NAME</fields>
        <fields>Total_Amount__c</fields>
        <fields>Date_Order__c</fields>
        <fields>Order_Status__c</fields>
        <fields>ORDER.RECORDTYPE</fields>
        <relatedList>Order.Store_Location__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <fields>NAME</fields>
        <fields>Year__c</fields>
        <fields>Month__c</fields>
        <fields>Week__c</fields>
        <fields>Model_Number__c</fields>
        <fields>Units__c</fields>
        <fields>Cost__c</fields>
        <relatedList>RetailPurchase__c.Customer_Store_Location__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <fields>NAME</fields>
        <fields>Model_Number__c</fields>
        <fields>Sales_Amount__c</fields>
        <fields>Sales_Unit__c</fields>
        <fields>Brand__c</fields>
        <fields>Product_Category__c</fields>
        <fields>Year__c</fields>
        <fields>Month__c</fields>
        <fields>Week__c</fields>
        <fields>Date__c</fields>
        <relatedList>POS_Data__c.Customer_Store_Location__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Attachment_Management_Of_Customer__c.Customer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TERRITORY.NAME</fields>
        <fields>MODEL.NAME</fields>
        <fields>MODEL.STATE</fields>
        <fields>ASSIGNMENT_DATE</fields>
        <fields>ASSOCIATION_CAUSE</fields>
        <relatedList>AccountAssignedTerritory2sRelatedList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>CORE.USERS.EMAIL</fields>
        <fields>ROLE_IN_TERRITORY</fields>
        <fields>TERRITORY.NAME</fields>
        <fields>MODEL.NAME</fields>
        <relatedList>AccountUserTerritory2RelatedList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Map_Survey_Taken__c.Store_Location__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CAMPAIGN.NAME</fields>
        <fields>Campaign_Type__c</fields>
        <fields>Sales_Manager__c</fields>
        <fields>Brands__c</fields>
        <fields>CAMPAIGN.START_DATE</fields>
        <fields>CAMPAIGN.END_DATE</fields>
        <fields>CAMPAIGN.STATUS</fields>
        <fields>CAMPAIGN.LAST_UPDATE</fields>
        <relatedList>RelatedAccountCampaignList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Visit_Survey__c.Store_Location__c</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Model_Number__c</fields>
        <fields>Theorical_Inventory_This_Period__c</fields>
        <fields>Update_Date_This_Period__c</fields>
        <relatedList>Store_Inventory_by_Model__c.Customer__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CreatedDate</fields>
        <fields>StepStatus</fields>
        <fields>OriginalActor</fields>
        <fields>Actor</fields>
        <fields>Comments</fields>
        <relatedList>RelatedProcessHistoryList</relatedList>
    </relatedLists>
    <relatedObjects>ParentId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h8a00000bmBjY</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>2</sizeY>
        <summaryLayoutItems>
            <field>Related_Entity__c</field>
            <posX>0</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Related_Entity_Prospect__c</field>
            <posX>0</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>RecordTypeId</field>
            <posX>1</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Contact_Name__c</field>
            <posX>1</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Phone</field>
            <posX>2</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Mobile__c</field>
            <posX>2</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Closed__c</field>
            <posX>3</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Store_Address__c</field>
            <posX>3</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
