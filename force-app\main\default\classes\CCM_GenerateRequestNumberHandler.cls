/**
 * <AUTHOR>
 * @date 2021-05-24
 * @description This is <PERSON><PERSON> is used to Generate Reverse Order Request Number "RO/CustomerNumber/OriginalOrderNumberinEBS/CountNumber".
 */
public without sharing  class CCM_GenerateRequestNumberHandler implements Triggers.Handler{
    public void handle() {
        List<Reverse_Order_Request__c> reverseOrderList = (List<Reverse_Order_Request__c>)Trigger.new;
        Map<Id, Reverse_Order_Request__c> oldMap = (Map<Id, Reverse_Order_Request__c>)Trigger.oldMap;
        // update trigger, update records' ids
        // Set<Id> reverseOrderOldIds = new Set<Id>();
        Set<Id> customerSet = new Set<Id>();
        Set<Id> OrderSet = new Set<Id>();
        for(Reverse_Order_Request__c ro : reverseOrderList){
            if(ro.Customer__c!=null){
                customerSet.add(ro.Customer__c);
            }
            if(ro.Order__c != null){
                OrderSet.add(ro.Order__c);
            }
            if(Trigger.isUpdate){
                // reverseOrderOldIds.add(ro.Id);

                // used in approval, if ops not update raise error
                if(Trigger.isBefore){
                    if(ro.Approval_Status__c == 'OPS Approved'){
                        if(String.isEmpty(ro.Confirmed_by_OPS__c)){
                            ro.addError('Please update ops verification result first!');
                        }
                    }
                    // validate delivery number
                    if (ro.Current_Approver__c == 'Inside Sales' && String.isEmpty(oldMap.get(ro.Id).Current_Approver__c)) {
                        if (String.isEmpty(ro.Shipment__c)) {
                            ro.addError('Please add delivery number first!');
                        }
                    }
                }
            }
        }
        // get all customer info and parent user role
        Map<Id, Account> customerMap = new Map<Id,Account>([Select Id,
                                                                AccountNumber,
                                                                OwnerId,
                                                                Owner.UserRoleId,
                                                                Owner.UserRole.Name,
                                                                Owner.UserRole.ParentRoleId,
                                                                Owner.Email,
                                                                Name,
                                                                (SELECT Id, UserId, TeamMemberRole FROM AccountTeamMembers WHERE TeamMemberRole IN ('Account Assistant', 'Account Manager')),
                                                                (SELECT Id, Reverse_Order_Request_Number__c, Counter__c FROM Reverse_Order_Requests__r ORDER BY Counter__c DESC) 
                                                                From Account 
                                                                where id in:customerSet]);

        Set<Id> parentRoleIdSet = new Set<Id>();
        for(String accId : customerMap.keySet()){
            parentRoleIdSet.add( customerMap.get(accId).Owner.UserRole.ParentRoleId );
        }
        List<User> directorUser = [Select id,UserRoleId, Email From User where UserRoleId in :parentRoleIdSet];
        Map<Id,User> directRoleUserMap = new Map<Id,User>();
        for( User userObj : directorUser ){
            directRoleUserMap.put(userObj.UserRoleId, userObj);
        }
        List<User> salesVPUser = [SELECT Id FROM User WHERE UserRole.Name LIKE '%Sales VP%' AND IsActive = true];

        // Map<Id, Purchase_Order__c> purchaseOrderMap = new Map<Id,Purchase_Order__c>([Select Id,
        //                                                         Customer_PO_Num__c
        //                                                         From Purchase_Order__c 
        //                                                         where id in:purchaseOrderSet]);
        Map<Id, Order> OrderMap = new Map<Id, Order>([Select Id,
                                                            Order_Number__c
                                                            From Order
                                                            where id in:OrderSet]);

        Map<Id, Reverse_Order_Request__c> reverseOrdersOld = (Map<Id, Reverse_Order_Request__c>)Trigger.oldMap;
        // if(Trigger.isUpdate){
        //     reverseOrdersOld = new Map<Id,Reverse_Order_Request__c>([Select Id,
        //                             Customer__c
        //                             From Reverse_Order_Request__c 
        //                             where id in:reverseOrderOldIds]);
        // }
        Map<String,Integer> reverseCounters = new Map<String,Integer>();
        for(Reverse_Order_Request__c reverseOrder : reverseOrderList){
            if(reverseCounters.containsKey(reverseOrder.Customer__c)){
                continue;
            }
            Integer count = 0;
            if(reverseOrder.Customer__c != null && (Trigger.isInsert || (Trigger.isUpdate && reverseOrder.Customer__c != reverseOrdersOld.get(reverseOrder.Id).Customer__c))){
                Integer size = customerMap.get(reverseOrder.Customer__c).Reverse_Order_Requests__r.size();
                if(size>0){
                    // String requestNo = customerMap.get(reverseOrder.Customer__c).Reverse_Order_Requests__r[0].Reverse_Order_Request_Number__c;
                    // System.debug('requestNo = ' + requestNo);
                    // count = Integer.valueOf(requestNo.substringAfterLast('-')) + 1;
                    // System.debug('count = ' + count);
                    if(customerMap.get(reverseOrder.Customer__c).Reverse_Order_Requests__r[0].Counter__c != null){
                        count = (Integer)customerMap.get(reverseOrder.Customer__c).Reverse_Order_Requests__r[0].Counter__c + 1;
                    }
                }else{
                    count = 1;
                }
                reverseCounters.put(reverseOrder.Customer__c,count);
            }
        }

        Messaging.SingleEmailMessage[] messages =   new List<Messaging.SingleEmailMessage>();
        for(Reverse_Order_Request__c reverseOrder : reverseOrderList){
            if(reverseOrder.Customer__c != null && (Trigger.isInsert || (Trigger.isUpdate && reverseOrder.Customer__c != reverseOrdersOld.get(reverseOrder.Id).Customer__c))){
                String customerCode = customerMap.get(reverseOrder.Customer__c).AccountNumber;
                // String customerPONumber = purchaseOrderMap.get(reverseOrder.Purchase_Order__c) == null ? ' ' : purchaseOrderMap.get(reverseOrder.Purchase_Order__c).Customer_PO_Num__c;
                String customerOrderNumberInEBS = OrderMap.get(reverseOrder.Order__c) == null ? ' ' : OrderMap.get(reverseOrder.Order__c).Order_Number__c;
                String customerReverseOrderNumber = '';
                Integer count = reverseCounters.get(reverseOrder.Customer__c);
                if (count >= 10000) {
                    customerReverseOrderNumber = String.valueOf(count);
                } else {
                    customerReverseOrderNumber = '000' + String.valueOf(count);
                    customerReverseOrderNumber = customerReverseOrderNumber.substring(customerReverseOrderNumber.length() - 4);
                }
                reverseOrder.Reverse_Order_Request_Number__c = 'RO' + customerOrderNumberInEBS + customerReverseOrderNumber;
                reverseOrder.Name = reverseOrder.Reverse_Order_Request_Number__c;
                reverseOrder.Counter__c = count;
                System.debug('Reverse_Order_Request_Number__c = ' + reverseOrder.Reverse_Order_Request_Number__c);
                reverseCounters.put(reverseOrder.Customer__c,count + 1);

                // set inside sales and account manager for new reverse order request
                for (AccountTeamMember member : customerMap.get(reverseOrder.Customer__c).AccountTeamMembers) {
                    // allen, change to assign Customer Owner
                    // if (member.TeamMemberRole == 'Account Manager') {
                    //     reverseOrder.Account_Manager__c = member.UserId;
                    // }
                    if (member.TeamMemberRole == 'Account Assistant') {
                        reverseOrder.Inside_Sales__c= member.UserId;
                    }
                }

                if(customerMap.get(reverseOrder.Customer__c).OwnerId != null){
                    reverseOrder.Account_Manager__c = customerMap.get(reverseOrder.Customer__c).OwnerId;
                }

                // if inside sales is null, notify account manager to maintain data, set account manager to inside sales field
                if (String.isEmpty(reverseOrder.Inside_Sales__c)) {
                    reverseOrder.Inside_Sales__c = reverseOrder.Account_Manager__c;
                    // List<User> accountManager = [SELECT Id, Email FROM User WHERE Id = :reverseOrder.Account_Manager__c];
                    Messaging.SingleEmailMessage message = new Messaging.SingleEmailMessage();
                    // message.toAddresses = new String[] { customerMap.get(reverseOrder.Customer__c).Owner.Email };
                    // message.ccaddresses = Label.CCM_ReverseOrder_MaintainInsideSales_NotifyEmail.split(';');
                    message.toAddresses = Label.CCM_ReverseOrder_MaintainInsideSales_NotifyEmail.split(';');
                    message.subject = 'Please maintain account assistant';
                    String customerName = '';
                    if(customerMap.get(reverseOrder.Customer__c).Name != null){
                        customerName = customerMap.get(reverseOrder.Customer__c).Name;
                    }else{
                        customerName = reverseOrder.Customer__c;
                    }
                    //获取系统的baseurl
                    String systemBaseURL = QueryUtils.getSalesforceUrl();
                    message.htmlbody = 'Customer <a href=\"'+ systemBaseURL + '/' + reverseOrder.Customer__c +'\">' + customerName + '</a> requested a reverse order. Please maitain Inside Sales Information for this customer in Salesforce.';
                    messages.add(message);
                }

                if( reverseOrder.Customer__c!=null && customerMap.containsKey(reverseOrder.Customer__c) ){
                    if( customerMap.get(reverseOrder.Customer__c).Owner.UserRoleId!=null){
                        if(customerMap.get(reverseOrder.Customer__c).Owner.UserRole.Name.contains('Sales Director') ){
                            reverseOrder.Sales_Manager__c = customerMap.get(reverseOrder.Customer__c).OwnerId;
                            reverseOrder.Sales_Director__c = customerMap.get(reverseOrder.Customer__c).OwnerId;
                        }else{
                            reverseOrder.Sales_Manager__c = customerMap.get(reverseOrder.Customer__c).OwnerId;
                            if( directRoleUserMap.containsKey(customerMap.get(reverseOrder.Customer__c).Owner.UserRole.ParentRoleId) ){
                                reverseOrder.Sales_Director__c = directRoleUserMap.get(customerMap.get(reverseOrder.Customer__c).Owner.UserRole.ParentRoleId).Id;
                            }
                        }
                    }
                }

                if( reverseOrder.Customer__c!=null && customerMap.containsKey(reverseOrder.Customer__c) ){
                    reverseOrder.Sales_Manager__c = customerMap.get(reverseOrder.Customer__c).OwnerId;
                    if( customerMap.get(reverseOrder.Customer__c).Owner.UserRoleId!=null){
                        // if users role is sales director or ka sales manager, set sales vp to sales_director__c
                        if(customerMap.get(reverseOrder.Customer__c).Owner.UserRole.Name.contains('Sales Director') || customerMap.get(reverseOrder.Customer__c).Owner.UserRole.Name.contains('KA Sales Manager')){
                            // reverseOrder.Sales_Director__c = customerMap.get(reverseOrder.Customer__c).OwnerId;
                            if (salesVPUser.size() > 0) {
                                reverseOrder.Sales_Director__c = salesVPUser[0].Id;
                            } else {
                                reverseOrder.Sales_Director__c = customerMap.get(reverseOrder.Customer__c).OwnerId;
                            }
                        }else{
                            if( directRoleUserMap.containsKey(customerMap.get(reverseOrder.Customer__c).Owner.UserRole.ParentRoleId) ){
                                reverseOrder.Sales_Director__c = directRoleUserMap.get(customerMap.get(reverseOrder.Customer__c).Owner.UserRole.ParentRoleId).Id;
                            }
                        }
                    }
                }
            }
        }
        if(messages.size()>0){
            Messaging.SendEmailResult[] results = Messaging.sendEmail(messages);
        }
    }
}