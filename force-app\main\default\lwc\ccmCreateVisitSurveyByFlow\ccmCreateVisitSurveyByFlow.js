import { LightningElement, track, api } from 'lwc';

export default class CcmCreateVisitSurveyByFlow extends LightningElement {
    @api recordId;
    renderedCallback() {
        //get recordId
		const urlParams = new URLSearchParams(window.location.search);
		this.url = urlParams+encodeURIComponent(encrypted.toString());
		const recordId = urlParams.get('0.recordId') == null ? urlParams.get('recordId') : urlParams.get('0.recordId');
		console.log('recordId:' + recordId);
		if (recordId !== undefined && recordId !== "" && recordId !== null) {
			this.recordId = recordId;
		}
        const flow = this.template.querySelector('lightning-flow');
        const flowInputVariables = [{ name: 'recordId', type: 'String', value: this.recordId }];
        flow.startFlow('ACE_Store_Survey', flowInputVariables);
    }
}