/**
 * @Author: <PERSON><PERSON>
 * @description: generate Warranty Return Claim Request Number Automatically
 * @Create Date: 2022-11-5
 */

public without sharing class CCM_WarrantyReturnRequestshare implements Triggers.Handler{
    public void handle(){
        //request no =  RA + Customer.accountNumber + 000x
        List<Warranty_Return_Claim__c> warrantyReturnList = (List<Warranty_Return_Claim__c>)Trigger.new;
        Map<Id, Warranty_Return_Claim__c> oldMap = (Map<Id, Warranty_Return_Claim__c>)Trigger.oldMap;
        Set<Id> customerIds = new Set<Id>();
        for(Warranty_Return_Claim__c obj : warrantyReturnList){
            if(obj.Customer__c != null){
                customerIds.add(obj.Customer__c);
            }
        }

        //通过customerId 查数据
        Map<Id, Account> customerMap = new Map<Id,Account>([Select Id,
                                                                AccountNumber,
                                                                OwnerId,
                                                                Owner.UserRoleId,
                                                                Owner.UserRole.Name,
                                                                Owner.UserRole.ParentRoleId,
                                                                Owner.Email,
                                                                Name,
                                                                (SELECT Id, UserId, TeamMemberRole FROM AccountTeamMembers WHERE TeamMemberRole = 'Account Assistant' LIMIT 1),
                                                                (SELECT Id, Warranty_Return_Request_No__c, Counter__c FROM Warranty_Return_Claim1__r ORDER BY Counter__c ASC)
                                                                From Account 
                                                                where Id in :customerIds]);

        Map<Id, Warranty_Return_Claim__c> warrantyReturnOld = (Map<Id, Warranty_Return_Claim__c>)Trigger.oldMap;

        Map<String,Integer> warrantyCounters = new Map<String,Integer>();
        for(Warranty_Return_Claim__c warrantyReturn : warrantyReturnList){
            if(warrantyCounters.containsKey(warrantyReturn.Customer__c)){
                continue;
            }
            Integer count = 0;
            if(warrantyReturn.Customer__c != null && (Trigger.isInsert || (Trigger.isUpdate && warrantyReturn.Customer__c != warrantyReturnOld.get(warrantyReturn.Id).Customer__c))){
                Integer size = customerMap.get(warrantyReturn.Customer__c).Warranty_Return_Claim1__r.size();
                Integer index = size - 1;
                if(size>0){
                    if(customerMap.get(warrantyReturn.Customer__c).Warranty_Return_Claim1__r[index].Counter__c != null){
                        count = (Integer)customerMap.get(warrantyReturn.Customer__c).Warranty_Return_Claim1__r[index].Counter__c + 1;
                    }
                }else{
                    count = 1;
                }
                warrantyCounters.put(warrantyReturn.Customer__c,count);
            }
        }

        for(Warranty_Return_Claim__c warrantyReturn : warrantyReturnList){
            if(warrantyReturn.Customer__c != null && (Trigger.isInsert || (Trigger.isUpdate && warrantyReturn.Customer__c != warrantyReturnOld.get(warrantyReturn.Id).Customer__c))){
                String customerCode = customerMap.get(warrantyReturn.Customer__c).AccountNumber;
                String customerWarrantyReturnNumber = '';
                Integer count = warrantyCounters.get(warrantyReturn.Customer__c);
                if (count >= 10000) {
                    customerWarrantyReturnNumber = String.valueOf(count);
                } else {
                    customerWarrantyReturnNumber = '000' + String.valueOf(count);
                    customerWarrantyReturnNumber = customerWarrantyReturnNumber.substring(customerWarrantyReturnNumber.length() - 4);
                }
                warrantyReturn.Warranty_Return_Request_No__c = 'RA' + customerCode + customerWarrantyReturnNumber;
                warrantyReturn.Name = warrantyReturn.Warranty_Return_Request_No__c;
                warrantyReturn.Counter__c = count;
                if (customerMap.containsKey(warrantyReturn.Customer__c) &&  customerMap.get(warrantyReturn.Customer__c) != null && customerMap.get(warrantyReturn.Customer__c).AccountTeamMembers.size() > 0) {
                    WarrantyReturn.Inside_Sales__c = customerMap.get(warrantyReturn.Customer__c).AccountTeamMembers[0].UserId;
                }
                
                System.debug('Warranty_Return_Request_No__c = ' + warrantyReturn.Warranty_Return_Request_No__c);
                warrantyCounters.put(warrantyReturn.Customer__c,count + 1);

                if(customerMap.get(warrantyReturn.Customer__c).OwnerId != null){
                    warrantyReturn.Account_Manager__c = customerMap.get(warrantyReturn.Customer__c).OwnerId;
                }

                Messaging.SingleEmailMessage[] messages =   new List<Messaging.SingleEmailMessage>();
                // if inside sales is null, notify account manager to maintain data, set account manager to inside sales field
                if (String.isEmpty(WarrantyReturn.Inside_Sales__c)) {
                    WarrantyReturn.Inside_Sales__c = WarrantyReturn.Account_Manager__c;
                    // List<User> accountManager = [SELECT Id, Email FROM User WHERE Id = :reverseOrder.Account_Manager__c];
                    Messaging.SingleEmailMessage message = new Messaging.SingleEmailMessage();
                    // message.toAddresses = new String[] { customerMap.get(WarrantyReturn.Customer__c).Owner.Email };
                    // message.ccaddresses = Label.CCM_ReverseOrder_MaintainInsideSales_NotifyEmail.split(';');
                    message.toAddresses = Label.CCM_ReverseOrder_MaintainInsideSales_NotifyEmail.split(';');
                    message.subject = 'Please maintain account assistant';
                    String customerName = '';
                    if(customerMap.get(WarrantyReturn.Customer__c).Name != null){
                        customerName = customerMap.get(WarrantyReturn.Customer__c).Name;
                    }else{
                        customerName = WarrantyReturn.Customer__c;
                    }
                    //获取系统的baseurl
                    String systemBaseURL = QueryUtils.getSalesforceUrl();
                    message.htmlbody = 'Customer <a href=\"'+ systemBaseURL + '/' + WarrantyReturn.Customer__c +'\">' + customerName + '</a> requested a warranty return. Please maitain Inside Sales Information for this customer in Salesforce.';
                    messages.add(message);
                }

                if(messages.size()>0){
                    Messaging.SendEmailResult[] results = Messaging.sendEmail(messages);
                }
            }
        }
    } 
}