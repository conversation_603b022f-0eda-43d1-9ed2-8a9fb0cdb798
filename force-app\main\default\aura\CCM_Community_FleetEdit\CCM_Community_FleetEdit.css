.THIS {
    width: 90%;
    margin: 0 auto;
}
.THIS .slds-grid{
    align-items: flex-end;
}
.THIS .slds-section__title{
     background-color: #d7d7d7;
     color: #484848;
     font-weight: bold;
}
.THIS .slds-form-element__label{
    font-weight: 700;
    display: inline-block;
    padding-right: 1rem;
    position: relative;
}
.THIS .field-required:before{
    content:"*";
    display: inline-block;
    transform: scale(1.5);
    margin-right:5px;
    position:relative;
    color:rgb(194, 57, 52);
    position: absolute;
    right: -.1rem;
    top: 50%;
    transform: translateY(-50%);
}
.THIS .error-text{
    color: rgb(194, 57, 52);
}
.THIS .fileName{
    display: inline-block;
    /*width: 80%;*/
    overflow: hidden;
    text-overflow: ellipsis;
}
.THIS .uploadFinished{
    color: #90c41f;
}
.THIS .delete{
    color: #777;
    cursor: pointer;
    float: right;
    text-decoration: underline;
}
.THIS .slds-flex > .slds-form-element > .slds-form-element__label{
    display: none!important;
}
.THIS .slds-truncate > .slds-form-element > .slds-form-element__label{
    display: none!important;
}
.THIS .input-width{
    width: 198px;
}
.THIS .required{
    white-space: normal;
    word-break: break-word;
}
/*行内必填样式*/
.THIS .rd_cell_required input{
    border-left: 2px solid rgba(194, 57, 52, 0.9);
}