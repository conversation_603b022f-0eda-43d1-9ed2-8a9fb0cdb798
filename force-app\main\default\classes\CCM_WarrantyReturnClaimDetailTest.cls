/**
 * @Author: <PERSON>
 * @description: test CCM_WarrantyReturnClaimDetail
 * @Create Date: 2022-11-21
*/
@IsTest
public class CCM_WarrantyReturnClaimDetailTest {
    // 初始化好需要的数据
    @TestSetup
    static void testSetUpData() {
        Test.startTest();
        CCM_SharingUtil.isSharingOnly = true;
        // customer info
        String recordId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName = 'Channel' LIMIT 1].Id;
        Account account = new Account();
        account.Name = 'TestCustomer';
        account.Distributor_or_Dealer__c = '2nd Tier Dealer';
        account.RecordTypeId = recordId;
        account.AccountNumber = '12345';
        insert account;

        // get user profile info
        Profile userProfile = [SELECT Id, Name FROM Profile WHERE Id = :UserInfo.getProfileId()];
        
        Contact contact = new Contact();
        contact.AccountId = account.Id;
        contact.LastName = 'xxx';
        contact.FirstName = 'YYY';
        contact.OwnerId = UserInfo.getUserId();
        insert contact;


        // address info
        Account_Address__c address = new Account_Address__c();
        address.Customer__c = account.Id;
        address.X2nd_Tier_Dealer__c = account.Id;
        address.Active__c = true;
        address.Approval_Status__c = 'Approved';
        address.Contact__c = contact.Id;
        insert address;

        // pricebook info
        Pricebook2 pb = new Pricebook2();
        pb.Name = 'Standard Price Book';
        pb.Org_Code__c = 'CNA';
        pb.IsActive = true;
        pb.Contract_Price_Book_OracleID__c = '11';
        pb.Price_Book_OracleID__c = '11';
        insert pb;
        
        Product2 pro = new Product2();
        pro.Name = 'TestProduct111';
        pro.Brand_Name__c = 'EGO';
        pro.ProductCode = '1234567';
        pro.IsActive = true;
        pro.Source__c = 'PIM';
        pro.Description = pro.SF_Description__c;
        insert pro;

        Product2 pro2 = new Product2();
        pro2.Name = 'TestProduct222';
        pro2.Brand_Name__c = 'EGO';
        pro2.ProductCode = '1234567';
        pro2.IsActive = true;
        pro2.Source__c = 'EBS';
        insert pro2;
        pro2.Description = pro2.SF_Description__c;
        update pro2;

        // pricebookEntry info
        PricebookEntry pbe = new PricebookEntry();
        pbe.IsActive = true;
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 1000;
        pbe.Pricebook2Id = pb.Id;
        pbe.UseStandardPrice = false;
        insert pbe;

        // authorized brand info
        Sales_Program__c salesProgram = new Sales_Program__c();
        salesProgram.Customer__c = account.Id;
        salesProgram.Approval_Status__c = 'Approved';
        salesProgram.Price_Book__c = pb.Id;
        salesProgram.Authorized_Brand_Name_To_Oracle__c = 'EGO_2020_Test';
        salesProgram.RecordTypeId = CCM_Contants.SALES_STANDARD_PROGRAM_RECORDTYPEID;
        salesProgram.Brands__c = 'EGO';
        salesProgram.ORG_Code__c = 'CNA';
        salesProgram.Payment_Term__c = 'NA001';
        salesProgram.Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.';
        insert salesProgram;

        // BAAB info
        Address_With_Program__c addPro = new Address_With_Program__c();
        addPro.Account_Address__c = address.Id;
        addPro.Program__c = salesProgram.Id;
        insert addPro;

        Invoice__c invoice = new Invoice__c();
        invoice.Customer__c = account.Id;
        invoice.Invoice_Number__c = '111';
        invoice.Total_Due__c = 100;
        insert invoice;

        Purchase_Order__c objPO = new Purchase_Order__c(Customer__c = account.Id);
        insert objPO;

        //创建Order数据
        Order o = TestDataFactory.createOrder();
        o.AccountId = account.Id;
        o.Order_Number__c = 'yyy';
        o.Order_Status__c = 'Partial Shipment';
        o.BillTo__c = addPro.Id;
        o.ShipTo__c = addPro.Id;
        o.Purchase_Order__c = objPO.Id;
        o.Price_Book__c = pb.Id;
        o.PO_Number__c = 'CCC-11111';
        o.Org_Code__c = 'CNA';
        o.Order_OracleID__c = '1234';
        insert o;

        Warranty_Return_Claim__c objWarrantyReturnClaim = new Warranty_Return_Claim__c();
        objWarrantyReturnClaim.Customer__c = account.Id;
        // objWarrantyReturnClaim.Approval_Status__c = 'Rejected';
        // objWarrantyReturnClaim.Approval_Status__c = 'N/A';
        objWarrantyReturnClaim.Chervon_or_Warehouse_Purchase__c = 'Chervon';
        objWarrantyReturnClaim.Contact_Name__c = 'test';
        objWarrantyReturnClaim.Warranty_Return_Order_Number_in_EBS__c = '1234,123435';
        objWarrantyReturnClaim.Credit_Memo_Number__c = '111,222';
        objWarrantyReturnClaim.Contact_Phone_Number__c = '********';
        objWarrantyReturnClaim.Contact_Email_Address__c = '<EMAIL>';
        objWarrantyReturnClaim.Customer_Reference_Number__c = '************';
        objWarrantyReturnClaim.Notes__c = 'test notes';
        objWarrantyReturnClaim.Billing_Address__c = address.Id;
        objWarrantyReturnClaim.Shipping_Address__c = address.Id;
        objWarrantyReturnClaim.Warehouse_Location__c = address.Id;
        objWarrantyReturnClaim.Store_Location__c = address.Id;
        objWarrantyReturnClaim.Store_Address__c = address.Id;
        objWarrantyReturnClaim.Is_Alternative_Address__c = false;
        objWarrantyReturnClaim.Is_Alternative_Store_Address__c = false;
        objWarrantyReturnClaim.Payment_Method__c = 'Credit Memo';
        objWarrantyReturnClaim.Debit_Memo_Number__c = invoice.Id;
        insert objWarrantyReturnClaim;

        Warranty_Return_Claim_Item__c objWarrantyItem = new Warranty_Return_Claim_Item__c();
        objWarrantyItem.Warranty_Return_Claim__c = objWarrantyReturnClaim.Id;
        objWarrantyItem.Model__c = pro.Id;
        objWarrantyItem.Quantity__c = 1;
        objWarrantyItem.DIF_RTV__c = 'DIF';
        objWarrantyItem.Serial_Number__c = '12121212121';
        objWarrantyItem.End_Consumer_Purchase_Date__c = Date.newInstance(2022, 11, 21);
        objWarrantyItem.End_Consumer_Return_Date__c = Date.newInstance(2022, 11, 30);
        objWarrantyItem.Index__c = '0';
        insert objWarrantyItem;

        Warranty_Return_Claim_Item_Attachment__c attachment = new Warranty_Return_Claim_Item_Attachment__c();
        attachment.Attachment_Type__c = 'End Customer Purchase Date';
        attachment.Warranty_Return_Claim__c = objWarrantyReturnClaim.Id;
        attachment.Warranty_Return_Claim_Item__c = objWarrantyItem.Id;
        insert attachment;

        ContentVersion contentVersionTest = new ContentVersion(
                Title = 'Contract',
                PathOnClient = 'Contract.pdf',
                VersionData = Blob.valueOf('Test Content'),
                IsMajorVersion = true
        );
        insert contentVersionTest;

        ContentDocumentLink cdl = New ContentDocumentLink();
        List<ContentDocument> documents = [
                SELECT Id, Title, LatestPublishedVersionId
                FROM ContentDocument
        ];
        cdl.LinkedEntityId = attachment.id;
        cdl.ContentDocumentId = documents[0].Id;
        cdl.ShareType = 'V';
        insert cdl;
    }

    @IsTest
    static void testUpdateWarrantyReturn() {
        Test.startTest();
        Warranty_Return_Claim__c objWarrnaty = [SELECT Id, Payment_Method__c, Debit_Memo_Number__c FROM Warranty_Return_Claim__c LIMIT 1];
        Invoice__c invoice = [SELECT Id FROM Invoice__c LIMIT 1];
        CCM_WarrantyReturnClaimDetail.WarrantyReturnWrapper wrapper = new CCM_WarrantyReturnClaimDetail.WarrantyReturnWrapper();
        wrapper.recordId = objWarrnaty.Id;
        wrapper.paymentMethod = 'Deduction';
        wrapper.debitMemoId = invoice.Id;

        CCM_WarrantyReturnClaimDetail.updateWarrantyReturn(JSON.serialize(wrapper));

        Test.stopTest();
    }

    // test getWarrantyReturnClaimInfo()
    @IsTest
    static void testGetWarrantyReturnClaimInfo() {
        Test.startTest();
        Warranty_Return_Claim__c objWarrantyReturn = [SELECT Id FROM Warranty_Return_Claim__c LIMIT 1];
        objWarrantyReturn.Is_Alternative_Address__c = true;
        objWarrantyReturn.Is_Alternative_Store_Address__c = true;
        update objWarrantyReturn;
        CCM_WarrantyReturnClaimDetail.getWarrantyReturnClaimInfo(objWarrantyReturn.Id);

        Test.stopTest();
    }

    @IsTest
    static void testGetWarrantyReturnClaimInfo2() {
        Test.startTest();
        Warranty_Return_Claim__c objWarrantyReturn = [SELECT Id FROM Warranty_Return_Claim__c LIMIT 1];

        objWarrantyReturn.Is_Alternative_Address__c = false;
        objWarrantyReturn.Is_Alternative_Store_Address__c = false;
        update objWarrantyReturn;
        CCM_WarrantyReturnClaimDetail.getWarrantyReturnClaimInfo(objWarrantyReturn.Id);

        Test.stopTest();
    }

}