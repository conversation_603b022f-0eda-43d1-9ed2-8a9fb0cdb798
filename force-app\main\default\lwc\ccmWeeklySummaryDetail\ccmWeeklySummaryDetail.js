import { LightningElement, api, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { CurrentPageReference } from 'lightning/navigation';
import getAllPicklist from '@salesforce/apex/CCM_WeeklySummaryCtrl.getAllPicklist';
import getRecordData from '@salesforce/apex/CCM_WeeklySummaryCtrl.getRecordData';
import saveSummary from '@salesforce/apex/CCM_WeeklySummaryCtrl.saveSummary';
import updateDraft from '@salesforce/apex/CCM_WeeklySummaryCtrl.updateDraft';

export default class CcmWeeklySummaryDetail extends LightningElement {
    isEdit = false;
    isLoading = false;

    weeklyHighlights;
    competitiveInfo;
    other;
    nextWeekFocus;

    week;
    weekOptions;

    month;
    monthOptions;

    year;
    yearOptions;

    region;
    regionOptions;
    userRegionMappings;

    comments;
    newComments;

    createdBy;
    submitDate;
    status;

    isSalesDirector;

    @api
    recordId;
    @api
    mode;

    @wire(CurrentPageReference)
    currentPageReference;

    connectedCallback() {
        if (this.currentPageReference?.state) {
            if(this.currentPageReference.state.c__recordId) {
                this.recordId = this.currentPageReference.state.c__recordId;
            }
            let mode = this.currentPageReference.state.c__mode;
            if(mode === 'edit') {
                this.isEdit = true;
            }
        }
        
        this.initPicklist();
    }

    initPicklist() {
        getAllPicklist().then(result=>{
            if(result) {
                console.log(JSON.parse(result));
                let picklistJson = JSON.parse(result);
                this.initWeekOptions(picklistJson['weekinfo']);
                this.initMonthOptions(picklistJson['monthInfo']);
                this.initYearOptions(picklistJson['yearInfo']);
                this.initRegionOptions(picklistJson['regionInfo']);
                if(this.recordId) {
                    this.initData();
                }
            }
        });
    }

    initWeekOptions(weekInfo) {
        const totalWeeks = weekInfo.totalWeeks;
        const currentWeek = weekInfo.currentWeek;

        let weekOptions = [];
        for(let i=1; i<=totalWeeks; i++) {
            let weekOption = {
                label: i,
                value: i
            };
            weekOptions.push(weekOption);
        }
        this.weekOptions = weekOptions;
        this.week = currentWeek;
    }

    initMonthOptions(monthInfo) {
        this.monthOptions = [
            {
                label: monthInfo.currentMonth,
                value: monthInfo.currentMonth
            }
        ];
        this.month = monthInfo.currentMonth;
    }

    initYearOptions(yearInfo) {
        this.yearOptions = [
            {
                label: yearInfo.currentYear,
                value: yearInfo.currentWeek
            }
        ];
        this.year = yearInfo.currentYear;
    }

    initRegionOptions(regionInfo) {
        let regionOptions = [];
        let regionList = regionInfo.Region.split(',');
        regionList.forEach(region=>{
            let regionOption = {
                label: region,
                value: region
            };
            regionOptions.push(regionOption);
        });
        this.regionOptions = regionOptions;
        this.region = regionInfo.DefaultRegion;
    }

    initData() {
        getRecordData({recordId: this.recordId}).then(result=>{
            if(result) {
                let resultJson = JSON.parse(result)['data'];
                this.weeklyHighlights = resultJson.Weekly_Highlights__c;
                this.competitiveInfo = resultJson.Competitive_Information__c;
                this.nextWeekFocus = resultJson.Next_Week_Focus__c;
                this.other = resultJson.Other__c;
                this.week = resultJson.Week__c;
                this.year = resultJson.Year__c;
                this.month = resultJson.Month__c;
                this.region = resultJson.Region__c;
                this.submitDate = resultJson.Submit_Date__c;
                if(this.submitDate != null) {
                    this.status = 'Submitted';
                }
                else {
                    this.status = 'Draft';
                }
                this.createdBy = resultJson.Owner.Name;
                this.isSalesDirector = JSON.parse(result)['isSalesDirector'];
            }
        });
    }

    handleInputChange(event) {
        const dataName = event.target.dataset.name;
        const value = event.target.value;
        this[dataName] = value;
    }

    handleAddComment() {
        this.comments += this.newComments + '\n';
        this.newComments = '';
    }

    handleSave() {
        this.isLoading = true;
        let param = {
            'weeklyHighlights': this.weeklyHighlights,
            'competitiveInfo': this.competitiveInfo,
            'other': this.other,
            'nextWeekFocus': this.nextWeekFocus,
            'week': this.week,
            'year': this.year,
            'month': this.month,
            'region': this.region,
            'comments': this.comments,
            'recordId': this.recordId
        };
        saveSummary({summaryParam: JSON.stringify(param), isSubmit: false}).then(result=>{
            if(result) {
                this.recordId = result;
                const event = new ShowToastEvent({
	                variant: 'success',
	                title: 'Success',
	                message:
	                    'Save Successfully'
	            });
	            this.dispatchEvent(event);
            }
            this.isLoading = false;
        });
    }

    handleSubmit() {
        const valid = this.validateRequired();
        if(!valid) {
            return;
        }
        this.isLoading = true;
        let param = {
            'weeklyHighlights': this.weeklyHighlights,
            'competitiveInfo': this.competitiveInfo,
            'other': this.other,
            'nextWeekFocus': this.nextWeekFocus,
            'week': this.week,
            'year': this.year,
            'region': this.region,
            'recordId': this.recordId
        };
        saveSummary({summaryParam: JSON.stringify(param), isSubmit: true}).then(result=>{
            this.isLoading = false;
            this.isEdit = false;
            const event = new ShowToastEvent({
                variant: 'success',
                title: 'Success',
                message:
                    'Submit Successfully'
            });
            this.dispatchEvent(event);
        });
    }

    handleEditAsDraft() {
        this.isLoading = true;
        updateDraft({recordId: this.recordId}).then(result=>{
            this.isEdit = true;
            this.isLoading = false;
        });
    }

    validateRequired() {
        if(!this.weeklyHighlights) {
            const event = new ShowToastEvent({
                variant: 'warning',
                title: 'Info',
                message:
                    'Please Input Weekly Highlights'
            });
            this.dispatchEvent(event);
            return false;
        }

        if(!this.competitiveInfo) {
            const event = new ShowToastEvent({
                variant: 'warning',
                title: 'Info',
                message:
                    'Please Input Competitive Info'
            });
            this.dispatchEvent(event);
            return false;
        }

        if(!this.other) {
            const event = new ShowToastEvent({
                variant: 'warning',
                title: 'Info',
                message:
                    'Please Input Other'
            });
            this.dispatchEvent(event);
            return false;
        }

        if(!this.nextWeekFocus) {
            const event = new ShowToastEvent({
                variant: 'warning',
                title: 'Info',
                message:
                    'Please Input Next Week\'s Focus'
            });
            this.dispatchEvent(event);
            return false;
        }
        return true;
    }
}