@isTest
private class Test_CCM_V_Program_Validations {

	@testSetup static void testSetup(){
		Test.startTest();
		User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');
        //User admin = Test_SalesData.createUser('TestAdmin', 'NA_Administrator', 'System Administrator');

        System.runAs(salesManager1) {
        	Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
        	//Lead theLead = Test_SalesData.createProspectData();
	        Lead theLead = new Lead(
	        	LastName = 'TestLead001',
	        	Company = 'TestCompanyName',
	        	User_Type__c = 'Enterprise',
	        	Invoicing_Method__c = 'MAIL',
	        	Email = '<EMAIL>',
	        	Credit_Limit__c = '1',
	        	Risk_Code__c = 'R01',
	        	Street = 'test street',
	        	City = 'New York',
	        	State = 'NY',
	        	Country = 'US',
	        	PostalCode = '11111',
	        	Intended_Brand__c = 'EGO',
	        	Distributor_or_Dealer__c = 'Direct Dealer',
                Sales_Group__c = 'SG01'
	        );
	        insert theLead;


        	//Sales_Program__c sp = Test_SalesData.createDefaultAuthorizedBrand(theLead.Id, null);
	        Sales_Program__c sp = new Sales_Program__c(
	    		Name = 'TestSP001',
	    		Brands__c = 'EGO',
		    	Payment_Term__c = 'NA059',
		    	Freight_Term__c = 'Due',
		    	//Price_List__c = 'Standard Price List',
		    	Starting_Date_of_Payment_Term__c = 'Invoice Date',
		    	Order_Lead_Time__c = 5,
		    	Prospect__c = theLead.Id,
		    	Price_List__c = 'Contract Price List',
				Sales_Group__c = 'SG01',
				Authorized_Brand_Name_To_Oracle__c='Test'
	    	);
	    	insert sp;
        	//Sales_Program__c sp2 = Test_SalesData.createDefaultAuthorizedBrand(theLead.Id, null);

        	Attachment_Management__c am1 = Test_SalesData.createAttachmentManagement(theLead.Id, null, 'Brand Program');
        	Attachment_Management__c am2 = Test_SalesData.createAttachmentManagement(theLead.Id, null, 'General Agreement');

        	Contact con =  Test_SalesData.createContact(theLead.Id, null);
        	Event eve = Test_SalesData.createEvent(theLead.Id);

        	//Customer_Profile__c cp = Test_SalesData.createCustomerProfile(theLead.Id, null);

        	List<Account_Address__c> aaList = [Select Id, Contact__c, Email_for_Invoicing__c, RecordType.Name from Account_Address__c where Prospect__c =: theLead.Id];
        	Account_Address__c billingAddress;
        	for (Account_Address__c aa : aaList) {
        		aa.Contact__c = con.Id;
        		if (aa.RecordType.Name == 'Billing Address') {
        			billingAddress = aa;
        		}
        	}
        	update aaList;

        	Address_With_Program__c awp = new Address_With_Program__c(
        		Account_Address__c = billingAddress.Id,
        		Program__c = sp.Id
        	);
        	insert awp;



        }
        Test.stopTest();
	}

    static testMethod void testMethod1() {
        Test.startTest();

        Sales_Program__c sp = [Select Id from Sales_Program__c where Name = 'TestSP001'];
        Blob beforeblob=Blob.valueOf('Unit Test Attachment Body');
        ContentVersion cv = new ContentVersion();
        cv.title = 'test content trigger';
        cv.PathOnClient ='test';
        cv.VersionData =beforeblob;
        insert cv;

        ContentVersion testContent = [SELECT id, ContentDocumentId FROM ContentVersion where Id = :cv.Id];

    	ContentDocumentLink cdl = new ContentDocumentLink(
    		LinkedEntityId = sp.Id,
    		ContentDocumentId = testContent.ContentDocumentId,
    		ShareType= 'V',
    		Visibility = 'AllUsers'
    	);
    	insert cdl;



    	sp.Approval_Status__c = 'Approved';
    	update sp;

    	sp.Approval_Status__c = 'Pending for Approval';
    	update sp;

        CCM_V_Program_Validations.testCoverageMethod();

        /*Lead theLead = [Select Id from lead where lastName = 'TestLead001' limit 1];
        Map<Id, Sales_Program__c> queriedProgramMap = new Map<Id, Sales_Program__c>([
        	Select Id, Name, Prospect__c, Prospect__r.Credit_Limit__c, Prospect__r.Risk_Code__c, Approval_Status__c, Price_List__c,
                (Select Id from Attachments),
                (Select Id from ContentDocumentLinks),
                (Select Id, Account_Address__c, Account_Address__r.Name, Account_Address__r.Contact__c, Program__c, Address_Type__c from Addresses_With_Program__r)
            from Sales_Program__c
            WHERE Prospect__c =:theLead.Id
        ]);
        Sales_Program__c sp1;
        for (ID i : queriedProgramMap.keySet()) {
        	System.debug(LoggingLevel.INFO, '*** queriedProgramMap.get(i): ' + queriedProgramMap.get(i));
        	System.debug(LoggingLevel.INFO, '*** queriedProgramMap.get(i).Addresses_With_Program__r: ' + queriedProgramMap.get(i).Addresses_With_Program__r);
        	if (queriedProgramMap.get(i).Addresses_With_Program__r != null) {
        		sp1 = queriedProgramMap.get(i);
        	}
        }

        Blob beforeblob=Blob.valueOf('Unit Test Attachment Body');
        ContentVersion cv = new ContentVersion();
        cv.title = 'test content trigger';
        cv.PathOnClient ='test';
        cv.VersionData =beforeblob;
        insert cv;

        ContentVersion testContent = [SELECT id, ContentDocumentId FROM ContentVersion where Id = :cv.Id];

    	ContentDocumentLink cdl = new ContentDocumentLink(
    		LinkedEntityId = sp1.Id,
    		ContentDocumentId = testContent.ContentDocumentId,
    		ShareType= 'V',
    		Visibility = 'AllUsers'
    	);
    	insert cdl;

        Sales_Program__c sp2 = Test_SalesData.createDefaultAuthorizedBrand(theLead.Id, null);

        CCM_V_Program_Validations.requireAttachmentForCustomPriceList(sp1, sp2, queriedProgramMap);*/

        Test.stopTest();
    }
}