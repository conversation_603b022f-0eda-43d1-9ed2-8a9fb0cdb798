@IsTest
private class CCM_SyncSalesProgramInfoTest {
    @TestSetup
    public static void buildData() {
        User salesManager1 = Test_SalesData.createUser('TestLoginUser', 'OPE_Territory_Manager_South_East', 'NA Sales Manager');
        User salesManager2 = Test_SalesData.createUser('TestSalesManager', 'District_Sales_Manager', 'NA Sales Manager');
        User salesDirector = Test_SalesData.createUser('TestSalesDirector', 'OPE_Sales_Director', 'NA Sales Manager');
        System.runAs(salesManager1) {
            Test_SalesData.createProspectAssignmentRules(salesManager1.Id, salesManager2.Id, salesDirector.Id);
            Lead l = new Lead();
            l.LastName = 'test';
            l.Company = 'TestCompanyName';
            l.User_Type__c = 'Enterprise';
            l.Invoicing_Method__c = 'EMAIL';
            l.Email = '<EMAIL>';
            l.Credit_Limit__c = 1;
            l.Risk_Code__c = 'R01';
            l.Street = 'test street';
            l.City = 'New York';
            l.State = 'NY';
            l.Country = 'US';
            l.PostalCode = '11111';
            l.Intended_Brand__c = 'EGO';
            l.Distributor_or_Dealer__c = 'Direct Dealer';
            insert l;
            Contact con = Test_SalesData.createContact(l.Id, null);
            Account_Address__c addl = new Account_Address__c();
            addl.Primary__c = true;
            addl.Prospect__c = l.Id;
            addl.Store_Number__c = '111';
            addl.RecordTypeId = CCM_Contants.BILLING_ADDRESS_RECORDTYPEID;
            addl.Contact__c = con.Id;
            insert addl;
            Sales_Program__c spl = new Sales_Program__c();
            spl.Prospect__c = l.Id;
            spl.Authorized_Brand_Name_To_Oracle__c = 'test1';
            insert spl;
            Attachment_Management__c am1 = Test_SalesData.createAttachmentManagement(l.Id, null, 'Brand Program');
            Attachment_Management__c am2 = Test_SalesData.createAttachmentManagement(l.Id, null, 'General Agreement');
            ContentVersion cv = new ContentVersion();
            cv.Title = 'Test Document';
            cv.PathOnClient = 'TestDocument.pdf';
            cv.VersionData = Blob.valueOf('Test Content');
            cv.IsMajorVersion = true;
            insert cv;
            Id conDocId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id].ContentDocumentId;
            ContentDocumentLink cdl = new ContentDocumentLink();
            cdl.LinkedEntityId = am1.Id;
            cdl.ContentDocumentId = conDocId;
            cdl.ShareType = 'V';
            insert cdl;
            ContentVersion cv1 = new ContentVersion();
            cv1.Title = 'Test Document';
            cv1.PathOnClient = 'TestDocument.pdf';
            cv1.VersionData = Blob.valueOf('Test Content');
            cv1.IsMajorVersion = true;
            insert cv1;
            Id conDocId1 = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cv1.Id].ContentDocumentId;
            ContentDocumentLink cdl1 = new ContentDocumentLink();
            cdl1.LinkedEntityId = am2.Id;
            cdl1.ContentDocumentId = conDocId1;
            cdl1.ShareType = 'V';
            insert cdl1;
            Test_SalesData.createCustomerProfile(l.Id, null);
        }
    }
    @IsTest
    static void testMethod1() {
        Test.startTest();
        List<Lead> leadList = [SELECT Id FROM Lead WHERE LastName = 'test'];
        Lead l = leadList[0];
        update l;
        List<User> users = [SELECT Id FROM User WHERE LastName = 'TestSalesManager'];
        l.OwnerId = users[0].Id;
        l.Director_Approver__c = users[0].Id;
        update l;
        Test.stopTest();
    }
}