/**
 * @Author:         Zack
 * @Date:           2018-07-17
 * @Description:    API for Account Migration
 * @Test_Class:     HerokuAPITest
 * @Related_Class: 
 ---------------------------------------------------
 * @Last_Modified_by: 
 * @Last_Modified_time: 
 * @Modifiy_Purpose: 
 *
 * @url: https://sandbox2-ego.cs97.force.com/services/apexrest/HerokuApi/v1/
 * @data:
 *  {
 *      
 *  }
*************************************************************************/
@RestResource(urlMapping='/HerokuApi/v1/')
global without sharing class HerokuAPI {
    @HttpPost
    global static HerokuEntity.ResponseEntity doPost() {
        // 获取请求
        RestRequest request = RestContext.request;
        // 获取请求头
        Map<String, String> headerMap = request.headers;

        //***************************校验请求****************************
        HerokuEntity.ResponseEntity response = checkHeaderMap(headerMap);
        if (response != null) {
            return response;
        }

        //***************************选用不同的操作类****************************
        // 获取请求的方法
        String requestMethod = headerMap.get('method');
        // 根据不同的请求选用不同的操作类
        HerokuAPIHandler handler;
        if (requestMethod == 'createUser' || requestMethod == 'updateUser'){
            handler = new HerokuAPIHandler.UpsertUserHandler();
        } else if (requestMethod == 'createCase') {
            handler = new HerokuAPIHandler.UpsertCaseHandler();
        } else if (requestMethod == 'registerWarranty') {
            handler = new HerokuAPIHandler.RegisterWarrantyHandler();
        } else {
            return HerokuAPIUtils.getErrorResponse(1007, 'Can not find the services that you request.');
        }

        //***************************执行具体的操作****************************
        // 获取请求体
        String requestBody = request.requestBody.toString();

        try {
            return handler.handlePost(requestBody);
        } catch (Exception ex) {
            System.debug('----:' + ex.getMessage() + ';' + ex.getStackTraceString());
            return HerokuAPIUtils.getErrorResponse(5001, 'Can not serializa your request.' + 'Cause:' + ex.getMessage() + ';' + ex.getStackTraceString());
        }
    }

    @HttpGet
    global static HerokuEntity.ResponseEntity doGet() {
        // 获取请求
        RestRequest request = RestContext.request;
        // 获取请求头
        Map<String, String> headerMap = request.headers;

        //***************************校验请求****************************
        HerokuEntity.ResponseEntity response = checkHeaderMap(headerMap);
        if (response != null) {
            return response;
        }

        //***************************选用不同的操作类****************************
        // 获取请求的方法
        // String requestMethod = headerMap.get('method');
        // 根据不同的请求选用不同的操作类
        // HerokuAPIHandler handler;
        // if (requestMethod == 'getUser'){
        //     handler = new HerokuAPIHandler.SelectUserByEmailHandler();
        // } else if (requestMethod == 'checkUserExist') {
        //     handler = new HerokuAPIHandler.SelectUserByIdHandler();
        // } else if (requestMethod == 'listAllOrder') {
        //     handler = new HerokuAPIHandler.SelectOrderHandler();
        // } else if (requestMethod == 'listCase') {
        //     handler = new HerokuAPIHandler.SelectCaseHandler();
        // } else if (requestMethod == 'listCaseType') {
        //     handler = new HerokuAPIHandler.SelectCaseTypeHandler();
        // } else if (requestMethod == 'closeCase') {
        //     handler = new HerokuAPIHandler.CloseCaseHandler();
        // } else if (requestMethod == 'reopenCase') {
        //     handler = new HerokuAPIHandler.ReopenCaseHandler();
        // } else if (requestMethod == 'listWarranty') {
        //     handler = new HerokuAPIHandler.SelectWarrantyHandler();
        // } else {
        //     return HerokuAPIUtils.getErrorResponse(1007, 'Can not find the services that you request.');
        // }

        //***************************执行具体的操作****************************
        // 获取请求参数
        // String objectId = headerMap.get('objectId');

        // try {
        //     return handler.handle(objectId);
        // } catch (Exception ex){
        //     System.debug('----:' + ex.getMessage() + ';' + ex.getStackTraceString());
        //     return HerokuAPIUtils.getErrorResponse(5001, 'Can not serializa your request.');
        // }

        //***************************选用不同的操作类,执行不同的操作****************************
        try {
            // 获取请求的方法
            String requestMethod = headerMap.get('method');
            System.debug('---------------RequestMethod:' + requestMethod);
            // 根据不同的请求选用不同的操作类进行操作
            if (requestMethod == 'getUser'){
                return new HerokuAPIHandler.SelectUserByEmailHandler().handleGet(headerMap);
            } else if (requestMethod == 'selectUserData') { //该接口用于IoT中获取用户数据
                return new HerokuAPIHandler.GetUserDataByEamilHandler().handleGet(headerMap);
            } else if (requestMethod == 'getUserData') {    //该接口用于R2C网站中获取用户数据
                return new HerokuAPIHandler.GetUserDataByIdHandler().handleGet(headerMap);
            } else if (requestMethod == 'getUserInfo') {    //该接口用于EU网站中获取用户数据
                //add by yujie for EU
                return new HerokuAPIHandler.GetUserInfoByIdHandler().handleGet(headerMap);
            } else if (requestMethod == 'checkUserExist') {
                return new HerokuAPIHandler.SelectUserByIdHandler().handleGet(headerMap);
            } else if (requestMethod == 'listAllOrder') {
                return new HerokuAPIHandler.SelectOrderHandler().handleGet(headerMap);
            } else if (requestMethod == 'listCase') {
                return new HerokuAPIHandler.SelectCaseHandler().handleGet(headerMap);
            } else if (requestMethod == 'listCaseType') {
                return new HerokuAPIHandler.SelectCaseTypeHandler().handleGet(headerMap);
            } else if (requestMethod == 'closeCase') {
                return new HerokuAPIHandler.CloseCaseHandler().handleGet(headerMap);
            } else if (requestMethod == 'reopenCase') {
                return new HerokuAPIHandler.ReopenCaseHandler().handleGet(headerMap);
            } else if (requestMethod == 'listWarranty') {
                return new HerokuAPIHandler.SelectWarrantyHandler().handleGet(headerMap);
            } else if (requestMethod == 'sendEmail') {
                return new HerokuAPIHandler.SendEmailHandler().handleGet(headerMap);
            } else if (requestMethod == 'getSNValidation') {
                return new HerokuAPIHandler.GetSNValidation().handleGet(headerMap);
            } else {
                return HerokuAPIUtils.getErrorResponse(1007, 'Can not find the services that you request.');
            }
        } catch (Exception ex){
            System.debug('----:' + ex.getMessage() + ';' + ex.getStackTraceString());
            return HerokuAPIUtils.getErrorResponse(5001, 'Can not serializa your request.');
        }
    }

    // 该方法用于校验请求头中的数据是否正确
    private static HerokuEntity.ResponseEntity checkHeaderMap(Map<String,String> headerMap) {
        Set<String> keySets = headerMap.keySet();
        for (String key : keySets) {
            System.debug('------------------Key:' + key + ', value:' + headerMap.get(key));
        }
        //请求头为空
        if(headerMap.isEmpty()){
            return HerokuAPIUtils.getErrorResponse(1002, 'Please enter the headers.');
        }

        //是否标明调用的方法名
        if(!headerMap.containsKey('method')){
            return HerokuAPIUtils.getErrorResponse(1003, 'Please enter the method.');
        }

        //是否包含签名
        if(!headerMap.containsKey('sign')){
            return HerokuAPIUtils.getErrorResponse(1005, 'Please enter the signature.');
        }

        //校验签名
        String toEncryptStr = headerMap.get('method') + headerMap.get('appKey');
        Blob md5Str = Crypto.generateDigest('MD5', Blob.valueOf(toEncryptStr));
        if (headerMap.get('sign') != EncodingUtil.convertToHex(md5Str)) {
            return HerokuAPIUtils.getErrorResponse(1006, 'Please enter the correct signature.');
        }

        return null;
    }
}