public without sharing class WarrantyClaimTriggerHandle {

    public static Boolean isRun = true;

    public static void UpdateCaseStatusFromClaim(List<Warranty_Claim__c> newClaimList){

        if(!isRun) {
            return;
        }

    	if(newClaimList.size() > 0){

    		Map<String,String> caseIdToStatus = new Map<String,String>();
    		Map<String,String> caseIdToPaymentStatus = new Map<String,String>();
            Map<String,String> caseIdToInventory = new Map<String,String>();

    		for(Warranty_Claim__c claim : newClaimList ){
    			caseIdToStatus.put(claim.Case__c, claim.Status__c);
    			caseIdToPaymentStatus.put(claim.Case__c, claim.Payment_Status__c);
                //caseIdToInventory.put(claim.Case__c, claim.Inventry__c);
    		}

    		List<Case> caseList = new List<Case>();
            Warranty__c wa = new Warranty__c();
            List<Warranty_Item__c> wi_update = new List<Warranty_Item__c>();
    		for(Case ca : [SELECT Id,Status,Warranty__c,Warranty__r.Order_Times__c,Actual_Solution__c,Warranty__r.One_Time_Exception__c,Warranty__r.Lost_Receipt__c,Product_Related_Issue__c,Case_Type__c,Subject FROM Case WHERE Id IN: caseIdToStatus.keyset()]){
				if(ca.Warranty__r.One_Time_Exception__c && ca.Warranty__r.Lost_Receipt__c && caseIdToStatus.get(ca.Id) == 'Approved' && !ca.Product_Related_Issue__c.contains('Leaking phase change material')){
                    wa.Id = ca.Warranty__c;
                    wa.One_Time_Exception__c = false;
                    wa.Used_One_Time_Exception__c = true;
                    wa.ClosedCase__c = true;
                    if(ca.Case_Type__c == 'Service Claim' && ca.Subject == 'Replacement Dealer inventory'){

                        List<Warranty_Item__c> waiLst = [SELECT Id, Warranty__c, Is_Expired__c, Expiration_Date_New__c,Store_Policy_Date__c
                                                         FROM Warranty_Item__c
                                                         WHERE Warranty__c =: ca.Warranty__c];

                        for (Warranty_Item__c wi : waiLst) {
                            if (wi.Is_Expired__c == false) {
                                wi.Expiration_Date_New__c = date.today()-1;
                                wi.Store_Policy_Date__c = date.today()-1;
                                wi_update.add(wi);
                            }
                        }

                    }
                }
				if(caseIdToStatus.get(ca.Id) != null ){
					if(caseIdToStatus.get(ca.Id) == 'Approved' && caseIdToPaymentStatus.get(ca.Id) != 'Credited'){

						ca.Status = 'On Hold';
					}else if(caseIdToStatus.get(ca.Id) == 'Approved' && caseIdToPaymentStatus.get(ca.Id) == 'Credited'){
						if(ca.Actual_Solution__c != 'Replacement' || (ca.Actual_Solution__c == 'Replacement' && ca.Warranty__r.Order_Times__c > 0 && caseIdToInventory.get(ca.Id) == 'No Inventory')){
                            ca.Status = 'Closed';
                        }else {
                            ca.Status = 'On Hold';
                        }

					}else if(caseIdToStatus.get(ca.Id) == 'Rejected'){
						ca.Status = 'On Hold';
					}
				}

				caseList.add(ca);
    		}
            if(wa.Id != null){
                update wa;
            }
    		update caseList;
    	}
    }

    // public static void CallPushMethodToEBS(List<Warranty_Claim__c> newClaimList){
    //     List<String> claimIdList = new List<String>();
    //     for(Warranty_Claim__c wc : newClaimList){
    //         if(wc.Status__c == 'Approved' && wc.Payment_Status__c == 'Pending'){
    //             claimIdList.add(wc.Id);
    //         }

    //     }
    //     callPushClaimMethod(claimIdList);
    // }

    // @Future(callout=true)
    // private static void callPushClaimMethod(List<String> claimIdList){
    //     CCM_Service.pushClaimInfo(claimIdList);
    // }

}