/**
 * @Author: <PERSON>
 * @description:  warranty return claim util
 * @Create Date: 2022-10-31
 */
public without sharing class CCM_WarrantyReturnUtil {

    public static Warranty_Return_Claim__c upsertWarrantyReturnClaim(Warranty_Return_Claim__c objWarranty) {
        upsert objWarranty;
        return objWarranty;
    }

    public static List<Warranty_Return_Claim_Item__c> upsertWarrantyReturnClaimItem(List<Warranty_Return_Claim_Item__c> objWarrantyItem) {
        upsert objWarrantyItem;
        return objWarrantyItem;
    }

    public static List<Warranty_Return_Claim_Item_Attachment__c> upsertWarrantyReturnClaimItemAttachment(List<Warranty_Return_Claim_Item_Attachment__c> objAttachmentItem) {
        upsert objAttachmentItem;
        return objAttachmentItem;
    }

    public static List<Warranty_Return_Claim__c> getWarrantyReturn(String queryStr, String warrantyReturnClaimId) {
        return (List<Warranty_Return_Claim__c>)Database.query(queryStr);
    }

    public static List<Warranty_Return_Claim_Item__c> getWarrantyReturnClaimItem(String queryWarrantyReturnItem, String warrantyReturnClaimId) {
        return (List<Warranty_Return_Claim_Item__c>)Database.query(queryWarrantyReturnItem);
    }

    public static void deleteWarrantyReturnClaimItems(List<Warranty_Return_Claim_Item__c> itemList) {
        delete itemList;
    }



    /**
    * @description: get billing & shipping address
    */
    @AuraEnabled
    public static CCM_WarrantyReturnInfoCtl.ResponseWrapper getAddress(String customerId) {
        CCM_WarrantyReturnInfoCtl.ResponseWrapper response = new CCM_WarrantyReturnInfoCtl.ResponseWrapper();
        try {
            CCM_WarrantyReturnInfoCtl.BillingShippingAddress billingShipping = new CCM_WarrantyReturnInfoCtl.BillingShippingAddress();

            if (String.isEmpty(customerId)) {
                // portal
                User currentUser = [SELECT Id, ContactId, Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId()];
                if (String.isNotBlank(currentUser.ContactId)) {
                    customerId = currentUser.contact.AccountId;
                }
            }

            if (String.isEmpty(customerId)) {
                return null;
            }

            List<String> addressIds = new List<String>();
            Account objAccount = [SELECT Distributor_or_Dealer__c FROM Account WHERE Id = :customerId];
            if (objAccount.Distributor_or_Dealer__c == '2nd Tier Dealer') {
                for (Account_Address__c objShipTo : [SELECT Customer__c FROM Account_Address__c WHERE X2nd_Tier_Dealer__c = :customerId LIMIT 1]) {
                    if (String.isNotBlank(objShipTo.Customer__c)) {
                        customerId = objShipTo.Customer__c;
                    }
                }
            }
            
            List<CCM_WarrantyReturnInfoCtl.AddressWrapper> addressList = new List<CCM_WarrantyReturnInfoCtl.AddressWrapper>();
            if (objAccount.Distributor_or_Dealer__c == '2nd Tier Dealer') {
                List<Account_Address__c> shippingAccountAddress = [
                    SELECT Customer__c, Customer__r.Name
                    FROM Account_Address__c
                    WHERE Active__c = TRUE
                    AND (RecordType_Name__c = 'Shipping_Address' OR RecordType_Name__c = 'Dropship_Shipping_Address')
                    AND Approval_Status__c = 'Approved'
                    AND X2nd_Tier_Dealer__c = :customerId
                    AND Customer__c != null
                    LIMIT 1
                ];

                if (shippingAccountAddress != null && shippingAccountAddress.size() > 0) {
                    billingShipping.billing = generateAddressWrapper(false, shippingAccountAddress[0].Customer__c, new List<String> {'Billing_Address', 'Dropship_Billing_Address'});
                    billingShipping.shipping = generateAddressWrapper(true, customerId, new List<String> {'Shipping_Address', 'Dropship_Shipping_Address'});
                    billingShipping.dropshipShipping = generateAddressWrapper(true, customerId, new List<String> {'Dropship_Shipping_Address', 'Shipping_Address'});
                    billingShipping.dropshipBilling = generateAddressWrapper(false, shippingAccountAddress[0].Customer__c, new List<String> {'Shipping_Address'});
                    billingShipping.dropship = generateAddressWrapper(true, customerId, new List<String> {'Dropship_Shipping_Address', 'Shipping_Address'});
                }
            } else {
                billingShipping.billing = generateAddressWrapper(false, customerId, new List<String> {'Billing_Address', 'Dropship_Billing_Address'});
                billingShipping.shipping = generateAddressWrapper(false, customerId, new List<String> {'Shipping_Address', 'Dropship_Shipping_Address'});
                billingShipping.dropshipShipping = generateAddressWrapper(false, customerId, new List<String> {'Dropship_Shipping_Address', 'Shipping_Address'});
                billingShipping.dropshipBilling = generateAddressWrapper(false, customerId, new List<String> {'Shipping_Address'});
                billingShipping.dropship = generateAddressWrapper(false, customerId, new List<String> {'Dropship_Shipping_Address','Shipping_Address'});
            }
            
            response.returnData = billingShipping;
            response.isSuccess = true;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
    * @description: generate address wrapper for existing 
    */
    public static List<CCM_WarrantyReturnInfoCtl.AddressWrapper> generateAddressWrapper(Boolean is2ndTierDealerShipingAddress, String customerId, List<String> recordtype) {
        String queryStr = 'SELECT Id, Name, Address1__c, Address2__c,Address1_In_French__c,Address2_In_French__c, City__c, Country__c, State__c FROM Account_Address__c WHERE Active__c = true AND RecordType.DeveloperName in :recordtype AND Approval_Status__c = \'Approved\' ';
        Boolean isShip = false;
        for (String record : recordtype) {
            if (record.contains('Shipping')) {
                isShip = true;
                break;
            }
        }
        if (isShip == true) {
            String currentUserProfileName = [SELECT Id, Name FROM Profile WHERE Id = :UserInfo.getProfileId()].Name;
            User currentUser = [SELECT Id, ContactId, Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId()];
            if (currentUserProfileName == 'Sales Agency') {
                Set<String> agencyIds = new Set<String>();
                List<Sales_Agency_Contact__c> salesAgencyContactList = [
                    SELECT Id, Agency__c
                    FROM Sales_Agency_Contact__c 
                    WHERE User__c = :currentUser.Id 
                    AND Is_Active__c = True
                    AND Agency__r.Is_Active__c = True
                ];
                if (salesAgencyContactList.size() > 0 ) {
                    for (Sales_Agency_Contact__c agencyContact : salesAgencyContactList) {
                        agencyIds.add(agencyContact.Agency__c);
                    }
                }
                queryStr += ' AND (EGO_Agency__c IN :agencyIds OR FLEX_Agency__c IN :agencyIds OR SKIL_SKILSAW_Agency__c IN :agencyIds) ';
            }  
        }

        if (is2ndTierDealerShipingAddress) {
            queryStr += ' AND X2nd_Tier_Dealer__c = :customerId ';
        } else {
            queryStr += ' AND Customer__c = :customerId ';
        }
        queryStr += ' LIMIT 1000 ';
        System.debug(Database.query(queryStr));
        List<CCM_WarrantyReturnInfoCtl.AddressWrapper> addressList = new List<CCM_WarrantyReturnInfoCtl.AddressWrapper>();
        for (Account_Address__c aa : (List<Account_Address__c>)Database.query(queryStr)) {

            CCM_WarrantyReturnInfoCtl.AddressWrapper wrapper = new CCM_WarrantyReturnInfoCtl.AddressWrapper();
            //add by vince, address in french ******** ,Address1_In_French__c
            if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                wrapper.address1    = aa.Address1_In_French__c;
                wrapper.address2    = aa.Address2_In_French__c;
             }else {
                wrapper.address1    = aa.Address1__c;
                wrapper.address2    = aa.Address2__c;
             }
            wrapper.state       = aa.State__c;
            wrapper.city        = aa.City__c;
            wrapper.country     = aa.Country__c;
            wrapper.name        = aa.Name;
            wrapper.addressId   = aa.Id;

            addressList.add(wrapper);
        }
        
        return addressList;
    }
}