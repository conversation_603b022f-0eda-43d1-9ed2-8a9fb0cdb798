({
    doGetQuotationDetail : function(component, helper) {
        component.set('v.isBusy', true);
        console.log('*****actiongetDataByInnerUser');
        var action = component.get("c.getDataByInnerUser");
        action.setParam("recordId", component.get("v.recordId"));
        action.setParam("customerId", component.get("v.customerId"));
        action.setParam("isDropShip", component.get("v.orderTypeVal"));
        console.log('999999999999'+ component.get("v.orderTypeVal"));
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    component.set('v.expirateDate', results.expirateDate);
                    component.set('v.quotation', results.po);
                    results.poItems.forEach(function(e){
                        e.ExpirateDate = results.expirateDate;
                        if(results.customerOrgCode != 'CCA'){
                            e.CS_Exchange_Rate__c = e.CS_Exchange_Rate__c ? e.CS_Exchange_Rate__c : (e.Product__r.CS_Exchange_Rate__c ? e.Product__r.CS_Exchange_Rate__c : 1);
                        }else{
                            e.CS_Exchange_Rate__c = 1;
                        }
                    });
                    component.set('v.quotationItems', results.poItems);
                    component.set('v.customerId', results.customerId);
                    component.set('v.customerType', results.customerType);
                    component.set('v.customerCluster', results.customerCluster);
                    component.set('v.customerOrgCode', results.customerOrgCode);
                    component.set('v.surcharge', results.surcharge);
                    if(results.po.Customer__r){
                        component.set('v.customerName', results.po.Customer__r.Name);
                    }
                    component.set('v.hasDropShipAddress', results.hasDropShipAddress);
                    if (results.hasDropShipAddress == false){
                        component.set('v.orderTypeVal', 'N');
                    }
                    if (component.get("v.orderTypeVal")  === '') {
                        if (results.po.Is_DropShip__c == true){
                            component.set('v.orderTypeVal', 'Y');
                        }else{
                            component.set('v.orderTypeVal', 'N');
                        }
                    }
                    component.set('v.brandScopeOpt', results.brandScopeList);
                    component.set('v.paymentTermAllOpts', results.paymentTermOptions);
                    component.set('v.brandScope', results.po.Brand_Scope__c);
                    component.set('v.freightTermLabel', results.freightTermVal);
                    component.set('v.paymentTermLabel', results.paymentTermVal);
                    component.set('v.paymentTerm', results.defautPaymentTerm);
                    component.set('v.paymentTermValue', results.paymentTermValue);
                    console.log('paymentTermValue-->'+ component.get('v.paymentTermValue'));
                    if(results.currentStep){
                        component.set("v.currentStep", results.currentStep);
                    }
                }
            } else {
                component.set('v.isBusy', false);
                var errors = response.getError();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },

    getEveryPrice: function (component, helper) {
        console.log('*****getEveryPrice');
        var action = component.get("c.getEveryPrice");
        action.setParam("recordId", component.get("v.recordId"));
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                console.log('*****getEveryPrice success');
            } else {
                var errors = response.getError();
                console.log(errors);
            }
        });
        $A.enqueueAction(action);
    },

    getUrlParameter : function(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam || sParameterName[0] === ('0.' + sParam)) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    }
})