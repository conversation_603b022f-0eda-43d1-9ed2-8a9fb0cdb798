public with sharing class CCM_AuthBrandValidationRulesHandler implements Triggers.Handler {
    public static Boolean isRun = true;
    public void handle() {
        if (isRun){
            if(Trigger.isBefore){
                //Update by <PERSON> on 7/15/2020
                //generateContractPriceList(Trigger.new);
                if (Trigger.isInsert){
                    //validateDuplicateBrands(Trigger.new, Trigger.newMap, Trigger.oldMap, false);
                }else if (Trigger.isUpdate){
                    //validateDuplicateBrands(Trigger.new, Trigger.newMap, Trigger.oldMap, true);
                    validateProgramUpdate(Trigger.new, Trigger.newMap, Trigger.oldMap);
                }
            }
            if (Trigger.isAfter) {
                if (Trigger.isUpdate){
                    validateProgramInactive(Trigger.new, Trigger.newMap, Trigger.oldMap);
                }
            }
        }
    }

    private void validateProgramUpdate(List<sObject> newList, Map<Id, sObject> newMap, Map<Id, sObject> oldMap){
        List<Sales_Program__c> itemList = (List<Sales_Program__c>) newList;
        Map<Id, Sales_Program__c> queriedProgramMap = new Map<Id, Sales_Program__c>([
            Select Id, Name, Prospect__c, Prospect__r.Risk_Code__c,Prospect__r.Credit_Limit__c,Prospect__r.Status, Approval_Status__c, Reject_By_Approval_Process__c,
                Customer__r.Distributor_or_Dealer__c, RecordType.DeveloperName,
                (Select Id from Attachments),
                (Select Id from ContentDocumentLinks),
                (Select Id, Account_Address__c, Account_Address__r.Name, Account_Address__r.Contact__c, Program__c, Address_Type__c, Status__c from Addresses_With_Program__r WHERE Account_Address__r.RecordTypeId = :CCM_Contants.BILLING_ADDRESS_RECORDTYPEID )
            from Sales_Program__c
            WHERE Id IN :newMap.keySet()
        ]);
        //
        Map<String, String> pbNameMap = new Map<String, String>();
        pbNameMap = getPriceBookName (newList);
        for (Sales_Program__c newItem : itemList) {
            Sales_Program__c oldItem = (Sales_Program__c) oldMap.get(newItem.Id);
            CCM_V_Program_Validations.requireAttachmentForCustomPriceList(newItem, oldItem, queriedProgramMap, pbNameMap);
            if (!queriedProgramMap.get(newItem.id).RecordType.DeveloperName.contains('Dropship_Sales_Standard') && !queriedProgramMap.get(newItem.id).RecordType.DeveloperName.contains('Dropship_Sales_Customized')) {
                CCM_V_Program_Validations.requireAddressWithProgram(newItem, oldItem, queriedProgramMap);
            }
            CCM_V_Program_Validations.validateRiskcodeAndCreditBeforeApproved(newItem, oldItem, queriedProgramMap);
        }

        Set<Id> accIds = new Set<Id>();
        for (Sales_Program__c newItem : itemList) {
            if (newItem.RecordTypeId == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_ID || newItem.RecordTypeId == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_ID) {
                accIds.add(newItem.Customer__c);
            }
        }
        if (accIds.size() > 0) {
            Map<Id, Account> accMap = new Map<Id, Account> ([SELECT Id, Buying_Group__c, Buying_Group__r.Name,
                                                                    (SELECT Id, Prospect__c, Customer__c FROM Customer_Profile__r)
                                                                FROM Account WHERE Id IN :accIds]);
            for (Sales_Program__c newItem : itemList) {
                Account acc = accMap.get(newItem.Customer__c);
                if(acc != null) {
                    if(String.isBlank(acc.Buying_Group__c) || (String.isNotBlank(acc.Buying_Group__c) && !acc.Buying_Group__r.Name.containsIgnoreCase('TAIT DISTRIBUTORS LTD.'))) {
                        if (newItem.RecordTypeId == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_ID || newItem.RecordTypeId == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_ID) {
                            if (newItem.Approval_Status__c == 'Pending For Approval' && accMap.get(newItem.Customer__c) != null && accMap.get(newItem.Customer__c).Customer_Profile__r.size() == 0) {
                                newItem.addError('Please add at least one customer profile for this customer');
                            }
                        }
                    }
                }
            }
        }

        for(Sales_Program__c newItem : itemList){
            Sales_Program__c oldItem = (Sales_Program__c) oldMap.get(newItem.Id);
            if((newItem.Payment_Term__c != oldItem.Payment_Term__c || newItem.Freight_Term__c != oldItem.Freight_Term__c) && newItem.RecordTypeId == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_ID){
                newItem.RecordTypeId = CCM_Constants.SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_ID;
            }
        }

    }

    private void validateProgramInactive(List<sObject> newList, Map<Id, sObject> newMap, Map<Id, sObject> oldMap) {
        List<Sales_Program__c> itemList = (List<Sales_Program__c>) newList;
        Map<Id, Sales_Program__c> queriedProgramMap = new Map<Id, Sales_Program__c>([SELECT Id, Approval_Status__c, Reject_By_Approval_Process__c, RecordType.DeveloperName,
                                                                                        (SELECT Id, Account_Address__c, Program__c, Address_Type__c, Status__c
                                                                                            FROM Addresses_With_Program__r
                                                                                            WHERE Account_Address__r.RecordTypeId = :CCM_Contants.BILLING_ADDRESS_RECORDTYPEID )
                                                                                    FROM Sales_Program__c
                                                                                    WHERE Id IN :newMap.keySet()]);
        for (Sales_Program__c sp : itemList) {
            if (sp.Approval_Status__c == 'Rejected' && ((Sales_Program__c)oldMap.get(sp.Id)).Approval_Status__c == 'Approved') {

                for (Address_With_Program__c awp : queriedProgramMap.get(sp.Id).Addresses_With_Program__r) {
                    if (awp.Status__c == 'A') {
                        sp.addError('Please inactive the Billing Address with Authorized Brand before rejected this authorized brand');
                    }
                }
            }
        }
    }

//    private void generateContractPriceList(List<sObject> newList){
//        Map<String, String> pbNameMap = new Map<String, String>();
//        pbNameMap = getPriceBookName (newList);
//        List<Sales_Program__c> itemList = (List<Sales_Program__c>) newList;
        /*List<Sales_Program__c> itemList = (List<Sales_Program__c>) newList;
        Set<Id> ids = new Set<Id>();
        for (Sales_Program__c sp : itemList) {
            if (sp.Price_Book_Mapping__c != null) {
                ids.add(sp.Price_Book_Mapping__c);
            }
        }
        Map<String, String> pbNameMap = new Map<String, String>();
        for (Customer_Brand_Pricebook_Mapping__c pbm : [
                SELECT Id,
                       Name
                FROM Customer_Brand_Pricebook_Mapping__c
                WHERE Id IN :ids
            ]) {
            pbNameMap.put(String.valueOf(pbm.Id),pbm.Name);
        }*/
//        for(Sales_Program__c item : itemList){
            /*if(item.Price_List__c == 'Contract Price List'){
                item.Contract_Price_List_Name__c = 'CNA-'+item.Prospect_Customer_Name__c+'-Price List';
            }else{
                item.Contract_Price_List_Name__c = '';
            }*/
//            if (pbNameMap.containsKey(item.Price_Book_Mapping__c)) {
//                if (pbNameMap.get(item.Price_Book_Mapping__c) == 'Contract Price List') {
//                    item.Contract_Price_List_Name__c = 'CNA-'+item.Prospect_Customer_Name__c+'-Price List';
//                } else {
//                    item.Contract_Price_List_Name__c = '';
//                }
//            }
//        }
//    }

    //add by nick 20200716
    private Map<String, String> getPriceBookName (List<sObject> newList) {
        List<Sales_Program__c> itemList = (List<Sales_Program__c>) newList;
        Set<Id> ids = new Set<Id>();
        for (Sales_Program__c sp : itemList) {
            if (sp.Price_Book_Mapping__c != null) {
                ids.add(sp.Price_Book_Mapping__c);
            }
        }
        Map<String, String> pbNameMap = new Map<String, String>();
        for (Customer_Brand_Pricebook_Mapping__c pbm : [
                SELECT Id,
                       Name
                FROM Customer_Brand_Pricebook_Mapping__c
                WHERE Id IN :ids
            ]) {
            pbNameMap.put(String.valueOf(pbm.Id),pbm.Name);
        }
        return pbNameMap;
    }
    /*private void validateDuplicateBrands(List<sObject> newList, Map<Id, sObject> newMap, Map<Id, sObject> oldMap, Boolean isUpdate) {
        List<Sales_Program__c> spLists = (List<Sales_Program__c>)newList;
        Set<Id> prospectIdSet = new Set<Id>();

        for (Sales_Program__c newItem : spLists){
            if (String.isNotBlank(newItem.Prospect__c) && !prospectIdSet.contains(newItem.Prospect__c)){
                prospectIdSet.add(newItem.Prospect__c);
            }
        }

        Map<String, List<String>> authBrandMap = new Map<String, List<String>>();
        if (prospectIdSet != null && prospectIdSet.size() > 0){
            List<Lead> leadList = [
                    SELECT Id,
                        (SELECT Brands__c FROM Sales_Program__r)
                    FROM Lead
                    WHERE Id IN :prospectIdSet];
            for (Lead prospect : leadList){
                List<String> brandNames = new List<String>();
                for (Sales_Program__c sp : prospect.Sales_Program__r){
                    if (!brandNames.contains(sp.Brands__c)){
                        brandNames.add(sp.Brands__c);
                    }
                }
                authBrandMap.put(prospect.Id, brandNames);
            }
        }

        for (Sales_Program__c newItem : spLists){
            List<String> brandList = authBrandMap.get(newItem.Prospect__c);
            if (brandList != null && brandList.size() > 0){
                if (isUpdate){
                    Sales_Program__c oldItem = (Sales_Program__c)oldMap.get(newItem.Id);
                    if (newItem.Brands__c != oldItem.Brands__c){
                        if (brandList.contains(newItem.Brands__c)){
                            newItem.addError('The Authorized Brand 【'+ newItem.Brands__c + '】 has already exists. Please do not add it again.');
                        }
                    }
                }else{
                    if (brandList.contains(newItem.Brands__c)){
                        newItem.addError('The Authorized Brand 【'+ newItem.Brands__c + '】 has already exists. Please do not add it again.');
                    }
                }
            }
        }
    } */
}