/**
 * @description       :
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 07-05-2024
 * @last modified by  : <EMAIL>
**/
public with sharing class CCM_DealPIMInfo {

    public static void dealKitInfo(){
        List<Pricebook2> pblist = [select id,Name from Pricebook2 where IsStandard = true];
        List<Product2> productlist = new List<Product2>();
        Map<String,Product2> productMap = new Map<String,Product2>();
        String PIMInfoStr = CCM_Service.getKitFromPIM();
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_032034_0", "rows": [{"values": ["CB738701", "PWRCore 12\u2122 Brushless 12V 3-Tool Combo Kit", "SKIL 12V PWRCORE\u2122 Brushless 3-Tool Combo Kit Drill Driver, MultiTool 12pcs and Area Light with One 2.0Ah PWRCORE-12\u2122 Lithium Batteries and Standard Charger", "<b>3-Tool Kit From SKIL PWRCore 12\u2122 </b> With these three essential tools within reach, you\'ve got the power to tackle that next home project. This kit includes three SKIL PWRCore 12\u2122 products designed for compact power, efficiency and performance: Brushless 12V 1/2 Inch Cordless Drill Driver, Brushless 12V Oscillating MultiTool and 12V Area Light.", "", "", "", "", "", "", "", "", "", "SKIL", "", "", "", "", "", {"id": "605@12", "entityId": 7300, "label": "Regular"}, "2019-11-12T01:41:50:458+0000",""], "object": {"id": "815@1", "entityId": 1000, "label": "CB738701"}}]}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        for(Object row : rowsObjs){
            Product2 p = new Product2();
            p.RecordTypeId = CCM_Contants.KIT_RECORDTYPEID;
            p.Source__c = 'PIM';
            Map<String, Object> objItem = (Map<String, Object>)row;
            List<Object> valueList = (List<Object>)objItem.get('values');
            for (Integer i=0; i<valueList.size(); i++) {
                //获取value
                String value = '';
                if (valueList.get(i) instanceof String) {
                    value = String.valueOf(valueList.get(i));
                } else {
                    Map<String,Object> m = (Map<String, Object>)valueList.get(i);
                    value = String.valueOf(m.get('label'));
                }

                //给product赋值
                if(i == 0){
                    p.ProductCode = value;
                }
                if(i == 1){
                    p.Name = value;
                }
                if(i == 2){
                    p.Short_description__c = value;
                }
                if(i == 3){
                    p.Long_description__c = value;
                }
                if(i == 4){
                    if(String.isNotBlank(value)){
                        p.Weight__c = Decimal.valueOf(value);
                    }
                }
                if(i == 5){
                    if(String.isNotBlank(value)){
                        p.Height__c = Decimal.valueOf(value);
                    }
                }
                if(i == 6){
                    if(String.isNotBlank(value)){
                        p.Width__c = Decimal.valueOf(value);
                    }
                }
                if(i == 7){
                    if(String.isNotBlank(value)){
                        p.Depth__c = Decimal.valueOf(value);
                    }
                }
                if(i == 8){
                    if(String.isNotBlank(value)){
                        p.GrossWeightInclPackage__c = Decimal.valueOf(value);
                    }
                }
                if(i == 9){
                   p.Tool_Height__c = value;
                }
                if(i == 10){
                    p.Tool_Length__c = value;
                }
                if(i == 11){
                    p.Tool_Weight__c = value;
                }
                if(i == 12){
                   p.Tool_Width__c = value;
                }
                if(i == 13){
                    p.Brand__c = value;
                    String bName = BrandMap.get(value)!=null ? BrandMap.get(value) : value;
                    p.Brand_Name__c = bName;
                }
                if(i == 14){
                    p.Weight_unit__c = value;
                }
                if(i == 15){
                    p.Height_unit__c = value;
                }
                if(i == 16){
                    p.Width_unit__c = value;
                }
                if(i == 17){
                    p.Depth_unit__c = value;
                }
                if(i == 18){
                    p.GrossWeightInclPackage_unit__c = value;
                }
                if(i == 19){
                    p.Status__c = value;
                }
                if(i == 20){
                    //p.LastModifiedDate__c = DateTime.Valueof(value.replace('T', ' '));
                }
                if(i == 21){
                    p.FilterType__c = value;
                }
                if(i == 22){
                    p.Sellable__c = Boolean.valueof(value);
                }
                if(i == 23){
                    if(String.isNotBlank(value)){
                        p.Full_Pallet_Quantity__c = Decimal.valueOf(value);
                    }
                }
                //Begin-Add by vince 20240704 get short description in french
                if(i == 24){
                    p.Product_Name_French__c = value;
                }
                //End-Add by vince 20240704
            }
            p.PIM_ExternalID__c = p.ProductCode + '-' + p.Source__c + '-' + p.RecordTypeId;
            productlist.add(p);
        }
        //insert productlist;
        List<Database.UpsertResult> urlist = Database.upsert(productlist, Product2.PIM_ExternalID__c.getDescribe().getSObjectField(),false);
        for(Database.UpsertResult ur : urlist){
            if (!ur.isSuccess()) {
                List<Database.Error> eList = ur.getErrors();
                System.debug(LoggingLevel.INFO, '*** eList.get(0).getMessage(): ' + eList.get(0).getMessage());
            }
        }
    }

    public static void dealComboKitInfo(){
        List<Pricebook2> pblist = [select id,Name from Pricebook2 where IsStandard = true];
        List<Product2> productlist = new List<Product2>();
        Map<String,Product2> productMap = new Map<String,Product2>();
        String PIMInfoStr = CCM_Service.getComboKitFromPIM();
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_032034_0", "rows": [{"values": ["CB738701", "PWRCore 12\u2122 Brushless 12V 3-Tool Combo Kit", "SKIL 12V PWRCORE\u2122 Brushless 3-Tool Combo Kit Drill Driver, MultiTool 12pcs and Area Light with One 2.0Ah PWRCORE-12\u2122 Lithium Batteries and Standard Charger", "<b>3-Tool Kit From SKIL PWRCore 12\u2122 </b> With these three essential tools within reach, you\'ve got the power to tackle that next home project. This kit includes three SKIL PWRCore 12\u2122 products designed for compact power, efficiency and performance: Brushless 12V 1/2 Inch Cordless Drill Driver, Brushless 12V Oscillating MultiTool and 12V Area Light.", "", "", "", "", "", "", "", "", "", "SKIL", "", "", "", "", "", {"id": "605@12", "entityId": 7300, "label": "Regular"}, "2019-11-12T01:41:50:458+0000",""], "object": {"id": "815@1", "entityId": 1000, "label": "CB738701"}}]}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        for(Object row : rowsObjs){
            Product2 p = new Product2();
            p.RecordTypeId = CCM_Contants.KIT_RECORDTYPEID;
            p.Source__c = 'PIM';
            Map<String, Object> objItem = (Map<String, Object>)row;
            List<Object> valueList = (List<Object>)objItem.get('values');
            for (Integer i=0; i<valueList.size(); i++) {
                //获取value
                String value = '';
                if (valueList.get(i) instanceof String) {
                    value = String.valueOf(valueList.get(i));
                } else {
                    Map<String,Object> m = (Map<String, Object>)valueList.get(i);
                    value = String.valueOf(m.get('label'));
                }

                //给product赋值
                if(i == 0){
                    p.ProductCode = value;
                }
                if(i == 1){
                    p.Name = value;
                }
                if(i == 2){
                    p.Short_description__c = value;
                }
                if(i == 3){
                    p.Long_description__c = value;
                }
                if(i == 4){
                    if(String.isNotBlank(value)){
                        p.Weight__c = Decimal.valueOf(value);
                    }
                }
                if(i == 5){
                    if(String.isNotBlank(value)){
                        p.Height__c = Decimal.valueOf(value);
                    }
                }
                if(i == 6){
                    if(String.isNotBlank(value)){
                        p.Width__c = Decimal.valueOf(value);
                    }
                }
                if(i == 7){
                    if(String.isNotBlank(value)){
                        p.Depth__c = Decimal.valueOf(value);
                    }
                }
                if(i == 8){
                    if(String.isNotBlank(value)){
                        p.GrossWeightInclPackage__c = Decimal.valueOf(value);
                    }
                }
                if(i == 9){
                   p.Tool_Height__c = value;
                }
                if(i == 10){
                    p.Tool_Length__c = value;
                }
                if(i == 11){
                    p.Tool_Weight__c = value;
                }
                if(i == 12){
                   p.Tool_Width__c = value;
                }
                if(i == 13){
                    p.Brand__c = value;
                    String bName = BrandMap.get(value)!=null ? BrandMap.get(value) : value;
                    p.Brand_Name__c = bName;
                }
                if(i == 14){
                    p.Weight_unit__c = value;
                }
                if(i == 15){
                    p.Height_unit__c = value;
                }
                if(i == 16){
                    p.Width_unit__c = value;
                }
                if(i == 17){
                    p.Depth_unit__c = value;
                }
                if(i == 18){
                    p.GrossWeightInclPackage_unit__c = value;
                }
                if(i == 19){
                    p.Status__c = value;
                }
                if(i == 20){
                    //p.LastModifiedDate__c = DateTime.Valueof(value.replace('T', ' '));
                }
                if(i == 21){
                    p.FilterType__c = value;
                }
                if(i == 22){
                    p.Sellable__c = Boolean.valueof(value);
                }
                if(i == 23){
                    if(String.isNotBlank(value)){
                        p.Full_Pallet_Quantity__c = Decimal.valueOf(value);
                    }
                }
                //Begin-Add by vince 20240704 get short description in french
                if(i == 24){
                    p.Product_Name_French__c = value;
                }
                //End-Add by vince 20240704
            }
            p.PIM_ExternalID__c = p.ProductCode + '-' + p.Source__c + '-' + p.RecordTypeId;
            productlist.add(p);
        }
        //insert productlist;
        List<Database.UpsertResult> urlist = Database.upsert(productlist, Product2.PIM_ExternalID__c.getDescribe().getSObjectField(),false);
        for(Database.UpsertResult ur : urlist){
            if (!ur.isSuccess()) {
                List<Database.Error> eList = ur.getErrors();
                System.debug(LoggingLevel.INFO, '*** eList.get(0).getMessage(): ' + eList.get(0).getMessage());
            }
        }
    }

    public static void dealProductInfo(){
        List<Product2> productlist = new List<Product2>();
        Map<String,Product2> productMap = new Map<String,Product2>();
        String PIMInfoStr = CCM_Service.getProductFromPIM();
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_032716_0", "rows": [{"values": ["", "", "A Power Drill That Gets The Job Done. Tightening screws? Done. Assembling new furniture? Done. Drilling through wood, plastic or metal surfaces? Done, done and done. The SKIL 20V 1/2 Inch Cordless Drill Driver is the power drill you want for any home project.", "", "", "", "", "", "7.72", "", "2.11", "2.78", "SKIL", "", "", "", "", "", {"id": "605@12", "entityId": 7300, "label": "Regular"}, "2019-10-11T03:13:08:603+0000"], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}]}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        for(Object row : rowsObjs){
            Product2 p = new Product2();
            p.RecordTypeId = CCM_Contants.PRODUCT_RECORDTYPEID;
            p.Source__c = 'PIM';
            Map<String, Object> objItem = (Map<String, Object>)row;
            List<Object> valueList = (List<Object>)objItem.get('values');
            for (Integer i=0; i<valueList.size(); i++) {
                //获取value
                String value = '';
                if (valueList.get(i) instanceof String) {
                    value = String.valueOf(valueList.get(i));
                } else {
                    Map<String,Object> m = (Map<String, Object>)valueList.get(i);
                    value = String.valueOf(m.get('label'));
                }

                //给product赋值
                if(i == 0){
                    p.ProductCode = value;
                }
                if(i == 1){
                    p.Name = value;
                }
                if(i == 2){
                    p.Short_description__c = value;
                }
                if(i == 3){
                    p.Long_description__c = value;
                }
                if(i == 4){
                    if(String.isNotBlank(value)){
                        p.Weight__c = Decimal.valueOf(value);
                    }
                }
                if(i == 5){
                    if(String.isNotBlank(value)){
                        p.Height__c = Decimal.valueOf(value);
                    }
                }
                if(i == 6){
                    if(String.isNotBlank(value)){
                        p.Width__c = Decimal.valueOf(value);
                    }
                }
                if(i == 7){
                    if(String.isNotBlank(value)){
                        p.Depth__c = Decimal.valueOf(value);
                    }
                }
                if(i == 8){
                    if(String.isNotBlank(value)){
                        p.GrossWeightInclPackage__c = Decimal.valueOf(value);
                    }
                }
                if(i == 9){
                    p.Tool_Height__c = value;
                }
                if(i == 10){
                    p.Tool_Length__c = value;
                }
                if(i == 11){
                    p.Tool_Weight__c = value;
                }
                if(i == 12){
                    p.Tool_Width__c = value;
                }
                if(i == 13){
                    p.Brand__c = value;
                    String bName = BrandMap.get(value)!=null ? BrandMap.get(value) : value;
                    p.Brand_Name__c = bName;
                }
                if(i == 14){
                    p.Weight_unit__c = value;
                }
                if(i == 15){
                    p.Height_unit__c = value;
                }
                if(i == 16){
                    p.Width_unit__c = value;
                }
                if(i == 17){
                    p.Depth_unit__c = value;
                }
                if(i == 18){
                    p.GrossWeightInclPackage_unit__c = value;
                }
                if(i == 19){
                    p.Status__c = value;
                }
                if(i == 20){
                    //p.LastModifiedDate__c = DateTime.Valueof(value.replace('T', ' '));
                }
                if(i == 21){
                    p.FilterType__c = value;
                }
                if(i == 22){
                    p.Sellable__c = Boolean.valueof(value);
                }
            }
            p.PIM_ExternalID__c = p.ProductCode + '-' + p.Source__c + '-' + p.RecordTypeId;
            productlist.add(p);
        }
        //insert productlist;
        List<Database.UpsertResult> urlist = Database.upsert(productlist, Product2.PIM_ExternalID__c.getDescribe().getSObjectField(),false);
        for(Database.UpsertResult ur : urlist){
            if (!ur.isSuccess()) {
                List<Database.Error> eList = ur.getErrors();
                System.debug(LoggingLevel.INFO, '*** eList.get(0).getMessage(): ' + eList.get(0).getMessage());
            }
        }
    }

    public static void dealAccessoriesProductInfo(){
        //yanko
        List<Product2> productlist = new List<Product2>();
        Map<String,Product2> productMap = new Map<String,Product2>();
        String PIMInfoStr = CCM_Service.getAccessoriesProductFromPIM();
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_032716_0", "rows": [{"values": ["", "", "A Power Drill That Gets The Job Done. Tightening screws? Done. Assembling new furniture? Done. Drilling through wood, plastic or metal surfaces? Done, done and done. The SKIL 20V 1/2 Inch Cordless Drill Driver is the power drill you want for any home project.", "", "", "", "", "", "7.72", "", "2.11", "2.78", "SKIL", "", "", "", "", "", {"id": "605@12", "entityId": 7300, "label": "Regular"}, "2019-10-11T03:13:08:603+0000","","","","","","","","","","","",""], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}]}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        //YANKO NNEDSENDEMAIL
        Boolean NeedSendEmail = false;
        String ErrorProduct = '';
        for(Object row : rowsObjs){
            Product2 p = new Product2();
            p.RecordTypeId = CCM_Contants.PRODUCT_RECORDTYPEID;
            p.Source__c = 'PIM';
            Map<String, Object> objItem = (Map<String, Object>)row;
            List<Object> valueList = (List<Object>)objItem.get('values');
            // Yanko
            Decimal Decimalqty = 0;
            Set<Decimal> qty = new set<Decimal>();
            list<Decimal> qtylist = new list<Decimal>();
            boolean hasTTinnerqty = false;
            for (Integer i=0; i<valueList.size(); i++) {
                //获取value
                String value = '';
                if (valueList.get(i) instanceof String) {
                    value = String.valueOf(valueList.get(i));
                } else {
                    Map<String,Object> m = (Map<String, Object>)valueList.get(i);
                    value = String.valueOf(m.get('label'));
                }

                //给product赋值
                if(i == 0){
                    p.ProductCode = value;
                }
                if(i == 1){
                    p.Name = value;
                }
                if(i == 2){
                    p.Short_description__c = value;
                }
                if(i == 3){
                    p.Long_description__c = value;
                }
                if(i == 4){
                    if(String.isNotBlank(value)){
                        p.Weight__c = Decimal.valueOf(value);
                    }
                }
                if(i == 5){
                    if(String.isNotBlank(value)){
                        p.Height__c = Decimal.valueOf(value);
                    }
                }
                if(i == 6){
                    if(String.isNotBlank(value)){
                        p.Width__c = Decimal.valueOf(value);
                    }
                }
                if(i == 7){
                    if(String.isNotBlank(value)){
                        p.Depth__c = Decimal.valueOf(value);
                    }
                }
                if(i == 8){
                    if(String.isNotBlank(value)){
                        p.GrossWeightInclPackage__c = Decimal.valueOf(value);
                    }
                }
                if(i == 9){
                    p.Tool_Height__c = value;
                }
                if(i == 10){
                    p.Tool_Length__c = value;
                }
                if(i == 11){
                    p.Tool_Weight__c = value;
                }
                if(i == 12){
                    p.Tool_Width__c = value;
                }
                if(i == 13){
                    p.Brand__c = value;
                    String bName = BrandMap.get(value)!=null ? BrandMap.get(value) : value;
                    p.Brand_Name__c = bName;
                }
                if(i == 14){
                    p.Weight_unit__c = value;
                }
                if(i == 15){
                    p.Height_unit__c = value;
                }
                if(i == 16){
                    p.Width_unit__c = value;
                }
                if(i == 17){
                    p.Depth_unit__c = value;
                }
                if(i == 18){
                    p.GrossWeightInclPackage_unit__c = value;
                }
                if(i == 19){
                    p.Status__c = value;
                }
                if(i == 20){
                    //p.LastModifiedDate__c = DateTime.Valueof(value.replace('T', ' '));
                }
                if(i == 21){
                    p.FilterType__c = value;
                }
                if(i == 22){
                    p.Sellable__c = Boolean.valueof(value);
                }
                if(i == 23){
                    if(String.isNotBlank(value)){
                        p.Full_Pallet_Quantity__c = Decimal.valueOf(value);
                    }
                }
                //yanko
                if(i == 24){
                    if(String.isNotBlank(value)){
                        p.CS_Exchange_Rate__c = Decimal.valueof(value);
                        hasTTinnerqty = true;
                    }
                }
                if(!hasTTinnerqty && (i == 25 || i == 26 || i == 27 || i == 28 || i == 29)){
                    if(String.isNotBlank(value)){
                        if(Decimalqty == 0){
                            Decimalqty = Decimal.valueof(value);
                        }
                        Decimal abc = Decimal.valueof(value);
                        if(Decimalqty != 0 && Decimalqty != abc){
                            Decimalqty = 1143999999;
                        }
                    }
                }
                //Begin-Add by vince 20240704 get short description in french
                if(i == 30){
                    p.Product_Name_French__c = value;
                }
                //End-Add by vince 20240704
            }
            if(Decimalqty != 0 && Decimalqty != 1143999999){
                p.CS_Exchange_Rate__c = Decimalqty;
            }else if(Decimalqty == 1143999999){
                NeedSendEmail = true;
                ErrorProduct = ErrorProduct + p.ProductCode + ',<br>';
            }
            p.PIM_ExternalID__c = p.ProductCode + '-' + p.Source__c + '-' + p.RecordTypeId;
            productlist.add(p);
        }
        //yanko --- 如果在TT不存在的情况下，剩余的IPSP、IPKB、IPCB、IPC、IPK有两种或以上的Value ， 则会发邮件到PIM邮箱
        if(NeedSendEmail == true){
            System.debug('send All Product = ' + ErrorProduct);
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            List<String> sendTo = new List<String>();

            // Set<String> PIMEmailAddressStr = new Set<String>();
            String PIMEmailAddress = Label.Package_Order_PIM_Email_Address;
            // PIMEmailAddressStr.addAll(PIMEmailAddress.split(','));
            sendTo.addAll(PIMEmailAddress.split(','));

            email.setSubject('Error Product');
            String line1 = 'Dear PIM <br>';
            String line2 = '  Here is Some product with error CS_Exchange_Rate__c , Please Check it.<br>';
            String line3 = ErrorProduct;
            String body = line1 + line2 + line3;

            OrgWideEmailAddress[] owea = [select Id from OrgWideEmailAddress where Address = '<EMAIL>'];
            email.setOrgWideEmailAddressId(owea.get(0).Id);

            email.setHtmlBody(body);
            email.setToAddresses(sendTo);

            Messaging.SendEmailResult [] r =
            Messaging.sendEmail(new Messaging.SingleEmailMessage[] {email});
        }
        //insert productlist;
        List<Database.UpsertResult> urlist = Database.upsert(productlist, Product2.PIM_ExternalID__c.getDescribe().getSObjectField(),false);
        for(Database.UpsertResult ur : urlist){
            if (!ur.isSuccess()) {
                List<Database.Error> eList = ur.getErrors();
                System.debug(LoggingLevel.INFO, '*** eList.get(0).getMessage(): ' + eList.get(0).getMessage());
            }
        }
    }

    public static void dealToolsOnlyProductInfo(){
        List<Product2> productlist = new List<Product2>();
        Map<String,Product2> productMap = new Map<String,Product2>();
        String PIMInfoStr = CCM_Service.getToolsOnlyProductFromPIM();
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_032716_0", "rows": [{"values": ["", "", "A Power Drill That Gets The Job Done. Tightening screws? Done. Assembling new furniture? Done. Drilling through wood, plastic or metal surfaces? Done, done and done. The SKIL 20V 1/2 Inch Cordless Drill Driver is the power drill you want for any home project.", "", "", "", "", "", "7.72", "", "2.11", "2.78", "SKIL", "", "", "", "", "", {"id": "605@12", "entityId": 7300, "label": "Regular"}, "","","","","","French Description"], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}]}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        for(Object row : rowsObjs){
            Product2 p = new Product2();
            p.RecordTypeId = CCM_Contants.PRODUCT_RECORDTYPEID;
            p.Source__c = 'PIM';
            Map<String, Object> objItem = (Map<String, Object>)row;
            List<Object> valueList = (List<Object>)objItem.get('values');
            for (Integer i=0; i<valueList.size(); i++) {
                //获取value
                String value = '';
                if (valueList.get(i) instanceof String) {
                    value = String.valueOf(valueList.get(i));
                } else {
                    Map<String,Object> m = (Map<String, Object>)valueList.get(i);
                    value = String.valueOf(m.get('label'));
                }

                //给product赋值
                if(i == 0){
                    p.ProductCode = value;
                }
                if(i == 1){
                    p.Name = value;
                }
                if(i == 2){
                    p.Short_description__c = value;
                }
                if(i == 3){
                    p.Long_description__c = value;
                }
                if(i == 4){
                    if(String.isNotBlank(value)){
                        p.Weight__c = Decimal.valueOf(value);
                    }
                }
                if(i == 5){
                    if(String.isNotBlank(value)){
                        p.Height__c = Decimal.valueOf(value);
                    }
                }
                if(i == 6){
                    if(String.isNotBlank(value)){
                        p.Width__c = Decimal.valueOf(value);
                    }
                }
                if(i == 7){
                    if(String.isNotBlank(value)){
                        p.Depth__c = Decimal.valueOf(value);
                    }
                }
                if(i == 8){
                    if(String.isNotBlank(value)){
                        p.GrossWeightInclPackage__c = Decimal.valueOf(value);
                    }
                }
                if(i == 9){
                    p.Tool_Height__c = value;
                }
                if(i == 10){
                    p.Tool_Length__c = value;
                }
                if(i == 11){
                    p.Tool_Weight__c = value;
                }
                if(i == 12){
                    p.Tool_Width__c = value;
                }
                if(i == 13){
                    p.Brand__c = value;
                    String bName = BrandMap.get(value)!=null ? BrandMap.get(value) : value;
                    p.Brand_Name__c = bName;
                }
                if(i == 14){
                    p.Weight_unit__c = value;
                }
                if(i == 15){
                    p.Height_unit__c = value;
                }
                if(i == 16){
                    p.Width_unit__c = value;
                }
                if(i == 17){
                    p.Depth_unit__c = value;
                }
                if(i == 18){
                    p.GrossWeightInclPackage_unit__c = value;
                }
                if(i == 19){
                    p.Status__c = value;
                }
                if(i == 20){
                    //p.LastModifiedDate__c = DateTime.Valueof(value.replace('T', ' '));
                }
                if(i == 21){
                    p.FilterType__c = value;
                }
                if(i == 22){
                    p.Sellable__c = Boolean.valueof(value);
                }
                if(i == 23){
                    if(String.isNotBlank(value)){
                        p.Full_Pallet_Quantity__c = Decimal.valueOf(value);
                    }
                }
                //Begin-Add by vince 20240704 get short description in french
                if(i == 24){
                    p.Product_Name_French__c = value;
                }
                //End-Add by vince 20240704
            }
            p.PIM_ExternalID__c = p.ProductCode + '-' + p.Source__c + '-' + p.RecordTypeId;
            productlist.add(p);
        }
        //insert productlist;
        List<Database.UpsertResult> urlist = Database.upsert(productlist, Product2.PIM_ExternalID__c.getDescribe().getSObjectField(),false);
        for(Database.UpsertResult ur : urlist){
            if (!ur.isSuccess()) {
                List<Database.Error> eList = ur.getErrors();
                System.debug(LoggingLevel.INFO, '*** eList.get(0).getMessage(): ' + eList.get(0).getMessage());
            }
        }
    }

    public static void dealPartInfo(){
        String PIMInfoStr = CCM_Service.getPartFromPIM('0','-1',true);
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_033017_0", "totalSize":1000,"rows": [{"values": [], "object": {"id": "6054@1", "entityId": 1000, "label": "2825312001"}}]}';
        }
        Integer totalSize = 0;
        Integer startIndex = PIMInfoStr.indexOf('"totalSize":');
        if(startIndex > 0) {
            Integer endIndex = PIMInfoStr.indexOf(',', startIndex);
            String totalSizeStr = PIMInfoStr.substring(startIndex + '"totalSize":'.length(), endIndex);
            totalSize = Integer.valueOf(totalSizeStr);
        }
        // Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        // get all rows from PIM
        // PIMInfoStr = CCM_Service.getPartFromPIM('0', String.valueOf(totalSize), false);
        // if(Test.isRunningTest()){
        //     PIMInfoStr = '{"cacheId": "20191127_033017_0", "totalSize":"1000","rows": [{"values": [], "object": {"id": "6054@1", "entityId": 1000, "label": "2825312001"}}]}';
        // }
        // PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        // List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');

        Integer pagesize = 1000;
        Integer batchCount = totalSize / pagesize;
        if((batchCount * pagesize) < totalSize){
            batchCount = batchCount + 1;
        }
        System.debug(LoggingLevel.INFO, '*** batchCount: ' + batchCount);
        List<Integer> batchList = new List<Integer>();
        for(Integer i=0; i<batchCount; i++){
            batchList.add(i);
        }
        System.debug(LoggingLevel.INFO, '*** Json.serialize(batchList): ' + Json.serialize(batchList));
        // CCM_DealPartsInfoBatch c = new CCM_DealPartsInfoBatch(batchList,pagesize);
        // Database.executeBatch(c,1);
        if(!Test.isRunningTest()){
            System.enqueueJob(new CCM_DealPartsBatchQueue(batchList,pagesize,'CCM_DealPartsInfoBatch',null,null));
        //     System.enqueueJob(new CCM_DealPartsBatchQueue(rowsObjs, pagesize, 'CCM_DealPartsInfoBatch'));
        }


        /*List<Product2> productlist = new List<Product2>();
        Map<String,Product2> productMap = new Map<String,Product2>();
        String PIMInfoStr = CCM_Service.getPartFromPIM();
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        for(Object row : rowsObjs){
            Product2 p = new Product2();
            p.RecordTypeId = CCM_Contants.PARTS_RECORDTYPEID;
            p.Source__c = 'PIM';
            Map<String, Object> objItem = (Map<String, Object>)row;
            List<Object> valueList = (List<Object>)objItem.get('values');
            for (Integer i=0; i<valueList.size(); i++) {
                //获取value
                String value = '';
                if (valueList.get(i) instanceof String) {
                    value = String.valueOf(valueList.get(i));
                } else {
                    Map<String,Object> m = (Map<String, Object>)valueList.get(i);
                    value = String.valueOf(m.get('label'));
                }

                //给product赋值
                if(i == 0){
                    p.ProductCode = value;
                }
                if(i == 1){
                    p.Name = value+'-test';
                }
                if(i == 2){
                    p.Short_description__c = value;
                }
                if(i == 3){
                    p.Long_description__c = value;
                }
                if(i == 4){
                    p.Brand__c = value;
                }
            }
            p.PIM_ExternalID__c = p.ProductCode + 'p.Source__c';
            productlist.add(p);
        }
        //insert productlist;
        List<Database.UpsertResult> urlist = Database.upsert(productlist, Product2.PIM_ExternalID__c.getDescribe().getSObjectField(),false);
        for(Database.UpsertResult ur : urlist){
            if (!ur.isSuccess()) {
                List<Database.Error> eList = ur.getErrors();
                System.debug(LoggingLevel.INFO, '*** eList.get(0).getMessage(): ' + eList.get(0).getMessage());
            }
        }*/
    }

    public static void dealKitsAndProducts(){
        String PIMInfoStr = CCM_Service.getKitsAndProducts('0','-1',true);
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_090746_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "820@1", "entityId": 1000, "label": "CB736801"}}], "entityIdentifier": "ArticleComponent", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 118, "rowCount": 296}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        Integer pagesize = 100;
        Integer totalSize = Integer.valueOf(PIMInfoObj.get('totalSize'));
        Integer batchCount = totalSize / pagesize;
        if((batchCount * pagesize) < totalSize){
            batchCount = batchCount + 1;
        }
        System.debug(LoggingLevel.INFO, '*** batchCount: ' + batchCount);
        List<Integer> batchList = new List<Integer>();
        for(Integer i=0; i<batchCount; i++){
            batchList.add(i);
        }
        System.debug(LoggingLevel.INFO, '*** Json.serialize(batchList): ' + Json.serialize(batchList));
        //CCM_DealKitsAndProductsBatch c = new CCM_DealKitsAndProductsBatch(batchList,pagesize);
        //Database.executeBatch(c,1);
        if(!Test.isRunningTest()){
            System.enqueueJob(new CCM_DealPartsBatchQueue(batchList,pagesize,'CCM_DealKitsAndProductsBatch',null,null));
        }

        /*List<Kit_Item__c> kilist = new List<Kit_Item__c>();
        List<String> slist = new List<String>();
        Map<String,Id> kitMap = new Map<String,Id>();
        Map<String,Id> productMap = new Map<String,Id>();

        List<Product2> kitlist = [select id,ProductCode from Product2
                                  where Source__c = 'PIM'
                                  And RecordTypeId = :CCM_Contants.KIT_RECORDTYPEID];
        for(Product2 p : kitlist){
            kitMap.put(p.ProductCode, p.Id);
        }

        List<Product2> productlist = [select id,ProductCode from Product2
                                      where Source__c = 'PIM'
                                      And RecordTypeId = :CCM_Contants.PRODUCT_RECORDTYPEID];
        for(Product2 p : productlist){
            productMap.put(p.ProductCode, p.Id);
        }

        String PIMInfoStr = CCM_Service.getKitsAndProducts();
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        for(Object row : rowsObjs){
            String productNumber = '';
            String kitNumber = '';
            Kit_Item__c ki = new Kit_Item__c();
            ki.Source__c = 'PIM';
            ki.RecordTypeId = CCM_Contants.KITS_AND_PRODUCTS_RECORDTYPEID;
            Map<String, Object> objItem = (Map<String, Object>)row;
            List<Object> valueList = (List<Object>)objItem.get('values');
            for (Integer i=0; i<valueList.size(); i++) {
                //获取value
                String value = '';
                if (valueList.get(i) instanceof String) {
                    value = String.valueOf(valueList.get(i));
                } else {
                    Map<String,Object> m = (Map<String, Object>)valueList.get(i);
                    value = String.valueOf(m.get('label'));
                }

                //给product赋值
                if(i == 0){
                    ki.Product__c = productMap.get(value);
                    productNumber = value;
                }
                if(i == 1){
                    ki.Quantity__c = String.isNotBlank(value)?Decimal.valueOf(value):null;
                }
                if(i == 2){
                    ki.ExplosionID__c = value;
                }
            }

            Map<String, Object> oMap = (Map<String, Object>)objItem.get('object');
            ki.Kit__c = kitMap.get(String.valueOf(oMap.get('label')));
            kitNumber = String.valueOf(oMap.get('label'));
            String referenceID = kitNumber+'-'+productNumber;
            ki.ReferenceId__c = referenceID;
            if(String.isNotBlank(ki.Product__c) && String.isNotBlank(ki.Kit__c)){
                kilist.add(ki);
            }else{
                slist.add(referenceID);
            }
        }*/
        //System.debug(LoggingLevel.INFO, '*** Json.serialize(slist): ' + Json.serialize(slist));
        //upsert kilist ReferenceId__c;
    }

    public static void dealComboKitsAndProducts(){
        String PIMInfoStr = CCM_Service.getComboKitsAndProducts('0','-1',true);
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_090746_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "820@1", "entityId": 1000, "label": "CB736801"}}], "entityIdentifier": "ArticleComponent", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 118, "rowCount": 296}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        Integer pagesize = 100;
        Integer totalSize = Integer.valueOf(PIMInfoObj.get('totalSize'));
        Integer batchCount = totalSize / pagesize;
        if((batchCount * pagesize) < totalSize){
            batchCount = batchCount + 1;
        }
        System.debug(LoggingLevel.INFO, '*** batchCount: ' + batchCount);
        List<Integer> batchList = new List<Integer>();
        for(Integer i=0; i<batchCount; i++){
            batchList.add(i);
        }
        System.debug(LoggingLevel.INFO, '*** Json.serialize(batchList): ' + Json.serialize(batchList));
        //CCM_DealKitsAndProductsBatch c = new CCM_DealKitsAndProductsBatch(batchList,pagesize);
        //Database.executeBatch(c,1);
        if(!Test.isRunningTest()){
            System.enqueueJob(new CCM_DealPartsBatchQueue(batchList,pagesize,'CCM_DealKitsAndProductsBatch',null,null));
        }
    }

    public static void dealProductsAndParts(){
        String PIMInfoStr = CCM_Service.getProductsAndParts('0','-1',true);
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_091552_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}], "entityIdentifier": "ArticleReference", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 554, "rowCount": 9330}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        Integer pagesize = 50;
        Integer totalSize = Integer.valueOf(PIMInfoObj.get('totalSize'));
        Integer batchCount = totalSize / pagesize;
        if((batchCount * pagesize) < totalSize){
            batchCount = batchCount + 1;
        }
        System.debug(LoggingLevel.INFO, '*** batchCount: ' + batchCount);
        List<Integer> batchList = new List<Integer>();
        for(Integer i=0; i<batchCount; i++){
            batchList.add(i);
        }
        System.debug(LoggingLevel.INFO, '*** Json.serialize(batchList): ' + Json.serialize(batchList));
        //CCM_DealProductsAndPartsBatch c = new CCM_DealProductsAndPartsBatch(batchList,pagesize);
        //Database.executeBatch(c,1);
        if(!Test.isRunningTest()){
            System.enqueueJob(new CCM_DealPartsBatchQueue(batchList,pagesize,'CCM_DealProductsAndPartsBatch',null,null));
        }

        /*List<Kit_Item__c> kilist = new List<Kit_Item__c>();
        List<String> slist = new List<String>();
        Map<String,Id> productMap = new Map<String,Id>();
        Map<String,Id> partsMap = new Map<String,Id>();

        List<Product2> productlist = [select id,ProductCode from Product2
                                      where Source__c = 'PIM'
                                      And RecordTypeId = :CCM_Contants.PRODUCT_RECORDTYPEID];
        for(Product2 p : productlist){
            productMap.put(p.ProductCode, p.Id);
        }

        List<Product2> partslist = [select id,ProductCode from Product2
                                      where Source__c = 'PIM'
                                      And RecordTypeId = :CCM_Contants.PARTS_RECORDTYPEID];
        for(Product2 p : partslist){
            partsMap.put(p.ProductCode, p.Id);
        }

        String PIMInfoStr = CCM_Service.getProductsAndParts();
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        for(Object row : rowsObjs){
            String partNumber = '';
            String productNumber = '';
            Kit_Item__c ki = new Kit_Item__c();
            ki.Source__c = 'PIM';
            Map<String, Object> objItem = (Map<String, Object>)row;
            List<Object> valueList = (List<Object>)objItem.get('values');
            Set<String> referenceTypeSet = new Set<String>{'Spare part','Spare part Version 1','Spare part Version 2'};
            String referenceType =  String.valueOf(valueList.get(0));
            //System.debug(LoggingLevel.INFO, '*** referenceType: ' + referenceType);
            if(referenceTypeSet.contains(referenceType)){
                for (Integer i=0; i<valueList.size(); i++) {
                    //获取value
                    String value = '';
                    if (valueList.get(i) instanceof String) {
                        value = String.valueOf(valueList.get(i));
                    } else {
                        Map<String,Object> m = (Map<String, Object>)valueList.get(i);
                        value = String.valueOf(m.get('label'));
                    }

                    //给product赋值
                    if(i == 0){
                        ki.Version__c = value;
                    }
                    if(i == 1){
                        ki.Parts__c = partsMap.get(value);
                        partNumber = value;
                    }
                    if(i == 2){
                        ki.Quantity__c = String.isNotBlank(value)?Decimal.valueOf(value):null;
                    }
                    if(i == 3){
                        ki.ExplosionID__c = value;
                    }
                    if(i == 4){
                        ki.Repairable__c = Boolean.valueOf(value);
                    }
                    if(i == 5){
                        ki.EstimatedRepairTime__c = String.isNotBlank(value)?Decimal.valueOf(value):null;
                    }
                }

                Map<String, Object> oMap = (Map<String, Object>)objItem.get('object');
                ki.Product__c = productMap.get(String.valueOf(oMap.get('label')));
                productNumber = String.valueOf(oMap.get('label'));
                String referenceID = productNumber+'-'+partNumber;
                if(String.isNotBlank(ki.Parts__c) && String.isNotBlank(ki.Product__c)){
                    kilist.add(ki);
                }else{
                    slist.add(referenceID);
                }
            }
        }
        //System.debug(LoggingLevel.INFO, '*** Json.serialize(slist): ' + Json.serialize(slist));
        //System.debug(LoggingLevel.INFO, '*** slist.size(): ' + slist.size());
        //System.debug(LoggingLevel.INFO, '*** kilist.size(): ' + kilist.size());
        upsert kilist ReferenceId__c;*/
    }

     public static void dealAccessoriesProductsAndParts(){
        String PIMInfoStr = CCM_Service.getAccessoriesAndParts('0','-1',true);
        System.debug(LoggingLevel.INFO, '*** PIMInfoStr: ' + PIMInfoStr);
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_091552_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}], "entityIdentifier": "ArticleReference", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 554, "rowCount": 9330}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        Integer pagesize = 50;
        Integer totalSize = Integer.valueOf(PIMInfoObj.get('totalSize'));
        // PIMInfoStr = CCM_Service.getAccessoriesAndParts('0', String.valueOf(totalSize), false);
        // if(Test.isRunningTest()){
        //     PIMInfoStr = '{"cacheId": "20191127_091552_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}], "entityIdentifier": "ArticleReference", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 554, "rowCount": 9330}';
        // }
        // PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        // List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        Integer batchCount = totalSize / pagesize;
        if((batchCount * pagesize) < totalSize){
            batchCount = batchCount + 1;
        }
        System.debug(LoggingLevel.INFO, '*** batchCount: ' + batchCount);
        List<Integer> batchList = new List<Integer>();
        for(Integer i=0; i<batchCount; i++){
            batchList.add(i);
        }
        // System.debug(LoggingLevel.INFO, '*** Json.serialize(batchList): ' + Json.serialize(batchList));
        if(!Test.isRunningTest()){
            System.enqueueJob(new CCM_DealPartsBatchQueue(batchList, pagesize, 'CCM_DealProductsAndPartsBatch', null, null));
        }

    }


     public static void dealToolsOnlyProductsAndParts(){
        String PIMInfoStr = CCM_Service.getToolsOnlyAndParts('0','-1',true);
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_091552_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}], "entityIdentifier": "ArticleReference", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 554, "rowCount": 9330}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        Integer pagesize = 50;
        Integer totalSize = Integer.valueOf(PIMInfoObj.get('totalSize'));
        // PIMInfoStr = CCM_Service.getToolsOnlyAndParts('0', String.valueOf(totalSize), false);
        // if(Test.isRunningTest()){
        //     PIMInfoStr = '{"cacheId": "20191127_091552_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}], "entityIdentifier": "ArticleReference", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 554, "rowCount": 9330}';
        // }
        // PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        // List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        Integer batchCount = totalSize / pagesize;
        if((batchCount * pagesize) < totalSize){
            batchCount = batchCount + 1;
        }
        System.debug(LoggingLevel.INFO, '*** batchCount: ' + batchCount);
        List<Integer> batchList = new List<Integer>();
        for(Integer i=0; i<batchCount; i++){
            batchList.add(i);
        }
        // System.debug(LoggingLevel.INFO, '*** Json.serialize(batchList): ' + Json.serialize(batchList));
        //CCM_DealProductsAndPartsBatch c = new CCM_DealProductsAndPartsBatch(batchList,pagesize);
        //Database.executeBatch(c,1);
        if(!Test.isRunningTest()){
            System.enqueueJob(new CCM_DealPartsBatchQueue(batchList, pagesize, 'CCM_DealProductsAndPartsBatch', null, null));
        }
    }

    public static void dealProductsAndDiagram(){
        String PIMInfoStr = CCM_Service.getProductsAndDiagram('0','-1',true);
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_091959_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}], "entityIdentifier": "ArticleMediaAssetMap", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 554, "rowCount": 170}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        Integer pagesize = 50;
        Integer totalSize = Integer.valueOf(PIMInfoObj.get('totalSize'));
        Integer batchCount = totalSize / pagesize;
        if((batchCount * pagesize) < totalSize){
            batchCount = batchCount + 1;
        }
        System.debug(LoggingLevel.INFO, '*** batchCount: ' + batchCount);
        List<Integer> batchList = new List<Integer>();
        for(Integer i=0; i<batchCount; i++){
            batchList.add(i);
        }
        System.debug(LoggingLevel.INFO, '*** Json.serialize(batchList): ' + Json.serialize(batchList));
        if(!Test.isRunningTest()){
            System.enqueueJob(new CCM_DealPartsBatchQueue(batchList,pagesize,'CCM_DealProductsAndDiagramBatch',null,null));
        }

/*

        List<Kit_Item__c> kilist = new List<Kit_Item__c>();
        List<String> slist = new List<String>();
        Map<String,Id> productMap = new Map<String,Id>();
        Map<String,Id> partsMap = new Map<String,Id>();

        List<Product2> productlist = [select id,ProductCode from Product2
                                      where Source__c = 'PIM'
                                      And RecordTypeId = :CCM_Contants.PRODUCT_RECORDTYPEID];
        for(Product2 p : productlist){
            productMap.put(p.ProductCode, p.Id);
        }

        String PIMInfoStr = CCM_Service.getProductsAndDiagram('0','-1',false);
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        for(Object row : rowsObjs){
            String productNumber = '';
            String version = '';
            Kit_Item__c ki = new Kit_Item__c();
            ki.Source__c = 'PIM';
            ki.RecordTypeId = CCM_Contants.PRODUCTS_AND_DIAGRAM_RECORDTYPEID;
            Map<String, Object> objItem = (Map<String, Object>)row;
            List<Object> valueList = (List<Object>)objItem.get('values');
            for (Integer i=0; i<valueList.size(); i++) {
                //获取value
                String value = '';
                if (valueList.get(i) instanceof String) {
                    value = String.valueOf(valueList.get(i));
                } else {
                    Map<String,Object> m = (Map<String, Object>)valueList.get(i);
                    value = String.valueOf(m.get('label'));
                }

                if(i == 0){
                    ki.DiagramId__c = value;
                }
                if(i == 1){
                    ki.Diagram_Name__c = value;
                }
                if(i == 2){
                    version = DiagramVersionMap.get(value)==null ? value : DiagramVersionMap.get(value);
                    ki.Version__c = version;
                }
                if(i == 3){
                    ki.Diagram_ModifiedDate__c = value;
                }
            }

            Map<String, Object> oMap = (Map<String, Object>)objItem.get('object');
            ki.Product__c = productMap.get(String.valueOf(oMap.get('label')));
            productNumber = String.valueOf(oMap.get('label'));
            String referenceID = productNumber+'-'+version;
            ki.ReferenceId__c = referenceID;
            ki.Status__c = 'A';
            if(String.isNotBlank(ki.Product__c)){
                kilist.add(ki);
            }else{
                slist.add(referenceID);
            }
        }
        upsert kilist ReferenceId__c;

        List<Kit_Item__c> updatekilist = new List<Kit_Item__c>();
        Datetime now = System.now();
        Datetime datefilter = now.addDays(-1);
        datefilter = datefilter.addHours(6);
        List<Kit_Item__c> kilist2 = [select id,Status__c from Kit_Item__c where lastModifiedDate < :datefilter And RecordTypeId = :CCM_Contants.PRODUCTS_AND_DIAGRAM_RECORDTYPEID];
        for(Kit_Item__c ki : kilist2){
            ki.Status__c = 'I';
            updatekilist.add(ki);
        }
        System.debug(LoggingLevel.INFO, '*** updatekilist.size(): ' + updatekilist.size());
        update updatekilist;

        String rId = CCM_Contants.PRODUCTS_AND_DIAGRAM_RECORDTYPEID;
        String queryStr = 'select id,DiagramId__c,(select id from Attachments) from Kit_Item__c '
                         +'where RecordTypeId = :rId And Status__c = \'A\'';
        List<SObject> objlist = Database.query(querystr);
        List<String> query2Idlist = new List<String>();
        for(SObject obj : objlist){
            Kit_Item__c ki = (Kit_Item__c)obj;
            List<Attachment> attlist = ki.Attachments;
            if(attlist == null || attlist.size() == 0){
                query2Idlist.add(ki.Id);
            }
        }
        String query2Str = 'select id,DiagramId__c,Diagram_Name__c from Kit_Item__c where id in :query2Idlist';
        //CCM_DealDiagramInfoBatch c = new CCM_DealDiagramInfoBatch(query2Str,query2Idlist);
        //Database.executeBatch(c, 1);
        system.enqueueJob(new CCM_DealPartsBatchQueue(null,null,'CCM_DealDiagramInfoBatch',query2Idlist,query2Str));*/
    }

    public static void dealAccessoriesProductsAndDiagram(){
        String PIMInfoStr = CCM_Service.getAccessoriesProductsAndDiagram('0','-1',true);
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_091959_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}], "entityIdentifier": "ArticleMediaAssetMap", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 554, "rowCount": 170}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        Integer pagesize = 100;
        Integer totalSize = Integer.valueOf(PIMInfoObj.get('totalSize'));
        PIMInfoStr = CCM_Service.getAccessoriesProductsAndDiagram('0', String.valueOf(totalSize), false);
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_091959_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}], "entityIdentifier": "ArticleMediaAssetMap", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 554, "rowCount": 170}';
        }
        PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        // Integer batchCount = totalSize / pagesize;
        // if((batchCount * pagesize) < totalSize){
        //     batchCount = batchCount + 1;
        // }
        // System.debug(LoggingLevel.INFO, '*** batchCount: ' + batchCount);
        // List<Integer> batchList = new List<Integer>();
        // for(Integer i=0; i<batchCount; i++){
        //     batchList.add(i);
        // }
        // System.debug(LoggingLevel.INFO, '*** Json.serialize(batchList): ' + Json.serialize(batchList));
        if(!Test.isRunningTest()){
            System.enqueueJob(new CCM_DealPartsBatchQueue(rowsObjs, pagesize,'CCM_DealProductsAndDiagramBatch'));
        }
    }

    public static void dealToolsOnlyProductsAndDiagram(){
        String PIMInfoStr = CCM_Service.getToolsOnlyAndDiagram('0','-1',true);
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_091959_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}], "entityIdentifier": "ArticleMediaAssetMap", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 554, "rowCount": 170}';
        }
        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        Integer pagesize = 100;
        Integer totalSize = Integer.valueOf(PIMInfoObj.get('totalSize'));

        PIMInfoStr = CCM_Service.getToolsOnlyAndDiagram('0', String.valueOf(totalSize), false);
        if(Test.isRunningTest()){
            PIMInfoStr = '{"cacheId": "20191127_091959_0", "rows": [{"qualification": {}, "values": [], "object": {"id": "816@1", "entityId": 1000, "label": "DL527501"}}], "entityIdentifier": "ArticleMediaAssetMap", "columns": [], "columnCount": 0, "pageSize": -1, "startIndex": 0, "totalSize": 554, "rowCount": 170}';
        }
        PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        // Integer batchCount = totalSize / pagesize;
        // if((batchCount * pagesize) < totalSize){
        //     batchCount = batchCount + 1;
        // }
        // System.debug(LoggingLevel.INFO, '*** batchCount: ' + batchCount);
        // List<Integer> batchList = new List<Integer>();
        // for(Integer i=0; i<batchCount; i++){
        //     batchList.add(i);
        // }
        // System.debug(LoggingLevel.INFO, '*** Json.serialize(batchList): ' + Json.serialize(batchList));
        if(!Test.isRunningTest()){
            System.enqueueJob(new CCM_DealPartsBatchQueue(rowsObjs, pagesize,'CCM_DealProductsAndDiagramBatch'));
        }
    }

    /*public static void dealDiagramInfo(){
        List<Kit_Item__c> kilist = [select id,DiagramId__c,Diagram_Name__c from Kit_Item__c where id = 'a1f4F000000FCvM'];
        if(kilist.size() > 0){
            Kit_Item__c ki = kilist.get(0);
            String DiagramId = ki.DiagramId__c;
            HttpResponse res = CCM_Service.getProductsAndDiagram(DiagramId);
            Attachment attach = new Attachment();
            attach.ParentId = ki.Id;
            attach.Body = res.getBodyAsBlob();
            attach.Name = ki.Diagram_Name__c;
            attach.ContentType = res.getHeader('Content-Type');
            Insert attach;
        }
    }*/

    public static void dealBrandSeries() {

        // List<Brand_Series__mdt> brandSeriesList = [SELECT Id, Brand_Series__c, Selling_Category__c FROM Brand_Series__mdt];
        // Map<String, String> brandSeriesMap = new Map<String, String>();
        // for (Brand_Series__mdt brand : brandSeriesList) {
        //     brandSeriesMap.put(brand.Brand_Series__c, brand.Selling_Category__c);
        // }
        String PIMInfoStr = CCM_Service.getBrandSeriesFromPIM();
        if (Test.isRunningTest()) {
            PIMInfoStr = '{"cacheId": "20230613_072736_0","entityIdentifier": "ArticleAdditionalFields2","totalSize": 790,"startIndex": 0,"pageSize": -1,"rowCount": 3,"columnCount": 0,"columns": [],"rows": [{"object": {"id": "825@1","label": "9295-01","entityId": 1000},"qualification": {},"values": ["SKIL(Red)-不含SKILSAW和XP"]},{"object": {"id": "832@1","label": "5280-01","entityId": 1000},"qualification": {},"values": ["SKIL(Red)-不含SKILSAW和XP"]},{"object": {"id": "837@1","label": "7510-01","entityId": 1000},"qualification": {},"values": ["SKIL(Red)-不含SKILSAW和XP"]}]}';
        }

        Map<String, Object> PIMInfoObj = (Map<String, Object>)JSON.deserializeUntyped(PIMInfoStr);
        List<Object> rowsObjs = (List<Object>) PIMInfoObj.get('rows');
        List<Product2> productlist = new List<Product2>();
        for (Object row : rowsObjs) {
            Product2 p = new Product2();
            p.RecordTypeId = CCM_Contants.PRODUCT_RECORDTYPEID;
            p.Source__c = 'PIM';
            Map<String, Object> objItem = (Map<String, Object>)row;
            Map<String, Object> objValues = (Map<String, Object>) objItem.get('object'); //get the values of the item.  this is a list
            p.ProductCode = (String)objValues.get('label');
            List<Object> valueList = (List<Object>)objItem.get('values');
            p.Brand_Series__c = (String)valueList[0];
            p.PIM_ExternalID__c = p.ProductCode + '-' + p.Source__c + '-' + p.RecordTypeId;
            Product2 p2 = new Product2();
            p2.RecordTypeId = CCM_Contants.KIT_RECORDTYPEID;
            p2.Source__c = 'PIM';
            p2.ProductCode = (String)objValues.get('label');
            p2.Brand_Series__c = (String)valueList[0];
            p2.PIM_ExternalID__c = p2.ProductCode + '-' + p2.Source__c + '-' + p2.RecordTypeId;
            productlist.add(p);
            productlist.add(p2);
        }
        List<Database.UpsertResult> urlist = Database.upsert(productlist, Product2.PIM_ExternalID__c.getDescribe().getSObjectField(),false);
        for(Database.UpsertResult ur : urlist){
            if (!ur.isSuccess()) {
                List<Database.Error> eList = ur.getErrors();
                System.debug(LoggingLevel.INFO, '*** eList.get(0).getMessage(): ' + eList.get(0).getMessage());
            }
        }
    }

    public static Map<String, String> BrandMap = new Map<String, String> {
        'EGO' => 'EGO',
        'HAMMERHEAD' => 'Hammerhead',
        'SKIL' => 'Skil',
        'SKILSAW' => 'SkilSaw',
        'HYPERTOUGH' => 'HyperTough',
        'FLEX' => 'Flex'
    };

    public static Map<String, String> DiagramVersionMap = new Map<String, String> {
        '1' => 'Spare part',
        '2' => 'Spare part Version 1',
        '3' => 'Spare part Version 2'
    };
}