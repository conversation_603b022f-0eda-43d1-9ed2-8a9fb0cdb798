/**
 * @description       :
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 07-12-2024
 * @last modified by  : <EMAIL>
**/
public without sharing class CCM_Community_InvoiceFilterPageCtl {
    @AuraEnabled
    public static String initData(Decimal pageNumber, Integer pageSize, String filterString, Boolean isInit) {
        // System.debug(LoggingLevel.INFO, '*** filterString: ' + filterString);
        ReturnData returnDD = new ReturnData();
        User currentUser = [SELECT Id, ContactId, Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId()];
        if (currentUser.ContactId == null || currentUser.Contact.AccountId == null) {
            return JSON.serialize(returnDD);
        }
        Account acc = [SELECT Id, OwnerId, Distributor_or_Dealer__c, ORG_Code__c,Account<PERSON><PERSON>ber,Name FROM Account WHERE Id = :currentUser.Contact.AccountId];
        returnDD.customerName=acc.Name;
        String queryStr =
            'SELECT Id' +
            ', CurrencyIsoCode' +
            ', Name' +
            ', Terms__c' +
            ', Payment_Terms_Record__c' +
            ', Freight_Term__c' +
            ', Invoice_Number__c' +
            ', Invoice_Status__c' +
            ', Invoice_Type__c' +
            ', Invoice_Date__c' +
            ', PO_Number__c' +
            ', Order_Date__c' +
            ', Order__c' +
            ', Total_Due__c' +
            ', Total_Amount__c' +
            ', Freight_and_other_charges__c' +
            ', Tracking_NO__c' +
            ', Total_Remaining__c' +
            ', isPaid__c' +
            ', CreatedDate' +
            ', (SELECT Id, Accounting_OracleID__c, Due_Date__c, Paypal_Status__c, Amt_Original__c, Amt_Due_Remaining__c, CreatedDate FROM Account_Balances_Invoice__r ORDER BY Due_Date__c ASC)' +
            ' FROM Invoice__c' +
            ' WHERE (Customer__c = \'' + currentUser.Contact.AccountId + '\' OR Order__r.Purchase_Order__r.Customer__c = \'' + currentUser.Contact.AccountId + '\')' +
            ' AND ID != NULL';
        // + ' AND Invoice_Source__c = \'CNA_Auto_Invoice\'';

        if (String.isNotBlank(filterString)) {
            FilterWrapper filterWrapper = (FilterWrapper) JSON.deserialize(filterString, FilterWrapper.class);
            if (String.isNotBlank(filterWrapper.invoiceNumber)) {
                queryStr += ' AND Invoice_Number__c LIKE \'%' + filterWrapper.invoiceNumber + '%\'';
            }
            if (String.isNotBlank(filterWrapper.orderNumber)) {
                // queryStr += ' AND Invoice_Number__c LIKE %\''+ + '\'%';
            }
            if (String.isNotBlank(filterWrapper.cutomerPoNum)) {
                queryStr += ' AND PO_Number__c LIKE \'%' + filterWrapper.cutomerPoNum + '%\'';
            }
            if (String.isNotBlank(filterWrapper.invoiceDateMin)) {
                queryStr += ' AND Invoice_Date__c >= ' + filterWrapper.invoiceDateMin;
            }
            if (String.isNotBlank(filterWrapper.invoiceDateMax)) {
                queryStr += ' AND Invoice_Date__c <= ' + filterWrapper.invoiceDateMax;
            }
            if (String.isNotBlank(filterWrapper.invoiceType)) {
                // update by eric, ********
                // 注释此段, 在外引入固定值 Invoice_Type__c IN (\'CNA_Invoice\', \'CNA_External_CM\')
                // queryStr += ' AND Invoice_Type__c = \''+ filterWrapper.invoiceType + '\'';
            }
            if (String.isNotBlank(filterWrapper.status)) {
                if (filterWrapper.status == 'Open') {
                    queryStr += ' AND isPaid__c = false';
                } else {
                    queryStr += ' AND isPaid__c = true';
                }
            }
        }

        Date lastYear = Date.newInstance((System.today().year() - 1), 1, 1);
        queryStr += ' AND Invoice_Date__c >= :lastYear';
        //queryStr += ' AND Invoice_Type__c IN (\'CNA_Invoice\', \'CNA_External_CM\') ORDER BY PO_Number__c DESC';
        /**
         * <AUTHOR> Dou
         * @date 2020/11/09
         * @description for 2nd tier dealer logic
         */
        List<Invoice__c> invoiceData = new List<Invoice__c>();
        List<Invoice__c> invoiceTypeZero = new List<Invoice__c>();
        List<Invoice__c> invoiceTypeOne = new List<Invoice__c>();
        List<Invoice__c> invoiceTypeTwo = new List<Invoice__c>();
        List<Invoice__c> invoiceTypeThree = new List<Invoice__c>();
        List<Invoice__c> invoiceTypeFour = new List<Invoice__c>();
        if (acc.Distributor_or_Dealer__c == '2nd Tier Dealer') {
            String queryStr0 =
                queryStr +
                ' AND( Invoice_Type__c = \'CNA_External_CM\' OR (Invoice_Type__c = \'CA_External_CM\')) ORDER BY Invoice_Date__c DESC';
            invoiceTypeZero = (List<Invoice__c>) Database.query(queryStr0);
        } else {
            String queryStr1 =
                queryStr +
                ' AND (Invoice_Type__c = \'CNA_Invoice\' OR Invoice_Type__c = \'CA_Invoice\' ) AND isPaid__c = false ORDER BY Invoice_Date__c ASC';
            String queryStr2 =
                queryStr +
                ' AND (Invoice_Type__c = \'CNA_Invoice\' OR Invoice_Type__c = \'CA_Invoice\' ) AND isPaid__c = true ORDER BY Invoice_Date__c ASC';
            String queryStr3 =
                queryStr +
                ' AND( Invoice_Type__c = \'CNA_External_CM\' OR (Invoice_Type__c = \'CA_External_CM\')) ORDER BY Invoice_Date__c ASC';
            String queryStr4 =
                queryStr +
                ' AND Invoice_Type__c = \'CNA_Export_Invoice\' AND Invoice_Source__c = \'CNA_Auto_Invoice\' ORDER BY Invoice_Date__c ASC';
            // queryStr += ' AND( Invoice_Type__c = \'CNA_External_CM\' OR (Invoice_Type__c = \'CA_External_CM\') OR (Invoice_Type__c = \'CNA_Invoice\' AND Invoice_Source__c = \'CNA_Auto_Invoice\' ) OR (Invoice_Type__c = \'CA_Invoice\' AND Invoice_Source__c = \'CA_Auto_Invoice\' )) ORDER BY CreatedDate DESC';
            invoiceTypeOne = (List<Invoice__c>) Database.query(queryStr1);
            invoiceTypeTwo = (List<Invoice__c>) Database.query(queryStr2);
            invoiceTypeThree = (List<Invoice__c>) Database.query(queryStr3);
            invoiceTypeFour = (List<Invoice__c>) Database.query(queryStr4);
        }

        invoiceData.addAll(invoiceTypeZero);
        invoiceData.addAll(invoiceTypeOne);
        invoiceData.addAll(invoiceTypeTwo);
        invoiceData.addAll(invoiceTypeThree);
        invoiceData.addAll(invoiceTypeFour);
        Integer min = ((Integer) pageNumber - 1) * pageSize;
        Integer max = (Integer) pageNumber * pageSize - 1;
        if (invoiceData.size() > 0) {
            for (Integer i = min; i <= max && i < invoiceData.size(); i++) {
                Invoice__c invoiceInfo = invoiceData.get(i);
                DataTableWrapper dd = new DataTableWrapper();
                dd.id = invoiceInfo.Id;
                dd.currencyType = invoiceInfo.CurrencyIsoCode;
                dd.invoiceNumber = invoiceInfo.Invoice_Number__c;
                dd.invoiceType = invoiceInfo.Invoice_Type__c;
                dd.poNumber = invoiceInfo.PO_Number__c;
                // add haibo: french
                dd.invoiceStatus = invoiceInfo.isPaid__c ? Label.CCM_Portal_Closed : Label.CCM_Portal_Open;
                dd.invoiceDate = invoiceInfo.Invoice_Date__c == null ? '' : DateTime.newInstance(invoiceInfo.Invoice_Date__c.year(),invoiceInfo.Invoice_Date__c.month(),invoiceInfo.Invoice_Date__c.day()).format('MM-dd-yyyy');
                dd.orderDate = invoiceInfo.Order_Date__c == null ? '' : DateTime.newInstance(invoiceInfo.Order_Date__c.year(),invoiceInfo.Order_Date__c.month(),invoiceInfo.Order_Date__c.day()).format('MM-dd-yyyy');
                dd.trackNo = invoiceInfo.Tracking_NO__c;
                dd.paymentTerm = invoiceInfo.Terms__c;
                dd.paymentTermString = invoiceInfo.Payment_Terms_Record__c;
                dd.freightTerm = invoiceInfo.Freight_Term__c;
                dd.othersFee = invoiceInfo.Freight_and_other_charges__c;
                dd.originalAmt = invoiceInfo.Total_Amount__c;
                dd.dueRemainingAmt = invoiceInfo.Total_Remaining__c;

                dd.orderId = invoiceInfo.Order__c;
                // update by eric, ********
                // 避免出现链接没有 label, 直接暴露 url 的问题
                if (String.isNotBlank(dd.poNumber)) {
                    dd.orderURL = '/s/order-detail-page?recordId=' + dd.orderId;
                }

                // dd.originalAmtLabel = currencyFormat(dd.originalAmt);
                // dd.othersFeeLabel = currencyFormat(dd.dueRemainingAmt);
                // dd.dueRemainingAmtLabel = currencyFormat(dd.othersFee);

                if (invoiceInfo.Account_Balances_Invoice__r != null && invoiceInfo.Account_Balances_Invoice__r.size() > 0) {
                    // Accounting_Balance__c balance = null;
                    List<AccountingInfo> accountings = new List<AccountingInfo>();
                    Boolean isNotAllPaid=false;
                    Boolean isNotAllZero=false;
                    for (Accounting_Balance__c ac : invoiceInfo.Account_Balances_Invoice__r) {
                        AccountingInfo data = new AccountingInfo();
                        data.Id = ac.Id;
                        data.accountingOracleID = ac.Accounting_OracleID__c;
                        data.invoiceNumber = invoiceInfo.Invoice_Number__c;
                        data.dueDate = ac.Due_Date__c != null ? DateTime.newInstance(ac.Due_Date__c.year(),ac.Due_Date__c.month(),ac.Due_Date__c.day()).format('MM-dd-yyyy') : '';
                        data.paypalStatus = ac.Paypal_Status__c;
                        if (data.paypalStatus!='Processing'&&data.paypalStatus != 'Payment Success') {
                            isNotAllPaid=true;
                        }
                        data.invoiceAmount = ac.Amt_Original__c == null ? 0.00 : ac.Amt_Original__c;
                        data.dueRemainingAmount = ac.Amt_Due_Remaining__c == null ? 0.00 : ac.Amt_Due_Remaining__c;
                        if (data.dueRemainingAmount!=0.00) {
                            isNotAllZero=true;
                        }
                        data.paidAmount = data.invoiceAmount - data.dueRemainingAmount;
                        // add haibo: french
                        data.isOverdue = (ac.Due_Date__c < Date.today() && ac.Amt_Due_Remaining__c > 0) ? Label.CCM_Portal_YES : Label.CCM_Portal_NO;

                        Integer days = ac.Due_Date__c.daysBetween(Date.today());
                        data.overdueDays = String.valueOf(days);
                        if (ac.Amt_Due_Remaining__c <= 0) {
                            data.overdueDays = 'N/A';
                        }
                        // add haibo: french
                        if (data.isOverdue == Label.CCM_Portal_YES) {
                            data.displayStyle = 'red';
                        }
                        accountings.add(data);
                    }

                    if (!isNotAllPaid) {
                        dd.paypalStatus = accountings.get(0).paypalStatus;
                    }

                    if (!isNotAllZero) {
                        dd.paypalStatus = 'Payment Success' ;
                    }

                    if (accountings.size() == 1) {
                        dd.dueDate = accountings.get(0).dueDate;
                        dd.isOverdue = accountings.get(0).isOverdue;
                        dd.overdueDays = accountings.get(0).overdueDays;
                        dd.displayStyle = accountings.get(0).displayStyle;
                        dd.paypalStatus = accountings.get(0).paypalStatus;
                    }

                    dd.accountingList = accountings;
                }

                returnDD.currentData.add(dd);
            }
        }

        returnDD.isCCA = acc.ORG_Code__c == 'CCA';
        //2023.4.25 全部放开pay now按钮
        // 23.12.28 该变量现在用于控制ACH Debit/PAD Debit支付方式按钮
        Boolean isAccess = false;
        //2023.6.6 开放20discount
        Boolean isDiscountAccess=false;
        // 24.2.1 控制能否访问GC支付方式
        List<String> lstAllowGcPayment = Label.Controlling_Access_to_payment.split(';');
        if(lstAllowGcPayment.contains(acc.AccountNumber) || (lstAllowGcPayment.contains('CNA ALL') && returnDD.isCCA == false) || (lstAllowGcPayment.contains('CCA ALL') && returnDD.isCCA)){
            isDiscountAccess = true;
            isAccess = true;
        }
        returnDD.isAccess = isAccess;

        //Start : Added by Zoe for CA credit card & debit card access on 2024-08-12
        Boolean showBtnCCA = false;
        List<String> lstAllowCACard = Label.CCM_Portal_AccessPayment.split(';');
        if(lstAllowCACard.contains(acc.AccountNumber) || (lstAllowCACard.contains('CCA ALL') && returnDD.isCCA)){
            showBtnCCA = true;
        }
        returnDD.showBtnCCA = showBtnCCA;
        //End : Added by Zoe for CA credit card & debit card access on 2024-08-12

        Date discountEffectiveDate =Date.valueOf(Label.Date_of_discount);
        // 23.12.28 增加对Processing状态的首次支付判定
        Integer payCount = [
            SELECT
                COUNT()
            FROM
                Payment_Information__c
            WHERE
                Customer__c = :currentUser.Contact.AccountId
                AND (
                    (Paypal_Status__c = 'Payment Success' AND Payment_Date__c > :discountEffectiveDate)
                    OR (Paypal_Status__c = 'Processing' AND First_Pay_By_Paypal__c = true)
                )
        ];
        if (payCount == 0&&isDiscountAccess) {
            returnDD.isFirstPay = true;
        } else {
            returnDD.isFirstPay = false;
        }
        returnDD.totalRecordCount = invoiceData.size();
        return JSON.serialize(returnDD);
    }

    @AuraEnabled
    public static string getExportData(String filterString){
        ReturnData returnDD = new ReturnData();
        User currentUser = [SELECT Id, ContactId, Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId()];
        if (currentUser.ContactId == null || currentUser.Contact.AccountId == null) {
            return JSON.serialize(returnDD);
        }
        Account acc = [SELECT Id, OwnerId, Distributor_or_Dealer__c, ORG_Code__c,AccountNumber,Name FROM Account WHERE Id = :currentUser.Contact.AccountId];

        String queryStr =
        'SELECT Id' +
        ', CurrencyIsoCode' +
        ', Name' +
        ', Terms__c' +
        ', Payment_Terms_Record__c' +
        ', Freight_Term__c' +
        ', Invoice_Number__c' +
        ', Invoice_Status__c' +
        ', Invoice_Type__c' +
        ', Invoice_Date__c' +
        ', PO_Number__c' +
        ', Order_Date__c' +
        ', Order__c' +
        ', Total_Due__c' +
        ', Total_Amount__c' +
        ', Freight_and_other_charges__c' +
        ', Tracking_NO__c' +
        ', Total_Remaining__c' +
        ', isPaid__c' +
        ', CreatedDate' +
        ', (SELECT Id, Accounting_OracleID__c, Due_Date__c, Paypal_Status__c, Amt_Original__c, Amt_Due_Remaining__c, CreatedDate FROM Account_Balances_Invoice__r ORDER BY Due_Date__c ASC)' +
        ' FROM Invoice__c' +
        ' WHERE (Customer__c = \'' + currentUser.Contact.AccountId + '\' OR Order__r.Purchase_Order__r.Customer__c = \'' + currentUser.Contact.AccountId + '\')' +
        ' AND ID != NULL';

        if (String.isNotBlank(filterString)) {
            FilterWrapper filterWrapper = (FilterWrapper) JSON.deserialize(filterString, FilterWrapper.class);
            if (String.isNotBlank(filterWrapper.invoiceNumber)) {
                queryStr += ' AND Invoice_Number__c LIKE \'%' + filterWrapper.invoiceNumber + '%\'';
            }
            if (String.isNotBlank(filterWrapper.cutomerPoNum)) {
                queryStr += ' AND PO_Number__c LIKE \'%' + filterWrapper.cutomerPoNum + '%\'';
            }
            if (String.isNotBlank(filterWrapper.invoiceDateMin)) {
                queryStr += ' AND Invoice_Date__c >= ' + filterWrapper.invoiceDateMin;
            }
            if (String.isNotBlank(filterWrapper.invoiceDateMax)) {
                queryStr += ' AND Invoice_Date__c <= ' + filterWrapper.invoiceDateMax;
            }
            if (String.isNotBlank(filterWrapper.status)) {
                if (filterWrapper.status == 'Open') {
                    queryStr += ' AND isPaid__c = false';
                } else {
                    queryStr += ' AND isPaid__c = true';
                }
            }
        }

        Date lastYear = Date.newInstance((System.today().year() - 1), 1, 1);
        queryStr += ' AND Invoice_Date__c >= :lastYear';

        List<Invoice__c> invoiceData = new List<Invoice__c>();
        List<Invoice__c> invoiceTypeZero = new List<Invoice__c>();
        List<Invoice__c> invoiceTypeOne = new List<Invoice__c>();
        List<Invoice__c> invoiceTypeTwo = new List<Invoice__c>();
        List<Invoice__c> invoiceTypeThree = new List<Invoice__c>();
        List<Invoice__c> invoiceTypeFour = new List<Invoice__c>();
        if (acc.Distributor_or_Dealer__c == '2nd Tier Dealer') {
            String queryStr0 =
                queryStr +
                ' AND( Invoice_Type__c = \'CNA_External_CM\' OR (Invoice_Type__c = \'CA_External_CM\')) ORDER BY Invoice_Date__c DESC';
            invoiceTypeZero = (List<Invoice__c>) Database.query(queryStr0);
        } else {
            String queryStr1 =
                queryStr +
                ' AND (Invoice_Type__c = \'CNA_Invoice\' OR Invoice_Type__c = \'CA_Invoice\' ) AND isPaid__c = false ORDER BY Invoice_Date__c ASC';
            String queryStr2 =
                queryStr +
                ' AND (Invoice_Type__c = \'CNA_Invoice\' OR Invoice_Type__c = \'CA_Invoice\' ) AND isPaid__c = true ORDER BY Invoice_Date__c ASC';
            String queryStr3 =
                queryStr +
                ' AND( Invoice_Type__c = \'CNA_External_CM\' OR (Invoice_Type__c = \'CA_External_CM\')) ORDER BY Invoice_Date__c ASC';
            String queryStr4 =
                queryStr +
                ' AND Invoice_Type__c = \'CNA_Export_Invoice\' AND Invoice_Source__c = \'CNA_Auto_Invoice\' ORDER BY Invoice_Date__c ASC';
            invoiceTypeOne = (List<Invoice__c>) Database.query(queryStr1);
            invoiceTypeTwo = (List<Invoice__c>) Database.query(queryStr2);
            invoiceTypeThree = (List<Invoice__c>) Database.query(queryStr3);
            invoiceTypeFour = (List<Invoice__c>) Database.query(queryStr4);
        }

        invoiceData.addAll(invoiceTypeZero);
        invoiceData.addAll(invoiceTypeOne);
        invoiceData.addAll(invoiceTypeTwo);
        invoiceData.addAll(invoiceTypeThree);
        invoiceData.addAll(invoiceTypeFour);

        if (invoiceData.size() > 0) {
            for (Integer i = 0; i < invoiceData.size(); i++) {
                Invoice__c invoiceInfo = invoiceData.get(i);
                DataTableWrapper dd = new DataTableWrapper();
                dd.id = invoiceInfo.Id;
                dd.currencyType = invoiceInfo.CurrencyIsoCode;
                dd.invoiceNumber = invoiceInfo.Invoice_Number__c;
                dd.invoiceType = invoiceInfo.Invoice_Type__c;
                dd.poNumber = invoiceInfo.PO_Number__c;
                dd.invoiceStatus = invoiceInfo.isPaid__c ? 'Closed' : 'Open';
                dd.invoiceDate = invoiceInfo.Invoice_Date__c == null ? '' : invoiceInfo.Invoice_Date__c.format();
                dd.orderDate = invoiceInfo.Order_Date__c == null ? '' : invoiceInfo.Order_Date__c.format();
                dd.trackNo = invoiceInfo.Tracking_NO__c;
                dd.paymentTerm = invoiceInfo.Terms__c;
                dd.paymentTermString = invoiceInfo.Payment_Terms_Record__c;
                dd.freightTerm = invoiceInfo.Freight_Term__c;
                dd.othersFee = invoiceInfo.Freight_and_other_charges__c;
                dd.originalAmt = invoiceInfo.Total_Amount__c;
                dd.dueRemainingAmt = invoiceInfo.Total_Remaining__c;
                dd.orderId = invoiceInfo.Order__c;

                Boolean hasAccountingBalance = false;
                if (invoiceInfo.Account_Balances_Invoice__r != null && invoiceInfo.Account_Balances_Invoice__r.size() > 0) {
                    List<AccountingInfo> accountings = new List<AccountingInfo>();
                    Boolean isNotAllPaid=false;
                    Boolean isNotAllZero=false;
                    String paypalStatus = invoiceInfo.Account_Balances_Invoice__r.get(0).Paypal_Status__c;
                    for (Accounting_Balance__c ac : invoiceInfo.Account_Balances_Invoice__r) {
                        AccountingInfo data = new AccountingInfo();
                        data.Id = ac.Id;
                        data.accountingOracleID = ac.Accounting_OracleID__c;
                        data.invoiceNumber = invoiceInfo.Invoice_Number__c;
                        data.dueDate = String.valueOf(ac.Due_Date__c);
                        data.paypalStatus = ac.Paypal_Status__c;
                        if (data.paypalStatus!='Processing'&&data.paypalStatus != 'Payment Success') {
                            isNotAllPaid=true;
                        }
                        data.invoiceAmount = ac.Amt_Original__c == null ? 0.00 : ac.Amt_Original__c;
                        data.dueRemainingAmount = ac.Amt_Due_Remaining__c == null ? 0.00 : ac.Amt_Due_Remaining__c;
                        if (data.dueRemainingAmount!=0.00) {
                            isNotAllZero=true;
                        }
                        data.paidAmount = data.invoiceAmount - data.dueRemainingAmount;
                        data.isOverdue = (ac.Due_Date__c < Date.today() && ac.Amt_Due_Remaining__c > 0) ? 'YES' : 'NO';

                        Integer days = ac.Due_Date__c.daysBetween(Date.today());
                        data.overdueDays = String.valueOf(days);
                        if (ac.Amt_Due_Remaining__c <= 0) {
                            data.overdueDays = 'N/A';
                        }
                        if (data.isOverdue == 'YES') {
                            data.displayStyle = 'red';
                        }
                        hasAccountingBalance = true;
                        accountings.add(data);
                    }

                    if (!isNotAllPaid) {
                        dd.paypalStatus = paypalStatus;
                    }

                    if (!isNotAllZero) {
                        dd.paypalStatus = 'Payment Success';
                    }

                    if (accountings.size() == 1 && invoiceInfo.Account_Balances_Invoice__r.size() == 1) {
                        dd.dueDate = accountings.get(0).dueDate;
                        dd.isOverdue = accountings.get(0).isOverdue;
                        dd.overdueDays = accountings.get(0).overdueDays;
                        dd.displayStyle = accountings.get(0).displayStyle;
                        dd.paypalStatus = accountings.get(0).paypalStatus;
                    }
                    else if(accountings.size() > 1 || invoiceInfo.Account_Balances_Invoice__r.size() > 1){
                        dd.accountingList = accountings;
                    }
                }
                if(hasAccountingBalance) {
                    returnDD.currentData.add(dd);
                }
            }
        }
        List<CCM_InvoiceExportDataWrapper.InvoiceDataWrapper> exportDatas = CCM_InvoiceExportDataWrapper.convertToInvoiceExportDataFormat(returnDD);
        return JSON.serialize(exportDatas);
    }

    @AuraEnabled
    public static String getDiscount(String selectInvoiceList) {
        ReturnData returnData = new ReturnData();
        try {
            List<DataTableWrapper> paidInvoiceList = (List<DataTableWrapper>) JSON.deserialize(selectInvoiceList, List<DataTableWrapper>.class);
            User currentUser = [SELECT Id, ContactId, Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId()];
            if (currentUser.ContactId == null || currentUser.Contact.AccountId == null) {
                return JSON.serialize(paidInvoiceList);
            }
            List<DataTableWrapper> hasDiscountInvoiceList = new List<DataTableWrapper>();
            for (DataTableWrapper paidInvoice : paidInvoiceList) {
                String[] terms = paidInvoice.paymentTermString.split(';');
                if (paidInvoice.hasNoSequence) {
                    for (String term : terms) {
                        String[] singleTerm = term.split(',');
                        if (Date.today() <= Date.valueOf(singleTerm[1])) {
                            paidInvoice.discountFee = 0 - Decimal.valueOf(singleTerm[3]);
                            paidInvoice.totalPaymentFee = (paidInvoice.dueRemainingAmt + paidInvoice.discountFee).setScale(2);
                            break;
                        } else {
                            paidInvoice.discountFee = 0;
                            paidInvoice.totalPaymentFee = (paidInvoice.dueRemainingAmt + paidInvoice.discountFee).setScale(2);
                        }
                    }
                } else {
                    for (String term : terms) {
                        String[] singleTerm = term.split(',');
                        for (AccountingInfo accountingInvoice : paidInvoice.accountingList) {
                            if (accountingInvoice.accountingOracleID == singleTerm[0]) {
                                if (Date.today() <= Date.valueOf(singleTerm[1])) {
                                    accountingInvoice.discountFee = 0 - Decimal.valueOf(singleTerm[3]);
                                    accountingInvoice.totalPaymentFee = (accountingInvoice.dueRemainingAmount + accountingInvoice.discountFee).setScale(2);
                                }else {
                                    accountingInvoice.discountFee = 0;
                                    accountingInvoice.totalPaymentFee = (accountingInvoice.dueRemainingAmount + accountingInvoice.discountFee).setScale(2);
                                }
                            }
                        }
                    }
                }
                /* Payment_Term__mdt[] term = [SELECT Name__c, Payment_Discount__c, Payment_Leadtime__c FROM Payment_Term__mdt WHERE Description__c = :paidInvoice.paymentTerm
                ];
                String[] str = paidInvoice.invoiceDate.split('/');
                Date invoiceDate = Date.newinstance(Integer.valueOf(str[2]), Integer.valueOf(str[0]), Integer.valueOf(str[1]));
                if (invoiceDate + Integer.valueOf(term[0].Payment_Leadtime__c == null ? 0 : term[0].Payment_Leadtime__c) > Date.today()) {
                    if (paidInvoice.hasNoSequence) {
                        paidInvoice.discountFee = (0 - paidInvoice.originalAmt * term[0].Payment_Discount__c * 0.01).setScale(2);
                        paidInvoice.totalPaymentFee = paidInvoice.originalAmt + paidInvoice.discountFee;
                    } else {
                        for (AccountingInfo accountingInvoice : paidInvoice.accountingList) {
                            accountingInvoice.discountFee = (0 - accountingInvoice.invoiceAmount * term[0].Payment_Discount__c * 0.01).setScale(2);
                            accountingInvoice.totalPaymentFee = accountingInvoice.invoiceAmount.setScale(2) + accountingInvoice.discountFee;
                        }
                    }
                } else {
                    if (paidInvoice.hasNoSequence) {
                        paidInvoice.discountFee = 0;
                        paidInvoice.totalPaymentFee = paidInvoice.originalAmt.setScale(2) + paidInvoice.discountFee;
                    } else {
                        for (AccountingInfo accountingInvoice : paidInvoice.accountingList) {
                            accountingInvoice.discountFee = 0;
                            accountingInvoice.totalPaymentFee = accountingInvoice.invoiceAmount.setScale(2) + accountingInvoice.discountFee;
                        }
                    }
                }*/
                hasDiscountInvoiceList.add(paidInvoice);
            }
            Decimal sumDiscountFee = 0.00;
            Decimal sumTotalPaymentFee = 0.00;
            for (DataTableWrapper hasDiscountInvoice : hasDiscountInvoiceList) {
                if (hasDiscountInvoice.hasNoSequence) {
                    if(hasDiscountInvoice.discountFee != null){
                        sumDiscountFee += hasDiscountInvoice.discountFee;
                    }
                    if(hasDiscountInvoice.dueRemainingAmt != null){
                        sumTotalPaymentFee += hasDiscountInvoice.dueRemainingAmt;
                    }
                }
                if (!hasDiscountInvoice.hasNoSequence) {
                    for (AccountingInfo accountingInvoice : hasDiscountInvoice.accountingList) {
                        if(accountingInvoice.discountFee != null){
                            sumDiscountFee += accountingInvoice.discountFee;
                        }
                        if(accountingInvoice.dueRemainingAmount != null){
                            sumTotalPaymentFee += accountingInvoice.dueRemainingAmount;
                        }
                    }
                }
            }
            returnData.currentData = hasDiscountInvoiceList;
            returnData.sumDiscountFee = sumDiscountFee;
            returnData.sumTotalPaymentFee = sumTotalPaymentFee;
        } catch (Exception e) {
            System.debug(
                'CCM_Community_InvoiceFilterPageCtl.getDiscount>>Err:' +
                e.getMessage() +
                e.getLineNumber()
            );
            returnData.errorMsg = 'Please confirm that the payment term is correct!';
        }

        return JSON.serialize(returnData);
    }

    @AuraEnabled
    public static String getCredit() {
        List<Invoice__c> creditList = new List<Invoice__c>();
        User currentUser = [SELECT Id, ContactId, Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId()];
        if (currentUser.ContactId == null || currentUser.Contact.AccountId == null) {
            return JSON.serialize(creditList);
        }
        creditList = [
            SELECT
                Id,
                CurrencyIsoCode,
                Name,
                Terms__c,
                Freight_Term__c,
                Invoice_Number__c,
                Invoice_Status__c,
                Invoice_Type__c,
                Invoice_Date__c,
                PO_Number__c,
                Order_Date__c,
                Order__c,
                Total_Due__c,
                Total_Amount__c,
                Freight_and_other_charges__c,
                Tracking_NO__c,
                Total_Remaining__c,
                isPaid__c,
                CreatedDate,
                (
                    SELECT
                        Id,
                        Accounting_OracleID__c,
                        Due_Date__c,
                        Paypal_Status__c,
                        Amt_Original__c,
                        Amt_Due_Remaining__c,
                        CreatedDate
                    FROM Account_Balances_Invoice__r
                    ORDER BY Due_Date__c ASC
                )
            FROM Invoice__c
            WHERE Customer__c = :currentUser.Contact.AccountId
                AND ID != NULL
                AND isPaid__c = FALSE
                AND (
                    (Invoice_Source__c = 'CNA_Manual_Invoice' AND Invoice_Type__c = 'CNA_External_CM')
                    OR (Invoice_Source__c = 'CNA_Auto_Invoice' AND Invoice_Type__c = 'CNA_CreditMemo')
                    OR (Invoice_Source__c = 'CA_Manual_Invoice' AND Invoice_Type__c = 'CA_External_CM')
                    OR (Invoice_Type__c = 'CA_CreditMemo' AND Total_Remaining__c != 0 AND Invoice_Date__c > 2021-01-01)
                    OR (Invoice_Source__c = 'CA_Auto_Invoice' AND Invoice_Type__c = 'CA_CreditMemo')
                    )
            ORDER BY Invoice_Date__c ASC
        ];
        List<Invoice__c> creditAvailableList = new List<Invoice__c>();
        for (Integer i = 0; i < creditList.size(); i++) {
            List<Accounting_Balance__c> accounting=creditList[i].Account_Balances_Invoice__r;
            for (Accounting_Balance__c var : accounting) {
                if (var.Paypal_Status__c != 'Payment Success'&&var.Paypal_Status__c != 'Processing') {
                    creditAvailableList.add(creditList[i]);
                }
            }
        }
        return JSON.serialize(creditAvailableList);
    }

    @AuraEnabled
    public static String getPicklist() {
        // List<Schema.PicklistEntry> picklist=Schema.getGlobalDescribe().get('Prospect_Assignment_Rule__c').getDescribe().fields.getMap().get('State__c').getDescribe().getPicklistValues();
        // add haibo: french
        List<State__mdt> stateOfCNA=[Select s.Country__c, s.Name__c, s.States_Name_French__c, s.State_Code__c from State__mdt s where s.Country__c='CNA' order by s.Name__c ];
        List<State__mdt> stateOfUSA=[Select s.Country__c, s.Name__c, s.State_Code__c from State__mdt s where s.Country__c='USA' order by s.Name__c ];
        List<Country__mdt> country=[Select c.Name__c,c.Alpha3__c from Country__mdt c order by c.Name__c ];
        PicklistReturnData re=new PicklistReturnData();
        re.stateOfCNA=stateOfCNA;
        re.stateOfUSA=stateOfUSA;
        re.country=country;
        return JSON.serialize(re);
    }

    @future
    public static void sendRemarkEmail(String name, String remark, String customer, String orgCode) {
        try {
            List<OrgWideEmailAddress> lstEmailAddress=[select Id from OrgWideEmailAddress WHERE DisplayName= 'SFDC Notification'];
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            // Set Organization-Wide Email Address Id
            email.setOrgWideEmailAddressId(lstEmailAddress[0].Id);
            List<String> sendTo = new List<String>();
            if(orgCode == CCM_Constants.ORG_CODE_CCA) {
                sendTo.add('<EMAIL>');
            }
            else {
                List<String> emailForAR = Label.Email_AR.split(';');
                sendTo.addAll(emailForAR);
            }
            email.setSubject(Label.Payment_Remark_Title);
            email.setHtmlBody('Dear AR,<br> Payment ' + name + ' has been submitted by customer ' + customer + ' with remarks: ' + remark + '. ' );
            email.setToAddresses(sendTo);
            Messaging.SendEmailResult[] r = Messaging.sendEmail(
                new List<Messaging.SingleEmailMessage>{ email }
            );
        } catch (Exception e) {
            System.debug(
                'CCM_Community_InvoiceFilterPageCtl.sendRemarkEmail>>Err:' +
                e.getMessage() +
                e.getLineNumber()
            );
        }
    }

    public static Messaging.SingleEmailMessage sendPayResultEmail(Boolean isSuccess, String customerEmail, Decimal actualTotalPay, String paymentName, String faileReason, String invoiceNumbers) {
        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
        try {
            List<OrgWideEmailAddress> lstEmailAddress=[select Id from OrgWideEmailAddress WHERE DisplayName= 'SFDC Notification'];
            // Set Organization-Wide Email Address Id
            email.setOrgWideEmailAddressId(lstEmailAddress[0].Id);
            List<String> sendTo = new List<String>();
            List<String> sendBccTo = new List<String>();
            sendTo.add(customerEmail);
            List<String> emailForAR=Label.Email_AR.split(';');
            sendBccTo.addAll(emailForAR);
            if (isSuccess) {
                // 'Payment Successed'
                email.setSubject('Chervon North America – USD '+ actualTotalPay.setScale(2) +' – Payment '+paymentName+' Has Been Processed Successfully');
                String line1 = 'Dear Customer,<br>';
                String line2 = '    Thank you for your payment. We are pleased to inform you that your payment Payment ID = '+paymentName+' has been processed successfully by payment gateway.Invoice number(s): '+ invoiceNumbers +'.<br>';
                String line3 = '    You can click this link to check the transaction history: https://chervon.my.site.com/s/myaccount'+'<br>';
                String line4 = '    Click this link to check the invoice status: https://chervon.my.site.com/s/invoice'+'<br>';
                String line5 = '    It will take about 1 day to update the status of your invoice. Please feel free to contact <NAME_EMAIL> with Payment ID if the invoice status is still "Open" after 24 hours.<br>';
                String body = line1 + line2 + line3 + line4 + line5;
                String line6 = generateCreditMemoInfoInEmail(paymentName);
                body = body + '<br><br>';
                body = body + line6;
                email.setHtmlBody(body);
            } else {
                // 'Payment Failed'
                email.setSubject('Chervon North America – USD '+ actualTotalPay.setScale(2) +' – Payment '+paymentName+' Failed To Be Processed');
                String line1 = 'Dear Customer,<br>';
                String line2 = '    Thank you for your payment. We are sorry to inform you  that your payment Payment ID = '+paymentName+' failed to be processed by payment gateway. Invoice number(s): '+ invoiceNumbers +'. The reason is <b><font color=red>'+faileReason+'</font></b>. Please retry the payment.    If there\'s still a problem, please contact <NAME_EMAIL> with Payment ID.';
                String body = line1 + line2 ;
                email.setHtmlBody(body);
            }
            email.setToAddresses(sendTo);
            email.setBccAddresses(sendBccTo);
        } catch (Exception e) {
            System.debug(
                'CCM_Community_InvoiceFilterPageCtl.sendPayResultEmail>>Err:' +
                e.getMessage() +
                e.getLineNumber()
            );
        }
        return email;
    }

    public static Messaging.SingleEmailMessage sendMonerisResultEmail(Boolean isSuccess, String customerEmail, Decimal actualTotalPay, String paymentName, String faileReason, String invoiceNumbers) {
        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
        try {
            List<OrgWideEmailAddress> lstEmailAddress=[select Id from OrgWideEmailAddress WHERE DisplayName= 'SFDC Notification'];
            // Set Organization-Wide Email Address Id
            email.setOrgWideEmailAddressId(lstEmailAddress[0].Id);
            List<String> sendTo = new List<String>();
            List<String> sendBccTo = new List<String>();
            sendTo.add(customerEmail);
            // List<String> emailForAR=Label.Email_AR.split(';');
            sendTo.add('<EMAIL>');
            // sendBccTo.addAll(emailForAR);
            if (isSuccess) {
                // 'Payment Successed'
                email.setSubject('Chervon North America – CAD '+ actualTotalPay.setScale(2) +' – Payment '+paymentName+' Has Been Processed Successfully');
                String line1 = 'Dear Customer,<br>';
                String line2 = '    Thank you for your payment. We are pleased to inform you that your payment Payment ID = '+paymentName+' has been processed successfully by payment gateway.Invoice number(s): '+ invoiceNumbers +'.<br>';
                String line3 = '    You can click this link to check the transaction history: https://chervon.my.site.com/s/myaccount'+'<br>';
                String line4 = '    Click this link to check the invoice status: https://chervon.my.site.com/s/invoice'+'<br>';
                String line5 = '    It will take about 1 day to update the status of your invoice. Please feel free to contact <NAME_EMAIL> with Payment ID if the invoice status is still "Open" after 24 hours.<br>';
                String body = line1 + line2 + line3 + line4 + line5;
                String line6 = generateCreditMemoInfoInEmail(paymentName);
                body = body + '<br><br>';
                body = body + line6;
                email.setHtmlBody(body);
            } else {
                // 'Payment Failed'
                email.setSubject('Chervon North America – CAD '+ actualTotalPay.setScale(2) +' – Payment '+paymentName+' Failed To Be Processed');
                String line1 = 'Dear Customer,<br>';
                String line2 = '    Thank you for your payment. We are sorry to inform you  that your payment Payment ID = '+paymentName+' failed to be processed by payment gateway. Invoice number(s): '+ invoiceNumbers +'. The reason is <b><font color=red>'+faileReason+'</font></b>. Please retry the payment.    If there\'s still a problem, please contact <NAME_EMAIL> with Payment ID.';
                String body = line1 + line2 ;
                email.setHtmlBody(body);
            }
            email.setToAddresses(sendTo);
            email.setBccAddresses(sendBccTo);
        } catch (Exception e) {
            System.debug(
                'CCM_Community_InvoiceFilterPageCtl.sendPayResultEmail>>Err:' +
                e.getMessage() +
                e.getLineNumber()
            );
        }
        return email;
    }
    // @AuraEnabled
    // public static String savePaidInvoice(String invoicesList, String creditList, String selectMethod, Decimal sumDiscountFee, Decimal sumCreditFee, Decimal actualTotalPay, String remark, Boolean isFirstPay, String paymentOptions) {

    //     paymentOptions paypalInfo = (paymentOptions) JSON.deserialize(paymentOptions, paymentOptions.class);

    //     List<DataTableWrapper> paidInvoiceList = (List<DataTableWrapper>) JSON.deserialize(invoicesList, List<DataTableWrapper>.class);
    //     List<DataTableWrapper> paidCreditList = new List<DataTableWrapper>();

    //     if (String.isNotBlank(creditList) && creditList != '[""]') {
    //         paidCreditList = (List<DataTableWrapper>) JSON.deserialize(creditList, List<DataTableWrapper>.class);
    //     }

    //     User currentUser = [SELECT Id, ContactId, Contact.AccountId, Contact.Account.CurrencyIsoCode, Contact.Account.ORG_Code__c,Contact.Account.name,Contact.Account.AccountNumber FROM User WHERE Id = :UserInfo.getUserId()];
    //     //二次验证
    //     Date discountEffectiveDate =Date.valueOf(Label.Date_of_discount);
    //     Integer payCount = [SELECT COUNT() FROM Payment_Information__c WHERE Customer__c = :currentUser.Contact.AccountId AND Paypal_Status__c In ('Payment Success','Processing') AND Payment_Date__c>:discountEffectiveDate];

    //     if (isFirstPay && payCount != 0) {
    //         return JSON.serialize('error');
    //     }
    //     for (DataTableWrapper tar : paidInvoiceList) {
    //         if (tar.invoiceStatus == 'closed') {
    //             return JSON.serialize('error');
    //         }
    //         for (AccountingInfo var2 : tar.accountingList) {
    //             if (var2.paypalStatus == 'Payment Success'||var2.paypalStatus == 'Processing') {
    //                 return JSON.serialize('error');
    //             }
    //         }
    //     }
    //     //调paypal接口支付
    //     paypalResponse result = usePaypal(selectMethod, actualTotalPay, paypalInfo);
    //     //存入paymentInfo
    //     Payment_Information__c paymentInfo = new Payment_Information__c();
    //     paymentInfo.Cash_Discount_Gross_Amount__c = sumDiscountFee;
    //     paymentInfo.Total_Credit_Memo_Amount__c = sumCreditFee;
    //     paymentInfo.Actual_Paid_Amount__c = actualTotalPay;
    //     // paymentInfo.CreatedById = currentUser.Id;
    //     // paymentInfo.CreatedDate = Date.today();
    //     paymentInfo.CurrencyIsoCode = currentUser.Contact.Account.CurrencyIsoCode == null ? '' : currentUser.Contact.Account.CurrencyIsoCode;
    //     paymentInfo.Customer__c = currentUser.Contact.AccountId;
    //     paymentInfo.ORG_Code__c = currentUser.Contact.Account.ORG_Code__c;
    //     paymentInfo.PNREF__c = result.PNREF;
    //     Address_With_Program__c[] billingAddress = [
    //         SELECT
    //             a.Id,
    //             a.Account_Address__r.customer__c,
    //             a.Customer_Line_Oracle_ID__c
    //         FROM Address_With_Program__c a
    //         WHERE
    //             a.Account_Address__r.customer__c = :currentUser.Contact.AccountId
    //             AND a.Account_Address__r.RecordType_name__c = 'Billing_Address'
    //             AND a.Customer_Line_Oracle_ID__c != NULL
    //     ];
    //     if (billingAddress.size() != 0) {
    //         paymentInfo.BillTo__c = billingAddress[0].Id;
    //     }
    //     paymentInfo.Pay_Method__c = selectMethod;
    //     if (selectMethod == 'Debit Card' || selectMethod == 'Credit Card') {
    //         paymentInfo.Total_Amount__c = actualTotalPay-sumCreditFee;
    //     } else {
    //         paymentInfo.Total_Amount__c = actualTotalPay-sumCreditFee-sumDiscountFee;
    //     }
    //     paymentInfo.Remark__c = remark;
    //     paymentInfo.First_Pay_By_Paypal__c = isFirstPay;
    //     if (String.isNotBlank(paypalInfo.cardNumber)) {
    //         paymentInfo.Card_Last_Four__c = paypalInfo.cardNumber.substring(paypalInfo.cardNumber.length() - 4, paypalInfo.cardNumber.length());
    //     }
    //     insert paymentInfo;
    //     //添加了备注时给AR发邮件提醒
    //     String name=[select Name from Payment_Information__c where id=:paymentInfo.id limit 1].name;
    //     if (String.isNotBlank(remark) && String.isNotEmpty(remark)){
    //         sendRemarkEmail(name,remark,currentUser.Contact.Account.name);
    //     }
    //     //用于邮件拼接
    //     List<String> invoiceNumbers=new List<String>();
    //     //paymentInfo_Item(invoice)
    //     List<Payment_Information_Item__c> paymentItems=new List<Payment_Information_Item__c>();
    //     List<Payment_Information_Item__c> paymentItems2=new List<Payment_Information_Item__c>();
    //     Map<String, List<AccountingInfo>> accountingItemWithInvoiceNumber = new Map<String, List<AccountingInfo>>();
    //     Map<String, List<AccountingInfo>> accountingItemWithId = new Map<String, List<AccountingInfo>>();
    //     Set<String> ids=new Set<String>();
    //     List<Payment_Information_Item__c> paymentItemAccountings=new List<Payment_Information_Item__c>();
    //     for (DataTableWrapper invoice : paidInvoiceList) {
    //         invoiceNumbers.add(invoice.invoiceNumber);
    //         if (invoice.hasNoSequence) {
    //             Payment_Information_Item__c paymentItem = new Payment_Information_Item__c();
    //             paymentItem.Actual_Paid_Amount__c = invoice.totalPaymentFee;
    //             paymentItem.Cash_Discount_Amount__c = invoice.discountFee;
    //             // paymentItem.CreatedDate = Date.today();
    //             paymentItem.CurrencyIsoCode = invoice.currencyType;
    //             paymentItem.Invoice__c = invoice.id;
    //             paymentItem.Payment_Information__c = paymentInfo.id;
    //             paymentItem.Invoice_Type__c = invoice.invoiceType;
    //             paymentItem.Total_Amount__c = invoice.dueRemainingAmt;
    //             paymentItem.Has_No_Sequence__c = invoice.hasNoSequence;
    //             paymentItems.add(paymentItem);
    //         } else {
    //             Payment_Information_Item__c paymentItem2 = new Payment_Information_Item__c();
    //             // paymentItem2.CreatedDate = Date.today();
    //             paymentItem2.CurrencyIsoCode = invoice.currencyType;
    //             paymentItem2.Invoice__c = invoice.id;
    //             paymentItem2.Payment_Information__c = paymentInfo.id;
    //             paymentItem2.Invoice_Type__c = invoice.invoiceType;
    //             paymentItem2.Has_No_Sequence__c = invoice.hasNoSequence;
    //             paymentItems2.add(paymentItem2);
    //             if (!accountingItemWithInvoiceNumber.containsKey(invoice.invoiceNumber)) {
    //                 accountingItemWithInvoiceNumber.put(invoice.id, invoice.accountingList);
    //             }
    //         }
    //     }
    //     insert paymentItems;
    //     insert paymentItems2;
    //     for (Payment_Information_Item__c item : paymentItems2) {
    //         ids.add(item.Id);
    //         accountingItemWithId.put(item.Id, accountingItemWithInvoiceNumber.get(item.Invoice__c));
    //     }
    //     List<Payment_Information_Item__c> queryPaymentItemAccountingList =[
    //                         SELECT
    //                             Id,
    //                             Invoice__r.Invoice_Number__c
    //                         FROM Payment_Information_Item__c
    //                         WHERE
    //                             Id In :ids];
    //     for (Payment_Information_Item__c invoice : queryPaymentItemAccountingList) {
    //         if (accountingItemWithId.containsKey(invoice.Id)) {
    //             for (AccountingInfo accountingBalance : accountingItemWithId.get(invoice.Id)) {
    //                 Payment_Information_Item__c paymentItemAccounting = new Payment_Information_Item__c();
    //                 paymentItemAccounting.Accounting_Balance__c = accountingBalance.Id;
    //                 paymentItemAccounting.Payment_Information_Item_accounting__c = invoice.Id;
    //                 paymentItemAccounting.Actual_Paid_Amount__c = (accountingBalance.dueRemainingAmount + accountingBalance.discountFee).setScale(2);
    //                 paymentItemAccounting.Cash_Discount_Amount__c = accountingBalance.discountFee;
    //                 paymentItemAccounting.Total_Amount__c = accountingBalance.dueRemainingAmount;
    //                 paymentItemAccountings.add(paymentItemAccounting);
    //             }
    //         }
    //     }
    //     insert paymentItemAccountings;


    //     //paymentInfo_Item(credit)
    //     List<Payment_Information_Item__c> paymentItems3=new List<Payment_Information_Item__c>();
    //     if (paidCreditList.size() > 0) {
    //         for (DataTableWrapper credit : paidCreditList) {
    //             Payment_Information_Item__c paymentItem3 = new Payment_Information_Item__c();
    //             paymentItem3.CurrencyIsoCode = credit.currencyType;
    //             paymentItem3.Invoice__c = credit.id;
    //             paymentItem3.Payment_Information__c = paymentInfo.id;
    //             paymentItem3.Total_Amount__c = credit.dueRemainingAmt;
    //             paymentItem3.Actual_Paid_Amount__c = credit.dueRemainingAmt;
    //             paymentItem3.Invoice_Type__c = credit.invoiceType;
    //             paymentItems3.add(paymentItem3);
    //         }
    //     }
    //     insert paymentItems3;

    //     //根据支付结果更新paymentInfo，invoice，accounting_balance
    //     Payment_Information__c payment = [
    //         SELECT Id,Payment_Date__c
    //         FROM Payment_Information__c
    //         WHERE id = :paymentInfo.Id
    //     ];
    //     // if (selectMethod == 'ACH') {
    //     //     result.resultCode = '0';
    //     // }
    //     // if (selectMethod == 'Telecheck') {
    //     //     result.resultCode = '0';
    //     // }
    //     if (Test.isRunningTest()) {
    //         CCM_Service.pushPaymentInfo(paymentInfo.Id);
    //     }
    //         payment.Payment_Date__c = Date.today();
    //         if (result.resultCode == '0') {
    //             payment.Paypal_Status__c = 'Payment Success';
    //             //传到oracle核销
    //             CCM_Service.pushPaymentInfo(paymentInfo.Id);
    //         } else {
    //             payment.Paypal_Status__c = 'Payment Failed';
    //         }
    //     update payment;
    //     List<Accounting_Balance__c> invoices =new List<Accounting_Balance__c>();
    //     List<Accounting_Balance__c> accountings =new List<Accounting_Balance__c>();
    //     for (DataTableWrapper paidInvoice : paidInvoiceList) {
    //         if (paidInvoice.hasNoSequence) {
    //             List<Accounting_Balance__c> invoice = [
    //                 SELECT a.Id, a.Invoice__c, a.Paypal_Status__c
    //                 FROM Accounting_Balance__c a
    //                 WHERE a.Invoice__c = :paidInvoice.id
    //             ];
    //             for (Accounting_Balance__c var : invoice) {
    //                     if (result.resultCode == '0') {
    //                         var.Paypal_Status__c = 'Payment Success';
    //                     } else {
    //                         var.Paypal_Status__c = 'Payment Failed';
    //                     }
    //             }
    //             invoices.addAll(invoice);
    //         } else {
    //             for (AccountingInfo paidAccounting : paidInvoice.accountingList) {
    //                 List<Accounting_Balance__c> accounting = [
    //                     SELECT Paypal_Status__c
    //                     FROM Accounting_Balance__c
    //                     WHERE ID = :paidAccounting.id
    //                     ];
    //                 for (Accounting_Balance__c var2 : accounting) {
    //                         if (result.resultCode == '0') {
    //                             var2.Paypal_Status__c = 'Payment Success';
    //                         } else {
    //                             var2.Paypal_Status__c = 'Payment Failed';
    //                         }
    //                 }
    //                 accountings.addAll(accounting);
    //             }
    //         }
    //     }
    //     update invoices;
    //     update accountings;
    //     List<Accounting_Balance__c> credits=new List<Accounting_Balance__c>();
    //     if (paidCreditList.size() > 0) {
    //         for (DataTableWrapper paidCredit : paidCreditList) {
    //             Accounting_Balance__c credit = [
    //                 SELECT a.Id, a.Invoice__c, a.Paypal_Status__c
    //                 FROM Accounting_Balance__c a
    //                 WHERE a.Invoice__c = :paidCredit.id
    //             ];
    //                 if (result.resultCode == '0') {
    //                     credit.Paypal_Status__c = 'Payment Success';
    //                 } else {
    //                     credit.Paypal_Status__c = 'Payment Failed';
    //                 }
    //             credits.add(credit);
    //         }
    //         update credits;
    //     }
    //     // String url = URL.getOrgDomainUrl().toExternalForm()+'/';
    //     String numbers=String.join(invoiceNumbers,',');
    //     Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
    //     if (selectMethod == 'Debit Card' || selectMethod == 'Credit Card') {
    //         if (result.resultCode == '0') {
    //             email=sendPayResultEmail(true,paypalInfo.email,actualTotalPay,name,result.respMsg,numbers);
    //         } else {
    //             email=sendPayResultEmail(false,paypalInfo.email,actualTotalPay,name,result.respMsg,numbers);
    //         }
    //         if(email!=null) {
    //             Messaging.SendEmailResult[] r = Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{email});
    //         }
    //     }
    //     return JSON.serialize(result);
    // }
    @AuraEnabled
    public static string savePaidInvoice(String invoicesList, String creditList, String selectMethod, Decimal sumDiscountFee, Decimal sumCreditFee, Decimal actualTotalPay, String remark, Boolean isFirstPay, String paymentOptions){
        List<Map<String, Object>> itemList = new List<Map<String, Object>>();
        paymentOptions paypalInfo = (paymentOptions) JSON.deserialize(paymentOptions, paymentOptions.class);

        List<DataTableWrapper> paidInvoiceList = (List<DataTableWrapper>) JSON.deserialize(invoicesList, List<DataTableWrapper>.class);
        List<DataTableWrapper> paidCreditList = new List<DataTableWrapper>();

        if (String.isNotBlank(creditList) && creditList != '[""]') {
            paidCreditList = (List<DataTableWrapper>) JSON.deserialize(creditList, List<DataTableWrapper>.class);
        }

        User currentUser = [SELECT Id, ContactId, Contact.AccountId, Contact.Account.CurrencyIsoCode, Contact.Account.ORG_Code__c,Contact.Account.name,Contact.Account.AccountNumber FROM User WHERE Id = :UserInfo.getUserId()];
        //二次验证
        Date discountEffectiveDate =Date.valueOf(Label.Date_of_discount);
        Integer payCount = [SELECT COUNT() FROM Payment_Information__c WHERE Customer__c = :currentUser.Contact.AccountId AND Paypal_Status__c In ('Payment Success','Processing') AND Payment_Date__c>:discountEffectiveDate];

        if (isFirstPay && payCount != 0) {
            return JSON.serialize('error');
        }
        for (DataTableWrapper tar : paidInvoiceList) {
            if (tar.invoiceStatus == 'closed') {
                return JSON.serialize('error');
            }
            for (AccountingInfo var2 : tar.accountingList) {
                if (var2.paypalStatus == 'Payment Success'||var2.paypalStatus == 'Processing') {
                    return JSON.serialize('error');
                }
            }
        }
        //存入paymentInfo
        Payment_Information__c paymentInfo = new Payment_Information__c();
        paymentInfo.Cash_Discount_Gross_Amount__c = sumDiscountFee;
        paymentInfo.Total_Credit_Memo_Amount__c = sumCreditFee;
        paymentInfo.Actual_Paid_Amount__c = actualTotalPay;
        // paymentInfo.CreatedById = currentUser.Id;
        // paymentInfo.CreatedDate = Date.today();
        paymentInfo.CurrencyIsoCode = currentUser.Contact.Account.CurrencyIsoCode == null ? '' : currentUser.Contact.Account.CurrencyIsoCode;
        paymentInfo.Customer__c = currentUser.Contact.AccountId;
        paymentInfo.ORG_Code__c = currentUser.Contact.Account.ORG_Code__c;

        Address_With_Program__c[] billingAddress = [
            SELECT
                a.Id,
                a.Account_Address__r.customer__c,
                a.Customer_Line_Oracle_ID__c
            FROM Address_With_Program__c a
            WHERE
                a.Account_Address__r.customer__c = :currentUser.Contact.AccountId
                AND a.Account_Address__r.RecordType_name__c = 'Billing_Address'
                AND a.Customer_Line_Oracle_ID__c != NULL
        ];
        if (billingAddress.size() != 0) {
            paymentInfo.BillTo__c = billingAddress[0].Id;
        }
        paymentInfo.Pay_Method__c = selectMethod;
        if (selectMethod == 'Debit Card' || selectMethod == 'Credit Card') {
            paymentInfo.Total_Amount__c = actualTotalPay-sumCreditFee;
        } else {
            paymentInfo.Total_Amount__c = actualTotalPay-sumCreditFee-sumDiscountFee;
        }
        paymentInfo.Remark__c = remark;
        paymentInfo.First_Pay_By_Paypal__c = isFirstPay;
        if (String.isNotBlank(paypalInfo.cardNumber)) {
            paymentInfo.Card_Last_Four__c = paypalInfo.cardNumber.substring(paypalInfo.cardNumber.length() - 4, paypalInfo.cardNumber.length());
        }
        insert paymentInfo;


        //添加了备注时给AR发邮件提醒
        String orgCode = '';
        for(Account acc : [SELECT ORG_Code__c FROM Account WHERE Id = :currentUser.Contact.AccountId]) {
            orgCode = acc.ORG_Code__c;
        }
        String name=[select Name from Payment_Information__c where id=:paymentInfo.id limit 1].name;
        if (String.isNotBlank(remark) && String.isNotEmpty(remark)){
            sendRemarkEmail(name, remark, currentUser.Contact.Account.name, orgCode);
        }
        //用于邮件拼接
        List<String> invoiceNumbers=new List<String>();
        //paymentInfo_Item(invoice)
        List<Payment_Information_Item__c> paymentItems=new List<Payment_Information_Item__c>();
        List<Payment_Information_Item__c> paymentItems2=new List<Payment_Information_Item__c>();
        Map<String, List<AccountingInfo>> accountingItemWithInvoiceNumber = new Map<String, List<AccountingInfo>>();
        Map<String, List<AccountingInfo>> accountingItemWithId = new Map<String, List<AccountingInfo>>();
        Set<String> ids=new Set<String>();
        List<Payment_Information_Item__c> paymentItemAccountings=new List<Payment_Information_Item__c>();
        for (DataTableWrapper invoice : paidInvoiceList) {
            invoiceNumbers.add(invoice.invoiceNumber);
            if (invoice.hasNoSequence) {
                Payment_Information_Item__c paymentItem = new Payment_Information_Item__c();
                paymentItem.Actual_Paid_Amount__c = invoice.totalPaymentFee;
                paymentItem.Cash_Discount_Amount__c = invoice.discountFee;
                // paymentItem.CreatedDate = Date.today();
                paymentItem.CurrencyIsoCode = invoice.currencyType;
                paymentItem.Invoice__c = invoice.id;
                paymentItem.Payment_Information__c = paymentInfo.id;
                paymentItem.Invoice_Type__c = invoice.invoiceType;
                paymentItem.Total_Amount__c = invoice.dueRemainingAmt;
                paymentItem.Has_No_Sequence__c = invoice.hasNoSequence;
                paymentItems.add(paymentItem);
            } else {
                Payment_Information_Item__c paymentItem2 = new Payment_Information_Item__c();
                // paymentItem2.CreatedDate = Date.today();
                paymentItem2.CurrencyIsoCode = invoice.currencyType;
                paymentItem2.Invoice__c = invoice.id;
                paymentItem2.Payment_Information__c = paymentInfo.id;
                paymentItem2.Invoice_Type__c = invoice.invoiceType;
                paymentItem2.Has_No_Sequence__c = invoice.hasNoSequence;
                paymentItems2.add(paymentItem2);
                if (!accountingItemWithInvoiceNumber.containsKey(invoice.invoiceNumber)) {
                    accountingItemWithInvoiceNumber.put(invoice.id, invoice.accountingList);
                }
            }
        }
        insert paymentItems;
        insert paymentItems2;
        for (Payment_Information_Item__c item : paymentItems2) {
            ids.add(item.Id);
            accountingItemWithId.put(item.Id, accountingItemWithInvoiceNumber.get(item.Invoice__c));
        }
        List<Payment_Information_Item__c> queryPaymentItemAccountingList =[
                            SELECT
                                Id,
                                Invoice__r.Invoice_Number__c
                            FROM Payment_Information_Item__c
                            WHERE
                                Id In :ids];
        for (Payment_Information_Item__c invoice : queryPaymentItemAccountingList) {
            if (accountingItemWithId.containsKey(invoice.Id)) {
                for (AccountingInfo accountingBalance : accountingItemWithId.get(invoice.Id)) {
                    Payment_Information_Item__c paymentItemAccounting = new Payment_Information_Item__c();
                    paymentItemAccounting.Accounting_Balance__c = accountingBalance.Id;
                    paymentItemAccounting.Payment_Information_Item_accounting__c = invoice.Id;
                    paymentItemAccounting.Actual_Paid_Amount__c = (accountingBalance.dueRemainingAmount + accountingBalance.discountFee).setScale(2);
                    paymentItemAccounting.Cash_Discount_Amount__c = accountingBalance.discountFee;
                    paymentItemAccounting.Total_Amount__c = accountingBalance.dueRemainingAmount;
                    paymentItemAccountings.add(paymentItemAccounting);
                }
            }
        }
        insert paymentItemAccountings;


        //paymentInfo_Item(credit)
        List<Payment_Information_Item__c> paymentItems3=new List<Payment_Information_Item__c>();
        if (paidCreditList.size() > 0) {
            for (DataTableWrapper credit : paidCreditList) {
                Payment_Information_Item__c paymentItem3 = new Payment_Information_Item__c();
                paymentItem3.CurrencyIsoCode = credit.currencyType;
                paymentItem3.Invoice__c = credit.id;
                paymentItem3.Payment_Information__c = paymentInfo.id;
                paymentItem3.Total_Amount__c = credit.originalAmt;
                paymentItem3.Actual_Paid_Amount__c = credit.originalAmt;
                paymentItem3.Invoice_Type__c = credit.invoiceType;
                paymentItems3.add(paymentItem3);
            }
        }
        insert paymentItems3;
        return paymentInfo.Id;
    }

    @AuraEnabled
    public static String syncPaymentInfo(String invoicesList, String creditList, String selectMethod, Decimal sumDiscountFee, Decimal sumCreditFee, Decimal actualTotalPay, String remark, Boolean isFirstPay, String paymentOptions,String paymentInfoId) {

        paymentOptions paypalInfo = (paymentOptions) JSON.deserialize(paymentOptions, paymentOptions.class);
        List<DataTableWrapper> paidInvoiceList = (List<DataTableWrapper>) JSON.deserialize(invoicesList, List<DataTableWrapper>.class);
        List<DataTableWrapper> paidCreditList = new List<DataTableWrapper>();
        if (String.isNotBlank(creditList) && creditList != '[""]') {
            paidCreditList = (List<DataTableWrapper>) JSON.deserialize(creditList, List<DataTableWrapper>.class);
        }

        //调用支付接口前推送数据
        // pushBefPaymentInfo(paymentInfoId);

        //调paypal接口支付
         paypalResponse result = usePaypal(selectMethod, actualTotalPay, paypalInfo);

        // paypalResponse result = new paypalResponse();
        // result.PNREF = 'test';
        // result.resultCode = '1';
        // result.respMsg= 'succeed';

        //根据支付结果更新paymentInfo，invoice，accounting_balance
        Payment_Information__c payment = [
            SELECT Id,Payment_Date__c
            FROM Payment_Information__c
            WHERE id = :paymentInfoId
        ];
        payment.PNREF__c = result.PNREF;

        // if (selectMethod == 'ACH') {
        //     result.resultCode = '0';
        // }
        // if (selectMethod == 'Telecheck') {
        //     result.resultCode = '0';
        // }
        if (Test.isRunningTest()) {
            CCM_Service.pushPaymentInfo(paymentInfoId);
        }
            payment.Payment_Date__c = Date.today();
            if (result.resultCode == '0') {
                payment.Paypal_Status__c = 'Payment Success';
                //传到oracle核销
                CCM_Service.pushPaymentInfo(paymentInfoId);
                CCM_Service.pushBeforePaymentInfo(paymentInfoId,true);
            } else {
                payment.Paypal_Status__c = 'Payment Failed';
                CCM_Service.pushBeforePaymentInfo(paymentInfoId,false);

            }
        update payment;
        List<Accounting_Balance__c> invoices =new List<Accounting_Balance__c>();
        List<Accounting_Balance__c> accountings =new List<Accounting_Balance__c>();
        for (DataTableWrapper paidInvoice : paidInvoiceList) {
            if (paidInvoice.hasNoSequence) {
                List<Accounting_Balance__c> invoice = [
                    SELECT a.Id, a.Invoice__c, a.Paypal_Status__c
                    FROM Accounting_Balance__c a
                    WHERE a.Invoice__c = :paidInvoice.id
                ];
                for (Accounting_Balance__c var : invoice) {
                        if (result.resultCode == '0') {
                            var.Paypal_Status__c = 'Payment Success';
                        } else {
                            var.Paypal_Status__c = 'Payment Failed';
                        }
                }
                invoices.addAll(invoice);
            } else {
                for (AccountingInfo paidAccounting : paidInvoice.accountingList) {
                    List<Accounting_Balance__c> accounting = [
                        SELECT Paypal_Status__c
                        FROM Accounting_Balance__c
                        WHERE ID = :paidAccounting.id
                        ];
                    for (Accounting_Balance__c var2 : accounting) {
                            if (result.resultCode == '0') {
                                var2.Paypal_Status__c = 'Payment Success';
                            } else {
                                var2.Paypal_Status__c = 'Payment Failed';
                            }
                    }
                    accountings.addAll(accounting);
                }
            }
        }
        update invoices;
        update accountings;
        List<Accounting_Balance__c> credits=new List<Accounting_Balance__c>();
        if (paidCreditList.size() > 0) {
            for (DataTableWrapper paidCredit : paidCreditList) {
                Accounting_Balance__c credit = [
                    SELECT a.Id, a.Invoice__c, a.Paypal_Status__c
                    FROM Accounting_Balance__c a
                    WHERE a.Invoice__c = :paidCredit.id
                ];
                    if (result.resultCode == '0') {
                        credit.Paypal_Status__c = 'Payment Success';
                    } else {
                        credit.Paypal_Status__c = 'Payment Failed';
                    }
                credits.add(credit);
            }
            update credits;
        }
        List<String> invoiceNumbers=new List<String>();
        // String url = URL.getOrgDomainUrl().toExternalForm()+'/';
        String name=[select Name from Payment_Information__c where id=:paymentInfoId limit 1].name;
        for (DataTableWrapper invoice : paidInvoiceList) {
            invoiceNumbers.add(invoice.invoiceNumber);
        }
        String numbers=String.join(invoiceNumbers,',');
        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
        if (selectMethod == 'Debit Card' || selectMethod == 'Credit Card') {
            if (result.resultCode == '0') {
                email=sendPayResultEmail(true,paypalInfo.email,actualTotalPay,name,result.respMsg,numbers);
            } else {
                email=sendPayResultEmail(false,paypalInfo.email,actualTotalPay,name,result.respMsg,numbers);
            }
            if(email!=null) {
                Messaging.SendEmailResult[] r = Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{email});
            }
        }
        // pushBefPaymentInfo(paymentInfo.Id);
        return JSON.serialize(result);
    }

    public static String generateCreditMemoInfoInEmail(String paymentName) {
        List<Payment_Information_Item__c> creditMemos = getCreditMemo(paymentName);
        return generateCreditMemoInfoInEmail(creditMemos);
    }

    private static List<Payment_Information_Item__c> getCreditMemo(String paymentName) {
        List<Payment_Information_Item__c> creditMemos = new List<Payment_Information_Item__c>();
        for(Payment_Information_Item__c queryPaymentItem : [ SELECT Id,
                                                Has_No_Sequence__c,
                                                CurrencyIsoCode,
                                                Total_Amount__c,
                                                Cash_Discount_Amount__c,
                                                Actual_Paid_Amount__c,
                                                Invoice__c,
                                                Invoice_Type__c,
                                                Invoice__r.Invoice_Number__c
                                            FROM Payment_Information_Item__c
                                            WHERE Payment_Information__r.Name = :paymentName]) {
            if (queryPaymentItem.Has_No_Sequence__c) {
                if (
                    queryPaymentItem.Invoice_Type__c == 'CNA_External_CM' ||
                    queryPaymentItem.Invoice_Type__c == 'CA_External_CM' ||
                    queryPaymentItem.Invoice_Type__c == 'CNA_CreditMemo' ||
                    queryPaymentItem.Invoice_Type__c == 'CA_CreditMemo'
                ) {
                    creditMemos.add(queryPaymentItem);
                }
            }
        }
        return creditMemos;
    }

    private static String generateCreditMemoInfoInEmail(List<Payment_Information_Item__c> creditMemos) {
        String creditMemoInfo = '';
        if(!creditMemos.isEmpty()) {
            creditMemoInfo = '<table style="border-collapse: collapse;border: 1px solid;">';
            creditMemoInfo += '<thead>';
            creditMemoInfo += '<tr style="border: 1px solid;">';
            creditMemoInfo += '<td style="border: 1px solid; padding-right:30px">Credit Memo#</td>';
            creditMemoInfo += '<td style="border: 1px solid;">Credit Amount</td>';
            creditMemoInfo += '</tr>';
            creditMemoInfo += '</thead>';
            creditMemoInfo += '<tbody>';
            for(Payment_Information_Item__c creditMemo : creditMemos) {
                creditMemoInfo += '<tr style="border: 1px solid;">';
                creditMemoInfo += '<td style="border: 1px solid;">' + creditMemo.Invoice__r.Invoice_Number__c + '</td><td style="border: 1px solid;">' + String.valueOf(creditMemo.Total_Amount__c) + '</td>';
            }
            creditMemoInfo += '</tbody>';
            creditMemoInfo += '</table>';
        }

        return creditMemoInfo;
    }

    @AuraEnabled
    public static PaypalResponse usePaypal(String selectMethod, Decimal actualTotalPay, paymentOptions paypalInfo) {
        String mdtName = 'PayPal_Sandbox';
        String orgId = UserInfo.getOrganizationId();
        Organization org = [
            SELECT Id, IsSandbox
            FROM Organization
            WHERE Id = :orgId
        ];
        if (!org.IsSandbox) {
            mdtName = 'PayPal_Production_Onlinepayment';
        }
        PayPal_Configuration__mdt config = [
            SELECT
                End_Point__c,
                PayPal_User__c,
                PayPal_Vender__c,
                User_credentia__c,
                Partner__c
            FROM PayPal_Configuration__mdt
            WHERE DeveloperName = :mdtName
            LIMIT 1
        ];

        Map<String, String> paramMap = new Map<String, String>();
        paramMap.put('USER', config.PayPal_User__c);
        paramMap.put('VENDOR', config.PayPal_Vender__c);
        paramMap.put('PWD', config.User_credentia__c);
        paramMap.put('PARTNER', config.Partner__c);
        paramMap.put('AMT', String.valueOf(actualTotalPay));
        paramMap.put('BILLTOCITY', paypalInfo.city);
        paramMap.put('BILLTOCOUNTRY', paypalInfo.country);
        paramMap.put('BILLTOLASTNAME', paypalInfo.lastName);
        paramMap.put('BILLTOZIP', paypalInfo.postalCode);
        paramMap.put('BILLTOEMAIL', paypalInfo.email);
        if (String.isNotBlank(paypalInfo.state)&&String.isNotEmpty(paypalInfo.state)) {
            paramMap.put('BILLTOSTATE', paypalInfo.state);
        }
        if (String.isNotBlank(paypalInfo.firstName)&&String.isNotEmpty(paypalInfo.firstName)) {
            paramMap.put('BILLTOFIRSTNAME', paypalInfo.firstName);
        }
        if (String.isNotBlank(paypalInfo.phoneNum)&&String.isNotEmpty(paypalInfo.phoneNum)) {
            paramMap.put('BILLTOPHONENUM', paypalInfo.phoneNum);
        }
        if (String.isNotBlank(paypalInfo.street)&&String.isNotEmpty(paypalInfo.street)) {
            paramMap.put('BILLTOSTREET', paypalInfo.street);
        }
        paramMap.put('TRXTYPE', 'S');
        paramMap.put('TENDER', 'C');
        paramMap.put('ACCT', paypalInfo.cardNumber);
        paramMap.put('EXPDATE', paypalInfo.month + paypalInfo.year);
        List<String> params = new List<String>();
        for (String key : paramMap.keySet()) {
            params.add(key + '=' + paramMap.get(key));
        }
        String body = String.join(params, '&');
        PaypalResponse resp = new PaypalResponse();
        try {
            HttpRequest req = new HttpRequest();
            req.setEndpoint(config.End_Point__c);
            req.setMethod('POST');
            req.setBody(body);
            HttpResponse response;
            if (!Test.isRunningTest()) {
                response = new Http().send(req);
            }else{
                response = new HttpResponse();
                String strBody = 'RESULT=0&RESPMSG=TESTRUN&PNREF=TESTRUN';
                response.setBody(strBody);
            }

            String respBody = response.getBody();
            Map<String, String> valMap = new Map<String, String>();
            for (String valPair : respBody.split('&')) {
                List<String> pairs = valPair.split('=');
                if (pairs.size() > 1) {
                    valMap.put(pairs[0], pairs[1]);
                }
            }
            Log__c paypalLog = new Log__c(
                ApexName__c = 'CCM_Community_InvoiceFilterPageCtl',
                Method__c = 'usePaypal',
                Name = 'CCM_Community_InvoiceFilterPageCtl.usePaypal: ' +
                    ' at ' +
                    Datetime.now()
                        .format(
                            CCM_Constants.DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS
                        ),
                ReqParam__c = body,
                ResParam__c = valMap.toString()
            );
            insert paypalLog;
            resp.resultCode = valMap.containsKey('RESULT') ? valMap.get('RESULT') : '';
            resp.respMsg = valMap.containsKey('RESPMSG') ? valMap.get('RESPMSG') : '';
            resp.PNREF = valMap.containsKey('PNREF') ? valMap.get('PNREF') : '';
            return resp;
        } catch (Exception e) {
            resp.resultCode = '9999';
            resp.respMsg = e.getMessage();
            resp.PNREF = '';
            return resp;
        }
    }

    @AuraEnabled
    public static void pushBefPaymentInfo(String paymentId) {
        String queryStr =
            'SELECT Id, OwnerId, Name, CurrencyIsoCode, CreatedDate, CreatedById, CreatedBy.TimeZoneSidKey, ' +
            'Customer__c, ' +
            'Customer__r.AccountNumber, ' +
            'Total_Amount__c, ' +
            'Actual_Paid_Amount__c, ' +
            'Result__c, ' +
            'Pay_Method__c, ' +
            'Transaction_ID__c, ' +
            'BillTo__r.Customer_Line_Oracle_ID__c, ' +
            'Billing_Address__c, ' +
            'ORG_Code__c, ' +
            'Payment_Date__c, ' +
            'Cash_Discount_Gross_Amount__c, ' +
            'Total_Credit_Memo_Amount__c, ' +
            'Remark__c, ' +
            'First_Pay_By_Paypal__c, ' +
            '(SELECT Id, CreatedDate, ' +
            'Invoice__c, ' +
            'Invoice__r.Invoice_Number__c, ' +
            'Invoice__r.Invoice_Type__c, ' +
            'Order__c, ' +
            'Invoice_Type__c, ' +
            'Accounting_Balance__c, ' +
            'Accounting_Balance__r.Accounting_OracleID__c, ' +
            'Cash_Discount_Amount__c, ' +
            'Total_Amount__c, ' +
            'Actual_Paid_Amount__c, ' +
            'Payment_Information__c, ' +
            'Payment_Information_Item_accounting__c, ' +
            'Has_No_Sequence__c ' +
            'FROM Payment_Information_Items__r) ' +
            'FROM Payment_Information__c WHERE Id = :paymentId';

        List<SObject> objList = Database.query(queryStr);
        if (objList.size() == 0) {
            return;
        }
        System.debug(LoggingLevel.INFO, '*** objList-->: ' + objList);
        List<Map<String, Object>> mapList = new List<Map<String, Object>>();
        Map<Id, String> invoiceMap = new Map<Id, String>();
        Map<String, Object> paramMap = new Map<String, Object>();
        Payment_Information__c pay = (Payment_Information__c) objList[0];
        List<Map<String, Object>> itemList = new List<Map<String, Object>>();
        Set<Id> itemIds = new Set<Id>();
        Set<Id> invoiceIds = new Set<Id>();
        for (Payment_Information_Item__c item : pay.Payment_Information_Items__r) {
            itemIds.add(item.id);
        }

        Map<String, List<Payment_Information_Item__c>> sequenseItem = new Map<String, List<Payment_Information_Item__c>>();

        for (Payment_Information_Item__c each : [SELECT Id,
                                                        Invoice__c,
                                                        Invoice__r.Invoice_Number__c,
                                                        Invoice__r.Invoice_Type__c,
                                                        Invoice_Type__c,
                                                        Accounting_Balance__c,
                                                        Accounting_Balance__r.Accounting_OracleID__c,
                                                        Actual_Paid_Amount__c,
                                                        Cash_Discount_Amount__c,
                                                        Payment_Information_Item_accounting__c,
                                                        Total_Amount__c
                                                    FROM Payment_Information_Item__c
                                                    WHERE Payment_Information_Item_accounting__c IN :itemIds]) {
            if (!sequenseItem.containsKey(each.Payment_Information_Item_accounting__c)) {
                List<Payment_Information_Item__c> accountingList = new List<Payment_Information_Item__c>();
                accountingList.add(each);
                sequenseItem.put(each.Payment_Information_Item_accounting__c, accountingList);
            } else {
                sequenseItem.get(each.Payment_Information_Item_accounting__c).add(each);
            }
        }

        for (Payment_Information_Item__c item : pay.Payment_Information_Items__r) {
            if(!sequenseItem.containsKey(item.id)){
                invoiceIds.add(Item.Invoice__c);
            }
        }
        for(Accounting_Balance__c ab : [SELECT Id,Accounting_OracleID__c,Invoice__c FROM Accounting_Balance__c WHERE Invoice__c IN :invoiceIds]){
            if(!invoiceMap.containsKey(ab.Invoice__c)){
                invoiceMap.put(ab.Invoice__c, ab.Accounting_OracleID__c);
            }
        }

        for (Payment_Information_Item__c item : pay.Payment_Information_Items__r) {
            if (sequenseItem.containsKey(item.Id)) {
                for (Payment_Information_Item__c sequense : sequenseItem.get(item.Id)) {
                    Map<String, Object> itemParamMap = new Map<String, Object>();
                    itemParamMap.put('Receipt_Number', pay.Name);
                    itemParamMap.put('Apply_Date', pay.CreatedDate);
                    //Update for CCA，cca only use [Lock Box Canada]  Zoe 2024-8-20
                    if(pay.ORG_Code__c =='CCA' && (pay.Pay_Method__c == 'Credit Card' || pay.Pay_Method__c == 'Debit Card')){
                        itemParamMap.put('Receipt_Method', 'Lock Box Canada'); // Lock Box Canada
                    } else if (pay.Pay_Method__c == 'Credit Card' || pay.Pay_Method__c == 'Debit Card') {
                        itemParamMap.put('Receipt_Method', 'Credit Card_Old Second'); // Lock box、
                    } else if (pay.Pay_Method__c == 'ACH') {
                        itemParamMap.put('Receipt_Method', 'Lock Box'); // Lock box、Credit Card Harris Bank
                    } else if (pay.Pay_Method__c == 'PAD') {
                        itemParamMap.put('Receipt_Method', 'Lock Box Canada'); // Lock Box Canada
                    }

                    itemParamMap.put('Payment_Method', pay.Pay_Method__c); // Credit Card、Debit Card、ACH
                    itemParamMap.put('Receipt_Amount', pay.Actual_Paid_Amount__c);
                    itemParamMap.put('Invoice_Number', item.Invoice__r.Invoice_Number__c);
                    if (!Test.isRunningTest()) {
                        itemParamMap.put('Accounting_OracleID',Integer.valueOf(sequense.Accounting_Balance__r.Accounting_OracleID__c));
                    }else{
                         itemParamMap.put('Accounting_OracleID',54234);
                    }

                    itemParamMap.put('Total_Invoice_Amount', sequense.Total_Amount__c);
                    itemParamMap.put('Invoice_Type', item.Invoice__r.Invoice_type__c);
                    if (sequense.Cash_Discount_Amount__c != 0 && sequense.Cash_Discount_Amount__c != null) {
                        itemParamMap.put('Cash_Discount_Amount', Integer.valueOf(sequense.Cash_Discount_Amount__c.abs()));
                    } else {
                        itemParamMap.put('Cash_Discount_Amount', 0);
                    }
                    itemParamMap.put('Actual_Apply_Amount', sequense.Actual_Paid_Amount__c);
                    itemParamMap.put('Account_Number', pay.Customer__r.AccountNumber);
                    itemParamMap.put('Bill_To', pay.BillTo__r.Customer_Line_Oracle_ID__c);
                    itemParamMap.put('Org_Code', pay.ORG_Code__c);
                    itemParamMap.put('Currency_Code', pay.CurrencyIsoCode);
                    itemParamMap.put('Payment_Status', 'SUBMIT');
                    System.debug(LoggingLevel.INFO, '*** itemParamMap1: ' + itemParamMap);
                    itemList.add(itemParamMap);
                }
            } else {
                Map<String, Object> itemParamMap = new Map<String, Object>();
                itemParamMap.put('Receipt_Number', pay.Name);
                itemParamMap.put('Apply_Date', pay.CreatedDate);
                //Update for CCA，cca only use [Lock Box Canada]  Zoe 2024-8-20
                if(pay.ORG_Code__c =='CCA' && (pay.Pay_Method__c == 'Credit Card' || pay.Pay_Method__c == 'Debit Card')){
                    itemParamMap.put('Receipt_Method', 'Lock Box Canada'); // Lock Box Canada
                } else if (pay.Pay_Method__c == 'Credit Card' || pay.Pay_Method__c == 'Debit Card') {
                    itemParamMap.put('Receipt_Method', 'Credit Card_Old Second'); // Lock box、
                } else if (pay.Pay_Method__c == 'ACH') {
                    itemParamMap.put('Receipt_Method', 'Lock Box'); // Lock box、Credit Card Harris Bank
                } else if (pay.Pay_Method__c == 'PAD') {
                    itemParamMap.put('Receipt_Method', 'Lock Box Canada'); // Lock Box Canada
                }
                itemParamMap.put('Payment_Method', pay.Pay_Method__c); // Credit Card、Debit Card、ACH
                itemParamMap.put('Receipt_Amount', pay.Actual_Paid_Amount__c);
                itemParamMap.put('Invoice_Number', item.Invoice__r.Invoice_Number__c);
                if (!Test.isRunningTest()) {
                    itemParamMap.put('Accounting_OracleID', Integer.valueOf(invoiceMap.get(item.Invoice__c)));
                }else{
                    itemParamMap.put('Accounting_OracleID', 54535);
                }
                itemParamMap.put('Total_Invoice_Amount', item.Total_Amount__c);
                itemParamMap.put('Invoice_Type', item.Invoice__r.Invoice_type__c);
                if (item.Cash_Discount_Amount__c != 0 && item.Cash_Discount_Amount__c != null) {
                    itemParamMap.put('Cash_Discount_Amount', Integer.valueOf(item.Cash_Discount_Amount__c.abs()));
                } else {
                    itemParamMap.put('Cash_Discount_Amount', 0);
                }
                itemParamMap.put('Actual_Apply_Amount', item.Actual_Paid_Amount__c);
                System.debug(LoggingLevel.INFO, '*** pay.Customer__r.AccountNumber: ' + pay.Customer__r.AccountNumber);
                itemParamMap.put('Account_Number', pay.Customer__r.AccountNumber);
                itemParamMap.put('Bill_To', pay.BillTo__r.Customer_Line_Oracle_ID__c);
                itemParamMap.put('Org_Code', pay.ORG_Code__c);
                itemParamMap.put('Currency_Code', pay.CurrencyIsoCode);
                itemParamMap.put('Payment_Status', 'SUBMIT');
                System.debug(LoggingLevel.INFO, '*** itemParamMap2: ' + itemParamMap);
                itemList.add(itemParamMap);
            }
        }
        String paramStr = JSON.serialize(itemList);
        System.debug(LoggingLevel.INFO, '*** paramStr: ' + paramStr);
        String endPointName = 'chervon_seeburger_uat';
        MeIntegration_Setting__mdt MeIS = [
            SELECT End_Point__c, Key__c
            FROM MeIntegration_Setting__mdt
            WHERE DeveloperName = :endPointName
        ];
        String headerToken = MeIS.Key__c;
        String endPoint = MeIS.End_Point__c + 'dunningpayment';
        String HeaderKey = 'Basic ' + headerToken;

        try{
            HttpRequest req = new HttpRequest();
            String resStatus = '';
            req.setEndpoint(endPoint);
            req.setMethod('POST');
            req.setBody(paramStr);
            req.setHeader('Authorization', headerKey);
            req.setHeader('Content-Type', 'application/json');
            req.setTimeout(20000);
            HttpResponse response;
            if (!Test.isRunningTest()) {
                response = new Http().send(req);
                Integer statusCode = response.getStatusCode();
                System.debug(LoggingLevel.INFO, '*** statusCode: ' + statusCode);
                if(statusCode == 200){
                    resStatus = 'Process_Status:Success';
                }else{
                    resStatus = 'Process_Status:Failed';
                }

            }
            Log__c pushPaymentLog = new Log__c(
            ApexName__c = 'CCM_Community_InvoiceFilterPageCtl',
            Method__c = 'pushBefPaymentInfo',
            Name = 'pushBefPaymentInfo: ' + ' at ' + Datetime.now().format(CCM_Constants.DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
            ReqParam__c = paramStr,
            ResParam__c = resStatus
            );
            insert pushPaymentLog;
            // LogInsertQueueable logTask = new LogInsertQueueable(paramStr,resStatus);
            // System.enqueueJob(logTask);
            //createPushBefPaymentInfoLog(paramStr,resStatus);
        }catch (Exception e) {
           System.debug(LoggingLevel.INFO, '*** e.getMessage(): ' + e.getMessage());
        }
    }

    // @future(callout=true)
    // public static void createPushBefPaymentInfoLog(String paramStr, String resStatus){
    //     Log__c pushPaymentLog = new Log__c(
    //         ApexName__c = 'CCM_Community_InvoiceFilterPageCtl',
    //         Method__c = 'pushBefPaymentInfo',
    //         Name = 'pushBefPaymentInfo: ' + ' at ' + Datetime.now().format(CCM_Constants.DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
    //         ReqParam__c = paramStr,
    //         ResParam__c = resStatus
    //         );
    //         insert pushPaymentLog;
    // }

    public static String currencyFormat(Decimal inputVal) {
        return inputVal == null ? '0.00' : Util.formatDecimal(inputVal, 2);
    }

    /**
     * GoCardless第一步：创建Billing Request
     * @param strOrgCode
     */
    @AuraEnabled
    public static ResultResponse createBillingRequest(String strOrgCode){
        ResultResponse response = new ResultResponse();
        String strEndPoint = 'billing_requests';
        try {
            Map<String,Object> mapRequestParam = new Map<String,Object>();
            Map<String,Object> mapBillingRequestsParam = new Map<String,Object>();
            Map<String,Object> mapMandateRequestsParam = new Map<String,Object>();
            mapRequestParam.put('billing_requests',mapBillingRequestsParam);
            mapBillingRequestsParam.put('mandate_request',mapMandateRequestsParam);
            if (strOrgCode == 'CCA') {
                mapMandateRequestsParam.put('scheme','pad');
            }else{
                mapMandateRequestsParam.put('scheme','ach');
            }
            HttpResponse objResponse = CCM_GoCardlessCallout.SyncData(JSON.serialize(mapRequestParam), strEndPoint, 'POST', strOrgCode);
            // 成功的响应
            if(objResponse != null && String.valueOf(objResponse.getStatusCode()).startsWith('2')){
                Map<String, Object> mapJsonResponse = (Map<String, Object>)JSON.deserializeUntyped(objResponse.getBody());
                Map<String, Object> mapJsonBillingRequests = (Map<String, Object>)mapJsonResponse.get('billing_requests');
                String strBillingRequestId = (String)mapJsonBillingRequests.get('id');
                System.debug('strBillingRequestId -> ' + strBillingRequestId);
                GoCardlessWrapper objGoCardlessWrapper = new GoCardlessWrapper();
                objGoCardlessWrapper.billingRequest.billingRequestId = strBillingRequestId;
                response.status = 'OK';
                response.data = objGoCardlessWrapper;
            }
            // 失败的响应
            else{
                GoCardlessError objGoCardlessError = parseGoCardlessError(objResponse);
                response.status = 'GCNG';
                response.data = objGoCardlessError;
            }
        } catch (Exception objEx) {
            response.message = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
            response.status = 'NG';
        }
        return response;
    }

    /**
     * GoCardless第二步：收集客户信息
     * @param strBillingRequestId *
     * @param strFirstName 当strCompanyName为空时必须提供
     * @param strLastName 当strCompanyName为空时必须提供
     * @param strCompanyName 当strLastName为空时必须提供
     * @param strEmail *
     * @param strOrgCode *
     */
    @AuraEnabled
    public static ResultResponse collectCustomerDetails(String strBillingRequestId, String strFirstName, String strLastName, String strCompanyName, String strEmail, String strOrgCode){
        ResultResponse response = new ResultResponse();
        String strEndPoint = 'billing_requests/' + strBillingRequestId + '/actions/collect_customer_details';
        try {
            Map<String,Object> mapRequestParam = new Map<String,Object>();
            Map<String,Object> mapDataParam = new Map<String,Object>();
            Map<String,Object> mapCustomerParam = new Map<String,Object>();
            Map<String,Object> mapCustomerBillingDetailParam = new Map<String,Object>{
                'address_line1' => '1203 East Warrenville Road',
                'city' => 'Naperville',
                'postal_code' => '60563',
                'country_code' => 'US',
                'region' => 'IL'
            };
            mapRequestParam.put('data', mapDataParam);
            mapDataParam.put('customer',mapCustomerParam);
            mapCustomerParam.put('email', strEmail);
            if (String.isNotBlank(strLastName) && String.isNotBlank(strFirstName) && String.isBlank(strCompanyName)) {
                mapCustomerParam.put('given_name', strFirstName);
                mapCustomerParam.put('family_name', strLastName);
            }
            if (String.isNotBlank(strCompanyName) && String.isBlank(strLastName) && String.isBlank(strFirstName)) {
                mapCustomerParam.put('company_name', strCompanyName);
            }
            if (strOrgCode == 'CNA') {
                mapDataParam.put('customer_billing_detail', mapCustomerBillingDetailParam);
                User currentUser = [
                    SELECT
                        Id, ContactId, Contact.AccountId, Contact.Account.CurrencyIsoCode
                        , Contact.Account.ORG_Code__c, Contact.Account.Name, Contact.Account.AccountNumber
                    FROM
                        User
                    WHERE
                        Id = :UserInfo.getUserId()
                ];
                List<Address_With_Program__c> lstBillingAddress = [
                    SELECT
                        Id, Customer_Line_Oracle_ID__c, Account_Address__r.Customer__c
                        , Account_Address__r.Address1__c, Account_Address__r.City__c, Account_Address__r.State__c
                        , Account_Address__r.Country__c, Account_Address__r.Postal_Code__c
                    FROM
                        Address_With_Program__c
                    WHERE
                        Account_Address__r.Customer__c = :currentUser.Contact.AccountId
                        AND Account_Address__r.RecordTypeId = :CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID
                        AND Customer_Line_Oracle_ID__c != null
                ];
                if (lstBillingAddress != null && lstBillingAddress.size() > 0) {
                    if (lstBillingAddress[0].Account_Address__r.Address1__c != null) {
                        mapCustomerBillingDetailParam.put('address_line1',lstBillingAddress[0].Account_Address__r.Address1__c);
                    }
                    if (lstBillingAddress[0].Account_Address__r.City__c != null) {
                        mapCustomerBillingDetailParam.put('city',lstBillingAddress[0].Account_Address__r.City__c);
                    }
                    if (lstBillingAddress[0].Account_Address__r.Postal_Code__c != null) {
                        mapCustomerBillingDetailParam.put('postal_code',lstBillingAddress[0].Account_Address__r.Postal_Code__c);
                    }
                    if (lstBillingAddress[0].Account_Address__r.Country__c != null) {
                        mapCustomerBillingDetailParam.put('country_code',lstBillingAddress[0].Account_Address__r.Country__c);
                    }
                    if (lstBillingAddress[0].Account_Address__r.State__c != null) {
                        mapCustomerBillingDetailParam.put('region',lstBillingAddress[0].Account_Address__r.State__c);
                    }
                }
                if (!Test.isRunningTest()) {
                    mapCustomerBillingDetailParam.put('ip_address', Auth.SessionManagement.getCurrentSession().get('SourceIp'));
                }
            }
            HttpResponse objResponse = CCM_GoCardlessCallout.SyncData(JSON.serialize(mapRequestParam), strEndPoint, 'POST', strOrgCode);
            // 成功的响应
            if(objResponse != null && String.valueOf(objResponse.getStatusCode()).startsWith('2')){
                Map<String, Object> mapJsonResponse = (Map<String, Object>)JSON.deserializeUntyped(objResponse.getBody());
                Map<String, Object> mapJsonBillingRequests = (Map<String, Object>)mapJsonResponse.get('billing_requests');
                List<Object> lstJsonActions = (List<Object>)mapJsonBillingRequests.get('actions');
                // 检查当前步骤是否完成
                for (Object jsonAction : lstJsonActions) {
                    Map<String, Object> action = (Map<String, Object>)jsonAction;
                    if(action.get('type') == 'collect_customer_details'){
                        // 已完成
                        if(action.get('status') == 'completed'){
                            response.status = 'OK';
                        }
                        // 未完成
                        else{
                            response.status = 'incomplete';
                            response.message = JSON.serialize(action.get('collect_customer_details'));
                        }
                        break;
                    }
                }
            }
            // 失败的响应
            else{
                GoCardlessError objGoCardlessError = parseGoCardlessError(objResponse);
                response.status = 'GCNG';
                response.data = objGoCardlessError;
            }
        } catch (Exception objEx) {
            response.message = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
            response.status = 'NG';
        }
        return response;
    }

    /**
     * GoCardless第三步：收集银行账户信息
     * @param strBillingRequestId *
     * @param strAccountNumber *
     * @param strAccountHolderName *
     * @param strBankCode *
     * @param strBranchCode CA必须提供，US保持空值
     * @param strAccountType 美元交易必须提供[checking, savings]
     * @param strOrgCode *
     */
    @AuraEnabled
    public static ResultResponse collectBankAccountDetails(String strBillingRequestId, String strAccountNumber, String strAccountHolderName, String strBankCode, String strBranchCode, String strAccountType, String strOrgCode){
        ResultResponse response = new ResultResponse();
        String strEndPoint = 'billing_requests/' + strBillingRequestId + '/actions/collect_bank_account';
        try {
            Map<String,Object> mapRequestParam = new Map<String,Object>();
            Map<String,Object> mapDataParam = new Map<String,Object>();
            mapRequestParam.put('data', mapDataParam);

            mapDataParam.put('account_number', strAccountNumber);
            mapDataParam.put('account_holder_name', strAccountHolderName);
            mapDataParam.put('bank_code', strBankCode);
            if (strOrgCode == 'CNA') {
                mapDataParam.put('account_type', strAccountType);
                mapDataParam.put('country_code', 'US');
            }
            if (strOrgCode == 'CCA') {
                mapDataParam.put('branch_code', strBranchCode);
                mapDataParam.put('country_code', 'CA');
            }
            HttpResponse objResponse = CCM_GoCardlessCallout.SyncData(JSON.serialize(mapRequestParam), strEndPoint, 'POST', strOrgCode);
            // 成功的响应
            if(objResponse != null && String.valueOf(objResponse.getStatusCode()).startsWith('2')){
                Map<String, Object> mapJsonResponse = (Map<String, Object>)JSON.deserializeUntyped(objResponse.getBody());
                Map<String, Object> mapJsonBillingRequests = (Map<String, Object>)mapJsonResponse.get('billing_requests');
                List<Object> lstJsonActions = (List<Object>)mapJsonBillingRequests.get('actions');
                // 检查当前步骤是否完成
                for (Object jsonAction : lstJsonActions) {
                    Map<String, Object> action = (Map<String, Object>)jsonAction;
                    if((String)action.get('type') == 'collect_bank_account'){
                        // 已完成
                        if((String)action.get('status') == 'completed'){
                            response.status = 'OK';
                        }
                        // 未完成
                        else{
                            response.status = 'NG';
                            response.message = JSON.serialize(action.get('collect_bank_account'));
                        }
                        break;
                    }
                }
            }
            // 失败的响应
            else{
                GoCardlessError objGoCardlessError = parseGoCardlessError(objResponse);
                response.status = 'GCNG';
                response.data = objGoCardlessError;
            }
        } catch (Exception objEx) {
            response.message = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
            response.status = 'NG';
        }
        return response;
    }

    /**
     * GoCardless第四步：确认支付信息
     * @param strBillingRequestId *
     * @param strOrgCode *
     */
    @AuraEnabled
    public static ResultResponse confirmPayerDetails(String strBillingRequestId,String strOrgCode){
        ResultResponse response = new ResultResponse();
        String strEndPoint = 'billing_requests/' + strBillingRequestId + '/actions/confirm_payer_details';
        try {
            HttpResponse objResponse = CCM_GoCardlessCallout.SyncData(null, strEndPoint, 'POST', strOrgCode);
            // 成功的响应
            if(objResponse != null && String.valueOf(objResponse.getStatusCode()).startsWith('2')){
                Map<String, Object> mapJsonResponse = (Map<String, Object>)JSON.deserializeUntyped(objResponse.getBody());
                Map<String, Object> mapJsonBillingRequests = (Map<String, Object>)mapJsonResponse.get('billing_requests');
                if((String)mapJsonBillingRequests.get('status') == 'ready_to_fulfil'){
                    response.status = 'OK';
                }else{
                    List<Object> lstJsonActions = (List<Object>)mapJsonBillingRequests.get('actions');
                    // 检索未完成的步骤
                    for (Object jsonAction : lstJsonActions) {
                        Map<String, Object> action = (Map<String, Object>)jsonAction;
                        if(action.get('required') != null && (Boolean)action.get('required')){
                            if((String)action.get('status') != 'completed'){
                                response.status = 'NG';
                                response.message = JSON.serialize(action);
                                break;
                            }
                        }
                    }
                }
            }
            // 失败的响应
            else{
                GoCardlessError objGoCardlessError = parseGoCardlessError(objResponse);
                response.status = 'GCNG';
                response.data = objGoCardlessError;
            }
        } catch (Exception objEx) {
            response.message = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
            response.status = 'NG';
        }
        return response;
    }

    /**
     * GoCardless第五步：执行BillingRequest获取Mandate
     * @param strBillingRequestId *
     * @param strOrgCode *
     */
    @AuraEnabled
    public static ResultResponse fulfilBillingRequest(String strBillingRequestId,String strOrgCode){
        ResultResponse response = new ResultResponse();
        String strEndPoint = 'billing_requests/' + strBillingRequestId + '/actions/fulfil';
        try {
            HttpResponse objResponse = CCM_GoCardlessCallout.SyncData(null, strEndPoint, 'POST', strOrgCode);
            // 成功的响应
            if(objResponse != null && String.valueOf(objResponse.getStatusCode()).startsWith('2')){
                Map<String, Object> mapJsonResponse = (Map<String, Object>)JSON.deserializeUntyped(objResponse.getBody());
                Map<String, Object> mapJsonBillingRequests = (Map<String, Object>)mapJsonResponse.get('billing_requests');
                String strBillingRequestStatus = (String)mapJsonBillingRequests.get('status');
                if (strBillingRequestStatus == 'fulfilled') {
                    response.status = 'OK';
                    Map<String, Object> mapJsonMandateRequest = (Map<String, Object>)mapJsonBillingRequests.get('mandate_request');
                    Map<String, Object> mapJsonMandateRequestLinks = (Map<String, Object>)mapJsonMandateRequest.get('links');
                    String strMandateId = (String)mapJsonMandateRequestLinks.get('mandate');
                    System.debug('strMandateId -> ' + strMandateId);
                    GoCardlessWrapper objGoCardlessWrapper = new GoCardlessWrapper();
                    objGoCardlessWrapper.billingRequest.mandateId = strMandateId;
                    response.status = 'OK';
                    response.data = objGoCardlessWrapper;
                }else{
                    response.status = 'NG';
                    response.message = objResponse.getBody();
                }
            }
            // 失败的响应
            else{
                GoCardlessError objGoCardlessError = parseGoCardlessError(objResponse);
                response.status = 'GCNG';
                response.data = objGoCardlessError;
            }
        } catch (Exception objEx) {
            response.message = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
            response.status = 'NG';
        }
        return response;
    }

    /**
     * GoCardless第六步：发起支付
     * @param strMandateId *
     * @param decAmount *
     * @param strOrgCode *
     */
    @AuraEnabled
    public static ResultResponse createPayment(String strMandateId, Decimal decAmount, String strPaymentDescription, String strOrgCode){
        ResultResponse response = new ResultResponse();
        String strEndPoint = 'payments';
        try {
            Map<String,Object> mapRequestParam = new Map<String,Object>();
            Map<String,Object> mapPaymentsParam = new Map<String,Object>();
            Map<String,Object> mapLinksParam = new Map<String,Object>();
            mapRequestParam.put('payments', mapPaymentsParam);
            mapPaymentsParam.put('links', mapLinksParam);

            Decimal decPayAmount = decAmount * 100;
            mapPaymentsParam.put('amount', decPayAmount.setScale(0, System.RoundingMode.DOWN));
            mapPaymentsParam.put('description', strPaymentDescription);
            if (strOrgCode == 'CCA') {
                mapPaymentsParam.put('currency','CAD');
            }else{
                mapPaymentsParam.put('currency','USD');
            }
            mapLinksParam.put('mandate', strMandateId);

            HttpResponse objResponse = CCM_GoCardlessCallout.SyncData(JSON.serialize(mapRequestParam), strEndPoint, 'POST', strOrgCode);
            // 成功的响应
            if(objResponse != null && String.valueOf(objResponse.getStatusCode()).startsWith('2')){
                Map<String, Object> mapJsonResponse = (Map<String, Object>)JSON.deserializeUntyped(objResponse.getBody());
                Map<String, Object> mapJsonPayments = (Map<String, Object>)mapJsonResponse.get('payments');
                System.debug('mapJsonPayments -> ' + mapJsonPayments);
                GoCardlessWrapper objGoCardlessWrapper = new GoCardlessWrapper();
                objGoCardlessWrapper.payment.amount = (Integer)mapJsonPayments.get('amount');
                objGoCardlessWrapper.payment.status = (String)mapJsonPayments.get('status');
                if (objGoCardlessWrapper.payment.status != null) {
                    objGoCardlessWrapper.payment.status = statusFormat(objGoCardlessWrapper.payment.status);
                }
                objGoCardlessWrapper.payment.paymentId = (String)mapJsonPayments.get('id');
                objGoCardlessWrapper.payment.mandateId = strMandateId;
                response.status = 'OK';
                response.data = objGoCardlessWrapper;
            }
            // 失败的响应
            else{
                GoCardlessError objGoCardlessError = parseGoCardlessError(objResponse);
                response.status = 'GCNG';
                response.data = objGoCardlessError;
            }
        } catch (Exception objEx) {
            response.message = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
            response.status = 'NG';
        }
        return response;
    }

    /**
     * GoCardless第七步：创建Payment Info、更新Account Balance
     *
     */
    public static ResultResponse insertPaymentInfo(
        String strPaymentId, String strMandatetId, String strPaymentStatus, String invoicesList, String creditList, String selectMethod, Boolean isFirstPay,
        Decimal sumDiscountFee, Decimal sumCreditFee, Decimal actualTotalPay, String remark, String strPaymentOptions
    ) {
        ResultResponse response = new ResultResponse();
        try {
            PaymentOptions objPaymentOptions = (PaymentOptions)JSON.deserialize(strPaymentOptions, PaymentOptions.class);
            List<DataTableWrapper> paidInvoiceList = (List<DataTableWrapper>)JSON.deserialize(invoicesList, List<DataTableWrapper>.class);
            List<DataTableWrapper> paidCreditList = new List<DataTableWrapper>();

            if (String.isNotBlank(creditList) && creditList != '[""]') {
                paidCreditList = (List<DataTableWrapper>) JSON.deserialize(creditList, List<DataTableWrapper>.class);
            }

            User currentUser = [
                SELECT
                    Id, ContactId, Contact.AccountId, Contact.Account.CurrencyIsoCode
                    , Contact.Account.ORG_Code__c, Contact.Account.Name, Contact.Account.AccountNumber
                FROM
                    User
                WHERE
                    Id = :UserInfo.getUserId()
            ];

            // 插入Payment_Information__c记录
            Payment_Information__c paymentInfo = new Payment_Information__c();
            paymentInfo.Cash_Discount_Gross_Amount__c = sumDiscountFee;
            paymentInfo.Total_Credit_Memo_Amount__c = sumCreditFee;
            paymentInfo.Actual_Paid_Amount__c = actualTotalPay;
            paymentInfo.CurrencyIsoCode = currentUser.Contact.Account.CurrencyIsoCode == null ? '' : currentUser.Contact.Account.CurrencyIsoCode;
            paymentInfo.Customer__c = currentUser.Contact.AccountId;
            paymentInfo.ORG_Code__c = currentUser.Contact.Account.ORG_Code__c;
            List<Address_With_Program__c> billingAddress = [
                SELECT
                    Id, Account_Address__r.customer__c, Customer_Line_Oracle_ID__c
                FROM
                    Address_With_Program__c
                WHERE
                    Account_Address__r.customer__c = :currentUser.Contact.AccountId
                    AND Account_Address__r.RecordTypeId = :CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID
                    AND Customer_Line_Oracle_ID__c != null
            ];
            if (billingAddress != null && billingAddress.size() > 0) {
                paymentInfo.BillTo__c = billingAddress[0].Id;
            }
            paymentInfo.Pay_Method__c = selectMethod;
            paymentInfo.Total_Amount__c = actualTotalPay - sumCreditFee - sumDiscountFee;
            paymentInfo.Remark__c = remark;
            paymentInfo.Paypal_Status__c = 'Processing';
            paymentInfo.GoCardless_Payment_Status__c = strPaymentStatus;
            paymentInfo.GoCardless_Payment_Id__c = strPaymentId;
            paymentInfo.GoCardless_Mandate_Id__c = strMandatetId;
            paymentInfo.First_Pay_By_Paypal__c = isFirstPay;
            insert paymentInfo;
            response.paymentId = paymentInfo.Id;

            // 添加了备注时给AR发邮件
            String orgCode = currentUser.Contact.Account.ORG_Code__c;
            if (String.isNotBlank(remark)){
                String strPaymentName = [SELECT Name FROM Payment_Information__c WHERE Id = :paymentInfo.Id LIMIT 1].Name;
                sendRemarkEmail(strPaymentName, remark, currentUser.Contact.Account.Name, orgCode);
            }

            List<String> invoiceNumbers = new List<String>();
            // PaymentInfoItem(Invoice)
            List<Payment_Information_Item__c> paymentItemsWithSequence = new List<Payment_Information_Item__c>();
            List<Payment_Information_Item__c> paymentItemsWithoutSequence = new List<Payment_Information_Item__c>();
            Map<String, List<AccountingInfo>> accountingItemWithInvoiceNumber = new Map<String, List<AccountingInfo>>();
            Map<String, List<AccountingInfo>> accountingItemWithId = new Map<String, List<AccountingInfo>>();
            Set<String> ids = new Set<String>();
            List<Payment_Information_Item__c> paymentItemAccountings = new List<Payment_Information_Item__c>();
            for (DataTableWrapper invoice : paidInvoiceList) {
                invoiceNumbers.add(invoice.invoiceNumber);
                if (invoice.hasNoSequence) {
                    Payment_Information_Item__c paymentItem = new Payment_Information_Item__c();
                    paymentItem.Actual_Paid_Amount__c = invoice.totalPaymentFee;
                    paymentItem.Cash_Discount_Amount__c = invoice.discountFee;
                    paymentItem.CurrencyIsoCode = invoice.currencyType;
                    paymentItem.Invoice__c = invoice.id;
                    paymentItem.Payment_Information__c = paymentInfo.id;
                    paymentItem.Invoice_Type__c = invoice.invoiceType;
                    paymentItem.Total_Amount__c = invoice.dueRemainingAmt;
                    paymentItem.Has_No_Sequence__c = invoice.hasNoSequence;
                    paymentItemsWithSequence.add(paymentItem);
                } else {
                    Payment_Information_Item__c paymentItem = new Payment_Information_Item__c();
                    paymentItem.CurrencyIsoCode = invoice.currencyType;
                    paymentItem.Invoice__c = invoice.id;
                    paymentItem.Payment_Information__c = paymentInfo.id;
                    paymentItem.Invoice_Type__c = invoice.invoiceType;
                    paymentItem.Has_No_Sequence__c = invoice.hasNoSequence;
                    paymentItemsWithoutSequence.add(paymentItem);
                    if (!accountingItemWithInvoiceNumber.containsKey(invoice.invoiceNumber)) {
                        accountingItemWithInvoiceNumber.put(invoice.id, invoice.accountingList);
                    }
                }
            }
            insert paymentItemsWithSequence;
            insert paymentItemsWithoutSequence;
            for (Payment_Information_Item__c item : paymentItemsWithoutSequence) {
                ids.add(item.Id);
                accountingItemWithId.put(item.Id, accountingItemWithInvoiceNumber.get(item.Invoice__c));
            }
            List<Payment_Information_Item__c> queryPaymentItemAccountingList = [
                SELECT
                    Id, Invoice__r.Invoice_Number__c
                FROM
                    Payment_Information_Item__c
                WHERE
                    Id IN :ids
            ];
            for (Payment_Information_Item__c invoice : queryPaymentItemAccountingList) {
                if (accountingItemWithId.containsKey(invoice.Id)) {
                    for (AccountingInfo accountingBalance : accountingItemWithId.get(invoice.Id)) {
                        Payment_Information_Item__c paymentItemAccounting = new Payment_Information_Item__c();
                        paymentItemAccounting.Accounting_Balance__c = accountingBalance.Id;
                        paymentItemAccounting.Payment_Information_Item_accounting__c = invoice.Id;
                        paymentItemAccounting.Actual_Paid_Amount__c = (accountingBalance.dueRemainingAmount + accountingBalance.discountFee).setScale(2);
                        paymentItemAccounting.Cash_Discount_Amount__c = accountingBalance.discountFee;
                        paymentItemAccounting.Total_Amount__c = accountingBalance.dueRemainingAmount;
                        paymentItemAccountings.add(paymentItemAccounting);
                    }
                }
            }
            insert paymentItemAccountings;

            // PaymentInfoItem(Credit)
            List<Payment_Information_Item__c> paymentCreditItems = new List<Payment_Information_Item__c>();
            if (paidCreditList.size() > 0) {
                for (DataTableWrapper credit : paidCreditList) {
                    Payment_Information_Item__c paymentItem = new Payment_Information_Item__c();
                    paymentItem.CurrencyIsoCode = credit.currencyType;
                    paymentItem.Invoice__c = credit.id;
                    paymentItem.Payment_Information__c = paymentInfo.id;
                    paymentItem.Total_Amount__c = credit.dueRemainingAmt;
                    paymentItem.Actual_Paid_Amount__c = credit.dueRemainingAmt;
                    paymentItem.Invoice_Type__c = credit.invoiceType;
                    paymentCreditItems.add(paymentItem);
                }
            }
            insert paymentCreditItems;

            List<Accounting_Balance__c> invoices = new List<Accounting_Balance__c>();
            List<Accounting_Balance__c> accountings = new List<Accounting_Balance__c>();
            for (DataTableWrapper paidInvoice : paidInvoiceList) {
                if (paidInvoice.hasNoSequence) {
                    List<Accounting_Balance__c> invoice = [
                        SELECT
                            Id, Invoice__c, Paypal_Status__c
                        FROM
                            Accounting_Balance__c
                        WHERE
                            Invoice__c = :paidInvoice.id
                    ];
                    for (Accounting_Balance__c var : invoice) {
                        if(strPaymentStatus == 'Pending Submission'){
                            var.Paypal_Status__c = 'Processing';
                        }
                    }
                    invoices.addAll(invoice);
                } else {
                    Set<String> setPaidAccountingId = new Set<String>();
                    for (AccountingInfo paidAccounting : paidInvoice.accountingList) {
                        setPaidAccountingId.add(paidAccounting.id);
                    }
                    List<Accounting_Balance__c> lstAccounting = [
                        SELECT
                            Id, Paypal_Status__c
                        FROM
                            Accounting_Balance__c
                        WHERE
                            Id IN :setPaidAccountingId
                    ];
                    for (Accounting_Balance__c accountBalance : lstAccounting) {
                        if(strPaymentStatus == 'Pending Submission'){
                            accountBalance.Paypal_Status__c = 'Processing';
                        }
                    }
                    accountings.addAll(lstAccounting);
                }
            }
            update invoices;
            update accountings;
            if (paidCreditList.size() > 0) {
                Set<String> setPaidCreditId = new Set<String>();
                for (DataTableWrapper paidCredit : paidCreditList) {
                    setPaidCreditId.add(paidCredit.id);
                }
                List<Accounting_Balance__c> lstCredit = [
                    SELECT
                        Id, Invoice__c, Paypal_Status__c
                    FROM
                        Accounting_Balance__c
                    WHERE
                        Invoice__c IN :setPaidCreditId
                ];
                for (Accounting_Balance__c credit : lstCredit) {
                    if(strPaymentStatus == 'Pending Submission'){
                        credit.Paypal_Status__c = 'Processing';
                    }
                }
                update lstCredit;
            }

            // 更新Mandate最后一次使用时间
            List<Mandate__c> lstMandate = [
                SELECT
                    Id, Mandate_Id__c, Last_Used_Date__c
                FROM
                    Mandate__c
                WHERE
                    Mandate_Id__c = :strMandatetId
            ];
            if(lstMandate != null && lstMandate.size() > 0){
                lstMandate[0].Last_Used_Date__c = Datetime.now();
                update lstMandate;
            }
            response.status = 'OK';
        } catch (Exception objEx) {
            response.message = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
            response.status = 'NG';
        }
        return response;
    }

    /**
     * GoCardless：保存Mandate信息
     */
    public static ResultResponse saveMandateInfo(String strMandateId, String strPaymentOptions){
        ResultResponse response = new ResultResponse();
        try {
            PaymentOptions objPaymentOptions = (PaymentOptions)JSON.deserialize(strPaymentOptions, PaymentOptions.class);
            User currentUser = [
                SELECT
                    Id, ContactId, Contact.AccountId, Contact.Account.CurrencyIsoCode
                    , Contact.Account.ORG_Code__c, Contact.Account.Name, Contact.Account.AccountNumber
                FROM
                    User
                WHERE
                    Id = :UserInfo.getUserId()
            ];
            Mandate__c objMandate = new Mandate__c();
            objMandate.Contact__c = currentUser.ContactId;
            objMandate.Customer_Name__c = objPaymentOptions.goCardlessCustomerName;
            objMandate.Email__c = objPaymentOptions.email;
            objMandate.Routing_Number__c = objPaymentOptions.routingNumber;
            objMandate.Bank_Code__c = objPaymentOptions.bankCode;
            objMandate.Branch_Code__c = objPaymentOptions.branchCode;
            objMandate.Bank_Account_Number__c = maskBankAccount(objPaymentOptions.bankAccountNumber);
            objMandate.Account_Holder_Name__c = objPaymentOptions.accountHolderName;
            objMandate.Mandate_Id__c = strMandateId;
            Database.insert(objMandate);
            response.status = 'OK';
        } catch (Exception objEx) {
            response.message = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
            response.status = 'NG';
        }
        return response;
    }

    /**
     * GoCardless第四、五、六、七、八步整合执行：确认、发起支付、创建Payment Info记录、创建Mandate记录
     */
    @AuraEnabled
    public static ResultResponse confirmAndBuildPay(
        String strBillingRequestId, String strMandateId, Decimal decAmount, String strOrgCode, String invoiceList, String creditList, String selectMethod, Boolean isFirstPay,
        Decimal sumDiscountFee, Decimal sumCreditFee, Decimal actualTotalPay, String remark, String paymentOptions
    ){
        ResultResponse response = new ResultResponse();
        GoCardlessWrapper objGoCardlessWrapper;
        try {
            User currentUser = [SELECT Id, ContactId, Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId()];
            List<DataTableWrapper> lstPaidInvoice = (List<DataTableWrapper>)JSON.deserialize(invoiceList, List<DataTableWrapper>.class);
            List<DataTableWrapper> lstPaidCredit = new List<DataTableWrapper>();
            if (String.isNotBlank(creditList) && creditList != '[""]') {
                lstPaidCredit = (List<DataTableWrapper>) JSON.deserialize(creditList, List<DataTableWrapper>.class);
            }
            List<String> lstInvoiceNumber = new List<String>();
            for (DataTableWrapper objInvoice : lstPaidInvoice) {
                lstInvoiceNumber.add(objInvoice.invoiceNumber);
            }
            String strPaymentDescription = 'Payment of invoices ' + lstInvoiceNumber;
            if (String.isBlank(strMandateId)) {
                response = confirmPayerDetails(strBillingRequestId, strOrgCode);
                if (response.status != 'OK') {
                    return response;
                }
                response = checkBeforeSubmitPay(lstPaidInvoice, lstPaidCredit, sumDiscountFee, isFirstPay);
                if (response.status != 'OK') {
                    return response;
                }
                response = fulfilBillingRequest(strBillingRequestId, strOrgCode);
                if (response.status != 'OK') {
                    return response;
                }
                objGoCardlessWrapper = (GoCardlessWrapper)response.data;
                response = createPayment(objGoCardlessWrapper.billingRequest.mandateId, decAmount, strPaymentDescription, strOrgCode);
                if (response.status != 'OK') {
                    return response;
                }
                saveMandateInfo(objGoCardlessWrapper.billingRequest.mandateId, paymentOptions);
            }else{
                response = checkBeforeSubmitPay(lstPaidInvoice, lstPaidCredit, sumDiscountFee, isFirstPay);
                if (response.status != 'OK') {
                    return response;
                }
                response = createPayment(strMandateId, decAmount, strPaymentDescription, strOrgCode);
                if (response.status != 'OK') {
                    return response;
                }
            }
            objGoCardlessWrapper = (GoCardlessWrapper)response.data;
            response = insertPaymentInfo(objGoCardlessWrapper.payment.paymentId, objGoCardlessWrapper.payment.mandateId, objGoCardlessWrapper.payment.status, invoiceList, creditList, selectMethod, isFirstPay, sumDiscountFee, sumCreditFee, actualTotalPay, remark, paymentOptions);
        } catch (Exception objEx) {
            response.message = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
            response.status = 'NG';
        }
        return response;
    }

    /**
     * @description: 发起支付前的二次校验
     * @date: 24.2.4
     */
    public static ResultResponse checkBeforeSubmitPay(List<DataTableWrapper> lstPaidInvoice, List<DataTableWrapper> lstPaidCredit, Decimal decSumDiscountFee, Boolean isFirstPay){
        Set<String> setAccountingId = new Set<String>();
        Set<String> setInvoiceId = new Set<String>();
        for (DataTableWrapper objInvoice : lstPaidInvoice) {
            setInvoiceId.add(objInvoice.Id);
            if(objInvoice.accountingList != null){
                for (AccountingInfo objAccountingBalance : objInvoice.accountingList) {
                    setAccountingId.add(objAccountingBalance.Id);
                }
            }
        }
        for (DataTableWrapper objCredit : lstPaidCredit) {
            setInvoiceId.add(objCredit.Id);
            if(objCredit.accountingList != null){
                for (AccountingInfo objAccountingBalance : objCredit.accountingList) {
                    setAccountingId.add(objAccountingBalance.Id);
                }
            }
        }
        // 二次检查Accounting Balance是否已经支付
        Integer intPaidAccountingCount = [
            SELECT
                COUNT()
            FROM
                Accounting_Balance__c
            WHERE
                Id IN :setAccountingId
                AND (
                    Paypal_Status__c IN ('Payment Success', 'Processing')
                    OR Amt_Due_Remaining__c = 0
                )
        ];
        if(intPaidAccountingCount > 0){
            return new ResultResponse('CHECKNG', 'There are invoices have been successfully paid or are currently being processed in the invoices you submitted for payment, please refresh the page and try again.', null);
        }
        // 二次检查Invoice是否已经支付
        Map<Id, Invoice__c> mapIdToInvoice = new Map<Id, Invoice__c>([
            SELECT
                Id, Payment_Terms_Record__c, isPaid__c
            FROM
                Invoice__c
            WHERE
                Id IN :setInvoiceId
        ]);
        for(Invoice__c objInvoice : mapIdToInvoice.values()){
            if(objInvoice.isPaid__c){
                return new ResultResponse('CHECKNG', 'There are invoices have been successfully paid in the invoices you submitted for payment, please refresh the page and try again.', null);
            }
        }
        // 二次检查折扣金额是否正确
        for(DataTableWrapper objPaidInvoice : lstPaidInvoice){
            Invoice__c objInvoice = mapIdToInvoice.get(objPaidInvoice.Id);
            objPaidInvoice.paymentTermString = objInvoice.Payment_Terms_Record__c;
        }
        ReturnData objDiscountInfo = (ReturnData)JSON.deserialize(getDiscount(JSON.serialize(lstPaidInvoice)), ReturnData.class);
        if(objDiscountInfo.sumDiscountFee != decSumDiscountFee){
            return new ResultResponse('CHECKNG', 'There are invoices failed to pass the discount verification in the invoices you submitted for payment, please refresh the page and try again.', null);
        }
        // 二次检查是否符合首次支付
        if (isFirstPay) {
            User currentUser = [
                SELECT
                    Id, ContactId, Contact.AccountId, Contact.Account.CurrencyIsoCode, Contact.Account.ORG_Code__c, Contact.Account.name, Contact.Account.AccountNumber
                FROM
                    User
                WHERE
                    Id = :UserInfo.getUserId()
            ];
            Date discountEffectiveDate = Date.valueOf(Label.Date_of_discount);
            Integer intPayCount = [
                SELECT
                    COUNT()
                FROM
                    Payment_Information__c
                WHERE
                    Customer__c = :currentUser.Contact.AccountId
                    AND (
                        (Paypal_Status__c = 'Payment Success' AND Payment_Date__c > :discountEffectiveDate)
                        OR (Paypal_Status__c = 'Processing' AND First_Pay_By_Paypal__c = true)
                    )
            ];
            if(intPayCount > 0){
                return new ResultResponse('CHECKNG', 'You do not meet the conditions for using One Time Online Payment Promotion, please refresh the page and try again.', null);
            }
        }
        return new ResultResponse('OK', null, null);
    }

    /**
     * GoCardless：获取当前用户是否有可用的Mandate
     */
    @AuraEnabled
    public static ResultResponse getMandateInfo(){
        ResultResponse response = new ResultResponse();
        try {
            User currentUser = [
                SELECT
                    Id, ContactId, Contact.AccountId, Contact.Account.CurrencyIsoCode
                    , Contact.Account.ORG_Code__c, Contact.Account.Name, Contact.Account.AccountNumber
                FROM
                    User
                WHERE
                    Id = :UserInfo.getUserId()
            ];
            List<Mandate__c> lstMandate = [
                SELECT
                    Id, Contact__c, Mandate_Id__c, Email__c, Routing_Number__c, Bank_Code__c, Branch_Code__c, Bank_Account_Number__c
                    , Account_Holder_Name__c, Customer_Name__c, Last_Used_Date__c
                FROM
                    Mandate__c
                WHERE
                    Contact__c = :currentUser.ContactId
                ORDER BY
                    Last_Used_Date__c DESC
            ];
            response.data = lstMandate;
            response.status = 'OK';
        } catch (Exception objEx) {
            response.message = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
            response.status = 'NG';
        }
        return response;
    }

    /**
     * @description: 解析GoCardless错误信息。
     */
    public static GoCardlessError parseGoCardlessError(HttpResponse objResponseBody){
        GoCardlessError objError = new GoCardlessError();
        try {
            Map<String, Object> mapJsonResponse = (Map<String, Object>)JSON.deserializeUntyped(objResponseBody.getBody());
            Map<String, Object> mapJsonError = (Map<String, Object>)mapJsonResponse.get('error');
            if (mapJsonError != null) {
                objError = (GoCardlessError)JSON.deserialize(JSON.serialize(mapJsonError), GoCardlessError.class);
            }else{
                objError.message = objResponseBody.getBody();
            }
        } catch (Exception objEx) {
            objError.message = 'ExceptionMessage: ' + objEx.getMessage() + '\n '
            + 'ExceptionType: ' + objEx.getTypeName() + '\n '
            + 'ExceptionLine: ' + objEx.getLineNumber() + '\n '
            + 'ExceptionStackTrace: \n ' + objEx.getStackTraceString();
        }
        return objError;
    }

    /**
     * @description: GoCardless Status格式转换
     * 例如：pending_submission -> Pending Submission
     */
    public static String statusFormat(String strOriginVal){
        if (String.isBlank(strOriginVal)) {
            return null;
        }
        List<String> lstPart = strOriginVal.split('_');
        for (Integer i = 0; i < lstPart.size(); i++) {
            lstPart[i] = lstPart[i].capitalize();
        }
        String strTargetVal = String.join(lstPart, ' ');
        return strTargetVal;
    }

    /**
     * @description: 银行账户加密
     * 例如：********** -> ******7890
     */
    public static String maskBankAccount(String strOriginVal){
        if (String.isBlank(strOriginVal)) {
            return null;
        }
        Integer intKeepDigits = 4;
        Integer intLength = strOriginVal.length();
        if (intLength < 7) {
            intKeepDigits = 2;
        }
        String strMasked = '';
        for (Integer i = 0; i < intLength - intKeepDigits; i++) {
            strMasked += '*';
        }
        strMasked += strOriginVal.substring(intLength - intKeepDigits, intLength);
        return strMasked;
    }


    // add by haibo
    @AuraEnabled
    public static CCM_MonerisUtil.Response createPaymentByMonerisUtil(String reqBodyStr,String invoicesList, String creditList, String selectMethod, Decimal sumDiscountFee, Decimal sumCreditFee, Decimal actualTotalPay, String remark, Boolean isFirstPay, String paymentOptions,String paymentInfoId){

        paymentOptions paypalInfo = (paymentOptions) JSON.deserialize(paymentOptions, paymentOptions.class);
        List<DataTableWrapper> paidInvoiceList = (List<DataTableWrapper>) JSON.deserialize(invoicesList, List<DataTableWrapper>.class);
        List<DataTableWrapper> paidCreditList = new List<DataTableWrapper>();
        if (String.isNotBlank(creditList) && creditList != '[""]') {
            paidCreditList = (List<DataTableWrapper>) JSON.deserialize(creditList, List<DataTableWrapper>.class);
        }
        System.debug(LoggingLevel.INFO, '*** paymentInfoId: ' + paymentInfoId);
        //调用支付接口前推送数据
        // pushBefPaymentInfo(paymentInfoId);

        //调paypal接口支付
         // paypalResponse result = usePaypal(selectMethod, actualTotalPay, paypalInfo);
        CCM_MonerisUtil.Response result = new CCM_MonerisUtil.Response();
        CCM_MonerisUtil.RequestBody reqBody = (CCM_MonerisUtil.RequestBody)Json.deserialize(reqBodyStr, CCM_MonerisUtil.RequestBody.class);
        result = CCM_MonerisUtil.createPayment(reqBodyStr);//Callout Moneris 付款
        System.debug(LoggingLevel.INFO, '*** result: ' + result);

        //根据支付结果更新paymentInfo，invoice，accounting_balance
        Payment_Information__c payment = [
            SELECT Id,Payment_Date__c
            FROM Payment_Information__c
            WHERE id = :paymentInfoId
        ];
        // payment.PNREF__c = result.PNREF;

        if (Test.isRunningTest()) {
            CCM_Service.pushPaymentInfo(paymentInfoId);
        }
            payment.Payment_Date__c = Date.today();
            if (result.resultCode == 201) { // 如果其他支付成功场景，编码是什么呢？
                System.debug(LoggingLevel.INFO, '*** 11111: ');
                payment.Paypal_Status__c = 'Payment Success';
                //传到oracle核销
                CCM_Service.pushPaymentInfo(paymentInfoId);
                CCM_Service.pushBeforePaymentInfo(paymentInfoId,true);
                System.debug(LoggingLevel.INFO, '*** 22222: ');
            } else {
                System.debug(LoggingLevel.INFO, '*** 33333: ');
                payment.Paypal_Status__c = 'Payment Failed';
                CCM_Service.pushBeforePaymentInfo(paymentInfoId,false);
                System.debug(LoggingLevel.INFO, '*** 44444: ');

            }
        update payment;
        List<Accounting_Balance__c> invoices =new List<Accounting_Balance__c>();
        List<Accounting_Balance__c> accountings =new List<Accounting_Balance__c>();
        for (DataTableWrapper paidInvoice : paidInvoiceList) {
            if (paidInvoice.hasNoSequence) {
                List<Accounting_Balance__c> invoice = [
                    SELECT a.Id, a.Invoice__c, a.Paypal_Status__c
                    FROM Accounting_Balance__c a
                    WHERE a.Invoice__c = :paidInvoice.id
                ];
                for (Accounting_Balance__c var : invoice) {
                        if (result.resultCode == 201) {
                            var.Paypal_Status__c = 'Payment Success';
                        } else {
                            var.Paypal_Status__c = 'Payment Failed';
                        }
                }
                invoices.addAll(invoice);
            } else {
                for (AccountingInfo paidAccounting : paidInvoice.accountingList) {
                    List<Accounting_Balance__c> accounting = [
                        SELECT Paypal_Status__c
                        FROM Accounting_Balance__c
                        WHERE ID = :paidAccounting.id
                        ];
                    for (Accounting_Balance__c var2 : accounting) {
                            if (result.resultCode == 201) {
                                var2.Paypal_Status__c = 'Payment Success';
                            } else {
                                var2.Paypal_Status__c = 'Payment Failed';
                            }
                    }
                    accountings.addAll(accounting);
                }
            }
        }
        update invoices;
        update accountings;
        List<Accounting_Balance__c> credits=new List<Accounting_Balance__c>();
        if (paidCreditList.size() > 0) {
            for (DataTableWrapper paidCredit : paidCreditList) {
                Accounting_Balance__c credit = [
                    SELECT a.Id, a.Invoice__c, a.Paypal_Status__c
                    FROM Accounting_Balance__c a
                    WHERE a.Invoice__c = :paidCredit.id
                ];
                    if (result.resultCode == 201) {
                        credit.Paypal_Status__c = 'Payment Success';
                    } else {
                        credit.Paypal_Status__c = 'Payment Failed';
                    }
                credits.add(credit);
            }
            update credits;
        }
        List<String> invoiceNumbers=new List<String>();
        // String url = URL.getOrgDomainUrl().toExternalForm()+'/';
        String name=[select Name from Payment_Information__c where id=:paymentInfoId limit 1].name;
        for (DataTableWrapper invoice : paidInvoiceList) {
            invoiceNumbers.add(invoice.invoiceNumber);
        }
        String numbers=String.join(invoiceNumbers,',');
        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
        selectMethod = selectMethod.replaceAll(' CCA','');
        if (selectMethod == 'Debit Card' || selectMethod == 'Credit Card') {
            if (result.resultCode == 201) {
                email=sendMonerisResultEmail(true,paypalInfo.email,actualTotalPay,name,result.respMsg,numbers);
            } else {
                email=sendMonerisResultEmail(false,paypalInfo.email,actualTotalPay,name,result.respMsg,numbers);
            }
            if(email!=null) {
                Messaging.SendEmailResult[] r = Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{email});
            }
        }
        // pushBefPaymentInfo(paymentInfo.Id);
        return result;
    }
    @AuraEnabled
    public static String CreatePaymentHistory(String reqBodyStr,String invoicesList, String creditList, String selectMethod, Decimal sumDiscountFee, Decimal sumCreditFee, Decimal actualTotalPay, String remark, Boolean isFirstPay, String paymentOptions){
        paymentOptions paypalInfo = (paymentOptions) JSON.deserialize(paymentOptions, paymentOptions.class);
        List<DataTableWrapper> paidInvoiceList = (List<DataTableWrapper>) JSON.deserialize(invoicesList, List<DataTableWrapper>.class);
        List<DataTableWrapper> paidCreditList = new List<DataTableWrapper>();
        CCM_MonerisUtil.RequestBody reqBody = (CCM_MonerisUtil.RequestBody)Json.deserialize(reqBodyStr, CCM_MonerisUtil.RequestBody.class);
        CCM_MonerisUtil.Response res = new CCM_MonerisUtil.Response();
        if (String.isNotBlank(creditList) && creditList != '[""]') {
            paidCreditList = (List<DataTableWrapper>) JSON.deserialize(creditList, List<DataTableWrapper>.class);
        }

        User currentUser = [SELECT Id, ContactId, Contact.AccountId, Contact.Account.CurrencyIsoCode, Contact.Account.ORG_Code__c,Contact.Account.name,Contact.Account.AccountNumber FROM User WHERE Id = :UserInfo.getUserId()];
        //二次验证
        Date discountEffectiveDate =Date.valueOf(Label.Date_of_discount);
        Integer payCount = [SELECT COUNT() FROM Payment_Information__c WHERE Customer__c = :currentUser.Contact.AccountId AND Paypal_Status__c In ('Payment Success','Processing') AND Payment_Date__c>:discountEffectiveDate];

        if (isFirstPay && payCount != 0) {
            return JSON.serialize('error');
        }
        for (DataTableWrapper tar : paidInvoiceList) {
            if (tar.invoiceStatus == 'closed') {
                return JSON.serialize('error');
            }
            for (AccountingInfo var2 : tar.accountingList) {
                if (var2.paypalStatus == 'Payment Success'||var2.paypalStatus == 'Processing') {
                    return JSON.serialize('error');
                }
            }
        }

        //payment information:
        Payment_Information__c paymentInfo = new Payment_Information__c();
        paymentInfo.Cash_Discount_Gross_Amount__c = sumDiscountFee;
        paymentInfo.Total_Credit_Memo_Amount__c = sumCreditFee;
        paymentInfo.Actual_Paid_Amount__c = actualTotalPay;
        paymentInfo.CurrencyIsoCode = 'CAD';
        paymentInfo.Customer__c = currentUser.Contact.AccountId;
        paymentInfo.ORG_Code__c = currentUser.Contact.Account.ORG_Code__c;
        paymentInfo.Pay_Method__c = selectMethod.replaceAll(' CCA','');
        if (selectMethod == 'Debit Card' || selectMethod == 'Credit Card') {
            paymentInfo.Total_Amount__c = actualTotalPay-sumCreditFee;
        } else {
            paymentInfo.Total_Amount__c = actualTotalPay-sumCreditFee-sumDiscountFee;
        }
        paymentInfo.Remark__c = remark;
        paymentInfo.First_Pay_By_Paypal__c = isFirstPay;

        Address_With_Program__c[] billingAddress = [
            SELECT
                a.Id,
                a.Account_Address__r.customer__c,
                a.Customer_Line_Oracle_ID__c
            FROM Address_With_Program__c a
            WHERE
                a.Account_Address__r.customer__c = :currentUser.Contact.AccountId
                AND a.Account_Address__r.RecordType_name__c = 'Billing_Address'
                AND a.Customer_Line_Oracle_ID__c != NULL
        ];
        if (billingAddress.size() != 0) {
            paymentInfo.BillTo__c = billingAddress[0].Id;
        }

        String cardNumber =  reqBody.PaymentMethod.Card.cardNumber;
        if (String.isNotBlank(cardNumber)) {
            paymentInfo.Card_Last_Four__c = cardNumber.substring(cardNumber.length() - 4, cardNumber.length());
        }
        paymentInfo.Payment_Date__c = Date.today();
        System.debug(LoggingLevel.INFO, '***paymentInfo-->zoe : ' + paymentInfo);
        insert paymentInfo;

        //用于邮件拼接
        //添加了备注时给AR发邮件提醒
        String orgCode = currentUser.Contact.Account.ORG_Code__c;
        String name=[select Name from Payment_Information__c where id=:paymentInfo.id limit 1].name;
        if (String.isNotBlank(remark) && String.isNotEmpty(remark)){
            sendRemarkEmail(name, remark, currentUser.Contact.Account.name, orgCode);
        }
        //用于邮件拼接
        List<String> invoiceNumbers=new List<String>();
        //paymentInfo_Item(invoice)
        List<Payment_Information_Item__c> paymentItems=new List<Payment_Information_Item__c>();
        List<Payment_Information_Item__c> paymentItems2=new List<Payment_Information_Item__c>();
        Map<String, List<AccountingInfo>> accountingItemWithInvoiceNumber = new Map<String, List<AccountingInfo>>();
        Map<String, List<AccountingInfo>> accountingItemWithId = new Map<String, List<AccountingInfo>>();
        Set<String> ids=new Set<String>();
        List<Payment_Information_Item__c> paymentItemAccountings=new List<Payment_Information_Item__c>();
        for (DataTableWrapper invoice : paidInvoiceList) {
            invoiceNumbers.add(invoice.invoiceNumber);
            if (invoice.hasNoSequence) {
                Payment_Information_Item__c paymentItem = new Payment_Information_Item__c();
                paymentItem.Actual_Paid_Amount__c = invoice.totalPaymentFee;
                paymentItem.Cash_Discount_Amount__c = invoice.discountFee;
                // paymentItem.CreatedDate = Date.today();
                paymentItem.CurrencyIsoCode = invoice.currencyType;
                paymentItem.Invoice__c = invoice.id;
                paymentItem.Payment_Information__c = paymentInfo.id;
                paymentItem.Invoice_Type__c = invoice.invoiceType;
                paymentItem.Total_Amount__c = invoice.dueRemainingAmt;
                paymentItem.Has_No_Sequence__c = invoice.hasNoSequence;
                paymentItems.add(paymentItem);
            } else {
                Payment_Information_Item__c paymentItem2 = new Payment_Information_Item__c();
                // paymentItem2.CreatedDate = Date.today();
                paymentItem2.CurrencyIsoCode = invoice.currencyType;
                paymentItem2.Invoice__c = invoice.id;
                paymentItem2.Payment_Information__c = paymentInfo.id;
                paymentItem2.Invoice_Type__c = invoice.invoiceType;
                paymentItem2.Has_No_Sequence__c = invoice.hasNoSequence;
                paymentItems2.add(paymentItem2);
                if (!accountingItemWithInvoiceNumber.containsKey(invoice.invoiceNumber)) {
                    accountingItemWithInvoiceNumber.put(invoice.id, invoice.accountingList);
                }
            }
        }
        insert paymentItems;
        insert paymentItems2;
        for (Payment_Information_Item__c item : paymentItems2) {
            ids.add(item.Id);
            accountingItemWithId.put(item.Id, accountingItemWithInvoiceNumber.get(item.Invoice__c));
        }
        List<Payment_Information_Item__c> queryPaymentItemAccountingList =[
                            SELECT
                                Id,
                                Invoice__r.Invoice_Number__c
                            FROM Payment_Information_Item__c
                            WHERE
                                Id In :ids];
        for (Payment_Information_Item__c invoice : queryPaymentItemAccountingList) {
            if (accountingItemWithId.containsKey(invoice.Id)) {
                for (AccountingInfo accountingBalance : accountingItemWithId.get(invoice.Id)) {
                    Payment_Information_Item__c paymentItemAccounting = new Payment_Information_Item__c();
                    paymentItemAccounting.Accounting_Balance__c = accountingBalance.Id;
                    paymentItemAccounting.Payment_Information_Item_accounting__c = invoice.Id;
                    paymentItemAccounting.Actual_Paid_Amount__c = (accountingBalance.dueRemainingAmount + accountingBalance.discountFee).setScale(2);
                    paymentItemAccounting.Cash_Discount_Amount__c = accountingBalance.discountFee;
                    paymentItemAccounting.Total_Amount__c = accountingBalance.dueRemainingAmount;
                    paymentItemAccountings.add(paymentItemAccounting);
                }
            }
        }
        insert paymentItemAccountings;


        //paymentInfo_Item(credit)
        List<Payment_Information_Item__c> paymentItems3=new List<Payment_Information_Item__c>();
        if (paidCreditList.size() > 0) {
            for (DataTableWrapper credit : paidCreditList) {
                Payment_Information_Item__c paymentItem3 = new Payment_Information_Item__c();
                paymentItem3.CurrencyIsoCode = credit.currencyType;
                paymentItem3.Invoice__c = credit.id;
                paymentItem3.Payment_Information__c = paymentInfo.id;
                paymentItem3.Total_Amount__c = credit.dueRemainingAmt;
                paymentItem3.Actual_Paid_Amount__c = credit.dueRemainingAmt;
                paymentItem3.Invoice_Type__c = credit.invoiceType;
                paymentItems3.add(paymentItem3);
            }
        }
        insert paymentItems3;

        return paymentInfo.Id;

        // return res;
    }
    public class FilterWrapper {
        public String orderNumber { get; set; }
        public String invoiceNumber { get; set; }
        public String cutomerPoNum { get; set; }
        public String invoiceDateMin { get; set; }
        public String invoiceDateMax { get; set; }
        public String status { get; set; }
        public String invoiceType { get; set; }
    }

    public class PaymentOptions {
        public String cardType { get; set; }
        public String cardNumber { get; set; }
        public String expirationDate { get; set; }
        public String invoiceNumber { get; set; }
        public String nameOnCard { get; set; }
        public String name { get; set; }
        public String month { get; set; }
        public String year { get; set; }
        public String address1 { get; set; }
        public String address2 { get; set; }
        public String city { get; set; }
        public String state { get; set; }
        public String postalCode { get; set; }
        public String country { get; set; }
        public String email { get; set; }
        public String firstName { get; set; }
        public String lastName { get; set; }
        public String phoneNum { get; set; }
        public String street { get; set; }
        public String routingNumber { get; set; }
        public String routingNumber2 { get; set; }
        public String accountNumber { get; set; }
        public String accountNumber2 { get; set; }
        public String companyName { get; set; }
        public String bankCode { get; set; }
        public String branchCode { get; set; }
        public String bankAccountNumber { get; set; }
        public String accountHolderName { get; set; }
        public String goCardlessCustomerName { get; set; }
    }

    public class PaypalResponse {
        public String resultCode { get; set; }
        public String respMsg { get; set; }
        public String PNREF { get; set; }
    }

    //Order List View
    public class DataTableWrapper {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String currencyType { get; set; }
        @AuraEnabled
        public String invoiceNumber { get; set; }
        @AuraEnabled
        public String invoiceStatus { get; set; }
        @AuraEnabled
        public String paypalStatus { get; set; }
        @AuraEnabled
        public String invoiceType { get; set; }
        @AuraEnabled
        public String orderDate { get; set; }
        @AuraEnabled
        public String invoiceDate { get; set; }
        @AuraEnabled
        public String poNumber { get; set; }
        @AuraEnabled
        public String overdue { get; set; } // Yes/No
        @AuraEnabled
        public Integer dueDays { get; set; }
        @AuraEnabled
        public Decimal originalAmt { get; set; }
        @AuraEnabled
        public Decimal othersFee { get; set; }
        @AuraEnabled
        public Decimal dueRemainingAmt { get; set; }
        // @AuraEnabled public Decimal currentDueAmt {get; set;}
        // @AuraEnabled public Decimal dueAmt1 {get; set;}
        // @AuraEnabled public Decimal dueAmt2 {get; set;}
        // @AuraEnabled public Decimal dueAmt3 {get; set;}
        // @AuraEnabled public Decimal dueAmt4 {get; set;}
        @AuraEnabled
        public Decimal totalDue { get; set; }
        @AuraEnabled
        public String trackNo { get; set; }
        @AuraEnabled
        public String paymentTerm { get; set; }
        @AuraEnabled
        public String paymentTermString { get; set; }
        @AuraEnabled
        public String freightTerm { get; set; }
        @AuraEnabled
        public String orderId { get; set; }
        @AuraEnabled
        public String orderURL { get; set; }

        @AuraEnabled
        public String dueDate { get; set; }
        @AuraEnabled
        public String isOverdue { get; set; }
        @AuraEnabled
        public String overdueDays { get; set; }
        @AuraEnabled
        public String displayStyle { get; set; }
        @AuraEnabled
        public Boolean hasNoSequence { get; set; }
        @AuraEnabled
        public Decimal discountFee { get; set; }
        @AuraEnabled
        public Decimal totalPaymentFee { get; set; }

        @AuraEnabled
        public List<AccountingInfo> accountingList { get; set; }

        // @AuraEnabled public String originalAmtLabel {get; set;}
        // @AuraEnabled public String othersFeeLabel {get; set;}
        // @AuraEnabled public String dueRemainingAmtLabel {get; set;}

        public DataTableWrapper() {
            this.accountingList = new List<AccountingInfo>();
        }
    }

    public class AccountingInfo {
        @AuraEnabled
        public String Id { get; set; }
        @AuraEnabled
        public String accountingOracleID { get; set; }
        @AuraEnabled
        public String invoiceNumber { get; set; }
        @AuraEnabled
        public String dueDate { get; set; }
        @AuraEnabled
        public Decimal invoiceAmount { get; set; }
        @AuraEnabled
        public Decimal paidAmount { get; set; }
        @AuraEnabled
        public Decimal dueRemainingAmount { get; set; }
        @AuraEnabled
        public String isOverdue { get; set; }
        @AuraEnabled
        public String overdueDays { get; set; }
        @AuraEnabled
        public String displayStyle { get; set; }
        @AuraEnabled
        public Decimal discountFee { get; set; }
        @AuraEnabled
        public Decimal totalPaymentFee { get; set; }
        @AuraEnabled
        public String paypalStatus { get; set; }

        public AccountingInfo() {
        }
    }

    public class ReturnData {
        @AuraEnabled
        public List<DataTableWrapper> currentData { get; set; }
        @AuraEnabled
        public Integer totalRecordCount { get; set; }
        @AuraEnabled
        public Decimal sumDiscountFee { get; set; }
        @AuraEnabled
        public Decimal sumTotalPaymentFee { get; set; }
        @AuraEnabled
        public String errorMsg { get; set; }
        @AuraEnabled
        public String customerName { get; set; }
        @AuraEnabled
        public Boolean isCCA { get; set; }
        @AuraEnabled
        public Boolean isAccess { get; set; }
        @AuraEnabled
        public Boolean isFirstPay { get; set; }
        @AuraEnabled
        public Boolean showBtnCCA { get; set; }

        public ReturnData() {
            this.currentData = new List<DataTableWrapper>();
            this.totalRecordCount = 0;
            this.sumDiscountFee = 0.00;
            this.sumTotalPaymentFee = 0.00;
            this.errorMsg = '';
            this.isCCA = false;
            this.isFirstPay = false;
            this.showBtnCCA = false;
        }
    }

    public class PicklistReturnData {
        @AuraEnabled
        public List<State__mdt> stateOfCNA { get; set; }
        @AuraEnabled
        public List<State__mdt> stateOfUSA { get; set; }
        @AuraEnabled
        public List<Country__mdt> country { get; set; }
    }

    public class ResultResponse {
        @AuraEnabled public String status;
        @AuraEnabled public String message;
        @AuraEnabled public Object data;
        @AuraEnabled public String paymentId;

        public ResultResponse(String status, String message, Object data){
            this.status = status;
            this.message = message;
            this.data = data;
            this.paymentId = '';
        }

        public ResultResponse(){

        }
    }

    public class GoCardlessWrapper {
        @AuraEnabled public BillingRequestWrapper billingRequest;
        @AuraEnabled public PaymentWrapper payment;

        public GoCardlessWrapper(){
            this.billingRequest = new BillingRequestWrapper();
            this.payment = new PaymentWrapper();
        }
    }

    public class BillingRequestWrapper {
        @AuraEnabled public String billingRequestId;
        @AuraEnabled public String status;
        @AuraEnabled public String mandateCurrency;
        @AuraEnabled public String paymentCurrency;
        @AuraEnabled public Decimal amount;
        @AuraEnabled public String customerId;
        @AuraEnabled public String creditorId;
        @AuraEnabled public String mandateId;
        @AuraEnabled public BillingRequestCustomerWrapper customer;
        @AuraEnabled public BillingRequestBankWrapper bank;

        public BillingRequestWrapper(){
            this.customer = new BillingRequestCustomerWrapper();
            this.bank = new BillingRequestBankWrapper();
        }
    }

    public class BillingRequestCustomerWrapper {
        @AuraEnabled public String id;
        @AuraEnabled public Datetime createdAt;
        @AuraEnabled public String email;
        @AuraEnabled public String givenName;
        @AuraEnabled public String familyName;
        @AuraEnabled public String companyName;
    }

    public class BillingRequestBankWrapper {
        @AuraEnabled public String id;
        @AuraEnabled public Datetime createdAt;
        @AuraEnabled public String accountNumberEnding;
        @AuraEnabled public String accountHolderName;
        @AuraEnabled public String bankName;
        @AuraEnabled public String currencyType;
        @AuraEnabled public String countryCode;
        @AuraEnabled public String customerId;
        @AuraEnabled public String branchCodeEnding;
    }

    public class PaymentWrapper {
        @AuraEnabled public String paymentId;
        @AuraEnabled public String mandateId;
        @AuraEnabled public Integer amount;
        @AuraEnabled public String status;
    }

    public class GoCardlessError{
        @AuraEnabled public String message;
        @AuraEnabled public String type;
        @AuraEnabled public String code;
        @AuraEnabled public List<GoCardlessErrorDetail> errors;

        public GoCardlessError(){
            this.errors = new List<GoCardlessErrorDetail>();
        }
    }

    public class GoCardlessErrorDetail{
        @AuraEnabled public String field;
        @AuraEnabled public String message;
        @AuraEnabled public String reason;
        @AuraEnabled public String request_pointer;
    }
}