import { LightningElement, api, wire } from 'lwc';
import { subscribe, MessageContext, unsubscribe, publish } from 'lightning/messageService';
import LIVEX_CHATBOT_CHANNEL from '@salesforce/messageChannel/LiveXChatbot__c';
import { getConversationSummary } from './conversationSummaryApi';

export default class SummaryPanel extends LightningElement {
    @api chatbotId;
    
    conversationId = '';
    isLoadingConversationId = false;
    pendingConversationIdRequestId = null;
    
    summaryResult = '';
    isLoadingSummary = false;
    summaryError = '';
    
    // Salesforce Lightning Message Service
    @wire(MessageContext)
    messageContext;

    get loadingMessage() {
        return this.isLoadingConversationId ? 'Getting conversation...' : 'Generating summary...';
    }
    
    get isInsertDisabled() {
        return this.isLoadingSummary || !this.summaryResult;
    }
    
    get showInitialMessage() {
        return !this.isLoadingSummary && !this.summaryResult && !this.summaryError;
    }

    connectedCallback() {
        this.subscribeToMessageChannel();
    }

    disconnectedCallback() {
        this.unsubscribeToMessageChannel();
    }
    
    async fetchConversationSummary() {
        if (!this.conversationId || !this.chatbotId) {
            this.summaryError = 'Missing conversation ID or chatbot ID';
            this.isLoadingSummary = false;
            return;
        }
        
        try {
            this.isLoadingSummary = true;
            this.summaryError = '';
            
            const response = await getConversationSummary(
                this.conversationId, 
                this.chatbotId
            );
            
            if (response.summary) {
                this.summaryResult = response.summary;
            } else {
                throw new Error('No summary returned from API');
            }
        } catch (error) {
            this.summaryError = error.message || 'Failed to generate summary';
        } finally {
            this.isLoadingSummary = false;
        }
    }
    
    handleInsertClick() {
        if (this.summaryResult) {
            const message = {
                messageType: 'SUMMARY_INSERT',
                summary: this.summaryResult,
                targetComponent: 'external'
            };
            
            publish(this.messageContext, LIVEX_CHATBOT_CHANNEL, message);
        }
    }

    handleSummaryClick() {
        this.isLoadingConversationId = true;
        this.isLoadingSummary = true;
        this.conversationId = '';
        this.summaryResult = '';
        this.summaryError = '';
        
        // Request conversation ID from the chatbot component
        const requestId = this.generateRequestId();
        this.pendingConversationIdRequestId = requestId;
        
        const message = {
            messageType: 'REQUEST',
            function: 'getConversationId',
            args: [],
            requestId: requestId,
            targetComponent: 'chatbot'
        };
        
        publish(this.messageContext, LIVEX_CHATBOT_CHANNEL, message);
    }

    handleSummaryTextChange(event) {
        this.summaryResult = event.target.value;
    }

    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    subscribeToMessageChannel() {
        this.subscription = subscribe(
            this.messageContext,
            LIVEX_CHATBOT_CHANNEL,
            (message) => this.handleChannelMessage(message)
        );
    }

    unsubscribeToMessageChannel() {
        unsubscribe(this.subscription);
        this.subscription = null;
    }
    
    async handleChannelMessage(message) {
        // Handle responses from the chatbot component
        if (message && 
            message.messageType === 'RESPONSE' && 
            message.requestId === this.pendingConversationIdRequestId &&
            message.targetComponent === 'agentRecap') {
            
            this.isLoadingConversationId = false;
            this.pendingConversationIdRequestId = null;
            
            if (message.result) {
                this.conversationId = message.result;
                await this.fetchConversationSummary();
            } else {
                console.warn('Error getting conversation id', message.error);
                this.isLoadingSummary = false;
                this.summaryError = message.error || 'Unable to get conversation ID';
            }
        }
    }
}