.slds-card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slds-card__body.slds-card__body_inner {
    padding: 1.5rem !important;
}

.button-container {
    display: flex;
    flex-direction: row;
    gap: 0.75rem;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 1.5rem;
}

.equal-width-button {
    flex: 1;
    min-width: 0;
}

.summary-section {
    margin-top: 0;
}

/* Add spacing between inner cards and outer container */
.summary-section .slds-card {
    margin: 0.75rem 0;
}

/* Fix text overflow and wrapping for summary content */
.summary-section .slds-card__body lightning-formatted-text {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    white-space: pre-wrap;
    max-width: 100%;
    display: block;
}

/* Ensure the summary container has proper overflow handling */
.summary-section .slds-card__body {
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
}

.slds-text-color_weak {
    color: #706e6b;
}

lightning-spinner {
    --slds-c-spinner-color-foreground: #0176d3;
}

.slds-notify_alert {
    border-radius: 4px;
    margin: 1rem 0;
}

.summary-container {
    min-height: 200px;
}

.summary-textarea {
    --slds-c-textarea-sizing-min-height: 300px;
}

.summary-textarea lightning-textarea {
    display: block;
}

.summary-textarea textarea,
.summary-textarea .slds-textarea {
    min-height: 500px !important;
    resize: vertical;
    font-family: 'Salesforce Sans', Arial, sans-serif;
    font-size: 0.875rem;
    line-height: 1.5;
    border: 1px solid #d8dde6;
    border-radius: 4px;
}

.summary-textarea .slds-textarea:focus {
    border-color: #0176d3;
    box-shadow: 0 0 0 1px #0176d3 inset;
}