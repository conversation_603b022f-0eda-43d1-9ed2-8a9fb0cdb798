.width90 {
    width: 90%;
}

ul.type li {
    background: #fff;
    padding: 0.2rem 1rem;
    cursor: pointer;
}

ul.type li.active {
    border-left: 4px solid #90c41f;
    border-radius: 4px;
}

.btn-search-wrap {
    margin-top: 25px;
}

.carousel-wrap {
    /* height: 410px; */
}

.brand-wrap {
    /* margin-top: 50px; */
}
.brand-border{
    border: 1px solid #e4e4e4;
    padding-bottom: 10px;
}
.swiper-container {
    width: 100%;
    padding: 0 50px 40px 50px;
    
}
.swiper-container::before{
    content: "";
    display: block;
    background: #fff;
    left: 0;
    position: absolute;
    top: 0;
    height: 480px;
    width: 40px;
    z-index: 9;
}
.swiper-container::after{
    content: "";
    display: block;
    background: #fff;
    right: 0;
    position: absolute;
    top: 0;
    height: 480px;
    width: 40px;
    z-index: 9;
}

.swiper-slide {
    text-align: center;
    font-size: 12px;
    background: #fff;

    /* Center slide text vertically */
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
}

.swiper-slide img{
    width: 100%;
    height: 100%;
}

.promotion-wrap {
    position: relative;
    /* height: 280px; */
    display:flex;
    width:100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: auto 100%;
}
.promotion-wrap:after {
    content: '';
    padding-top: 75%;
}

.promotion-wrap span {
    position: absolute;
    background: #c00000;
    color: #fff;
    padding: 4px 6px;
    z-index: 11;
    font-weight: 700;
}

.title {
    line-height: 1.8rem;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.buttons {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}
.buttons lightning-button{
    margin-top: 6px;
}

.window {
    line-height: 1.8rem;
}
.viewall{
    color: #90c41f;
    cursor: pointer;
}
.promotion-list{
    padding: 6px;
    border: 1px solid #e4e4e4;
    margin-bottom: 24px;
}
.promotion-wrap img{
    position: relative;
    /* height: 280px; */
}
.brand-wrap h2.slds-text-heading_medium{
    margin-bottom: 10px;
}
.brandBg{
    background-size: auto 100%;
    background-position: center;
    background-repeat: no-repeat;
    text-indent: -9999px;
}