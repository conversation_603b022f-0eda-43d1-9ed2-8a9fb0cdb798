@isTest
public without sharing class CCM_StoreLocationLeadUpdateHandlerTest {
    @isTest static void CCM_StoreLocationLeadUpdateHandlerTest() {
        CCM_LeadConvertHandler.testRun();
        User admin = [SELECT Id, Username, UserRoleId FROM User WHERE Profile.Name = 'System Administrator'  AND UserRoleId != null AND IsActive = true LIMIT 1];
        System.runAs(admin){
        Account acc1 = new Account();
        acc1.Name = 'Community';
        acc1.AccountNumber = '14355';
        acc1.Sales_Group__c = 'SG01';
        acc1.PaymentMethod__c = 'CHECK';
        acc1.TaxID__c = '123456';
        acc1.RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Account').get('Channel');
        insert acc1;

        Lead lead = new Lead(LastName = 'Yankotest',
        Email = '<EMAIL>',
        Country = 'US',
        State = 'FL',
        City = 'Miami',
        Street = '3241 Marigold Lane',
        PostalCode = '33196',
        Credit_Limit__c = 1000,
        Invoicing_Method__c = 'EMAIL',
        Intended_Brand__c = 'SKIL/SKILSAW/FLEX',
        Company = 'aaaaatest',
        Director_Approver__c = UserInfo.getUserId(),
        Customer_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_CLUSTER_FOR_NA_CNA_CG01,
        Distributor_or_Dealer__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_NA_DISTRIBUTOR,
        Customer_Sub_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_NA_KA001,
        Sales_Channel__c = 'SC01',
        Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[0],
        Customer_Business_Type__c = 'Sales',
        RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Lead').get('Channel'),
        To_Be_Direct_Indirect_Customer__c = 'TO BE CHERVON DIRECT CUSTOMER');
        insert lead;

        insert new Customer_Profile__c(RecordTypeId = CCM_Constants.CUSTOMER_PROFILE_RECORD_TYPE_PROSPECT_ID, Prospect__c = lead.Id, Payment_Behavior_Code__c = 'C');

        Attachment_Management__c am = new Attachment_Management__c(Prospect__c = lead.Id, Attachment_Type__c = 'Brand Program');
        insert am;
        Attachment_Management__c am1 = new Attachment_Management__c(Prospect__c = lead.Id, Attachment_Type__c = 'General Agreement');
        insert am1;
        ContentVersion objCV = new ContentVersion(VersionData = Blob.valueOf('test'), Title = 'test', PathOnClient = 'test.txt');
        insert objCV;
        insert new ContentDocumentLink(LinkedEntityId = am.Id, ContentDocumentId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :objCV.Id][0].ContentDocumentId);

        Account acc = new Account();
        acc.Name = 'Test';
        acc.RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Account').get  ('Channel');
        acc.Store_Address__City__s = 'NY';
        acc.Store_Address__PostalCode__s = '123456';
        acc.Store_Address__CountryCode__s= 'AT';
        acc.Store_Address__Street__s= '123456';
        acc.AccountNumber = '12345';
        acc.Related_Entity_Prospect__c = lead.Id;
        acc.TaxID__c= '123456';
        insert acc;

        Sales_Program__c sp = new Sales_Program__c(
            Name = 'TestSP001',
            Brands__c = 'EGO',
            Payment_Term__c = 'NA059',
            Freight_Term__c = 'Due',
            Starting_Date_of_Payment_Term__c = 'Invoice Date',
            Order_Lead_Time__c = 5,
            Customer__c = acc.Id,
            Prospect__c = lead.Id,
            Approval_Status__c = 'Approved',
            Authorized_Brand_Name_To_Oracle__c = 'test1'
        );
        insert sp;

        Contact c1 = new Contact();
        c1.LastName = 'test1';
        c1.AccountId = acc.Id;
        insert c1;

        Account_Address__c add = new Account_Address__c();
        add.Prospect__c = lead.Id;
        add.ORG_Id__c = 'CCA';
        add.Primary__c = true;
        add.Customer__c = acc.Id;
        add.Store_Number__c = '111';
        add.Approval_Status__c = 'Approved';
        add.RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Account_Address__c').get('Billing_Address');
        add.Sales_Director__c = UserInfo.getUserId();
        add.Contact__c = c1.Id;
        add.Email_for_Invoicing__c = lead.Email;
        insert add;

        Address_With_Program__c BAAB1 = new Address_With_Program__c();
        BAAB1.Account_Address__c = add.Id;
        BAAB1.Program__c = sp.Id;
        insert BAAB1;

        Test.StartTest();
        Database.LeadConvert objLC = new Database.LeadConvert();
        objLC.setLeadId(lead.Id);
        objLC.setConvertedStatus('Converted');
        Database.LeadConvertResult objConvertResult = Database.convertLead(objLC);
        System.assertEquals(objConvertResult.isSuccess(), true);
        System.assertNotEquals(null, objConvertResult.getAccountId());
        System.assertNotEquals(null, objConvertResult.getContactId());
        Test.stopTest();
        }
    }

    // @IsTest
    // static void testLeadConversionFailure4MissingCustomerProfiles() {
    //     Test.startTest();
    //     List<Lead> lstLead = [SELECT Id FROM Lead LIMIT 1];
    //     Database.LeadConvert objLC = new Database.LeadConvert();
    //     objLC.setLeadId(lstLead[0].Id);
    //     objLC.setConvertedStatus('Converted');
    //     Database.convertLead(objLC);
    //     Test.stopTest();
    // }
    // @IsTest
    // static void testLeadConversionSuccess() {
    //     Test.startTest();
    //     List<Lead> lstLead = [SELECT Id FROM Lead LIMIT 1];
    //     Database.LeadConvert objLC = new Database.LeadConvert();
    //     objLC.setLeadId(lstLead[0].Id);
    //     objLC.setConvertedStatus('Converted');
    //     Database.LeadConvertResult objConvertResult = Database.convertLead(objLC);
    //     System.assertEquals(objConvertResult.isSuccess(), true);
    //     System.assertNotEquals(null, objConvertResult.getAccountId());
    //     System.assertNotEquals(null, objConvertResult.getContactId());
    //     Test.stopTest();
    // }

}