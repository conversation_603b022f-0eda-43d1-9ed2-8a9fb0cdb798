<template>
    <div class="slds-form-element">
        <label class="slds-form-element__label">
            <template if:true={required}>
                <abbr class="slds-required" title="required">*</abbr>
            </template>
            {label}
        </label>
        <div class="slds-form-element__control">
            <div class="slds-combobox_container">
                <div class="slds-combobox slds-dropdown-trigger slds-dropdown-trigger_click slds-is-open"
                     aria-expanded="true" aria-haspopup="listbox" role="combobox">
                    <div class="slds-combobox__form-element slds-input-has-icon slds-input-has-icon_right" role="none">
                        <div class="slds-form-element__control slds-grow"><!--onkeyup={handleKeyUp}-->
                            <lightning-input class={errorCss} name="enter-search"
                                             label="Search when user hits the 'enter' key" type="search" value={inputVal}
                                             onblur={onInputBlur} onfocus={onInputFocus} onchange={onInputChange} variant="label-hidden" oncommit={commitHandler}
                                             disabled={disabled}>
                            </lightning-input>
                            <div if:true={showRequiredMsg} class="error-msg">{customLabel.CCM_Portal_ThisFieldIsRequired}</div>
                        </div>
                    </div>
                    <div class="item-group">
                        <ul if:true={showMultiList} class="slds-listbox slds-listbox_horizontal" role="listbox"
                            aria-label="Selected Options:" aria-orientation="horizontal">
                            <template for:each={multiList} for:item="selected" for:index="index">
                                <li class="slds-listbox-item" role="presentation" key={selected.keyId}>
                                    <span class="slds-pill" role="option" aria-selected="true">
                                        <span class="slds-pill__label" title={selected.label}>{selected.label}</span>
                                        <span class="slds-icon_container slds-pill__remove" title={customLabel.CCM_Portal_Remove}
                                              onclick={clickRemoveIcon} data-index={index}>
                                            <lightning-icon icon-name="utility:close" size="xx-small"
                                                            alternative-text={customLabel.CCM_Portal_Remove}>
                                            </lightning-icon>
                                        </span>
                                    </span>
                                </li>
                            </template>
                        </ul>

                    </div>
                    <div if:true={showDropdown} id="listbox-id-11"
                         class="slds-dropdown slds-dropdown_length-5 slds-dropdown_fluid" role="listbox">
                        <template if:true={isSearching}>
                            <ul class="slds-listbox slds-listbox_vertical" role="presentation">
                                <li role="presentation" class="slds-listbox__item">
                                    <div class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small"
                                        role="option">
                                        <span class="slds-media__figure slds-listbox__option-icon">
                                        </span>
                                        <span class="slds-media__body">
                                            {customLabel.CCM_Portal_SearchforText} "{queryTerm}".
                                        </span>
                                    </div>
                                </li>
                            </ul>
                        </template>
                        <template if:false={isSearching}>
                            <ul if:true={dataListEmpty} class="slds-listbox slds-listbox_vertical" role="presentation">

                                <li role="presentation" class="slds-listbox__item">
                                    <div class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small"
                                         role="option">
                                        <span class="slds-media__figure slds-listbox__option-icon">
                                        </span>
                                        <span class="slds-media__body">
                                            {customLabel.CCM_Portal_NoRecordFound}
                                        </span>
                                        </span>
                                    </div>
                                </li>
                            </ul>
                            <ul if:false={dataListEmpty} class="slds-listbox slds-listbox_vertical" role="presentation">
                                <template for:each={dataList} for:item="item" for:index="index">
                                    <li role="presentation" class="slds-listbox__item li" key={item.keyId}
                                        onclick={onSelectItem} data-index={index}>
                                        <div class="slds-media slds-media_small"
                                             role="option">
                                            <span if:true={item.iconName} if:false={multiple}
                                                  class="slds-media__figure slds-listbox__option-icon">
                                                <lightning-icon icon-name={item.iconName} alternative-text="icon">
                                                </lightning-icon>
                                            </span>
                                            <span if:true={multiple} class="slds-media__figure slds-listbox__option-icon">
                                                <template if:true={item.selected}>
                                                    <lightning-icon icon-name="utility:check" alternative-text="selected"
                                                                    size="xx-small">
                                                    </lightning-icon>
                                                </template>
    
                                            </span>
                                            <span class="slds-media__body">
    
                                                <span class="slds-truncate" title={item.label}>
                                                    <span>{item.keyHighlight.left}<mark>{item.keyHighlight.mid}</mark>{item.keyHighlight.right}</span>
                                                </span>
                                            </span>
                                        </div>
                                        <div class="description">
                                            <span class="slds-truncate" title={item.description}>
                                                <span>{item.descriptionHighlight.left}<mark>{item.descriptionHighlight.mid}</mark>{item.descriptionHighlight.right}</span>
                                            </span>
                                        </div>
    
                                    </li>
                                </template>
                            </ul>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>