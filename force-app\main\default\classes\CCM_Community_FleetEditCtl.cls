/**************************************************************************************************
 * Name: CCM_Community_FleetEditCtl
 * Author: <PERSON>
 * Create Date: 3/9/2020
 * Purpose: 为 community 上创建编辑 fleet program 提供后台类
 * Modify History:
 **************************************************************************************************/
public without sharing class CCM_Community_FleetEditCtl {

    private static final String SOURCE = 'Business Portal';
    private static final String EGO = 'EGO';
    private static final String PRIMARY_USE = 'Industrial/Professional/Commercial;Residential;Rental';
    public static final String AUTHORIZED_DEALER = 'Authorized Dealer';

    public static final Map<Boolean,String> yesOrNoMap = new Map<Boolean,String>{
        true => 'Yes',
        false => 'No'
    };

    public class FleetProgramRule{
        @AuraEnabled public Boolean deliverAtOnce;
        @AuraEnabled public Decimal purchaseOrderAmount;
        @AuraEnabled public Decimal maximumDiscountCriteria;
        @AuraEnabled public Decimal discountReturn;
        @AuraEnabled public String programCode;
        @AuraEnabled public List<DiscountReturnRule> discountReturnRules;

        public FleetProgramRule(){
            this.deliverAtOnce = false;
            this.discountReturnRules = new List<DiscountReturnRule>();
        }
    }

    public class FleetClaim{
        @AuraEnabled public String id;
        @AuraEnabled public String name;
        @AuraEnabled public String claimPackName;
        @AuraEnabled public String fleetProgramRuleId;
        @AuraEnabled public String approvalStatus;
        @AuraEnabled public String isPaidLabel;
        @AuraEnabled public String currencyCode;
        //Organization Information
        @AuraEnabled public AccountInfo channelCustomer;
        //Owner Information
        @AuraEnabled public AccountInfo endUserCustomer;
        //Sales Information
        @AuraEnabled public String salesDateStr;
        @AuraEnabled public String brand;
        @AuraEnabled public List<String> lstPrimaryUseOption;
        @AuraEnabled public String primaryUse;
        @AuraEnabled public List<FleetItem> fleetItemList;
        //Invoice Information
        @AuraEnabled public Decimal totalSalesAmount;
        @AuraEnabled public Decimal fleetDiscount;
        @AuraEnabled public Decimal totalRetailPrice;
        @AuraEnabled public Decimal estimatedCreditReturn;
        @AuraEnabled public Boolean deliverAtOnce;
        @AuraEnabled public String deliverAtOnceLabel;
        @AuraEnabled public String billToAddressId;
        @AuraEnabled public String billToAddressName;
        @AuraEnabled public String addressIdListStr;//channelCustomer Billing Address
        @AuraEnabled public String shipToAddressId;
        @AuraEnabled public String shipToAddressName;
        @AuraEnabled public String shipToAddressIdListStr;
        @AuraEnabled public Boolean haveApprovalHistory;
        @AuraEnabled public String invoiceNumber;
        @AuraEnabled public List<FileInfo> fileInfoList;
        @AuraEnabled public Boolean hasOneMatchThreshold{get;set;}

        public FleetClaim(){
            this.channelCustomer = new AccountInfo();
            this.endUserCustomer = new AccountInfo();
            this.fleetItemList = new List<FleetItem>();
            this.deliverAtOnce = false;
            this.haveApprovalHistory = false;
            this.fileInfoList = new List<FileInfo>();
            this.lstPrimaryUseOption = PRIMARY_USE.split(';');
        }
    }

    public class AccountInfo{
        @AuraEnabled public String id;
        @AuraEnabled public String name;
        @AuraEnabled public String lastName;
        @AuraEnabled public String firstName;
        @AuraEnabled public String country;
        @AuraEnabled public String zipPostalCode;
        @AuraEnabled public String orgName;
        @AuraEnabled public String phone;
        @AuraEnabled public String city;
        @AuraEnabled public String state;
        @AuraEnabled public String addressLine;
        @AuraEnabled public String emailAddress;
        @AuraEnabled public String productType;
        @AuraEnabled public Boolean eligibleForFleet;

        @AuraEnabled public String source;

        public AccountInfo(){
            this.eligibleForFleet = false;
        }
    }

    public class FileInfo{
        @AuraEnabled public String contentId;
        @AuraEnabled public String uploadFileName;
        @AuraEnabled public String contentUrl;
        @AuraEnabled public String contentDocumentId;
        @AuraEnabled public Boolean islinkedEntity;

        public FileInfo(){
            this.islinkedEntity = false;
        }
    }

    public class DiscountReturnRule {
        @AuraEnabled public String productType {get;set;}
        @AuraEnabled public Decimal proportion {get;set;}
    }

    public class FleetItem{
        @AuraEnabled public String id;
        @AuraEnabled public String fakeId;
        @AuraEnabled public String kitId;
        @AuraEnabled public String kitCode;
        @AuraEnabled public String kitName;
        @AuraEnabled public Decimal msrp;
        @AuraEnabled public Integer qtyPurchased;
        @AuraEnabled public Decimal total;
        @AuraEnabled public Decimal fleetDiscount;
        @AuraEnabled public Decimal dealerRebate;
        @AuraEnabled public Decimal unitSalesPrice;
        @AuraEnabled public Decimal totalSalesPrice;
        @AuraEnabled public String productType;

        //Product Information
        @AuraEnabled public List<Warranty> warrantyList;

        public FleetItem(){
            this.warrantyList = new List<Warranty>();
        }
    }

    public class Warranty{
        @AuraEnabled public Integer index;
        @AuraEnabled public String id;
        @AuraEnabled public String fakeId;
        @AuraEnabled public String kitId;
        @AuraEnabled public String kitCode;
        @AuraEnabled public String kitName;
        @AuraEnabled public String purchaseDate;
        @AuraEnabled public Boolean createdByFleet;
        @AuraEnabled public Boolean checked;

        @AuraEnabled public List<WarrantyItem> warrantyItemList;

        public Warranty(){
            this.warrantyItemList = new List<WarrantyItem>();
            this.createdByFleet = false;//默认为 false, 检查现有  warranty 数据, 没有匹配, 再设置为 true
            this.checked = false;
        }
    }

    public class WarrantyItem{
        @AuraEnabled public String id;
        @AuraEnabled public String fakeId;
        @AuraEnabled public String kitId;
        @AuraEnabled public String kitCode;
        @AuraEnabled public String kitName;
        @AuraEnabled public String kitBrand;
        @AuraEnabled public String productId;
        @AuraEnabled public String productCode;
        @AuraEnabled public String productName;
        @AuraEnabled public String productModel;
        @AuraEnabled public String productType;
        @AuraEnabled public String sequence;
        @AuraEnabled public Boolean haveSequenceProduct;
        @AuraEnabled public List<ProductEntity> productEntityList;
        @AuraEnabled public String serialNumber;

        @AuraEnabled public Boolean inRecallProject;
        @AuraEnabled public Boolean isFormatCorrect;
        @AuraEnabled public String snFormatErrorMessage;


        public WarrantyItem(){
            this.productEntityList = new List<ProductEntity>();
            this.haveSequenceProduct = false;
            this.inRecallProject = false;
            this.isFormatCorrect = true;
        }
    }

    public class ProductEntity{
        @AuraEnabled public String productId;
        @AuraEnabled public String productCode;
        @AuraEnabled public String productName;

        public ProductEntity(String productId, String productCode, String productName){
            this.productId = productId;
            this.productCode = productCode;
            this.productName = productName;
        }
    }

    @AuraEnabled
    public static AuraResponseEntity getInitData(String userId, String programId) {
        AuraResponseEntity auraResponse = new AuraResponseEntity();
        FleetClaim fleetClaim = new FleetClaim();
        Set<Id> kitIds = new Set<Id>();
        Set<String> setKitProductCode = new Set<String>();
        Map<Id, Decimal> mapKitId2MSRP = new Map<Id, Decimal>();

        fleetClaim.approvalStatus = 'Draft';
        fleetClaim.currencyCode = UserInfo.getDefaultCurrency();
        getSalesInfo(fleetClaim);
        getOrgInfo(fleetClaim, userId);
        auraResponse.data = new Map<String, Object>{
            'fleetProgramRule' => getFleetProgramRule(fleetClaim, programId, kitIds, setKitProductCode, mapKitId2MSRP),
            'kitId2WarrantyItemsInitMap' => getKitId2WarrantyItemsInitMap(kitIds, new Map<String, WarrantyItem>(), new Set<Id>()),
            'fleetClaim' => fleetClaim,
            'kitId2MsrpMap' => mapKitId2MSRP
        };
        return auraResponse;
    }

    @AuraEnabled
    public static AuraResponseEntity getFleetClaimData(String recordId) {
        AuraResponseEntity auraResponse = new AuraResponseEntity();
        FleetClaim fleetClaim = new FleetClaim();
        FleetProgramRule fleetProgramRule = new FleetProgramRule();
        Set<Id> kitIds = new Set<Id>();
        Set<Id> fleetItemIdSet = new Set<Id>();
        Set<Id> productIdSet = new Set<Id>();
        Map<Id, Decimal> mapKitId2MSRP = new Map<Id, Decimal>();
        Map<String, WarrantyItem> kitSequence2WarrantyItemMap = new Map<String, WarrantyItem>();
        Map<Id, List<WarrantyItem>> kitId2WarrantyItemsInitMap;

        getFleetClaim(recordId, fleetClaim, fleetProgramRule, kitIds, fleetItemIdSet, mapKitId2MSRP);
        kitId2WarrantyItemsInitMap = getKitId2WarrantyItemsInitMap(kitIds, kitSequence2WarrantyItemMap, productIdSet);
        getWarranty(fleetItemIdSet, fleetClaim, kitSequence2WarrantyItemMap, productIdSet);
        getFileInfoList(fleetClaim);
        auraResponse.data = new Map<String, Object>{
            'kitId2WarrantyItemsInitMap' => kitId2WarrantyItemsInitMap,
            'fleetProgramRule' => fleetProgramRule,
            'fleetClaim' => fleetClaim,
            'kitId2MsrpMap' => mapKitId2MSRP
        };
        return auraResponse;
    }

    @AuraEnabled
    public static List<Fleet_Claim_Item__c> getFleetClaimItemData(String recordId) {
        return [SELECT Id,Name,Dealer_Rebate__c,Fleet_Discount__c,Kit__c,kit__r.Name,Kit__r.ProductCode,Kit_Number__c,MSRP__c,Qty_Purchased__c,Total__c,Total_Sales_Price__c,Unit_Sales_Price__c FROM Fleet_Claim_Item__c WHERE Fleet_Claim__c =:recordId];
    }

    private static void getFleetClaim(
        String recordId,
        FleetClaim fleetClaim,
        FleetProgramRule fleetProgramRule,
        Set<Id> allKitIds,
        Set<Id> setFleetItemId,
        Map<Id, Decimal> mapKitId2MSRP
    ) {
        List<Fleet_Claim__c> fleetClaimList = getFleetClaimList(recordId);
        String ruleId;

        if(!fleetClaimList.isEmpty()){
            Fleet_Claim__c fleetClaimObj = fleetClaimList[0];
            fleetClaim.id = fleetClaimObj.Id;

            if(String.isNotBlank(fleetClaimObj.Approval_Status__c)){
                fleetClaim.approvalStatus = fleetClaimObj.Approval_Status__c;
            }else{
                fleetClaim.approvalStatus = 'Draft';
            }

            if(fleetClaimObj.Claim_Pack__c != null){
                fleetClaim.claimPackName = fleetClaimObj.Claim_Pack__r.Name;
            }

            fleetClaim.haveApprovalHistory = fleetClaimObj.Have_Approval_History__c;
            fleetClaim.currencyCode = fleetClaimObj.CurrencyIsoCode;
            fleetClaim.invoiceNumber = fleetClaimObj.Invoice_Number__c;
            fleetClaim.isPaidLabel = yesOrNoMap.get(fleetClaimObj.Is_Paid__c);
            fleetClaim.name = fleetClaimObj.Name;

            if(String.isNotBlank(fleetClaimObj.Fleet_Program_Rule__c)){
                List<Fleet_Program_Discount_Return_Rule__c> returnRules = [SELECT Product_Type__c, Proportion__c FROM Fleet_Program_Discount_Return_Rule__c WHERE Fleet_Program_Rule__c = :fleetClaimObj.Fleet_Program_Rule__c];
                fleetClaim.fleetProgramRuleId = fleetClaimObj.Fleet_Program_Rule__c;

                List<DiscountReturnRule> discountReturnRules = new List<DiscountReturnRule>();
                for(Fleet_Program_Discount_Return_Rule__c itemRule : returnRules) {
                    DiscountReturnRule returnRule = new DiscountReturnRule();
                    returnRule.productType = itemRule.Product_Type__c;
                    returnRule.proportion = itemRule.Proportion__c;
                    discountReturnRules.add(returnRule);
                }
                fleetProgramRule.discountReturnRules = discountReturnRules;
                // fleetProgramRule.deliverAtOnce = fleetClaimObj.Fleet_Program_Rule__r.Deliver_At_Once__c;
                fleetProgramRule.purchaseOrderAmount = fleetClaimObj.Fleet_Program_Rule__r.Purchase_Order_Amount__c;
                fleetProgramRule.maximumDiscountCriteria = fleetClaimObj.Fleet_Program_Rule__r.Maximum_Discount_Criteria__c;
                fleetProgramRule.discountReturn = fleetClaimObj.Fleet_Program_Rule__r.Discount_Return__c;
                // add haibo: 添加 Program Code (French)法语翻译字段
                if (UserInfo.getLanguage() == Label.CCM_Portal_French) {
                    fleetProgramRule.programCode = fleetClaimObj.Fleet_Program_Rule__r.Program_Code_French__c;
                } else {
                    fleetProgramRule.programCode = fleetClaimObj.Fleet_Program_Rule__r.Program_Code__c;
                }
                ruleId = fleetClaimObj.Fleet_Program_Rule__c;

                fleetClaim.hasOneMatchThreshold = hasOneClaimMatchAmountThreshold(fleetClaimObj.Channel_Customer__c, fleetClaimObj.Fleet_Program_Rule__c);
            }

            if(String.isNotBlank(fleetClaimObj.Channel_Customer__c)){
                fleetClaim.channelCustomer.id = fleetClaimObj.Channel_Customer__c;
                fleetClaim.channelCustomer.name = fleetClaimObj.Channel_Customer__r.Name;
                fleetClaim.channelCustomer.source = SOURCE;

                fleetClaim.addressIdListStr = getAddressIdListStr(fleetClaimObj.Channel_Customer__c, fleetClaimObj.Brand_Name__c);
                fleetClaim.shipToAddressIdListStr = getShipToAddressIdListStr(fleetClaimObj.Channel_Customer__c);
            }

            if(String.isNotBlank(fleetClaimObj.End_User_Customer__c)){
                fleetClaim.endUserCustomer = getAccountInfo(fleetClaimObj.End_User_Customer__r);
            }

            if(fleetClaimObj.Sales_Date__c != null){
                fleetClaim.salesDateStr = String.valueOf(fleetClaimObj.Sales_Date__c);
            }
            fleetClaim.brand = fleetClaimObj.Brand_Name__c;
            fleetClaim.primaryUse = fleetClaimObj.Product_Use_Type__c;
            fleetClaim.totalSalesAmount = fleetClaimObj.Total_Sales_Amount__c;
            fleetClaim.fleetDiscount = fleetClaimObj.Fleet_Discount__c;
            fleetClaim.totalRetailPrice = fleetClaimObj.Total_Retail_Price__c;
            fleetClaim.estimatedCreditReturn = fleetClaimObj.Estimated_Credit_Return__c;
            // fleetClaim.deliverAtOnce = fleetClaimObj.Deliver_At_Once__c;
            // fleetClaim.deliverAtOnceLabel = yesOrNoMap.get(fleetClaimObj.Deliver_At_Once__c);
            if(String.isNotBlank(fleetClaimObj.Bill_To_Address__c)){
                if(fleetClaimObj.Bill_To_Address__r.Account_Address__c != null){
                    fleetClaim.billToAddressId = fleetClaimObj.Bill_To_Address__r.Account_Address__c;
                    fleetClaim.billToAddressName = getAddressInfoLabel(fleetClaimObj.Bill_To_Address__r.Account_Address__r);
                }
            }

            if(String.isNotBlank(fleetClaimObj.Ship_To_Address__c)) {
                if(fleetClaimObj.Ship_To_Address__r.Account_Address__c != null){
                    fleetClaim.shipToAddressId = fleetClaimObj.Ship_To_Address__r.Account_Address__c;
                    fleetClaim.shipToAddressName = getAddressInfoLabel(fleetClaimObj.Ship_To_Address__r.Account_Address__r);
                }
            }

            if(fleetClaimObj.Fleet_Claim_Items__r != null){
                Map<String, String> productCodeTypeMap = new Map<String, String>();
                if(ruleId != null) {
                    for(Program_Rule_Product_Relationship__c ruleProduct : [SELECT Kit__r.ProductCode, Product_Type__c FROM Program_Rule_Product_Relationship__c
                                                                            WHERE Fleet_Program_Rule__c = :ruleId]) {
                        productCodeTypeMap.put(ruleProduct.Kit__r.ProductCode, ruleProduct.Product_Type__c);
                    }
                }
                for(Fleet_Claim_Item__c fleetItemObj : fleetClaimObj.Fleet_Claim_Items__r){
                    FleetItem fleetItem = new FleetItem();
                    fleetItem.id = fleetItemObj.Id;
                    fleetItem.fakeId = fleetItemObj.Id;
                    if(fleetItemObj.Kit__c != null){
                        fleetItem.kitId = fleetItemObj.Kit__c;
                        fleetItem.kitCode = fleetItemObj.Kit__r.ProductCode;
                        fleetItem.kitName = fleetItemObj.Kit__r.Name;
                        if(productCodeTypeMap.containsKey(fleetItemObj.Kit__r.ProductCode)) {
                            fleetItem.productType = productCodeTypeMap.get(fleetItemObj.Kit__r.ProductCode);
                        }

                        allKitIds.add(fleetItemObj.Kit__c);
                    }
                    fleetItem.msrp = fleetItemObj.MSRP__c;
                    if(fleetItemObj.Qty_Purchased__c != null){
                        fleetItem.qtyPurchased = Integer.valueOf(fleetItemObj.Qty_Purchased__c);
                    }
                    fleetItem.total = fleetItemObj.Total__c;
                    fleetItem.fleetDiscount = fleetItemObj.Fleet_Discount__c;
                    fleetItem.dealerRebate = fleetItemObj.Dealer_Rebate__c;
                    fleetItem.unitSalesPrice = fleetItemObj.Unit_Sales_Price__c;
                    fleetItem.totalSalesPrice = fleetItemObj.Total_Sales_Price__c;

                    fleetClaim.fleetItemList.add(fleetItem);

                    setFleetItemId.add(fleetItemObj.Id);
                }
            }
        }

        if (fleetClaim.approvalStatus == 'Draft') {
            fleetClaim.fleetItemList.addAll(getDraftFleetItemList(ruleId, allKitIds, mapKitId2MSRP));
        }
    }

    private static Boolean hasOneClaimMatchAmountThreshold(String customerId, String fleetProgramRuleId) {
        List<Fleet_Claim__c> claims = [SELECT Total_Sales_Amount__c, Fleet_Program_Rule__r.Purchase_Order_Amount__c
                                       FROM Fleet_Claim__c WHERE Channel_Customer__c = :customerId AND Fleet_Program_Rule__c = :fleetProgramRuleId];
        Boolean hasOneExist = false;
        for(Fleet_Claim__c claim : claims) {
            if(claim.Total_Sales_Amount__c >= claim.Fleet_Program_Rule__r.Purchase_Order_Amount__c) {
                hasOneExist = true;
                break;
            }
        }
        return hasOneExist;
    }

    private static List<FleetItem> getDraftFleetItemList(String ruleId, Set<Id> setKitId, Map<Id, Decimal> mapKitId2MSRP) {
        FleetItem objFleetItem;
        Set<String> setKitProductCode = new Set<String>();
        List<FleetItem> draftFleetItemList = new List<FleetItem>();
        Map<String, Decimal> mapKitProductCode2MSRP = new Map<String, Decimal>();
        for (Program_Rule_Product_Relationship__c kit : [
            SELECT Id, Kit__c, Kit__r.ProductCode, Kit__r.Name, Product_Type__c
            FROM Program_Rule_Product_Relationship__c
            WHERE
                Fleet_Program_Rule__c = :ruleId
                AND Kit__r.RecordType.DeveloperName = 'Kit'
                AND Kit__c NOT IN :setKitId
                AND Kit__r.IsActive = TRUE
                AND Kit__r.Country_of_Origin__c = 'United States'
                AND Kit__r.Source__c = 'PIM'
            ORDER BY Kit__r.ProductCode
        ]) {
            objFleetItem = new FleetItem();
            objFleetItem.fakeId = getFakeId(new Set<String>());
            objFleetItem.kitId = kit.Kit__c;
            objFleetItem.kitCode = kit.Kit__r.ProductCode;
            objFleetItem.kitName = kit.Kit__r.Name;
            objFleetItem.productType = kit.Product_Type__c;
            draftFleetItemList.add(objFleetItem);
            setKitId.add(kit.Kit__c);
            setKitProductCode.add(kit.Kit__r.ProductCode);
        }
        getKitProductCode2MsrpMap(setKitId, setKitProductCode, mapKitProductCode2MSRP);
        for (FleetItem fleetItem : draftFleetItemList) {
            fleetItem.msrp = mapKitProductCode2MSRP.get(fleetItem.kitCode);
            mapKitId2MSRP.put(fleetItem.kitId, fleetItem.msrp);
        }
        return draftFleetItemList;
    }

    private static List<Fleet_Claim__c> getFleetClaimList(String recordId){
        List<Fleet_Claim__c> fleetClaimList = new List<Fleet_Claim__c>([
            SELECT Id, Name, Brand_Name__c, Deliver_At_Once__c, CurrencyIsoCode,
            Approval_Status__c, Have_Approval_History__c, Is_Paid__c, Invoice_Number__c,
            Estimated_Credit_Return__c, Fleet_Discount__c, Product_Use_Type__c,
            Sales_Date__c, Total_Retail_Price__c, Total_Sales_Amount__c,
            Claim_Pack__c,
            Claim_Pack__r.Name,
            Bill_To_Address__c,
            Bill_To_Address__r.Account_Address__c,
            Bill_To_Address__r.Account_Address__r.Address1__c,
            Bill_To_Address__r.Account_Address__r.City__c,
            Bill_To_Address__r.Account_Address__r.State__c,
            Bill_To_Address__r.Account_Address__r.Country__c,
            Bill_To_Address__r.Account_Address__r.Postal_Code__c,
            Ship_To_Address__c,
            Ship_To_Address__r.Account_Address__c,
            Ship_To_Address__r.Account_Address__r.Address1__c,
            Ship_To_Address__r.Account_Address__r.City__c,
            Ship_To_Address__r.Account_Address__r.State__c,
            Ship_To_Address__r.Account_Address__r.Country__c,
            Ship_To_Address__r.Account_Address__r.Postal_Code__c,
            Channel_Customer__c,
            Channel_Customer__r.Name,
            End_User_Customer__c,
            End_User_Customer__r.Id,
            End_User_Customer__r.LastName,
            End_User_Customer__r.Firstname,
            End_User_Customer__r.Organization_Name__c,
            End_User_Customer__r.PersonEmail,
            End_User_Customer__r.Phone,
            End_User_Customer__r.Shippingcountry,
            End_User_Customer__r.Product_Type__c,
            End_User_Customer__r.ShippingStreet,
            End_User_Customer__r.ShippingPostalCode,
            End_User_Customer__r.ShippingCity,
            End_User_Customer__r.ShippingState,
            End_User_Customer__r.Eligible_For_Fleet__c,
            Fleet_Program_Rule__c,
            // add haibo: 添加 Program Code (French)法语翻译字段
            Fleet_Program_Rule__r.Program_Code__c,
            Fleet_Program_Rule__r.Program_Code_French__c,
            Fleet_Program_Rule__r.Deliver_At_Once__c,
            Fleet_Program_Rule__r.Purchase_Order_Amount__c,
            Fleet_Program_Rule__r.Maximum_Discount_Criteria__c,
            Fleet_Program_Rule__r.Discount_Return__c,
            (
                SELECT Id, Dealer_Rebate__c, Fleet_Claim__c,
                Fleet_Discount__c, MSRP__c, Qty_Purchased__c,
                Total__c, Unit_Sales_Price__c, Total_Sales_Price__c,
                Kit__c,
                Kit__r.ProductCode,
                Kit__r.Name
                FROM Fleet_Claim_Items__r
            )
            FROM Fleet_Claim__c
            WHERE Id = :recordId
        ]);
        return fleetClaimList;
    }

    private static void getWarranty(Set<Id> fleetItemIdSet, FleetClaim fleetClaim, Map<String, WarrantyItem> kitSequence2WarrantyItemMap, Set<Id> productIdSet){
        Map<Id, List<Warranty__c>> ciId2WarrantysMap = getClaimItemId2WarrantysMap(fleetItemIdSet);
        Map<String, Warranty_Rules__c> warrantyRulesMap = getWarrantyRulesMap();
        //获得系统中所有有效的验证规则  保证正则有  Name有
        Map<String,System_Configuration__c> snRegExpMap = QueryUtils.getSerialNumberRegExpMap();

        Map<String, List<Project_SN__c>> uniqueKey2ProjectSnsMap = getUniqueKey2ProjectSnsMap(fleetClaim.fleetItemList);

        Map<Id, Set<String>> productId2SequencesMap = getProductId2SequencesMap(productIdSet);
        for(FleetItem fleetItem : fleetClaim.fleetItemList){
            List<Warranty__c> warrantyList = ciId2WarrantysMap.get(fleetItem.id);
            if(warrantyList == null) continue;
            for(Warranty__c warrantyObj : warrantyList){
                Warranty warranty = new Warranty();
                warranty.id = warrantyObj.Id;
                warranty.fakeId = warrantyObj.Id;
                warranty.kitId = warrantyObj.Master_Product__c;
                warranty.createdByFleet = warrantyObj.Created_By_Fleet__c;
                if(warrantyObj.Warranty_Items__r == null) continue;
                for(Warranty_Item__c warrantyItemObj : warrantyObj.Warranty_Items__r){
                    WarrantyItem warrantyItem = new WarrantyItem();
                    warrantyItem.id = warrantyItemObj.Id;
                    warrantyItem.fakeId = warrantyItemObj.Id;
                    if(warrantyObj.Master_Product__c != null){
                        warrantyItem.kitId = warrantyObj.Master_Product__c;
                        warrantyItem.kitCode = warrantyObj.Master_Product__r.ProductCode;
                        warrantyItem.kitName = warrantyObj.Master_Product__r.Name;
                        warrantyItem.kitBrand = warrantyObj.Master_Product__r.Brand_Name__c;
                    }
                    if(warrantyItemObj.Product__c != null){
                        warrantyItem.productId = warrantyItemObj.Product__c;
                        warrantyItem.productCode = warrantyItemObj.Product__r.ProductCode;
                        warrantyItem.productName = warrantyItemObj.Product__r.Name;
                        warrantyItem.productModel = warrantyItemObj.Product__r.ProductModel__c;
                        warrantyItem.productType = warrantyItemObj.Product__r.Product_Type__c;
                        warrantyItem.serialNumber = warrantyItemObj.Serial_Number__c;

                        String uniqueKey = warrantyItem.kitBrand;
                        uniqueKey += '-' + warrantyItem.productCode;
                        checkSerialNumber(warrantyItem, warrantyRulesMap, snRegExpMap, uniqueKey2ProjectSnsMap.get(uniqueKey));

                        Set<String> sequenceSet = productId2SequencesMap.get(warrantyItem.productId);
                        if(sequenceSet != null){
                            for(String sequence : sequenceSet){
                                String kitSequenceKey = warrantyItem.kitId + '-' + sequence;
                                WarrantyItem warrantyItemTemp = kitSequence2WarrantyItemMap.get(kitSequenceKey);
                                if(warrantyItemTemp != null){
                                    warrantyItem.sequence = warrantyItemTemp.sequence;
                                    warrantyItem.haveSequenceProduct = warrantyItemTemp.haveSequenceProduct;
                                    warrantyItem.productEntityList = warrantyItemTemp.productEntityList;
                                }
                            }
                        }

                        warranty.warrantyItemList.add(warrantyItem);
                    }
                }

                fleetItem.warrantyList.add(warranty);
            }
        }
    }

    public static Map<Id, Set<String>> getProductId2SequencesMap(Set<Id> productIdSet){
        List<Kit_Item__c> kitItemList = new List<Kit_Item__c>([
            SELECT Sequence__c, Product__c
            FROM Kit_Item__c
            WHERE Product__c IN :productIdSet
            // AND Status__c = 'A'
            AND Sequence__c != NULL
        ]);
        Map<Id, Set<String>> productId2SequencesMap = new Map<Id, Set<String>>();

        for(Kit_Item__c kitItem : kitItemList){
            if(!productId2SequencesMap.containsKey(kitItem.Product__c)){
                productId2SequencesMap.put(kitItem.Product__c, new Set<String>());
            }
            productId2SequencesMap.get(kitItem.Product__c).add(kitItem.Sequence__c);
        }

        return productId2SequencesMap;
    }

    private static Map<Id, List<Warranty__c>> getClaimItemId2WarrantysMap(Set<Id> fleetItemIdSet){
        List<Warranty__c> warrantyList = new List<Warranty__c>([
            SELECT Id, AccountCustomer__c, Fleet_Claim_Item__c,
            Product_Use_Type2__c, Brand_Name__c, Created_By_Fleet__c,
            Master_Product__c,
            Master_Product__r.ProductCode,
            Master_Product__r.Name,
            Master_Product__r.Brand_Name__c,
            (
                SELECT Id, Warranty__c, Product_Code__c,
                Product_Name__c, Product_Model__c,
                Serial_Number__c, Product_Type__c,
                Product__c,
                Product__r.ProductCode,
                Product__r.Name,
                Product__r.ProductModel__c,
                Product__r.Product_Type__c,
                Product__r.Brand_Name__c
                FROM Warranty_Items__r
            )
            FROM Warranty__c
            WHERE Fleet_Claim_Item__c IN :fleetItemIdSet
        ]);

        Map<Id, List<Warranty__c>> ciId2WarrantysMap = new Map<Id, List<Warranty__c>>();

        for(Warranty__c warranty : warrantyList){
            if(!ciId2WarrantysMap.containsKey(warranty.Fleet_Claim_Item__c)){
                ciId2WarrantysMap.put(warranty.Fleet_Claim_Item__c, new List<Warranty__c>());
            }
            ciId2WarrantysMap.get(warranty.Fleet_Claim_Item__c).add(warranty);
        }

        return ciId2WarrantysMap;
    }

    private static void getFileInfoList(FleetClaim fleetClaim){
        List<ContentDocumentLink> cDocList = new List<ContentDocumentLink>([
            SELECT Id, ContentDocumentId
            FROM ContentDocumentLink
            WHERE LinkedEntityId = :fleetClaim.id
        ]);
        Set<Id> cdIdSet = new Set<Id>();
        for(ContentDocumentLink cDoc : cDocList){
            cdIdSet.add(cDoc.ContentDocumentId);
        }
        if(!cdIdSet.isEmpty()){
            List<ContentVersion> cVerList = new List<ContentVersion>([
                SELECT Id, ContentDocumentId, Title, ContentUrl, PathOnClient
                FROM ContentVersion
                WHERE ContentDocumentId IN :cdIdSet
                ORDER BY CreatedDate
            ]);
            for(ContentVersion cVer : cVerList){
                FileInfo fileInfo = new FileInfo();
                fileInfo.uploadFileName = cVer.PathOnClient;
                fileInfo.islinkedEntity = true;
                fileInfo.contentId = cVer.Id;
                fileInfo.contentUrl = '/sfc/servlet.shepherd/document/download/' + cVer.ContentDocumentId + '?operationContext=S1';
                fileInfo.contentDocumentId = cVer.ContentDocumentId;

                fleetClaim.fileInfoList.add(fileInfo);
            }

        }
    }

    private static void getSalesInfo(FleetClaim fleetClaim){
        fleetClaim.brand = EGO;
    }

    private static void getOrgInfo(FleetClaim fleetClaim, String userId){
        //获取 account id
        String accId = CCM_PortalPageUtil.getCustomerByUser(userId);

        if(String.isNotBlank(accId)){
            List<Account> accList = new List<Account>([
                SELECT Id, Name
                FROM Account
                WHERE Id = :accId
            ]);
            if(!accList.isEmpty()){
                fleetClaim.channelCustomer.id = accList[0].Id;
                fleetClaim.channelCustomer.name = accList[0].Name;
                fleetClaim.channelCustomer.source = SOURCE;

                fleetClaim.addressIdListStr = getAddressIdListStr(accList[0].Id, fleetClaim.brand);
                fleetClaim.shipToAddressIdListStr = getShipToAddressIdListStr(accList[0].Id);
            }
        }

    }

    private static String getAddressIdListStr(String accountId, String brand){
        String addressIdListStr = '';
        List<Address_With_Program__c> addressByBrandList = new List<Address_With_Program__c>([
            SELECT Id, Account_Address__c
            FROM Address_With_Program__c
            WHERE Account_Address__r.Customer__c = :accountId
            AND Account_Address__r.RecordType.DeveloperName = 'Billing_Address'
            AND Program__r.Brands__c = :brand
            AND Program__r.RecordType.DeveloperName IN ('Customized', 'Standard')
        ]);

        List<String> addressIdList = new List<String>();
        for(Address_With_Program__c addressByBrand : addressByBrandList){
            addressIdList.add(addressByBrand.Account_Address__c);
        }
        if(addressIdList.isEmpty()){
            addressIdList.add(null);
        }

        addressIdListStr = String.join(addressIdList, ', ');

        return addressIdListStr;
    }

    private static String getShipToAddressIdListStr(String accountId) {
        String addressIdListStr = '';
        List<Address_With_Program__c> addressByBrandList = new List<Address_With_Program__c>([
            SELECT Id, Account_Address__c
            FROM Address_With_Program__c
            WHERE Account_Address__r.Customer__c = :accountId
            AND Status__c = 'A'
            AND (Account_Address__r.RecordType.DeveloperName = :CCM_Constants.ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME OR Account_Address__r.RecordType.DeveloperName = :CCM_Constants.ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME)
        ]);

        List<String> addressIdList = new List<String>();
        for(Address_With_Program__c addressByBrand : addressByBrandList){
            addressIdList.add(addressByBrand.Account_Address__c);
        }
        if(addressIdList.isEmpty()){
            addressIdList.add(null);
        }

        addressIdListStr = String.join(addressIdList, ', ');

        return addressIdListStr;
    }

    private static FleetProgramRule getFleetProgramRule(FleetClaim fleetClaim, String programId, Set<Id> setKitId, Set<String> setKitProductCode, Map<Id, Decimal> mapKitId2MSRP){
        String yearStr = String.valueOf(Date.today().year());
        List<Fleet_Program_Rule__c> fleetRuleList = new List<Fleet_Program_Rule__c>([
            // add haibo: 添加 Program Code (French)法语翻译字段
            SELECT Id, Program_Code__c, Program_Code_French__c, Year__c,
            Deliver_At_Once__c,
            Purchase_Order_Amount__c,
            Maximum_Discount_Criteria__c,
            Discount_Return__c,
            (
                SELECT Id, Kit__c, Kit__r.ProductCode, Kit__r.Name, Product_Type__c
                FROM Program_Rule_Product_Relationships__r
                WHERE Kit__r.RecordType.DeveloperName = 'Kit'
                AND Kit__r.IsActive = TRUE
                AND Kit__r.Country_of_Origin__c = 'United States'
                AND Kit__r.Source__c = 'PIM'
            ),
            (
                SELECT Customer__c FROM Fleet_Program_Target_Customers__r
            ),
            (
                SELECT Product_Type__c, Proportion__c FROM Fleet_Program_Discount_Return_Rules__r
            )
            FROM Fleet_Program_Rule__c
            WHERE Actived__c = TRUE
            AND Year__c = :yearStr
        ]);
        Fleet_Program_Rule__c fleetProgramRuleObj = null;
        List<DiscountReturnRule> discountReturnRules = new List<DiscountReturnRule>();
        if(!fleetRuleList.isEmpty()){
            for(Fleet_Program_Rule__c rule : fleetRuleList) {
                if(String.isNotBlank(programId)) {
                    if(rule.Id == programId) {
                        fleetProgramRuleObj = rule;
                    }
                }
                else {
                    if(rule.Fleet_Program_Target_Customers__r != null) {
                        for(Fleet_Program_Target_Customer__c targetCustomer : rule.Fleet_Program_Target_Customers__r) {
                            if(targetCustomer.Customer__c == fleetClaim.channelCustomer.id) {
                                fleetProgramRuleObj = rule;
                            }
                        }
                    }
                    else {
                        fleetProgramRuleObj = fleetRuleList[0];
                    }
                }
            }
        }
        FleetProgramRule fleetProgramRule = new FleetProgramRule();
        //目前只会配置一条有效数据
        if(fleetProgramRuleObj != null){
            if(fleetProgramRuleObj.Fleet_Program_Discount_Return_Rules__r != null) {
                for(Fleet_Program_Discount_Return_Rule__c returnRule : fleetProgramRuleObj.Fleet_Program_Discount_Return_Rules__r) {
                    DiscountReturnRule itemRule = new DiscountReturnRule();
                    itemRule.productType = returnRule.Product_Type__c;
                    itemRule.proportion = returnRule.Proportion__c;
                    discountReturnRules.add(itemRule);
                }
            }
            fleetClaim.fleetProgramRuleId = fleetProgramRuleObj.Id;
            fleetProgramRule.discountReturnRules = discountReturnRules;
            // fleetProgramRule.deliverAtOnce = fleetProgramRuleObj.Deliver_At_Once__c;//是否一次性交付
            fleetProgramRule.purchaseOrderAmount = fleetProgramRuleObj.Purchase_Order_Amount__c;//申请补偿的订单最小金额
            fleetProgramRule.maximumDiscountCriteria = fleetProgramRuleObj.Maximum_Discount_Criteria__c;//客户给的最大折扣
            fleetProgramRule.discountReturn = fleetProgramRuleObj.Discount_Return__c;//折扣返回
            // add haibo: 添加 Program Code (French)法语翻译字段
            if (UserInfo.getLanguage() == Label.CCM_Portal_French) {
                fleetProgramRule.programCode = fleetProgramRuleObj.Program_Code_French__c;
            } else {
                fleetProgramRule.programCode = fleetProgramRuleObj.Program_Code__c;
            }
            Set<String> fakeIdSet = new Set<String>();
            for(Program_Rule_Product_Relationship__c productRelationship : fleetProgramRuleObj.Program_Rule_Product_Relationships__r){
                FleetItem fleetItem = new FleetItem();
                fleetItem.fakeId = getFakeId(fakeIdSet);
                fleetItem.kitId = productRelationship.Kit__c;
                fleetItem.kitCode = productRelationship.Kit__r.ProductCode;
                fleetItem.kitName = productRelationship.Kit__r.Name;
                fleetItem.productType = productRelationship.Product_Type__c;
                setKitId.add(productRelationship.Kit__c);
                setKitProductCode.add(productRelationship.Kit__r.ProductCode);
                fleetClaim.fleetItemList.add(fleetItem);
            }
            Map<String, Decimal> mapKitProductCode2MSRP = new Map<String, Decimal>();
            getKitProductCode2MsrpMap(setKitId, setKitProductCode, mapKitProductCode2MSRP);
            for(FleetItem fleetItem:fleetClaim.fleetItemList){
                fleetItem.msrp = mapKitProductCode2MSRP.get(fleetItem.kitCode);
                mapKitId2MSRP.put(fleetItem.kitId, fleetItem.msrp);
            }

            fleetClaim.hasOneMatchThreshold = hasOneClaimMatchAmountThreshold(fleetClaim.channelCustomer.id, fleetProgramRuleObj.Id);
        }
        return fleetProgramRule;
    }
    private static void getKitProductCode2MsrpMap(Set<Id> setKitId, Set<String> setKitProductCode, Map<String, Decimal> mapKitProductCode2MSRP) {
        String accId = CCM_PortalPageUtil.getCustomerByUser(UserInfo.getUserId());
        String orgCode = '';
        if(String.isNotBlank(accId)) {
            List<Account> accList = [SELECT ORG_Code__c FROM Account WHERE ID = :accId];
            for(Account acc : accList) {
                orgCode = acc.ORG_Code__c;
            }
        }
        String priceBookOracleId = Label.CCM_Price_Book_Oracle_ID_CNA_EGO_MSRP;
        if(CCM_Constants.ORG_CODE_CCA == orgCode) {
            priceBookOracleId = '********';
        }
        for (PricebookEntry objPBE : [
            SELECT Id, UnitPrice, Product2Id, ProductCode
            FROM PricebookEntry
            WHERE
                Pricebook2.Price_Book_OracleID__c = :priceBookOracleId
                AND ((Product2Id IN :setKitId
                AND Product2.RecordType.DeveloperName = 'Kit')
                OR (ProductCode IN :setKitProductCode
                AND Product2.RecordType.DeveloperName = 'Product'))
                AND IsActive = TRUE
                AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
        ]) {
            mapKitProductCode2MSRP.put(objPBE.ProductCode, objPBE.UnitPrice);
        }
    }

    private static Map<Id, List<WarrantyItem>> getKitId2WarrantyItemsInitMap(Set<Id> kitIds, Map<String, WarrantyItem> kitSequence2WarrantyItemMap, Set<Id> productIdSet){
        List<Kit_Item__c> kitItemList = new List<Kit_Item__c>([
            SELECT Sequence__c,
            Kit__c, Kit__r.ProductCode, Kit__r.Name, Kit__r.Brand_Name__c,
            Product__c,Product__r.ProductCode,Product__r.Type__c, Product__r.Brand_Name__c,
            Product__r.Name,Product__r.ProductModel__c
            FROM Kit_Item__c
            WHERE Kit__c IN :kitIds
            // AND Status__c = 'A'
            AND Product__r.IsActive = true
        ]);

        Map<Id, List<WarrantyItem>> kitId2WarrantyItemsInitMap = new Map<Id, List<WarrantyItem>>();
        for(Kit_Item__c kitItem : kitItemList){
            productIdSet.add(kitItem.Product__c);
            if(!kitId2WarrantyItemsInitMap.containsKey(kitItem.Kit__c)){
                kitId2WarrantyItemsInitMap.put(kitItem.Kit__c, new List<WarrantyItem>());
            }

            WarrantyItem warrantyItem = null;

            if(String.isNotBlank(kitItem.Sequence__c)){
                String kitSequenceKey = kitItem.Kit__c + '-' + kitItem.Sequence__c;
                warrantyItem = kitSequence2WarrantyItemMap.get(kitSequenceKey);
                //没有则新建
                if(warrantyItem == null){
                    warrantyItem = new WarrantyItem();
                    warrantyItem.sequence = kitItem.Sequence__c;
                    warrantyItem.haveSequenceProduct = TRUE;
                    //并且塞进map
                    kitSequence2WarrantyItemMap.put(kitSequenceKey, warrantyItem);

                    kitId2WarrantyItemsInitMap.get(kitItem.Kit__c).add(warrantyItem);
                }
                warrantyItem.productEntityList.add(new ProductEntity(kitItem.Product__c, kitItem.Product__r.ProductCode, kitItem.Product__r.Name));
            }

            //当不是 Sequence__c 时, 还是会等于 null
            //没有则新建
            if(warrantyItem == null){
                warrantyItem = new WarrantyItem();
            }
            warrantyItem.kitId = kitItem.Kit__c;
            warrantyItem.kitCode = kitItem.Kit__r.ProductCode;
            warrantyItem.kitName = kitItem.Kit__r.Name;
            warrantyItem.kitBrand = kitItem.Kit__r.Brand_Name__c;

            warrantyItem.productId = kitItem.Product__c;
            warrantyItem.productCode = kitItem.Product__r.ProductCode;
            warrantyItem.productName = kitItem.Product__r.Name;
            warrantyItem.productType = kitItem.Product__r.Type__c;
            warrantyItem.productModel = kitItem.Product__r.ProductModel__c;

            //haveSequenceProduct 此种类型的在创建初, 就 put 过, 无需再 put
            if(!warrantyItem.haveSequenceProduct){
                kitId2WarrantyItemsInitMap.get(kitItem.Kit__c).add(warrantyItem);
            }
        }

        return kitId2WarrantyItemsInitMap;
    }

    @AuraEnabled
    public static AuraResponseEntity getCustomerInfo(String email){
        AuraResponseEntity auraResponse = new AuraResponseEntity();
        AccountInfo endUserCustomer = new AccountInfo();
        if(String.isNotBlank(email)){
            String searchStr = '*'+email+'*';
            String searchQuery =
                ' FIND \'' + searchStr + '\' ' +
                ' IN email fields ' +
                ' RETURNING Account( ' +
                    ' Id, LastName, Firstname, Organization_Name__c, Eligible_For_Fleet__c, ' +
                    ' PersonEmail, Phone, Shippingcountry, Product_Type__c, ' +
                    ' ShippingStreet, ShippingPostalCode, ShippingCity, ShippingState,RecordTypeId ' +
                    ' WHERE RecordTypeId = \'' + CCM_Constants.CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_ID + '\'' +
                ' ) ';
            List<List<SObject>> objectList = Search.query(searchQuery);
            if(!objectList.isEmpty()){
                List<Account> accList = objectList[0];
                if(!accList.isEmpty()){
                    // if(accList.size() > 1){
                    //     for(Account ac : accList){
                    //         if(ac.RecordTypeId == CCM_Constants.CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_ID){
                    //              endUserCustomer = getAccountInfo(ac);
                    //         }
                    //     }
                    // }else{
                        endUserCustomer = getAccountInfo(accList[0]);
                    // }
                }
                // if(endUserCustomer == null ){
                //      endUserCustomer = getAccountInfo(accList[0]);
                // }
            }
        }
        auraResponse.data = endUserCustomer;
        return auraResponse;
    }

    public static AccountInfo getAccountInfo(Account account){
        AccountInfo accountInfo = new AccountInfo();
        accountInfo.id = account.Id;
        accountInfo.lastName = account.LastName;//80
        accountInfo.firstName = account.Firstname;//40
        accountInfo.country = account.Shippingcountry;//80
        accountInfo.zipPostalCode = account.ShippingPostalCode;//20
        accountInfo.orgName = account.Organization_Name__c;
        accountInfo.phone = account.Phone;//40
        accountInfo.city = account.ShippingCity;//40
        accountInfo.state = account.ShippingState;//80
        accountInfo.addressLine = account.ShippingStreet;//255
        accountInfo.emailAddress = account.PersonEmail;//80
        accountInfo.productType = account.Product_Type__c;
        accountInfo.eligibleForFleet = account.Eligible_For_Fleet__c;
        return accountInfo;
    }

    @AuraEnabled
    public static AuraResponseEntity uploadFile(String fleetClaimId, String fileName, String content) {
        //String base64Data = EncodingUtil.urlDecode(content, 'UTF-8');
        AuraResponseEntity auraResponse = new AuraResponseEntity();

        try{
            ContentVersion conVer = new ContentVersion();
            conVer.ContentLocation = 'S'; // S specify this document is in SF, use E for external files
            conVer.PathOnClient = fileName; // The files name, extension is very important here which will help the file in preview.
            conVer.Title = fileName; // Display name of the files
            conVer.VersionData = EncodingUtil.base64Decode(content); // converting your binary string to Blog
            INSERT conVer;

            List<ContentVersion> cVerList = new List<ContentVersion>([
                SELECT ContentDocumentId
                FROM ContentVersion
                WHERE Id =: conVer.Id
            ]);

            FileInfo fileInfo = new FileInfo();
            if(!cVerList.isEmpty()){
                fileInfo.contentId = conVer.Id;
                fileInfo.uploadFileName = fileName;
                fileInfo.contentUrl = '/sfc/servlet.shepherd/document/download/' + cVerList[0].ContentDocumentId + '?operationContext=S1';
                fileInfo.contentDocumentId = cVerList[0].ContentDocumentId;

                //如果记录已经存在了, 则关联记录
                if(String.isNotBlank(fleetClaimId)){
                    ContentDocumentLink cDoc = new ContentDocumentLink();
                    cDoc.ContentDocumentId  = cVerList[0].ContentDocumentId;
                    cDoc.LinkedEntityId = fleetClaimId;
                    cDoc.ShareType = 'V'; // Inferred permission, checkout description of ContentDocumentLink object for more details
                    cDoc.Visibility = 'AllUsers';
                    INSERT cDoc;

                    fileInfo.islinkedEntity = true;
                }
            }

            auraResponse.data = fileInfo;
        }catch(Exception ex){
            Quota_Generate_Utils.exceptionLog(ex, 'CCM_Community_FleetEditCtl', 'uploadFile');
            auraResponse.code = 400;
            auraResponse.message = 'Cause:' + ex.getCause() + '; Line:' + ex.getLineNumber() + '; Message:' + ex.getMessage() + '; Type:' + ex.getTypeName() + '; StackTrace:' + ex.getStackTraceString();
        }

        return auraResponse;
    }

    @AuraEnabled
    public static AuraResponseEntity deleteFile(String fileId) {
        AuraResponseEntity auraResponse = new AuraResponseEntity();
        try{
            List<ContentVersion> cvList = new List<ContentVersion>([
                SELECT Id, ContentDocumentId
                FROM ContentVersion
                WHERE Id = :fileId
            ]);
            if(!cvList.isEmpty()){
                List<ContentDocument> cdList = new List<ContentDocument>([
                    SELECT Id
                    FROM ContentDocument
                    WHERE Id = :cvList[0].ContentDocumentId
                ]);
                DELETE cdList;
            }
        }catch(Exception ex){
            Quota_Generate_Utils.exceptionLog(ex, 'CCM_Community_FleetEditCtl', 'deleteFile');
            auraResponse.code = 400;
            auraResponse.message = 'Cause:' + ex.getCause() + '; Line:' + ex.getLineNumber() + '; Message:' + ex.getMessage() + '; Type:' + ex.getTypeName() + '; StackTrace:' + ex.getStackTraceString();
        }
        return auraResponse;
    }

    @AuraEnabled
    public static AuraResponseEntity getAddressInfo(String zipPostalCode,String country) {
        AuraResponseEntity auraResponse = new AuraResponseEntity();
        auraResponse.data = CalloutService.getAddressByPostalCode(zipPostalCode,country);
        return auraResponse;
    }

    @AuraEnabled
    public static AuraResponseEntity checkSNAndUpdateIndicator(String fleetItemListStr){
        AuraResponseEntity auraResponse = new AuraResponseEntity();

        try{
            Map<String, Warranty_Rules__c> warrantyRulesMap = getWarrantyRulesMap();

            //获得系统中所有有效的验证规则  保证正则有  Name有
            Map<String,System_Configuration__c> snRegExpMap = QueryUtils.getSerialNumberRegExpMap();

            List<FleetItem> fleetItemList = (List<FleetItem>)JSON.deserialize(fleetItemListStr, List<FleetItem>.class);

            Map<String, List<Project_SN__c>> uniqueKey2ProjectSnsMap = getUniqueKey2ProjectSnsMap(fleetItemList);

            for(FleetItem fleetItem : fleetItemList){
                for(Warranty warranty : fleetItem.warrantyList){
                    for(WarrantyItem warrantyItem : warranty.warrantyItemList){
                        String uniqueKey = warrantyItem.kitBrand;
                        uniqueKey += '-' + warrantyItem.productCode;
                        checkSerialNumber(warrantyItem, warrantyRulesMap, snRegExpMap, uniqueKey2ProjectSnsMap.get(uniqueKey));
                    }
                }
            }

            auraResponse.data = fleetItemList;
        }catch(Exception ex){
            Quota_Generate_Utils.exceptionLog(ex, 'CCM_Community_FleetEditCtl', 'checkSNAndUpdateIndicator', fleetItemListStr);
            auraResponse.code = 400;
            auraResponse.message = 'Cause:' + ex.getCause() + '; Line:' + ex.getLineNumber() + '; Message:' + ex.getMessage() + '; Type:' + ex.getTypeName() + '; StackTrace:' + ex.getStackTraceString();
        }
        return auraResponse;
    }

    public static void checkSerialNumber(
        WarrantyItem warrantyItem,
        Map<String, Warranty_Rules__c> warrantyRulesMap,
        Map<String,System_Configuration__c> snRegExpMap,
        List<Project_SN__c> projectSnList
    ){
        if(String.isBlank(warrantyItem.serialNumber) || String.isBlank(warrantyItem.kitBrand)) return;

        warrantyItem.isFormatCorrect = true;
        warrantyItem.snFormatErrorMessage = '';
        //目前默认只有 EGO
        if(warrantyItem.kitBrand.equalsIgnoreCase('Skil')) {
            if(warrantyItem.serialNumber.length() != 9 && warrantyItem.serialNumber.length() != 3){
                warrantyItem.isFormatCorrect = false;
                warrantyItem.snFormatErrorMessage = 'The length of the SN should be 3 or 9 characters.';
                return;
            }
        }else if(warrantyItem.kitBrand.equalsIgnoreCase('SkilSaw') || warrantyItem.kitBrand.equalsIgnoreCase('Hammerhead')){
            if(warrantyItem.serialNumber.length() != 9){
                warrantyItem.isFormatCorrect = false;
                warrantyItem.snFormatErrorMessage = 'The length of the SN should be 9 characters.';
                return;
            }
        }else if(warrantyItem.kitBrand.equalsIgnoreCase('EGO')){
            if(
                warrantyItem.serialNumber.length() != 10
                && warrantyItem.serialNumber.length() != 14
                && warrantyItem.serialNumber.length() != 15
                && warrantyItem.serialNumber.length() != 16
            ){
                warrantyItem.isFormatCorrect = false;
                warrantyItem.snFormatErrorMessage = 'The length of the SN should be 10 or 14 or 15 or 16 characters.';
                return;
            }
        }

        if(
            warrantyItem.serialNumber.length() == 14
            || warrantyItem.serialNumber.length() == 15
        ){
            String productCodeInSN = '';
            if(warrantyItem.serialNumber.startsWith('R')){
                productCodeInSN = warrantyItem.serialNumber.subString(2,6).toUpperCase();
            }else{
                productCodeInSN = warrantyItem.serialNumber.subString(1,5).toUpperCase();
            }
            if(warrantyRulesMap.containsKey(warrantyItem.productCode)) {
                List<String> serialCodeList = new List<String>();
                if(String.isNotBlank(warrantyRulesMap.get(warrantyItem.productCode).Code_in_Serial__c)){
                    serialCodeList.addAll(warrantyRulesMap.get(warrantyItem.productCode).Code_in_Serial__c.split(';'));
                }
                if(!serialCodeList.contains(productCodeInSN)) {
                    warrantyItem.isFormatCorrect = false;
                    warrantyItem.snFormatErrorMessage = 'Warning: Entered SN may not match the product model, please double confirm.';
                    return;
                }
            }

            // if(
            //     warrantyRulesMap.get(productCodeInSN) != null
            //     && warrantyItem.productCode != warrantyRulesMap.get(productCodeInSN).NA_FC_Model__c
            //     && warrantyItem.productCode != warrantyRulesMap.get(productCodeInSN).NA_Model__c
            // ){
            //     warrantyItem.isFormatCorrect = false;
            //     warrantyItem.snFormatErrorMessage = 'Warning: Entered SN may not match the product model, please double confirm.';
            //     return;
            // }
        }else if(warrantyItem.serialNumber.length() == 16){
            String productCodeInSN = warrantyItem.serialNumber.subString(2,6);
            if(warrantyRulesMap.containsKey(warrantyItem.productCode)) {
                List<String> serialCodeList = new List<String>();
                if(String.isNotBlank(warrantyRulesMap.get(warrantyItem.productCode).Code_in_Serial__c)){
                    serialCodeList.addAll(warrantyRulesMap.get(warrantyItem.productCode).Code_in_Serial__c.split(';'));
                }
                if(!serialCodeList.contains(productCodeInSN)) {
                    warrantyItem.isFormatCorrect = false;
                    warrantyItem.snFormatErrorMessage = 'Warning: Entered SN may not match the product model, please double confirm.';
                    return;
                }
            }
            // if(
            //     warrantyRulesMap.get(productCodeInSN) != null
            //     && warrantyItem.productCode != warrantyRulesMap.get(productCodeInSN).NA_FC_Model__c
            //     && warrantyItem.productCode != warrantyRulesMap.get(productCodeInSN).NA_Model__c
            // ){
            //     warrantyItem.isFormatCorrect = false;
            //     warrantyItem.snFormatErrorMessage = 'Warning: Entered SN may not match the product model, please double confirm.';
            //     return;
            // }
        }


        if (!verifySerialNumber(warrantyItem.kitBrand, warrantyItem.serialNumber, snRegExpMap, warrantyItem.productCode)) {
            warrantyItem.isFormatCorrect = false;
            warrantyItem.snFormatErrorMessage = 'The SN format is not correct!';
            return;
        }

        // 检查用户输入的serial number是否处于召回的项目中
        //Check the serial number entered by the user is in the recalled project
        if(warrantyItem.isFormatCorrect && projectSnList != null){
            Boolean flag = false;
            for(Project_SN__c projectSn : projectSnList){
                String startSn = projectSn.Star_SN__c;
                String endSn = projectSn.End_SN__c;
                // 判断传入的serial number是否在区间内
                if(warrantyItem.serialNumber >= startSn && warrantyItem.serialNumber <= endSn){
                    flag = true;
                    break;
                }
            }
            if(flag){
                warrantyItem.isFormatCorrect = false;
                warrantyItem.snFormatErrorMessage = 'Warning: This product is in recall queue!';
                return;
            }
        }
    }

    /**
     *@Function: 校验sn格式是否符合要求
     */
    public static Boolean verifySerialNumber(String brandName, String serialNumber, Map<String,System_Configuration__c> snRegExpMap, String modal){
        //判断品牌为 null
        if(String.isBlank(brandName)){
            return false;
        }

        serialNumber = serialNumber.toUpperCase();
        if(snRegExpMap.isEmpty()){
            return true;
        }
        //根据品牌不同  SN校验不同  校验EGO Hamerhead
        List<System_Configuration__c> scList = snRegExpMap.values();

        //匹配正则表达式
        Pattern p;
        Matcher m;
        for(System_Configuration__c sc : scList){
            String regStr = null;
            //根据不同的品牌，查询对应的SN规则
            if(!String.isBlank(sc.Name) && sc.Name.containsIgnoreCase(brandName + '_')) {
                regStr = sc.RegExp__c;
            }

            if(!String.isBlank(regStr)){
                p = Pattern.compile(regStr);
                m = p.matcher(serialNumber);
                if(m.matches()){
                    return true;
                }
            }
        }
        // 允许特定产品并且在一定SN范围内的通过校验
        if(modal =='SC535801' && serialNumber.length() <= 9){
            Integer intSN = Integer.valueof(serialNumber);
            System.debug('222222222  '+ intSN);
            if(intSN >= Integer.valueof('420000001') && intSN <= Integer.valueof('420015000')){
                System.debug('420开头的SN');
                return true;
            }
        }

        if(modal =='NA1800B-00' && serialNumber.length() <= 9){
            Integer intSN = Integer.valueof(serialNumber);
            if(intSN >= Integer.valueof('420000001') && intSN <= Integer.valueof('420001848')){
                return true;
            }
        }

        return false;
    }

    // 检查用户输入的serial number是否处于召回的项目中
    private static Map<String, List<Project_SN__c>> getUniqueKey2ProjectSnsMap(List<FleetItem> fleetItemList){
        Set<String> productCodeSet = new Set<String>();
        Set<String> brandSet = new Set<String>();
        for(FleetItem fleetItem : fleetItemList){
            for(Warranty warranty : fleetItem.warrantyList){
                for(WarrantyItem warrantyItem : warranty.warrantyItemList){
                    if(String.isNotBlank(warrantyItem.productCode)){
                        productCodeSet.add(warrantyItem.productCode);
                    }
                    if(String.isNotBlank(warrantyItem.kitBrand)){
                        brandSet.add(warrantyItem.kitBrand);
                    }
                }
            }
        }

        Date today = Date.today();
        List<Project_SN__c> projectSnList = new List<Project_SN__c>([
            SELECT Id, Star_SN__c, End_SN__c,
            Project__c,
            Project__r.Brand_Name__c,
            Project__r.Product__c,
            Project__r.Product__r.ProductCode
            FROM Project_SN__c
            WHERE Project__r.Brand_Name__c IN :brandSet
            AND Project__r.Product__r.ProductCode IN :productCodeSet
            AND Project__r.Deadline__c >= :today
        ]);

        Map<String, List<Project_SN__c>> uniqueKey2ProjectSnsMap = new Map<String, List<Project_SN__c>>();

        for(Project_SN__c projectSn : projectSnList){
            if(projectSn.Project__c == null || projectSn.Project__r.Product__c == null) continue;
            String uniqueKey = projectSn.Project__r.Brand_Name__c;
            uniqueKey += '-' + projectSn.Project__r.Product__r.ProductCode;
            if(!uniqueKey2ProjectSnsMap.containsKey(uniqueKey)){
                uniqueKey2ProjectSnsMap.put(uniqueKey, new List<Project_SN__c>());
            }
            uniqueKey2ProjectSnsMap.get(uniqueKey).add(projectSn);
        }

        return uniqueKey2ProjectSnsMap;
    }

    private static Map<String, Warranty_Rules__c> getWarrantyRulesMap(){
        Map<String, Warranty_Rules__c> warrantyRulesMap = new Map<String, Warranty_Rules__c>();

        for(Warranty_Rules__c warrantyRules : [
            SELECT Code_in_Serial__c, NA_FC_Model__c, NA_Model__c
            FROM  Warranty_Rules__c
            WHERE RecordType.Name = 'Model# - Code'
        ]){
            // warrantyRulesMap.put(warrantyRules.Code_in_Serial__c, warrantyRules);
            if(String.isNotBlank(warrantyRules.NA_Model__c)) {
                warrantyRulesMap.put(warrantyRules.NA_Model__c, warrantyRules);
            }
            if(String.isNotBlank(warrantyRules.NA_FC_Model__c)) {
                warrantyRulesMap.put(warrantyRules.NA_FC_Model__c, warrantyRules);
            }
        }

        return warrantyRulesMap;
    }

    @AuraEnabled
    public static AuraResponseEntity saveFleetClaim(String fleetClaimStr, String type){
        AuraResponseEntity auraResponse = new AuraResponseEntity();
        SavePoint sp = Database.setSavePoint();
        try{
            FleetClaim fleetClaim = (FleetClaim)JSON.deserialize(fleetClaimStr, FleetClaim.class);
            upsertEndUserCustomer(fleetClaim);
            upsertFleetClaim(fleetClaim);
            upsertFleetClaimItem(fleetClaim, type);
            upsertWarranty(fleetClaim);
            upsertWarrantyItem(fleetClaim);
            upsertFileInfoList(fleetClaim);

            if(type == 'Submit'){
                submitForApproval(fleetClaim.id);
                fleetClaim.approvalStatus = 'Pending Approval';
            }

            auraResponse.data = fleetClaim;
        }catch(Exception ex){
            Database.rollback(sp);
            Quota_Generate_Utils.exceptionLog(ex, 'CCM_Community_FleetEditCtl', 'saveFleetClaim', fleetClaimStr);
            auraResponse.code = 400;
            auraResponse.message = 'Cause:' + ex.getCause() + '; Line:' + ex.getLineNumber() + '; Message:' + ex.getMessage() + '; Type:' + ex.getTypeName() + '; StackTrace:' + ex.getStackTraceString();
        }
        return auraResponse;
    }

    private static void upsertEndUserCustomer(FleetClaim fleetClaim){
        if(fleetClaim.endUserCustomer != null && String.isNotBlank(fleetClaim.endUserCustomer.lastName)){
            Account endUserCustomerObj = new Account();
            if(String.isNotBlank(fleetClaim.endUserCustomer.id)){
                endUserCustomerObj.Id = fleetClaim.endUserCustomer.id;
            }else{
                //没有 id, 说明是新增客户
                List<RecordType> rtList = new List<RecordType>([
                    SELECT Id
                    FROM RecordType
                    WHERE SObjectType = 'Account'
                    AND Name = 'End User Account'
                ]);
                if(!rtList.isEmpty()){
                    endUserCustomerObj.RecordTypeId = rtList[0].Id;
                }
            }
            endUserCustomerObj.LastName = fleetClaim.endUserCustomer.lastName;
            endUserCustomerObj.Firstname = fleetClaim.endUserCustomer.firstName;
            endUserCustomerObj.Shippingcountry = fleetClaim.endUserCustomer.country;
            endUserCustomerObj.ShippingPostalCode = fleetClaim.endUserCustomer.zipPostalCode;
            endUserCustomerObj.Organization_Name__c = fleetClaim.endUserCustomer.orgName;
            endUserCustomerObj.Phone = fleetClaim.endUserCustomer.phone;
            endUserCustomerObj.ShippingCity = fleetClaim.endUserCustomer.city;
            endUserCustomerObj.ShippingState = fleetClaim.endUserCustomer.state;
            endUserCustomerObj.ShippingStreet = fleetClaim.endUserCustomer.addressLine;
            endUserCustomerObj.PersonEmail = fleetClaim.endUserCustomer.emailAddress;
            //处理 Product_Type__c 字段
            if(fleetClaim.endUserCustomer.productType == null){
                fleetClaim.endUserCustomer.productType = '';
            }
            List<String> productTypeList = fleetClaim.endUserCustomer.productType.split(';');
            if(!productTypeList.contains(fleetClaim.brand)){
                productTypeList.add(fleetClaim.brand);
            }
            endUserCustomerObj.Product_Type__c = String.join(productTypeList, ';');

            UPSERT endUserCustomerObj;

            //补新增数据的id
            fleetClaim.endUserCustomer.id = endUserCustomerObj.Id;
            fleetClaim.endUserCustomer.productType = endUserCustomerObj.Product_Type__c;
        }
    }

    private static void upsertFleetClaim(FleetClaim fleetClaim){
        Fleet_Claim__c fleetClaimObj = new Fleet_Claim__c();
        if(String.isNotBlank(fleetClaim.id)){
            fleetClaimObj.Id = fleetClaim.id;
        }

        fleetClaimObj.Approval_Status__c = fleetClaim.approvalStatus;
        fleetClaimObj.Submitter__c = UserInfo.getUserId();

        fleetClaimObj.Brand_Name__c = fleetClaim.brand;
        fleetClaimObj.Product_Use_Type__c = fleetClaim.primaryUse;
        fleetClaimObj.Invoice_Number__c = fleetClaim.invoiceNumber;
        if(String.isNotBlank(fleetClaim.salesDateStr)){
            fleetClaimObj.Sales_Date__c = Date.valueOf(fleetClaim.salesDateStr);
        }

        fleetClaimObj.Fleet_Program_Rule__c = fleetClaim.fleetProgramRuleId;
        fleetClaimObj.Bill_To_Address__c = getAddressesWithAuthorizedBrandId(fleetClaim.billToAddressId, fleetClaim.brand);
        fleetClaimObj.Ship_To_Address__c = getShipToAddressesWithAuthorizedBrandId(fleetClaim.shipToAddressId);
        if(fleetClaim.channelCustomer != null){
            fleetClaimObj.Channel_Customer__c = fleetClaim.channelCustomer.id;
            fleetClaimObj.Place_of_Purchase__c = fleetClaim.channelCustomer.name;
            fleetClaimObj.Place_of_Purchase_picklist__c = AUTHORIZED_DEALER;
        }
        if(fleetClaim.endUserCustomer != null){
            fleetClaimObj.End_User_Customer__c = fleetClaim.endUserCustomer.id;
        }
        // fleetClaimObj.Deliver_At_Once__c = fleetClaim.deliverAtOnce;
        fleetClaim.deliverAtOnceLabel = yesOrNoMap.get(fleetClaim.deliverAtOnce);

        UPSERT fleetClaimObj;

        //补新增数据的id
        fleetClaim.id = fleetClaimObj.Id;
        fleetClaim.billToAddressName = getAddressInfoById(fleetClaim.billToAddressId);
        fleetClaim.shipToAddressName = getAddressInfoById(fleetClaim.shipToAddressId);
        if(String.isBlank(fleetClaim.name)){
            fleetClaim.name = getFleetClaimName(fleetClaim.id);
        }
    }

    private static Id getAddressesWithAuthorizedBrandId(String addressesId, String brand){
        Id idBAWB = null;
        for (Address_With_Program__c objBAWB : [
            SELECT Id
            FROM Address_With_Program__c
            WHERE
                Account_Address__c = :addressesId
                AND Account_Address__r.RecordTypeId = :CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID
                AND Program__r.Brands__c = :brand
                AND Program__r.RecordTypeId IN (
                    :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_ID,
                    :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_ID
                )
                AND Program__r.Approval_Status__c = :CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED
                AND Customer_Line_Oracle_ID__c != NULL
            WITH SECURITY_ENFORCED
            LIMIT 1
        ]) {
            idBAWB = objBAWB.Id;
        }
        return idBAWB;
    }

    private static Id getShipToAddressesWithAuthorizedBrandId(String addressesId){
        Id idBAWB = null;
        for (Address_With_Program__c objBAWB : [
            SELECT Id
            FROM Address_With_Program__c
            WHERE
                Account_Address__c = :addressesId
                AND Status__c = 'A'
                AND (Account_Address__r.RecordType.DeveloperName = :CCM_Constants.ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME OR Account_Address__r.RecordType.DeveloperName = :CCM_Constants.ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME)
            LIMIT 1
        ]) {
            idBAWB = objBAWB.Id;
        }
        return idBAWB;
    }

    private static void upsertFleetClaimItem(FleetClaim fleetClaim, String type){
        if(fleetClaim.fleetItemList != null){
            Map<String, Fleet_Claim_Item__c> fleetItemObjMap = new Map<String, Fleet_Claim_Item__c>();
            Set<Id> existIdSet = new Set<Id>();
            Set<Id> deleteIdSet = new Set<Id>();
            for(Integer i = fleetClaim.fleetItemList.size() - 1; i >= 0; i--){
                FleetItem fleetItem = fleetClaim.fleetItemList[i];
                if(fleetItem.qtyPurchased >= 1){
                    Fleet_Claim_Item__c fleetItemObj = new Fleet_Claim_Item__c();
                    String idTemp;
                    if(String.isNotBlank(fleetItem.id)){
                        fleetItemObj.Id = fleetItem.id;
                        idTemp = fleetItem.id;
                        existIdSet.add(fleetItem.id);
                    }else{
                        idTemp = fleetItem.fakeId;
                        fleetItemObj.Fleet_Claim__c = fleetClaim.id;
                    }
                    fleetItemObj.Dealer_Rebate__c = fleetItem.dealerRebate;
                    fleetItemObj.Fleet_Discount__c = fleetItem.fleetDiscount;
                    fleetItemObj.Kit__c = fleetItem.kitId;
                    fleetItemObj.MSRP__c = fleetItem.msrp;
                    fleetItemObj.Qty_Purchased__c = fleetItem.qtyPurchased;
                    fleetItemObj.Unit_Sales_Price__c = fleetItem.unitSalesPrice;

                    fleetItemObjMap.put(idTemp, fleetItemObj);
                    // System.debug('fleetItemObjMap.put(idTemp, fleetItemObj)');
                }else{
                    //如果原先有id, 则需要把其下的 warranty 也删除
                    if(String.isNotBlank(fleetItem.id)){
                        deleteIdSet.add(fleetItem.id);
                    }
                    // 如果数量小于1, 则置空 id, 不保存
                    fleetItem.id = null;
                    if(type == 'Submit'){
                        fleetClaim.fleetItemList.remove(i);
                    }
                }
            }

            if(!deleteIdSet.isEmpty()){
                deleteNotExistWarrantyByFleetClaimItemIds(deleteIdSet);
            }

            deleteNotExistFleetClaimItem(fleetClaim.id, existIdSet);

            // System.debug('fleetItemObjMap: ' + JSON.serialize(fleetItemObjMap.values()));

            if(!fleetItemObjMap.values().isEmpty()){
                UPSERT fleetItemObjMap.values();

                //补新增数据的id
                for(FleetItem fleetItem : fleetClaim.fleetItemList){
                    if(String.isBlank(fleetItem.id)){
                        Fleet_Claim_Item__c fleetItemObj = fleetItemObjMap.get(fleetItem.fakeId);
                        if(fleetItemObj != null){
                            fleetItem.id = fleetItemObj.Id;
                        }
                    }
                }
            }
        }
    }

    private static void deleteNotExistFleetClaimItem(String fleetClaimId, Set<Id> existIdSet){
        List<Fleet_Claim_Item__c> fleetClaimItemList = new List<Fleet_Claim_Item__c>([
            SELECT Id
            FROM Fleet_Claim_Item__c
            WHERE Fleet_Claim__c = :fleetClaimId
            AND Id NOT IN :existIdSet
        ]);

        if(!fleetClaimItemList.isEmpty()) DELETE fleetClaimItemList;
    }

    private static void deleteNotExistWarrantyByFleetClaimItemIds(Set<Id> fleetClaimItemIds){
        List<Warranty__c> warrantyList = new List<Warranty__c>([
            SELECT Id
            FROM Warranty__c
            WHERE Fleet_Claim_Item__c IN :fleetClaimItemIds
            AND Created_By_Fleet__c = TRUE //只删除由 fleet 创建的 warranty, 如果是匹配的 warranty, 在删除 FleetClaimItem 之后, 会自动取消关联
        ]);

        if(!warrantyList.isEmpty()) DELETE warrantyList;
    }

    private static void upsertWarranty(FleetClaim fleetClaim){
        if(fleetClaim.fleetItemList != null){
            String dealerId = CCM_PortalPageUtil.getCustomerByUser(UserInfo.getUserId());
            String actualAccount = dealerId;
            String year = String.valueOf(date.today().year());
            String month = String.valueOf(date.today().month());
            Boolean is2ACE = false;
            String claimPackId = '';
            Decimal totalAmount = 0;
            Set<String> recordTypeIdSet = new Set<String>{CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_ID,CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_ID};
            List<Account> secondDealer = [SELECT Id,ParentId,Parent.AccountNumber,ORG_Code__c,Sales_Group__c,Customer_Cluster__c,OwnerId FROM Account WHERE Id =:dealerId];
            List<Address_With_Program__c> awpList = [SELECT Id
                                                FROM Address_With_Program__c
                                                WHERE Program__r.Customer__c = :actualAccount
                                                AND Status__c = 'A'
                                                AND Address_Type__c ='Billing Address'
                                                AND  Program__r.Approval_Status__c ='Approved'
                                                AND Program__r.RecordTypeId IN :recordTypeIdSet];
            if(secondDealer != null && secondDealer.size() > 0 && secondDealer[0].ParentId != null){
                is2ACE = true;
            }
            if(!is2ACE){
                for(Claim_Pack__c cp : [SELECT Id, Amount__c FROM Claim_Pack__c WHERE Year__c =:year AND Month__c =:month AND Type__c ='Registration Claim' AND Channel_Customer__c =:actualAccount]){
                    claimPackId = cp.Id;
                    totalAmount = cp.Amount__c;
                }
            }
            Boolean isCanPack = false;
            Set<String> salesGroup = new Set<String>{'SG21','SG22','SG23','SG24','SG25','SG26','SG30','SG31','SG32','SG43','SG44','SG45','SG46','SG47','SG48','SG49','SG50','SG51','SG54'};
            Set<String> customerCluster = new Set<String>{'CA-CG03','CA-CG05','CA-CG11'};
            if(secondDealer != null && secondDealer.size() >0){
                if(secondDealer[0].ORG_Code__c == 'CCA'){
                    if(customerCluster.contains(secondDealer[0].Customer_Cluster__c)){
                        isCanPack = true;
                    }
                }else{
                    if(salesGroup.contains(secondDealer[0].Sales_Group__c)){
                        isCanPack = true;
                    }
                }
                List<String> customerOwnerList = Label.CustomerOwner.split(',');
                if(customerOwnerList.contains(secondDealer[0].OwnerId)){
                    isCanPack = false;
                }
            }
            //在保存之前, 先检查系统中是否有已经注册过的 warranty, 如果有则不新建, 直接匹配
            replaceWarranty(fleetClaim);
            Map<String, Warranty__c> warrantyObjMap = new Map<String, Warranty__c>();
            Set<Id> existIdSet = new Set<Id>();
            Set<String> existingSNs = getExistingSN(fleetClaim.fleetItemList, dealerId);
            for(FleetItem fleetItem : fleetClaim.fleetItemList){
                if(!(fleetItem.qtyPurchased >= 1)) continue;
                if(fleetItem.warrantyList == null) continue;
                for(Warranty warranty : fleetItem.warrantyList){
                    Warranty__c warrantyObj = new Warranty__c();

                    String idTemp;
                    if(String.isNotBlank(warranty.id)){
                        warrantyObj.Id = warranty.id;
                        idTemp = warranty.id;
                        existIdSet.add(warranty.id);
                    }else{
                        idTemp = warranty.fakeId;
                        //当执行 replaceWarranty 之后,
                        //还是没有 id 则说明是新建的
                        warrantyObj.Created_By_Fleet__c = true;
                        warranty.createdByFleet = true;
                    }
                    Boolean isHaveSN = false;
                    Boolean isSameSN = false;
                    if(warranty.warrantyItemList.size() > 0){
                        for(WarrantyItem warrantyItem : warranty.warrantyItemList){
                            if(String.isNotBlank(warrantyItem.serialNumber)){
                                isHaveSN = true;
                            }
                            isSameSN = findDealerHaveSameSerialNum(warrantyItem.serialNumber, existingSNs);
                        }
                    }
                    String waBrandName = fleetClaim.brand;
                    if(isHaveSN && !is2ACE && waBrandName == 'EGO' && isCanPack && !isSameSN){
                        if(claimPackId == ''){
                            Claim_Pack__c claimPack = new Claim_Pack__c();
                            claimPack.Year__c = year;
                            claimPack.Month__c = month;
                            claimPack.Channel_Customer__c = dealerId;
                            if(awpList.size() > 0){
                                claimPack.Bill_To_Address__c = awpList[0].Id;
                            }
                            claimPack.Type__c = 'Registration Claim';
                            if(secondDealer != null && secondDealer.size() >0){
                                if(secondDealer[0].ORG_Code__c == 'CCA'){
                                    claimPack.CurrencyIsoCode = 'CAD';
                                }else{
                                    claimPack.CurrencyIsoCode = 'USD';
                                }
                            }
                            Insert claimPack;
                            claimPackId = claimPack.Id;
                        }
                        warrantyObj.Claim_Pack__c = claimPackId;

                        if(secondDealer[0].ORG_Code__c == 'CCA'){
                            totalAmount += 5;
                            totalAmount += 5 * 0.13;
                        }else{
                            totalAmount += 5;
                        }
                    }

                    warrantyObj.Fleet_Claim_Item__c = fleetItem.id;

                    if(fleetClaim.endUserCustomer != null){
                        warrantyObj.AccountCustomer__c = fleetClaim.endUserCustomer.id;
                    }

                    if(fleetClaim.channelCustomer != null){
                        warrantyObj.Place_of_Purchase__c = fleetClaim.channelCustomer.name;
                        warrantyObj.Place_of_Purchase_picklist__c = AUTHORIZED_DEALER;
                    }

                    if(String.isNotBlank(fleetClaim.salesDateStr)){
                        warrantyObj.Purchase_Date__c = Date.valueOf(fleetClaim.salesDateStr);
                    }else{
                        warrantyObj.Purchase_Date__c = null;
                    }

                    warrantyObj.Master_Product__c = fleetItem.kitId;
                    warrantyObj.Product_Use_Type2__c = fleetClaim.primaryUse;
                    warrantyObj.Brand_Name__c = fleetClaim.brand;
                    warrantyObj.Send_confirmation__c = true;//避免触发 work flow, 参照自 product registration
                    warrantyObj.Pending__c = TRUE;//默认 pending
                    warrantyObj.Receipt_received_and_verified__c = false;//默认 为 false

                    warrantyObjMap.put(idTemp, warrantyObj);
                }
            }


            deleteNotExistWarranty(fleetClaim.id, existIdSet);

            if(!warrantyObjMap.values().isEmpty()){
                UPSERT warrantyObjMap.values();
                if(claimPackId != ''){
                    List<Claim_Pack__c> claimList =  [SELECT Id,Channel_Customer__c,Channel_Customer__r.ORG_Code__c,Amount__c  FROM Claim_Pack__c  WHERE Id = :claimPackId];
                    if(claimList != null && claimList.size() > 0){
                        claimList[0].Amount__c = totalAmount;
                    }
                    update claimList;
                }
                //补新增数据的id
                for(FleetItem fleetItem : fleetClaim.fleetItemList){
                    if(!(fleetItem.qtyPurchased >= 1)) continue;
                    if(fleetItem.warrantyList == null) continue;
                    for(Warranty warranty : fleetItem.warrantyList){
                        if(String.isBlank(warranty.id)){
                            Warranty__c warrantyObj = warrantyObjMap.get(warranty.fakeId);
                            if(warrantyObj != null){
                                warranty.id = warrantyObj.Id;
                            }
                        }
                    }
                }
            }
        }
    }

    private static Set<String> getExistingSN(List<FleetItem> fleetItems, String dealer) {
        Set<String> serialNumbers = new Set<String>();
        for(FleetItem fleetItem : fleetItems) {
            if(!(fleetItem.qtyPurchased >= 1)) continue;
            if(fleetItem.warrantyList == null) continue;
            for(Warranty warranty : fleetItem.warrantyList){
                if(warranty.warrantyItemList.size() > 0){
                    for(WarrantyItem warrantyItem : warranty.warrantyItemList){
                        if(String.isNotBlank(warrantyItem.serialNumber)){
                            serialNumbers.add(warrantyItem.serialNumber);
                        }
                    }
                }
            }
        }
        Set<String> existingSNs = new Set<String>();
        for(Warranty_Item__c warrantyItem : [SELECT Serial_Number__c FROM Warranty_Item__c WHERE Serial_Number__c IN :serialNumbers AND CreatedBy.Contact.AccountId = :dealer]) {
            existingSNs.add(warrantyItem.Serial_Number__c);
        }
        return existingSNs;
    }

    //检查dealer注册时填写的SN是否与该dealer注册过的SN重复
    @TestVisible
    private static Boolean findDealerHaveSameSerialNum(String serialNumber, Set<String> existingSNs){
        Boolean isSameSN = false;
        if(existingSNs.contains(serialNumber)){
            isSameSN = true;
        }
        return isSameSN;
    }

    private static void deleteNotExistWarranty(String fleetClaimId, Set<Id> existIdSet){
        List<Warranty__c> warrantyList = new List<Warranty__c>([
            SELECT Id
            FROM Warranty__c
            WHERE Fleet_Claim_Item__r.Fleet_Claim__c = :fleetClaimId
            AND Id NOT IN :existIdSet
            AND Created_By_Fleet__c = TRUE //只删除由 fleet 创建的 warranty, 如果是匹配的 warranty, 在删除 FleetClaimItem 之后, 会自动取消关联
        ]);

        if(!warrantyList.isEmpty()) DELETE warrantyList;
    }

    private class WarrantyItemFilter{
        //Warranty
        private Id endUserCustomerId;
        private String channelCustomerName;
        private String primaryUse;
        private String brand;
        private Set<Id> kitIdSet;

        public WarrantyItemFilter(){
            this.kitIdSet = new Set<Id>();
        }
    }

    private static Map<String, Map<Id, Map<String, Id>>> getUniqueKey2WarrantysMap(WarrantyItemFilter warrantyItemFilter, String fleetId){
        List<Warranty_Item__c> warrantyItemList = new List<Warranty_Item__c>([
            SELECT Id, Product__c, Serial_Number__c,
            Warranty__c,
            Warranty__r.AccountCustomer__c,
            Warranty__r.Place_of_Purchase__c,
            Warranty__r.Brand_Name__c,
            Warranty__r.Product_Use_Type2__c,
            Warranty__r.Master_Product__c,
            Warranty__r.Fleet_Claim_Item__c,
            Warranty__r.Fleet_Claim_Item__r.Fleet_Claim__c
            FROM Warranty_Item__c
            WHERE Warranty__r.AccountCustomer__c = :warrantyItemFilter.endUserCustomerId
            AND Warranty__r.Master_Product__c IN :warrantyItemFilter.kitIdSet
            AND Warranty__r.Place_of_Purchase__c = :warrantyItemFilter.channelCustomerName
            AND Warranty__r.Product_Use_Type2__c = :warrantyItemFilter.primaryUse
            AND Warranty__r.Brand_Name__c = :warrantyItemFilter.brand
        ]);

        Map<String, Map<Id, Map<String, Id>>> uniqueKey2WarrantysMap = new Map<String, Map<Id, Map<String, Id>>>();
        for(Warranty_Item__c warrantyItem : warrantyItemList){

            if(
                warrantyItem.Warranty__r.Fleet_Claim_Item__c != null
                && warrantyItem.Warranty__r.Fleet_Claim_Item__r.Fleet_Claim__c != fleetId
            ){
                //需要排除已经匹配上的 warranty, 并且不属于当前 Fleet Claim 的
                continue;
            }

            String uniqueKey = warrantyItem.Warranty__r.AccountCustomer__c;//end account id
            uniqueKey += '-' + warrantyItem.Warranty__r.Place_of_Purchase__c;//channel account name
            uniqueKey += '-' + warrantyItem.Warranty__r.Brand_Name__c;//brand
            uniqueKey += '-' + warrantyItem.Warranty__r.Product_Use_Type2__c;//primary use
            uniqueKey += '-' + warrantyItem.Warranty__r.Master_Product__c;//kit id

            if(!uniqueKey2WarrantysMap.containsKey(uniqueKey)){
                uniqueKey2WarrantysMap.put(uniqueKey, new Map<Id, Map<String, Id>>());
            }

            Map<Id, Map<String, Id>> warrantyId2ItemUniqueKeysMap = uniqueKey2WarrantysMap.get(uniqueKey);

            if(!warrantyId2ItemUniqueKeysMap.containsKey(warrantyItem.Warranty__c)){
                warrantyId2ItemUniqueKeysMap.put(warrantyItem.Warranty__c, new Map<String, Id>());
            }

            String itemUniqueKey = warrantyItem.Product__c + '-' + warrantyItem.Serial_Number__c;
            warrantyId2ItemUniqueKeysMap.get(warrantyItem.Warranty__c).put(itemUniqueKey, warrantyItem.Id);
        }
        return uniqueKey2WarrantysMap;
    }

    //在保存之前, 先检查系统中是否有已经注册过的 warranty, 如果有则不新建, 直接匹配
    private static void replaceWarranty(FleetClaim fleetClaim){
        WarrantyItemFilter warrantyItemFilter = new WarrantyItemFilter();
        warrantyItemFilter.brand = fleetClaim.brand;
        warrantyItemFilter.primaryUse = fleetClaim.primaryUse;
        if(fleetClaim.endUserCustomer != null){
            warrantyItemFilter.endUserCustomerId = fleetClaim.endUserCustomer.id;
        }
        if(fleetClaim.channelCustomer != null){
            warrantyItemFilter.channelCustomerName = fleetClaim.channelCustomer.name;
        }
        for(FleetItem fleetItem : fleetClaim.fleetItemList){
            if(!(fleetItem.qtyPurchased >= 1)) continue;
            if(fleetItem.warrantyList == null) continue;
            for(Warranty warranty : fleetItem.warrantyList){
                warrantyItemFilter.kitIdSet.add(warranty.kitId);
            }
        }

        //获取系统现存的 warranty, 如果匹配上了, 就不新建 warranty, 直接匹配, 否则新建
        if(!warrantyItemFilter.kitIdSet.isEmpty()){
            Map<String, Map<Id, Map<String, Id>>> uniqueKey2WarrantysMap = getUniqueKey2WarrantysMap(warrantyItemFilter, fleetClaim.id);

            Set<Id> cancelWarrantyIdSet = new Set<Id>();

            for(FleetItem fleetItem : fleetClaim.fleetItemList){
                if(!(fleetItem.qtyPurchased >= 1)) continue;
                if(fleetItem.warrantyList == null) continue;
                for(Warranty warranty : fleetItem.warrantyList){
                    Id warrantyIdTemp = null;
                    Map<String, Id> itemUniqueKey2itemIdMap = new Map<String, Id>();
                    if(warranty.warrantyItemList != null){
                        Set<String> itemUniqueKeySet = new Set<String>();
                        for(WarrantyItem warrantyItem : warranty.warrantyItemList){
                            String itemUniqueKey = warrantyItem.productId + '-' + warrantyItem.serialNumber;
                            itemUniqueKeySet.add(itemUniqueKey);
                        }
                        if(!itemUniqueKeySet.isEmpty()){
                            String uniqueKey = '';
                            if(fleetClaim.endUserCustomer != null){
                                uniqueKey = fleetClaim.endUserCustomer.id;//end account id
                            }
                            if(fleetClaim.channelCustomer != null){
                                uniqueKey += '-' + fleetClaim.channelCustomer.name;//channel account name
                            }
                            uniqueKey += '-' + fleetClaim.brand;//brand
                            uniqueKey += '-' + fleetClaim.primaryUse;//primary use
                            uniqueKey += '-' + fleetItem.kitId;//kit id

                            Map<Id, Map<String, Id>> warrantyId2ItemUniqueKeysMap = uniqueKey2WarrantysMap.get(uniqueKey);
                            if(warrantyId2ItemUniqueKeysMap != null){
                                for(Id warrantyId : warrantyId2ItemUniqueKeysMap.keySet()){
                                    itemUniqueKey2itemIdMap = warrantyId2ItemUniqueKeysMap.get(warrantyId);
                                    //如果填写的序列号也一致, 则可以为匹配成功
                                    if(itemUniqueKey2itemIdMap != null && itemUniqueKey2itemIdMap.keySet().equals(itemUniqueKeySet)){
                                        warrantyIdTemp = warrantyId;
                                        //一旦匹配上, 则要移除, 防止下一批也匹配上
                                        warrantyId2ItemUniqueKeysMap.remove(warrantyId);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    //匹配成功, 并且与原来的 warranty id 不一致
                    if(String.isNotBlank(warrantyIdTemp) && warranty.id != warrantyIdTemp){
                        //如果之前有值, 则之前那个要取消关联
                        if(String.isNotBlank(warranty.id)){
                            cancelWarrantyIdSet.add(warranty.id);
                        }
                        //进行替换
                        warranty.id = warrantyIdTemp;
                        //不是由 fleet 创建的, 是由匹配关联的
                        warranty.createdByFleet = false;
                        if(warranty.warrantyItemList == null || itemUniqueKey2itemIdMap == null) continue;
                        for(WarrantyItem warrantyItem : warranty.warrantyItemList){
                            //替换 item id
                            String itemUniqueKey = warrantyItem.productId + '-' + warrantyItem.serialNumber;
                            warrantyItem.id = itemUniqueKey2itemIdMap.get(itemUniqueKey);
                        }
                    }
                    //如果匹配不成功, 并且之前不是由 fleet 创建的, 是由匹配关联的, 则改成这次算新建
                    else if(String.isBlank(warrantyIdTemp) && warranty.createdByFleet != true){
                        //如果之前有值, 则之前那个要取消关联
                        if(String.isNotBlank(warranty.id)){
                            cancelWarrantyIdSet.add(warranty.id);
                        }
                        //置空 id
                        warranty.id = null;
                        //不由 fleet 创建的, 不是由匹配关联的
                        warranty.createdByFleet = true;
                        if(warranty.warrantyItemList == null) continue;
                        for(WarrantyItem warrantyItem : warranty.warrantyItemList){
                            //置空 item id
                            warrantyItem.id = null;
                        }
                    }
                }
            }

            cancelWarranty(cancelWarrantyIdSet);
        }
    }

    private static void cancelWarranty(Set<Id> cancelWarrantyIdSet){
        List<Warranty__c> warrantyList = new List<Warranty__c>([
            SELECT Id, Fleet_Claim_Item__c
            FROM Warranty__c
            WHERE Id IN :cancelWarrantyIdSet
            AND Created_By_Fleet__c = false //取消关联那些不是由 Fleet 创建的 Warranty
        ]);

        for(Warranty__c warranty : warrantyList){
            warranty.Fleet_Claim_Item__c = null;
        }

        if(!warrantyList.isEmpty()) UPDATE warrantyList;
    }

    private static void upsertWarrantyItem(FleetClaim fleetClaim){
        if(fleetClaim.fleetItemList != null){
            Map<String, Warranty_Item__c> warrantyItemObjMap = new Map<String, Warranty_Item__c>();
            Set<Id> existIdSet = new Set<Id>();
            for(FleetItem fleetItem : fleetClaim.fleetItemList){
                if(!(fleetItem.qtyPurchased >= 1)) continue;
                if(fleetItem.warrantyList == null) continue;
                for(Warranty warranty : fleetItem.warrantyList){
                    if(warranty.warrantyItemList == null) continue;
                    for(WarrantyItem warrantyItem : warranty.warrantyItemList){
                        Warranty_Item__c warrantyItemObj = new Warranty_Item__c();

                        String idTemp;
                        if(String.isNotBlank(warrantyItem.id)){
                            warrantyItemObj.Id = warrantyItem.id;
                            idTemp = warrantyItem.id;
                            existIdSet.add(warrantyItem.id);
                        }else{
                            idTemp = warrantyItem.fakeId;
                            warrantyItemObj.Warranty__c = warranty.id;
                        }
                        warrantyItemObj.Product__c = warrantyItem.productId;
                        warrantyItemObj.Product_Code__c = warrantyItem.productCode;
                        warrantyItemObj.Product_Name__c = warrantyItem.productName;
                        warrantyItemObj.Product_Model__c = warrantyItem.productModel;
                        warrantyItemObj.Serial_Number__c = warrantyItem.serialNumber;
                        warrantyItemObj.Product_Type__c = warrantyItem.productType;

                        warrantyItemObjMap.put(idTemp, warrantyItemObj);
                    }
                }
            }

            deleteNotExistWarrantyItem(fleetClaim.id, existIdSet);

            if(!warrantyItemObjMap.values().isEmpty()){
                UPSERT warrantyItemObjMap.values();
                //补新增数据的id
                for(FleetItem fleetItem : fleetClaim.fleetItemList){
                    if(!(fleetItem.qtyPurchased >= 1)) continue;
                    if(fleetItem.warrantyList == null) continue;
                    for(Warranty warranty : fleetItem.warrantyList){
                        if(warranty.warrantyItemList == null) continue;
                        for(WarrantyItem warrantyItem : warranty.warrantyItemList){
                            if(String.isBlank(warrantyItem.id)){
                                Warranty_Item__c warrantyItemObj = warrantyItemObjMap.get(warrantyItem.fakeId);
                                if(warrantyItemObj != null){
                                    warrantyItem.id = warrantyItemObj.Id;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private static void deleteNotExistWarrantyItem(String fleetClaimId, Set<Id> existIdSet){
        List<Warranty_Item__c> warrantyItemList = new List<Warranty_Item__c>([
            SELECT Id
            FROM Warranty_Item__c
            WHERE Warranty__r.Fleet_Claim_Item__r.Fleet_Claim__c = :fleetClaimId
            AND Id NOT IN :existIdSet
            AND Warranty__r.Created_By_Fleet__c = false //只删除由 fleet 创建的 warranty item, 如果是匹配的 warranty item, 在删除 FleetClaimItem 之后, 会自动取消关联
        ]);

        if(!warrantyItemList.isEmpty()) DELETE warrantyItemList;
    }

    private static void upsertFileInfoList(FleetClaim fleetClaim){
        //并且未关联记录
        if(fleetClaim.fileInfoList != null){
            List<ContentDocumentLink> cDocList = new List<ContentDocumentLink>();
            for(FileInfo fileInfo : fleetClaim.fileInfoList){
                if(!fileInfo.islinkedEntity && String.isNotBlank(fileInfo.contentId)){
                    ContentDocumentLink cDoc = new ContentDocumentLink();
                    cDoc.ContentDocumentId  = fileInfo.contentDocumentId;
                    cDoc.LinkedEntityId = fleetClaim.id;
                    cDoc.ShareType = 'V'; // Inferred permission, checkout description of ContentDocumentLink object for more details
                    cDoc.Visibility = 'AllUsers';

                    cDocList.add(cDoc);

                    fileInfo.islinkedEntity = true;
                }
            }

            if(!cDocList.isEmpty()) INSERT cDocList;
        }
    }


    private static void submitForApproval (String fleetClaimId) {
        Approval.ProcessSubmitRequest [] requestList = new Approval.ProcessSubmitRequest []{};
        // Create an approval request
        Approval.ProcessSubmitRequest req1 = new Approval.ProcessSubmitRequest();
        req1.setComments('Submitting request for approval.');
        req1.setObjectId(fleetClaimId);
        // Submit the record to specific process
        // req1.setProcessDefinitionNameOrId('Fleet_Claim_Approval');
        requestList.add(req1);
        // Submit the approval request
        Approval.ProcessResult[] result = Approval.process(requestList);
    }

    private static String getAddressInfoById(String addressId){
        List<Account_Address__c> awpList = new List<Account_Address__c>([
            SELECT Id, Address1__c, City__c, State__c,
            Country__c, Postal_Code__c
            FROM Account_Address__c
            WHERE Id = :addressId
        ]);

        String str;
        if(!awpList.isEmpty()){
            str = getAddressInfoLabel(awpList[0]);
        }

        return str;
    }

    private static String getAddressInfoLabel(Account_Address__c accountAddress){
        List<String> addressList = new List<String>();
        addressList.add(accountAddress.Address1__c);
        addressList.add(accountAddress.City__c);
        addressList.add(accountAddress.State__c);
        addressList.add(accountAddress.Country__c);
        addressList.add(accountAddress.Postal_Code__c);

        return String.join(addressList, ', ');
    }

    private static String getFleetClaimName(String fleetId){
        List<Fleet_Claim__c> fleetClaimList = new List<Fleet_Claim__c>([
            SELECT Id, Name
            FROM Fleet_Claim__c
            WHERE Id = :fleetId
        ]);
        String name = '';
        if(!fleetClaimList.isEmpty()){
            name = fleetClaimList[0].Name;
        }
        return name;
    }

    private static String randomWord(){
        String str = '';
        List<String> arr = new List<String>{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};

        for(Integer i = 0; i < 32; i++){
            Integer pos = System.Math.round(System.Math.random() * (arr.size() - 1));
            str += arr[pos];
        }
        return str;
    }

    private static String getFakeId(Set<String> fakeIdSet){
        String fakeId;
        Boolean flag = true;
        while(flag){
            fakeId = randomWord();
            if(!fakeIdSet.contains(fakeId)){
                flag = false;
            }
        }
        fakeIdSet.add(fakeId);

        return fakeId;
    }
}