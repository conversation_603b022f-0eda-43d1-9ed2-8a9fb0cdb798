/**
 * @description: controller class for warranty return batch upload page
 */
public without sharing class CCM_WarrantyReturnItemCtl {

    /**
     * @description getBillingShipping description
     * @param  accountId accountId 
     * @param  billto    billto Name
     * @param  shipto    shipto Name
     * @return           return description
     */
    @AuraEnabled
    public static ResponseWrapper getBillingShipping(String accountNumber, String billto, String shipto){
        if (String.isBlank(accountNumber) || String.isBlank(billto) || String.isBlank(shipto)) {
            return null;
        }

        ResponseWrapper response = new ResponseWrapper();
        try {
            List<AddressWrapper> addressList = new List<AddressWrapper>();
            List<Address_With_Program__c> addWithProgramList = [SELECT Id, Name, Account_Address__r.RecordType.DeveloperName, Account_Address__c, Account_Address__r.Address1__c,Account_Address__r.Address1_In_French__c, Account_Address__r.Address2__c, Account_Address__r.Address2_In_French__c,Account_Address__r.City__c, Account_Address__r.State__c, Account_Address__r.Country__c, Account_Address__r.Postal_Code__c
                                                                    FROM Address_With_Program__c
                                                                    WHERE (Name = :billTo OR Name = :shipTo)
                                                                    AND Account_Address__r.Customer__r.AccountNumber = :accountNumber ORDER BY Account_Address__r.RecordType.DeveloperName];
                                                                    
            for (Address_With_Program__c baab : addWithProgramList) {
                if (baab.Account_Address__r.RecordType.DeveloperName == CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME || baab.Account_Address__r.RecordType.DeveloperName == CCM_Constants.ADDRESS_DROPSHIP_BILLING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME) {
                    AddressWrapper address = new AddressWrapper();
                    address.name = baab.Name;
                    address.type = 'bill';
                    
                    //add by vince, address in french ******** ,Address1_In_French__c
                    if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                        address.street = String.isNotEmpty(baab.Account_Address__r.Address1_In_French__c) ? baab.Account_Address__r.Address1_In_French__c : baab.Account_Address__r.Address2_In_French__c;
                     }else {
                        address.street = String.isNotEmpty(baab.Account_Address__r.Address1__c) ? baab.Account_Address__r.Address1__c : baab.Account_Address__r.Address2__c;
                    }
                    address.city = baab.Account_Address__r.City__c;
                    address.state = baab.Account_Address__r.State__c;
                    address.country = baab.Account_Address__r.Country__c;
                    address.postalCode = baab.Account_Address__r.Postal_Code__c;
                    if (addressList.size() == 0) {
                        addressList.add(address);
                    } else {
                        addressList.add(0, address);
                    }
                } else {
                    AddressWrapper address = new AddressWrapper();
                    address.name = baab.Name;
                    address.type = 'ship';
                    
                    //add by vince, address in french ******** ,Address1_In_French__c
                    if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                        address.street = String.isNotEmpty(baab.Account_Address__r.Address1_In_French__c) ? baab.Account_Address__r.Address1_In_French__c : baab.Account_Address__r.Address2_In_French__c;
                     }else {
                        address.street = String.isNotEmpty(baab.Account_Address__r.Address1__c) ? baab.Account_Address__r.Address1__c : baab.Account_Address__r.Address2__c;
                     }
                    address.city = baab.Account_Address__r.City__c;
                    address.state = baab.Account_Address__r.State__c;
                    address.country = baab.Account_Address__r.Country__c;
                    address.postalCode = baab.Account_Address__r.Postal_Code__c;
                    if (addressList.size() == 1) {
                        addressList.add(address);
                    } else {
                        addressList.add(1, address);
                    }
                }
            }
            response.isSuccess = true;
            response.returnData = addressList;
        } catch (Exception e) {
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description insertWarrantyReturnItem description
     * @param  warrantyReturnItemStr warrantyReturnItemStr description
     * @return                       return description
     */
    @AuraEnabled
    public static ResponseWrapper insertWarrantyReturnItem(String warrantyReturnItemStr) {
        if (String.isBlank(warrantyReturnItemStr)) {
            return null;
        }

        ResponseWrapper response = new ResponseWrapper();
        Savepoint sp = Database.setSavePoint();
        try {
            List<WarrantyReturnItemWrapper> warrantyReturnItemList = (List<WarrantyReturnItemWrapper>)JSON.deserialize(warrantyReturnItemStr, List<WarrantyReturnItemWrapper>.class);
    
            if (warrantyReturnItemList.size() > 0) {
                List<Warranty_Return_Claim_Item__c> itemList = new List<Warranty_Return_Claim_Item__c>();
                Map<String, Address_With_Program__c> addWithProgramMap = new Map<String, Address_With_Program__c>(); // bill to / ship to 和 id的映射
                Map<String, List<Order_Item__c>> productOrderItemMap = new Map<String, List<Order_Item__c>>();

                // 根据表格中的信息，查到lookup字段对应的Id，然后赋值做insert
                List<Account> accList = [SELECT Id FROM Account WHERE AccountNumber = :warrantyReturnItemList[0].accountNumber];
                System.debug('account info===========>' + accList);     

                List<String> productCodeList = new List<String>();
                for (WarrantyReturnItemWrapper itemWrapper : warrantyReturnItemList) {
                    productCodeList.add(itemWrapper.productCode);
                }

                productOrderItemMap = getOrderItems(productCodeList, accList[0].Id);

                List<Address_With_Program__c> addWithProgramList = [
                    SELECT Id, Name, Account_Address__c
                    FROM Address_With_Program__c
                    WHERE (Name = :warrantyReturnItemList[0].billTo OR Name = :warrantyReturnItemList[0].shipTo)
                    AND Account_Address__r.Customer__c = :accList[0].Id
                ];
                System.debug('addWithProgramList=========>' + addWithProgramList);

                if (addWithProgramList.size() <= 0) {
                    throw new QueryException('ERROR: ' + ' Bill To and Ship To are invalid!');
                }

                for (Address_With_Program__c addWithProgram : addWithProgramList) {
                    addWithProgramMap.put(addWithProgram.Name, addWithProgram);
                }
                // addWithProgramMap只存ship to 和 bill to。如果少了哪一条数据，说明哪一条数据不在该customer下边，提示以下错误。
                if (!addWithProgramMap.containsKey(warrantyReturnItemList[0].billTo)) {
                    throw new QueryException('ERROR: ' + warrantyReturnItemList[0].billTo + ' is an invalid BILL TO!');
                }
                if (!addWithProgramMap.containsKey(warrantyReturnItemList[0].shipTo)) {
                    throw new QueryException('ERROR: ' + warrantyReturnItemList[0].shipTo + ' is an invalid SHIP TO!');
                }

                List<Invoice__c> invList = new List<Invoice__c>();
                if (warrantyReturnItemList[0].debitMemoNumber != null) {
                    invList = [SELECT Id, Invoice_Number__c FROM Invoice__c WHERE Invoice_Number__c = :warrantyReturnItemList[0].debitMemoNumber];
                    if (invList.size() <= 0) {
                        throw new QueryException('ERROR: ' + warrantyReturnItemList[0].debitMemoNumber + ' is an invalid DEBIT MEMO NUMBER!');
                    }
                }
              
                //头信息的插入，表格中每一行数据的头信息是相同的。即都是一条warranty return claim的的warranty return claim item。
                Warranty_Return_Claim__c objWarranty = new Warranty_Return_Claim__c();
                objWarranty.Customer__c = accList.size() > 0 ? accList[0].Id : null;
                objWarranty.Contact_Name__c = warrantyReturnItemList[0].contactName;
                objWarranty.Contact_Email_Addresses__c = warrantyReturnItemList[0].contactEmailAddress;
                objWarranty.Contact_Phone_Number__c = warrantyReturnItemList[0].contactPhoneNumber;
                objWarranty.BillTo__c = addWithProgramMap.get(warrantyReturnItemList[0].billTo).Id;
                objWarranty.ShipTo__c = addWithProgramMap.get(warrantyReturnItemList[0].shipTo).Id;
                objWarranty.Billing_Address__c = addWithProgramMap.get(warrantyReturnItemList[0].billTo).Account_Address__c;
                objWarranty.Shipping_Address__c = addWithProgramMap.get(warrantyReturnItemList[0].shipTo).Account_Address__c;
                objWarranty.Payment_Method__c = warrantyReturnItemList[0].paymentMethod;
                if (warrantyReturnItemList[0].debitMemoNumber != null) {
                    objWarranty.Debit_Memo_Number__c = invList.size() > 0 ? invList[0].Id : ''; 
                }
                objWarranty.Subtotal__c = 0;
                for (WarrantyReturnItemWrapper itemWrapper : warrantyReturnItemList) {
                    objWarranty.Subtotal__c += itemWrapper.invoicePrice * itemWrapper.quantity;
                }
                objWarranty.Created_By_Batch_Upload__c = true;
                insert objWarranty;

                // warranty return claim item
                for (WarrantyReturnItemWrapper itemWrapper : warrantyReturnItemList) {
                    Warranty_Return_Claim_Item__c item = new Warranty_Return_Claim_Item__c();
                    item.Warranty_Return_Claim__c = objWarranty.Id;
                    // 如果不是该customer下的product，则不会在map中,抛出异常。
                    if (productOrderItemMap.get(itemWrapper.productCode) != null) {
                        item.Model__c = productOrderItemMap.get(itemWrapper.productCode).get(0).Product__c; // 获取第一个order item中的product即可
                    } else {
                        throw new queryException('ERROR: ' + itemWrapper.productCode + ' is an invalid Model!');
                    }
                    if (itemWrapper.customerPONumber != null) {
                        for (Order_Item__c orderItem : productOrderItemMap.get(itemWrapper.productCode)) {
                            if (orderItem.Order__r.PO_Number__c.equalsIgnoreCase(itemWrapper.customerPONumber)) { // 获取同一个product code下边对应的order item
                                item.Order_Item__c = orderItem.Id;
                                break;
                            }
                        } 
                    }
                    // if (String.isEmpty(item.Order_Item__c)) {
                    //     throw new queryException('ERROR: ' + itemWrapper.customerPONumber + ' is an invalid CUSTOMER PO NUMBER!');
                    // }
                    item.Quantity__c = itemWrapper.quantity;
                    item.Invoice_Price__c = itemWrapper.invoicePrice;
                    item.End_Consumer_Purchase_Date__c = String.isNotBlank(itemWrapper.endConsumerPurchaseDate) ? Date.valueOf(itemWrapper.endConsumerPurchaseDate) : null;
                    item.End_Consumer_Return_Date__c = String.isNotBlank(itemWrapper.endConsumerReturnDate) ? Date.valueOf(itemWrapper.endConsumerReturnDate) : null;
                    item.DIF_RTV__c = itemWrapper.DIFRTV;
                    item.Serial_Number__c = itemWrapper.serialNumber;
                    item.Return_Reason__c = itemWrapper.returnReason;
                    item.Return_Reason_Remark__c = itemWrapper.returnReasonRemark != null ? itemWrapper.returnReasonRemark : '';
                    item.Index__c = String.valueOf(itemWrapper.index);
                    itemList.add(item);
                }

                System.debug('item list======================>' + itemList);
    
                // insert
                if (itemList.size() > 0) {
                    insert itemList;
                }

                Approval.ProcessSubmitRequest request = new Approval.ProcessSubmitRequest();
                request.setComments('Submit by batch upload');
                request.setObjectId(objWarranty.Id);
                request.setSubmitterId(UserInfo.getUserId());
                Approval.ProcessResult result;
                result = Approval.process(request);

                response.isSuccess = true;
                response.returnData = objWarranty;
            }
        } catch (Exception e) {
            Database.rollback(sp);
            response.isSuccess = false;
            response.errorLineNumbber = e.getLineNumber();
            response.errorMsg = e.getMessage();
        }
        return response;
    }

    /**
     * @description: 找到product code和order item的对应关系。
     */
    private static Map<String, List<Order_Item__c>> getOrderItems(List<String> productCodeList, String customerId) {
        if (productCodeList.size() == 0 || String.isEmpty(customerId)) {
            return null;
        }

        Map<String, List<Order_Item__c>> productOrderItemMap = new Map<String, List<Order_Item__c>>();

        List<Order_Item__c> orderItemList = [
            SELECT Product__c, Product__r.Name, Product__r.ProductCode, Product__r.SF_Description__c, Product__r.Brand_Name__c, Order__r.PO_Number__c
            FROM Order_Item__c
            WHERE Order__r.AccountId = :customerId
            AND Product__r.ProductCode IN :productCodeList
            AND Order__r.Order_Status__c IN('Partial Shipment', 'Ship Complete', 'CLOSED')
            AND (Order__r.Order_Type__c = 'CNA Sales Order - USD' OR Order__r.Order_Type__c = 'CNA Dropship Order' OR Order__r.Order_Type__c = 'CNA Special Order')
            // GROUP BY Product__r.ProductCode
            ORDER BY Order__r.Order_Date__c DESC
        ];

        if (orderItemList.size() <= 0) {
            return null;
        }

        for (Order_Item__c orderItem : orderItemList) {
            if (!productOrderItemMap.containsKey(orderItem.Product__r.ProductCode)) {
                productOrderItemMap.put(orderItem.Product__r.ProductCode, new List<Order_Item__c>());
            }
            productOrderItemMap.get(orderItem.Product__r.ProductCode).add(orderItem);
        }

        return productOrderItemMap;
    }

    /**
     * @description: wrapper class for response from frontend
     */
    public class ResponseWrapper {

        @AuraEnabled public Object returnData {get; set;}

        @AuraEnabled public Boolean isSuccess {get; set;}

        @AuraEnabled public String stateCode {get; set;}

        @AuraEnabled public String errorMsg {get; set;}

        @AuraEnabled public String errorText {get; set;}

        @AuraEnabled public Integer errorLineNumbber {get; set;}
    }

    /**
     * @description: warranty return item wrapper class
     */  
    public class WarrantyReturnItemWrapper {

        // warranty return claim。
        @AuraEnabled public String customerId {get; set;}

        @AuraEnabled public String accountNumber {get; set;}

        @AuraEnabled public String contactName {get; set;}

        @AuraEnabled public String contactEmailAddress {get; set;}

        @AuraEnabled public String contactPhoneNumber {get; set;}

        @AuraEnabled public String billTo {get; set;}

        @AuraEnabled public String shipTo {get; set;}

        @AuraEnabled public String paymentMethod {get; set;}

        @AuraEnabled public String debitMemoId {get; set;}
        
        @AuraEnabled public String debitMemoNumber {get; set;}

        // warranty return claim item
        @AuraEnabled public String index {get; set;}

        @AuraEnabled public String recordId {get; set;}

        @AuraEnabled public String productId {get; set;}

        @AuraEnabled public String customerPONumber{get; set;}

        @AuraEnabled public String brand {get; set;}

        @AuraEnabled public String unit {get; set;} //230216, add by jet.

        @AuraEnabled public String pricebookId {get; set;}

        @AuraEnabled public String productDescription {get; set;}

        @AuraEnabled public Decimal invoicePrice {get; set;}

        @AuraEnabled public Decimal quantity {get; set;}

        @AuraEnabled public String DIFRTV {get; set;}

        @AuraEnabled public String endConsumerPurchaseDate {get; set;}

        @AuraEnabled public String endConsumerReturnDate {get; set;}

        @AuraEnabled public String productCode {get; set;}

        @AuraEnabled public Decimal subTotal {get; set;}

        @AuraEnabled public String returnReason {get; set;}

        @AuraEnabled public String returnReasonRemark {get; set;}

        @AuraEnabled public String serialNumber {get; set;}

        @AuraEnabled public String name {get; set;}
    }

    public class AddressWrapper {
        @AuraEnabled public String name;
        @AuraEnabled public String type;
        @AuraEnabled public String street;
        @AuraEnabled public String city;
        @AuraEnabled public String state;
        @auraEnabled public String country;
        @AuraEnabled public String postalCode;
    }
}