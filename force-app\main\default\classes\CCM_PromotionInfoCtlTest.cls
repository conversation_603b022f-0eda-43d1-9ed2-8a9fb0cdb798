/**
 * @description Test class for CCM_PromotionInfoCtl
 */
@isTest
public class CCM_PromotionInfoCtlTest {

    @isTest
    static void testSearchCustomerByStandardGroup() {
        CCM_ValidationRelateListHandler.isRun = false;
        CCM_BaabASGHandler.isRun = false;
        CCM_BaabAccessHandlerAfter.isRun = false;
        CCM_AddressWithProgramInsertHandler.isRun = false;
        CCM_AddressWithProgramUpdateHandler.isRun = false;
        CCM_BrandAccessHandler.isRun = false;
        dropshipPromotion();
        Promotion2__c objPromotion = new Promotion2__c(Name = 'test promotion');
        insert objPromotion;

        Test.startTest();
        CCM_PromotionInfoCtl.searchCustomerByStandardGroup(objPromotion.Id, 'Standard Groups',
                                                        new List<String> {[SELECT Id FROM PriceBook2 WHERE Name = 'NA Distributors price list' LIMIT 1].Id}, 
                                                        new List<String> {'CNA-CG01'}, 
                                                        new List<String> {'KA001'}, 
                                                        new List<String> {'SC01'}, 
                                                        new List<String> {'SG24'},
                                                        new List<String>(),
                                                        true,
                                                        true, 
                                                        false);
        Test.stopTest();
    }
    @isTest
    static void testSearchCustomerByStandardGroup2() {
        CCM_ValidationRelateListHandler.isRun = false;
        CCM_BaabASGHandler.isRun = false;
        CCM_BaabAccessHandlerAfter.isRun = false;
        CCM_AddressWithProgramInsertHandler.isRun = false;
        CCM_AddressWithProgramUpdateHandler.isRun = false;
        CCM_BrandAccessHandler.isRun = false;
        dropshipPromotion();
        Promotion2__c objPromotion = new Promotion2__c(Name = 'test promotion');
        insert objPromotion;

        Test.startTest();
        CCM_PromotionInfoCtl.searchCustomerByStandardGroup(objPromotion.Id, 'Standard Groups',
                                                        new List<String> {[SELECT Id FROM PriceBook2 WHERE Name = 'NA Distributors price list' LIMIT 1].Id}, 
                                                        new List<String> {'CNA-CG01'}, 
                                                        new List<String> {'KA001'}, 
                                                        new List<String> {'SC01'}, 
                                                        new List<String> {'SG24'},
                                                        new List<String>(),
                                                        true,
                                                        true, 
                                                        true);
        Test.stopTest();
    }

    @isTest
    static void testSearchCustomerByStandardGroupDropShip() {
        CCM_ValidationRelateListHandler.isRun = false;
        CCM_BaabASGHandler.isRun = false;
        CCM_BaabAccessHandlerAfter.isRun = false;
        CCM_AddressWithProgramInsertHandler.isRun = false;
        CCM_AddressWithProgramUpdateHandler.isRun = false;
        dropshipPromotion();
        Promotion2__c objPromotion = new Promotion2__c(Name = 'test promotion');
        insert objPromotion;
        
        Test.startTest();
        CCM_PromotionInfoCtl.searchCustomerByStandardGroup(objPromotion.Id, 'Standard Groups',
                                                        new List<String> {[SELECT Id FROM PriceBook2 WHERE Name = 'NA Distributors price list' LIMIT 1].Id}, 
                                                        new List<String> {'CNA-CG01'}, 
                                                        new List<String> {'KA001'}, 
                                                        new List<String> {'SC01'}, 
                                                        new List<String> {'SG24'}, 
                                                        new List<String>(),
                                                        true,
                                                        true,
                                                        true);
        Test.stopTest();
    }
    @isTest
    static void testSearchCustomerByStandardGroupDropShip2() {
        CCM_ValidationRelateListHandler.isRun = false;
        CCM_BaabASGHandler.isRun = false;
        CCM_BaabAccessHandlerAfter.isRun = false;
        CCM_AddressWithProgramInsertHandler.isRun = false;
        CCM_AddressWithProgramUpdateHandler.isRun = false;
        dropshipPromotion();
        Promotion2__c objPromotion = new Promotion2__c(Name = 'test promotion');
        insert objPromotion;

        Test.startTest();
        CCM_PromotionInfoCtl.searchCustomerByStandardGroup(objPromotion.Id, 'Standard Groups',
                                                        new List<String> {[SELECT Id FROM PriceBook2 WHERE Name = 'NA Distributors price list' LIMIT 1].Id}, 
                                                        new List<String> {'CNA-CG01'}, 
                                                        new List<String> {'KA001'}, 
                                                        new List<String> {'SC01'}, 
                                                        new List<String> {'SG24'}, 
                                                        new List<String>(),
                                                        false,
                                                        true,
                                                        true);
        Test.stopTest();
    }

	/**
	 * @description: test method for getCustomerOrGroup
	 */    
    @isTest
    static void testgetCustomerOrGroup() {
        
        Account objAccount = new Account(Name = 'test', TaxID__c = 'Test', PaymentMethod__c = 'CHECK', ORG_Code__c = 'CNA',Distributor_or_Dealer__c = 'Dealer Location');
        insert objAccount;
        Account objAccountCCA = new Account(Name = 'test', TaxID__c = 'Test', PaymentMethod__c = 'CHECK', ORG_Code__c = 'CCA');
        insert objAccountCCA;
        Test_SalesData.createAuthorizedBrand(objAccount.Id, null, 'EGO 2023', 'EGO', CCM_Constants.SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_STANDARD_ID,'CNA');
        Customer_Profile__c objProfile = new Customer_Profile__c(Segmentation__c = '1', Customer__c = objAccount.Id, Overall_Estimated_Revenue__c = 1, Payment_Behavior_Code__c = 'C');
        insert objProfile;
        Promotion2__c objPromotion = new Promotion2__c();
    	insert objPromotion;
        Promotion_Customer_Group__c objGroup = new Promotion_Customer_Group__c(Name = 'test');
        insert objGroup;
        Test.startTest();
        List<CCM_PromotionInfoCtl.CustomerOrGroupWrapper> result1 = CCM_PromotionInfoCtl.getAllCustomerOrGroup('test', true, true, false);
        List<CCM_PromotionInfoCtl.CustomerOrGroupWrapper> result2 = CCM_PromotionInfoCtl.getAllCustomerOrGroup('test', false, true, true);
        Test.stopTest();
    }
    
    
    /**
     * @description: test method for getCustomerByGroup
     */
    @isTest
    static void testGetCustomerByGroup() {
        Test.startTest();
        Account objAccount = new Account(Name = 'test',  TaxID__c = 'Test', PaymentMethod__c = 'CHECK', RecordTypeId =  Schema.SObjectType.Account.getRecordTypeInfosByName().get('Channel').getRecordTypeId());
        insert objAccount;
        Customer_Profile__c objProfile = new Customer_Profile__c(Segmentation__c = '1', Customer__c = objAccount.Id, Overall_Estimated_Revenue__c = 1, Payment_Behavior_Code__c = 'C');
        insert objProfile;
        Promotion2__c objPromotion = new Promotion2__c();
    	insert objPromotion;
        Promotion_Customer_Group__c objGroup = new Promotion_Customer_Group__c(Name = 'Test');
        insert objGroup;
        Promotion_Customer_In_Group__c objCustomerGroup = new Promotion_Customer_In_Group__c(Promotion_Customer_Group__c = objGroup.Id, Customer__c = objAccount.Id);
        insert objCustomerGroup;
        List<CCM_PromotionInfoCtl.CustomerOrGroupWrapper> result1 = CCM_PromotionInfoCtl.getAllCustomerByGroup(objGroup.Id,true, true);
        Test.stopTest();
    }
    
    /**
     * @description: test method for getPromotionInfo
     */
    @isTest
    static void testgetPromotionInfo() {
        Test.startTest();
        CCM_UpdateCampaignMemberStatusHandler.isRun = false;
        Promotion2__c objPromotion = new Promotion2__c(RecordTypeId = CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_ID, List_View_Name__c = 'Customized Promotion');
    	insert objPromotion;
        Campaign objCampaign = new Campaign(Name = 'Test Campaign', Sales_Manager__c = UserInfo.getUserId(), Expected_Budget__c = 1000);
        insert objCampaign;
        Promotion_Campaign__c objPC = new Promotion_Campaign__c(Campaign__c = objCampaign.Id, Promotion__c = objPromotion.Id);
        insert objPC;
        CCM_PromotionInfoCtl.PromotionInfoWrapper result = CCM_PromotionInfoCtl.getPromotionInfo(objPromotion.Id);
        Test.stopTest();
        
    }
    
    /**
     * @description: test method for getPromotionBasicInfo
     */
    @isTest
    static void testgetPromotionBasicInfo() {
        Test.startTest();
        Product2 objProduct = new Product2(Name = 'test Product', Full_Pallet_Quantity__c = 2, Source__c = 'PIM', Sellable__c = true);
        insert objProduct;
        Pricebook2 customPB = new Pricebook2(Name = 'Test PriceBook', isActive = true);
        insert customPB;
        insert new PricebookEntry(Pricebook2Id = customPB.Id, Product2Id = objProduct.Id, UnitPrice = 1000, IsActive = true);
        Promotion2__c objPromotion = new Promotion2__c(RecordTypeId = CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_ID);
    	insert objPromotion;
        CCM_PromotionInfoCtl.getPromotionBasicInfo('Sell_In_Promotion');
        Test.stopTest();
        
    }
    
    /**
     * @description: test method for getPromotionBasicInfoById
     */
    @isTest
    static void testgetPromotionBasicInfoById() {
        Test.startTest();
        Promotion2__c objPromotion = new Promotion2__c(RecordTypeId = CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_ID);
    	insert objPromotion;
        CCM_PromotionInfoCtl.getPromotionBasicInfoById(objPromotion.Id);
        Test.stopTest();
        
    }
    
    /**
     * @description: test method for searchPriceList
     */
    @isTest
    static void testsearchPriceList() {
        Test.startTest();
        Product2 objProduct = new Product2(Name = 'test Product', Full_Pallet_Quantity__c = 2, Source__c = 'PIM', Sellable__c = true);
        insert objProduct;
        Id pricebookId = Test.getStandardPricebookId();
        //insert new PricebookEntry(Pricebook2Id = pricebookId, Product2Id = objProduct.Id, UnitPrice = 10002, IsActive = true);
        Pricebook2 customPB = new Pricebook2(Name = 'Test PriceBook', isActive = true, 	Org_Code__c = 'CNA');
        insert customPB;
        insert new PricebookEntry(Pricebook2Id = customPB.Id, Product2Id = objProduct.Id, UnitPrice = 1000, IsActive = true);

		Account objAccount = new Account(Name = 'test', TaxID__c = 'Test', PaymentMethod__c = 'CHECK', RecordTypeId =  Schema.SObjectType.Account.getRecordTypeInfosByName().get('Channel').getRecordTypeId());
        insert objAccount;        
        insert new List<Sales_Program__c> {
            new Sales_Program__c(Customer__c = objAccount.Id, Brands__c = 'EGO', Price_Book__c = customPB.Id, Authorized_Brand_Name_To_Oracle__c = 'EGO', Approval_Status__c = 'Approved', Org_Code__c = 'CNA', Payment_Term__c = 'NA001', Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.'),
            new Sales_Program__c(Customer__c = objAccount.Id, Brands__c = 'EGO', Price_Book__c = customPB.Id, Authorized_Brand_Name_To_Oracle__c = 'EGO', Approval_Status__c = 'Approved', Org_Code__c = 'CNA', Payment_Term__c = 'NA001', Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.')
        };
        
        CCM_PromotionInfoCtl.searchPriceList(new String[]{objAccount.Id}, new String[]{'EGO'});
        CCM_PromotioninfoCtl.searchPriceListByName('test', true, false, true);
        CCM_PromotioninfoCtl.searchPriceListByName('test', true, true, true);
        CCM_PromotioninfoCtl.searchPriceListByName('test', false, true, true);
        CCM_PromotioninfoCtl.searchPriceListByName('test', false, false, true);

        Test.stopTest();
    }

    /**
     * @description: test method for CCMPromotionTargetCustomerBatch batch
     */
    @isTest
    static void testschedule() {
        CCM_ValidationRelateListHandler.isRun = false;
        CCM_BaabASGHandler.isRun = false;
        CCM_BaabAccessHandlerAfter.isRun = false;
        CCM_AddressWithProgramInsertHandler.isRun = false;
        CCM_AddressWithProgramUpdateHandler.isRun = false;
        dropshipPromotion();
        Promotion2__c objPromotion = new Promotion2__c(Name = 'test promotion', Promotion_Status__c = 'Open', Select_Customer_Method__c = 'Standard Groups', Brands__c = 'EGO;SKIL;SKILSAW;FLEX', Opening_Order_Discount__c = true, ORG_Code__c='CNA');
        insert objPromotion;
        // Promotion2__c objPromotion2 = new Promotion2__c(Name = 'test2 promotion', Promotion_Status__c = 'Open', Select_Customer_Method__c = 'Standard Groups', Brands__c = 'EGO;SKIL;SKILSAW;FLEX', Opening_Order_Discount__c = true, ORG_Code__c='CNA');
        // insert objPromotion2;
        Test.startTest();
        CCM_PromotionTargetCustomerSchedule myClass = new CCM_PromotionTargetCustomerSchedule();   
        String chron = '0 0 23 * * ?';        
        System.schedule('Test Sched', chron, myClass);
        Test.stopTest();
    }

    /**
     * @description: test method for getSecondCustomer
     */
    // @isTest
    // static void testGetSecondCustomer() {
    //     dropshipPromotion();
    //     Promotion2__c objPromotion = new Promotion2__c(Name = 'test promotion', Promotion_Status__c = 'Open', Select_Customer_Method__c = 'Standard Groups', Brands__c = 'EGO;SKIL;SKILSAW;FLEX', Opening_Order_Discount__c = true);
    //     insert objPromotion;
    //     Pricebook2 customPB = new Pricebook2(Name = 'Test PriceBook', isActive = true, 	Org_Code__c = 'CNA');
    //     insert customPB;

    //     //1st tier
    //     Account acc = new Account();
    //     acc.AccountNumber = '1789933';
    //     acc.Sales_Group__c = 'SG01';
    //     acc.TaxID__c = 'Test';
    //     acc.PaymentMethod__c = 'CHECK';
    //     acc.Director_Approver__c = UserInfo.getUserId();
    //     acc = (Account)CCM_TESTDataUtil.createSobject(acc, 'Channel');
    //     CCM_ShippingAddressSendApproval.isRun = false;
    //     Sales_Program__c program = new Sales_Program__c();
    //     program.Customer__c = acc.Id;
    //     program.Approval_Status__c = 'Approved';
    //     program.Director_Approver__c = UserInfo.getUserId();
    //     program.Brands__c = 'EGO';
    //     program.Order_Type__c =  'CNA Dropship Order';
    //     program.Org_Code__c = 'CNA';
    //     program.Price_Book__c = customPB.id;
    //     insert program;
    //     // program = (Sales_Program__c)CCM_TESTDataUtil.createSobject(program, 'Dropship_Sales_Standard');

    //     //2nd tier buying group
    //     Account acc1 = new Account();
    //     acc1.AccountNumber = '1789932';
    //     acc1.Sales_Group__c = 'SG01';
    //     acc1.TaxID__c = 'Test';
    //     acc1.PaymentMethod__c = 'CHECK';
    //     acc1.Director_Approver__c = UserInfo.getUserId();
    //     acc1 = (Account)CCM_TESTDataUtil.createSobject(acc1, 'Channel');

    //     //Sales hierarchy
    //     Sales_Hierarchy__c hie = new Sales_Hierarchy__c();
    //     hie.X1st_tier_dealer__c = acc.id;
    //     hie.X2st_tier_dealer__c = acc1.id;
    //     insert hie;

    //     Test.startTest();
    //     List<CCM_PromotionInfoCtl.CustomerOrGroupWrapper> result1 = CCM_PromotionInfoCtl.getSecondCustomer(acc.id, true);
    //     Set<String> result2 = CCM_PromotionInfoCtl.getMixedAuthorizedBrands(new List<Sales_Program__c>{program});
    //     Test.stopTest();
    // }

    /**
     * Test for getSecondCustomer,getMixedAuthorizedBrands,getDropshipAuthorizedBrandsBy2ndCustomerId
     * 
     * add on 23.11.8
     */
    @isTest
    static void testGetSecondCustomer(){
        Account objAccount1 = new Account(Name = '1nd', TaxID__c = 'Test', PaymentMethod__c = 'CHECK', ORG_Code__c = 'CNA');
        Account objAccount2 = new Account(Name = '2st', TaxID__c = 'Test', PaymentMethod__c = 'CHECK', ORG_Code__c = 'CNA',Distributor_or_Dealer__c = 'Dealer Location');
        List<Account> accList = new List<Account>{objAccount1,objAccount2};
        insert accList;
        Sales_Hierarchy__c objSalesHierarchy = new Sales_Hierarchy__c(X1st_tier_dealer__c = objAccount1.Id, X2st_tier_dealer__c = objAccount2.Id, Active__c = true, Approval_Status__c = 'Approved');
        insert objSalesHierarchy;
        Customer_Profile__c objProfile1 = new Customer_Profile__c(Segmentation__c = '1', Customer__c = objAccount1.Id, Overall_Estimated_Revenue__c = 1, Payment_Behavior_Code__c = 'C');
        insert objProfile1;
        Customer_Profile__c objProfile2 = new Customer_Profile__c(Segmentation__c = '1', Customer__c = objAccount2.Id, Overall_Estimated_Revenue__c = 1, Payment_Behavior_Code__c = 'C');
        insert objProfile2;
        Test.startTest();
        Sales_Program__c authBrand = Test_SalesData.createAuthorizedBrand(objAccount1.Id, null, 'EGO 2023', 'EGO', CCM_Constants.SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_STANDARD_ID,'CNA');
        CCM_PromotionInfoCtl.getSecondCustomer(objAccount1.Id, false);
        Test.stopTest();
        CCM_PromotionInfoCtl.getMixedAuthorizedBrands(new List<Sales_Program__c>{authBrand});
        CCM_PromotionInfoCtl.getDropshipAuthorizedBrandsBy2ndCustomerId(objAccount2.Id, false);
        
    }

    public static void dropshipPromotion() {
        CCM_PromotionCodeHandler.isRun = false;
        Account customer = new Account();
        customer.Name = 'Test' + Math.rint(Math.random() * 100000);
        customer.Type = '0=Company';
        customer.Phone = '010-********';
        customer.RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Account').get('Channel');
        customer.Region__c = 'America Rep Office';
        customer.Customer_Cluster__c = 'CNA-CG01';
        customer.Customer_Sub_Cluster__c = 'KA001';
        customer.Sales_Group__c = 'SG24';
        customer.Sales_Channel__c = 'SC01';
        customer.ORG_Code__c = 'CNA';
        customer.TaxID__c = 'Test';
        customer.PaymentMethod__c = 'CHECK';
        insert customer;

        Customer_Profile__c objProfile = new Customer_Profile__c(Segmentation__c = '1', Customer__c = customer.Id, Overall_Estimated_Revenue__c = 1, Payment_Behavior_Code__c = 'C');
        insert objProfile;

        Product2 product = new Product2();
        product.Name = '15" Foldable Shaft String Trimmer Kit (5.0Ah Battery, 210W Charger)';
        product.IsActive = true;
        product.Brand_Name__c = 'EGO';
        product.Short_description__c = '15" Foldable Shaft String Trimmer Kit (5.0Ah Battery, 210W Charger)';
        product.ProductCode = 'ST1504-S';
        product.RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Product2').get('Product');
        product.Source__c = 'PIM';
        product.PIM_ExternalID__c = 'ST1504-S-PIM';
        insert product;

        Pricebook2 priceBook = new Pricebook2();
        priceBook.Name = 'NA Distributors price list';
        priceBook.IsActive = true;
        priceBook.Price_Book_OracleID__c = '693903';
        priceBook.Org_Code__c = 'CNA';
        insert priceBook;

        PricebookEntry pbe = new PricebookEntry();
        pbe.PriceBook2Id = priceBook.Id;
        pbe.Product2Id = product.Id;
        pbe.UnitPrice = 20;
        insert pbe;

        Sales_Program__c authBrand = new Sales_Program__c();
        authBrand.Name = 'EGO 2019';
        authBrand.Brands__c = 'EGO';
        authBrand.Order_Type__c = 'CNA Dropship Order';
        authBrand.Approval_Status__c = 'Approved';
        authBrand.Customer_Type__c = 'Distributor';
        authBrand.Deliver_From__c = 'CNA01, Kenco Chino Warehouse';
        authBrand.Payment_Term__c = 'NA001';
        authBrand.Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.';
        authBrand.Freight_Term__c = 'Paid';
        authBrand.Authorized_Brand_Name_To_Oracle__c = 'test1';
        authBrand.Price_Book__c = priceBook.Id;
        authBrand.Customer__c = customer.Id;
        authBrand.Org_Code__c = 'CNA';
        insert authBrand;
        

        Account_Address__c address = new Account_Address__c();
        address.Customer__c = customer.Id;
        address.Name = 'EGO Partner';
        address.Address1__c = address.Address1__c == null ? '1355 Ocean Ave, Brooklyn' : address.Address1__c;
        address.Country__c = 'US';
        address.City__c = 'NY';
        address.Approval_Status__c = 'Approved';
        address.Postal_Code__c = '11230';
        address.ORG_ID__c = 'CNA';
        //address.RecordTypeId = getSobjectRecordTypeNameMap('Account_Address__c').get(recordTypeName);
        address.Sales_Director__c = UserInfo.getUserId();

        Contact c = new Contact();
        c.lastname = 'test1';
        insert c;
        address.Contact__c = c.Id;
        insert address;

        Address_With_Program__c baab = new Address_With_Program__c(
            Special_Dropship_Address__c = false, 
            Program__c = authBrand.Id, 
            Account_Address__c = address.Id,
            Customer_Line_Oracle_ID__c = '512077'
        );
        insert baab;

        Customer_Brand_Pricebook_Mapping__c mapping1 = new Customer_Brand_Pricebook_Mapping__c(
            Brand__c = 'EGO',
            Type__c = 'Dropship',
            Price_Book__c = priceBook.Id,
            Special_Dropship_Address__c = true,
            Billing_Address_With_Authorized_Brand__c = baab.Id
        );
        Customer_Brand_Pricebook_Mapping__c mapping2 = new Customer_Brand_Pricebook_Mapping__c();
        mapping2.Brand__c = 'EGO';
        mapping2.Type__c = 'Dropship';
        mapping2.Special_Dropship_Address__c = false;
        mapping2.Billing_Address_With_Authorized_Brand__c = baab.Id;
        insert new List<Customer_Brand_Pricebook_Mapping__c> {mapping1, mapping2};
    }
}