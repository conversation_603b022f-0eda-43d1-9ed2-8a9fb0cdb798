<aura:component controller="CCM_Community_FleetEditCtl" implements="forceCommunity:availableForAllPageTypes">

    <aura:attribute name="readonly" type="Boolean" default="true"/>
    <aura:attribute name="fleetClaim" type="Object" description="fleetClaim information"/>
    <aura:attribute name="kitId2MsrpMap" type="Map"/>
    <aura:attribute name="fleetProgramRule" type="Object"/>
    <aura:attribute name="options" type="List" default="[]" />
    <aura:attribute name="showWarrantyInfo" type="Boolean" default="false"/>

    <aura:attribute name="fleetClaimList" type="List" default="[]" />
    <aura:attribute name="fleetItemList" type="List" default="[]" />
    <aura:attribute name="filteredItems" type="List" default="[]"/>
    <aura:attribute name="fleetClaimLoaded" type="Boolean" default="false" />

    <aura:handler name="change" value="{!v.fleetClaimLoaded}" action="{!c.loadFleetClaim}" />

    <!-- add haibo: french -->
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <!-- Sales Information start -->
    <article class="slds-card">
        <div class="slds-grid">
            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                <div class="slds-media__body">
                    <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                        <span class="section-header-title slds-p-horizontal--small slds-truncate"
                              title="{!$Label.c.CCM_Portal_SalesInformation}">
                            <span><strong>{!$Label.c.CCM_Portal_SalesInformation}</strong></span>
                         </span>
                    </h2>
                </div>
            </header>
        </div>
        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
            <lightning:layout multipleRows="true" horizontalAlign="space">
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <label class="{! 'slds-form-element__label' + if(v.readonly, '', ' field-required')}">{!$Label.c.CCM_Portal_Salesdate}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    <aura:renderIf isTrue="{!v.readonly}">
                        <ui:outputDate value="{!v.fleetClaim.salesDateStr}" format="MM/dd/yyyy"/>
                        <aura:set attribute="else">
                            <div class="input-width">
                                <ui:inputDate aura:id="salesDateStr"
                                              displayDatePicker="true"
                                              format="MM/dd/yyyy"
                                              value="{!v.fleetClaim.salesDateStr}"/>
                            </div>
                            <div aura:id="salesDateStr-error-required" class="error-text slds-hide">{!$Label.c.CCM_Portal_ThisFieldIsRequired}</div>
                        </aura:set>
                    </aura:renderIf>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Brandname}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    {!v.fleetClaim.brand}
                </lightning:layoutItem>

                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Primaryuse}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    <lightning:select name="primary use" variant="label-hidden" required="false" value="{!v.fleetClaim.primaryUse}" label="">
                        <!-- add haibo: french -->
                        <aura:iteration items="{!v.options}" var="option">
                            <option text="{!option.label}" value="{!option.value}"></option>
                        </aura:iteration>
                    </lightning:select>
                </lightning:layoutItem>

                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small slds-text-align--right">
                    <label class="slds-form-element__label">{!$Label.c.CCM_Portal_Purchaseplace}:</label>
                </lightning:layoutItem>
                <lightning:layoutItem size="3" class="slds-p-top_xx-small slds-p-bottom_xx-small">
                    {!v.fleetClaim.channelCustomer.name}
                </lightning:layoutItem>
            </lightning:layout>

<!--             <c:CCM_Community_FleetWarrantyInfo showModal="{!v.showWarrantyInfo}"
                                               fleetProgramRule="{!v.fleetProgramRule}"
                                               fleetClaim="{!v.fleetClaim}"
                                               kitId2MsrpMap="{!v.kitId2MsrpMap}"/> -->
            <div class="table_container slds-size_1-of-1 slds-medium-size_1-of-1 slds-p-top_xx-large slds-p-bottom_xx-small slds-scrollable_x">
<!--                 <lightning:button variant="brand"
                                  label="Select Warranty"
                                  title="Select Warranty"
                                  onclick="{!c.showWarrantyInfo}"/><br/><br/> -->
                <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols">
                    <lightning:spinner aura:id="proListSpinner" class="slds-hide" alternativeText="{!$Label.c.CCM_Portal_Loading}" size="small" variant="brand" />
                    <thead>
                        <tr class="slds-line-height_reset" aria-selected="false">
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 100px">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_NO}">{!$Label.c.CCM_Portal_NO}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 16%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="1">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Descriptions}">{!$Label.c.CCM_Portal_Descriptions}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 16%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="1">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_ModelNum}">{!$Label.c.CCM_Portal_ModelNum}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 14%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="2">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_MSRP}">{!$Label.c.CCM_Portal_MSRP}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 14%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="3">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_QtyPurchased}">{!$Label.c.CCM_Portal_QtyPurchased}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 14%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="4">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Total}">{!$Label.c.CCM_Portal_Total}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 14%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="5">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Unitsalesprice}">{!$Label.c.CCM_Portal_Unitsalesprice}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 14%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="6">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Totalsalesprice}">{!$Label.c.CCM_Portal_Totalsalesprice}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 14%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="7">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_DealerRebate}">{!$Label.c.CCM_Portal_DealerRebate}</span>
                                    </div>
                                </a>
                            </th>
                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col" style="width: 14%">
                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="8">
                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                        <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Actions1}">{!$Label.c.CCM_Portal_Actions1}</span>
                                    </div>
                                </a>
                            </th>

                        </tr>
                    </thead>
                    <tbody>

                    <aura:iteration items="{!v.fleetClaimList}" var="fleetItem" indexVar="index">
                        <tr aria-selected="false" class="slds-hint-parent">
                            <td class="slds-truncate" title="{!(index + 1)}">
                                {!(index + 1)}
                            </td>
                          <td class="slds-truncate" title="{!fleetItem.kitName}">
                                <div class="search-container">
                                    <lightning:input
                                        name="{!index}"
                                        type="text"
                                        variant="label-hidden"
                                        value="{!fleetItem.kitName}"
                                        onchange="{!c.handleSearch}"/>
                                    <div aura:id="searchResults"
                                         class="search-results"
                                         data-empty="{!empty(fleetItem.filteredItems)}">
                                        <ul>
                                            <aura:iteration items="{!fleetItem.filteredItems}" var="item">
                                                <li onclick="{!c.selectItem}" data-value="{!item.kitCode}" data-index="{!index}">
                                                    <p>
                                                        <span>{!item.kitCode}</span><br/>
                                                        {!item.kitName}
                                                    </p>
                                                </li>
                                            </aura:iteration>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                            <td class="slds-truncate" title="{!fleetItem.kitCode}">
                                {!fleetItem.kitCode}
                            </td>
                            <td class="slds-truncate" title="{!fleetItem.msrp}">
                                <lightning:formattedNumber value="{!fleetItem.msrp}" minimumFractionDigits="2" currencyCode="{!v.fleetClaim.currencyCode}" style="currency" currencyDisplayAs="code"/>
                            </td>
                            <td class="slds-truncate">
                                <aura:renderIf isTrue="{!v.readonly}">
                                    {!fleetItem.qtyPurchased}
                                    <aura:set attribute="else">
                                        <div style="width: 80px">
                                            <lightning:input name="{!index}"
                                                             disabled="{!empty(fleetItem.msrp)}"
                                                             variant="label-hidden"
                                                             value="{!fleetItem.qtyPurchased}"
                                                             onblur="{!c.qtyPurchasedChange}"
                                                             type="Number"
                                                             min="0"
                                                             max="99999999"/>
                                        </div>
                                    </aura:set>
                                </aura:renderIf>
                            </td>
                            <td class="slds-truncate" title="{!fleetItem.total}">
                                <lightning:formattedNumber value="{!fleetItem.total}" minimumFractionDigits="2" currencyCode="{!v.fleetClaim.currencyCode}" style="currency" currencyDisplayAs="code"/>
                            </td>
                            <td class="slds-truncate">
                                <aura:renderIf isTrue="{!v.readonly}">
                                <lightning:formattedNumber value="{!fleetItem.unitSalesPrice}" minimumFractionDigits="2" currencyCode="{!v.fleetClaim.currencyCode}" style="currency" currencyDisplayAs="code"/>
                                    <aura:set attribute="else">
                                        <div style="width: 80px">
                                            <lightning:input name="{!index}"
                                                             disabled="{!empty(fleetItem.msrp)}"
                                                             variant="label-hidden"
                                                             value="{!fleetItem.unitSalesPrice}"
                                                             onblur="{!c.qtyPurchasedChange}"
                                                             type="Number"
                                                             step="0.01"
                                                             min="0"
                                                             class="{! if(fleetItem.qtyPurchased > 0, 'rd_cell_required', '')}"
                                                             max="99999999"/>
                                        </div>
                                    </aura:set>
                                </aura:renderIf>
                            </td>
                            <td class="slds-truncate" title="{!fleetItem.totalSalesPrice}">
                                <lightning:formattedNumber value="{!fleetItem.totalSalesPrice}" minimumFractionDigits="2" currencyCode="{!v.fleetClaim.currencyCode}" style="currency" currencyDisplayAs="code"/>
                            </td>
                            <td class="slds-truncate" title="{!fleetItem.dealerRebate}">
                                <lightning:formattedNumber value="{!fleetItem.dealerRebate}" minimumFractionDigits="2" currencyCode="{!v.fleetClaim.currencyCode}" style="currency" currencyDisplayAs="code"/>
                            </td>
                            <td class="slds-truncate" title="Delete" data-index="{!index}"  onclick="{!c.handleDelete}">
                                <lightning:icon iconName="utility:delete" alternativeText="Delete"
                                                size="x-small"/>
                            </td>
                        </tr>
                    </aura:iteration>
                    </tbody>
                </table>
                <div style="height:100px;"></div>
                 <div class="slds-m-vertical_small slds-m-horizontal_medium slds-border_bottom test1">
                    <lightning:layout>
                        <div class="c-container" style="padding: 10px;">
                            <lightning:layoutItem alignmentBump="right">
                                <lightning:button label="{!$Label.c.CCM_Portal_Add_Item}" iconName="utility:add" iconPosition="left"
                                    onclick="{!c.addItem}" />
                            </lightning:layoutItem>
                        </div>
                    </lightning:layout>
                </div>
            </div>
        </div>
    </article>

</aura:component>