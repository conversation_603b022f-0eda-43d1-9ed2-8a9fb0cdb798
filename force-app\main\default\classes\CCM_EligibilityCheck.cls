/**
 * @description       :
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 02-29-2024
 * @last modified by  : <EMAIL>
 * @revision          : Chuck Huang 2024-03-13 Remove shipping cost = 0 check in eligible check
**/
public without sharing class CCM_EligibilityCheck {

    @AuraEnabled
    public static String SetupBomList(String productId, String version){
        return CCM_NewPartsOrder.SetupBomList(productId, version);
    }

    @AuraEnabled
    public static String GenerateVersionList(String productId){
        return CCM_NewPartsOrder.GenerateVersionList(productId);
    }

    @AuraEnabled
    public static String GenerateBrandList(){
    	String accId = CCM_PortalPageUtil.getCustomerByUser(UserInfo.getUserId());
        Set<String> brandList = new Set<String>();
        for(Sales_Program__c sp : [SELECT Id, Brands__c FROM Sales_Program__c WHERE RecordTypeId IN :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_IDS AND Approval_Status__c = 'Approved' AND Customer__c =: accId]){
            brandList.add(sp.Brands__c);
        }
		return JSON.serialize(brandList);
    }

    @AuraEnabled
    public static Boolean IsBatteryOrCharger(String productId){
    	Product2 prod = [SELECT Id, Product_Type__c FROM Product2 WHERE Id =: productId
                        AND Is_History_Product__c = :CCM_Constants.blHistoryProduct];
    	if(prod.Product_Type__c == 'Product'){
    		return false;
    	}else{
    		return true;
    	}

        // return null;
    }

    @AuraEnabled
    public static String eligibilityCheck(String partsList, String productId, String brand,Integer clickNum){
		Map<String,Object> result = new Map<String,Object>();
        Id idAccount = CCM_PortalPageUtil.getCustomerByUser(UserInfo.getUserId());
        Boolean boolIsAltaQuip = false;
        Decimal productCOGSPrice = 0.00;
        Decimal shippingCost = 0.00;
        Decimal diagnosisFee;
        Decimal partsCostHours = 0.00;
        Decimal laborRate;
        Decimal partsCostTP =0.00;
        Decimal markupFee = 0.00;
        Decimal warrantyCreditMarkUp = 0.00;
        String priceBookName = '';
        String org = 'CNA';
        if (String.isNotBlank(idAccount)) {
            for (Account objA : [SELECT Name, AccountNumber, ORG_Code__c FROM Account WHERE Id = :idAccount]) {
                for (String strAq : ((String) Label.CCM_Alta_Quip_Account).split('::')) {
                    if (strAq.equals(objA.Name) || strAq.equals(objA.AccountNumber)) {
                        boolIsAltaQuip = true;
                        break;
                    }
                }
                org = objA.ORG_Code__c;
            }
        }
        Product2 product = [SELECT Id,ProductCode,Category_1__c FROM Product2 WHERE Id = :productId];
        List<Eligibility_Check_Price__c> ecPriceList = [SELECT Id,Name,Product_Code__c,convertCurrency(Shipping_Cost__c),convertCurrency(Produce_COGS_Price__c) FROM Eligibility_Check_Price__c WHERE Product_Code__c =:product.ProductCode ORDER BY CreatedDate DESC LIMIT 1];

        if(ecPriceList.size() > 0){
            if(ecPriceList[0].Shipping_Cost__c == null && ecPriceList[0].Produce_COGS_Price__c == null){
                priceBookName = '(Shipping Cost AND Produce COGS Price)';
                if(clickNum == 0){
                    sendEmailWhenNoPrice(priceBookName,product.ProductCode);
                    result.put('clickNu',true);
                }

            }else{
                if(ecPriceList[0].Shipping_Cost__c != null){
                    shippingCost = ecPriceList[0].Shipping_Cost__c;
                }else{
                    priceBookName = '(Shipping Cost)';
                    if(clickNum == 0){
                        sendEmailWhenNoPrice(priceBookName,product.ProductCode);
                        result.put('clickNu',true);
                    }
                }

                if(ecPriceList[0].Produce_COGS_Price__c != null){
                    productCOGSPrice = ecPriceList[0].Produce_COGS_Price__c;
                }else{
                    priceBookName = '(Produce COGS Price)';
                    if(clickNum == 0){
                        sendEmailWhenNoPrice(priceBookName,product.ProductCode);
                        result.put('clickNu',true);
                    }
                }
            }
        }else{
            priceBookName = '(Shipping Cost AND Produce COGS Price)';
            if(clickNum == 0){
                sendEmailWhenNoPrice(priceBookName,product.ProductCode);
                result.put('clickNu',true);
            }
        }
        List<Object> piList = (List<Object>)JSON.deserializeUntyped(partsList);
        if(boolIsAltaQuip){
            String strProductCategory = String.isNotBlank(product.Category_1__c) ? product.Category_1__c : '';
            List<Labor_Rate__c> authBrand = [
                                                SELECT convertCurrency(Labor_Rate__c), Category__c,CreatedDate
                                                FROM Labor_Rate__c
                                                WHERE Category__c In ('Wheeled','Ride on','Hand tools')
                                                ORDER BY CreatedDate DESC
                                            ];
            if(authBrand.size() > 0){
                if (strProductCategory == '') {
                    throw new AuraHandledException(Label.CCM_Portal_EligibilityCheckTips4);
                } else{
                    for (Labor_Rate__c objLR : authBrand) {
                        if (objLR.Category__c.equals(strProductCategory)) {
                            diagnosisFee = objLR.Labor_Rate__c;
                            laborRate = objLR.Labor_Rate__c;
                            break;
                        }
                    }
                }
            }else{
                throw new AuraHandledException(Label.CCM_Portal_EligibilityCheckTips4);
            }
            List<Pricebook2> priceBookIdList = new List<Pricebook2>();
            String tpPriceBookId = '';
            String msrpPriceBookId = '';
            // if(org == 'CCA'){
            //     priceBookIdList = [Select Id,Name  FROM Pricebook2 WHERE Name = 'CA Intercompany Price List'  AND IsActive = true LIMIT 1];
            //     if(priceBookIdList != null && priceBookIdList.size() > 0){
            //         tpPriceBookId = priceBookIdList[0].Id;
            //     }
            // }else{
            priceBookIdList = [Select Id,Name  FROM Pricebook2 WHERE Name = 'CNA Interco Price List'  AND IsActive = true LIMIT 1];
            if(priceBookIdList != null && priceBookIdList.size() > 0){
                tpPriceBookId = priceBookIdList[0].Id;
            }
            // }
            priceBookIdList = [Select Id,Name  FROM Pricebook2 WHERE Name = 'CNA-MSRP for Parts'  AND IsActive = true LIMIT 1];
            if(priceBookIdList != null && priceBookIdList.size() > 0){
                msrpPriceBookId = priceBookIdList[0].Id;
            }
            if(piList.size() > 0){
                List<String> partsCodeList = new List<String>();
                Map<String,String> partCodeMapToQuantity = new Map<String,String>();
                for(Object pi : piList){
                    Map<String, Object> piMap = (Map<String, Object>)pi;
                    partsCodeList.add((String)piMap.get('ProductCode'));
                    if(partCodeMapToQuantity.containsKey((String)piMap.get('ProductCode'))){
                        Integer amount = Integer.valueOf(partCodeMapToQuantity.get((String)piMap.get('ProductCode'))) + Integer.valueOf(piMap.get('quantity'));
                        partCodeMapToQuantity.put((String)piMap.get('ProductCode'), String.valueOf(amount));
                    }else{
                        partCodeMapToQuantity.put((String)piMap.get('ProductCode'), (String)piMap.get('quantity'));
                    }
                 }
                List<Kit_Item__c> kitItemsList = [SELECT Id,Parts__c,Valid_Hours__c,Parts__r.ProductCode
                                                    FROM Kit_Item__c WHERE Id != NULL
                                                    AND Product__c =: productId
                                                    AND RecordType.Name = 'Products and Parts'
                                                    AND Parts__r.ProductCode IN: partsCodeList
                                                    AND Status__c = 'A'
                                                    AND (ReferenceId__c LIKE '%Spare part' OR ReferenceId__c LIKE '%Spare part Version%')
                                                    AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                                    AND Parts__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                                    ];
                Map<Id,String> partsCodeMapToPriceId = new Map<Id,String>();
                if(kitItemsList.size() > 0){
                    for(Kit_Item__c kit : kitItemsList){
                        if(!partsCodeMapToPriceId.containsKey(kit.Parts__c)){
                            List<PricebookEntry> prodTpEntryList = Util.getPriceBookEntryByProdId(kit.Parts__c, tpPriceBookId);
                            List<PricebookEntry> prodMsrpEntryList = Util.getPriceBookEntryByProdId(kit.Parts__c, msrpPriceBookId);
                            if(prodTpEntryList != null && prodTpEntryList.size() > 0){
                                partsCostTP += prodTpEntryList[0].UnitPrice * Integer.valueOf(partCodeMapToQuantity.get(kit.Parts__r.ProductCode));
                            }else{

                                priceBookName = '(Transfer Price)';
                                if(clickNum == 0){
                                    sendEmailWhenNoPrice(priceBookName,kit.Parts__r.ProductCode);
                                    result.put('clickNu',true);
                                }

                            }
                            if(prodMsrpEntryList != null && prodMsrpEntryList.size() > 0){
                                markupFee += prodMsrpEntryList[0].UnitPrice * Integer.valueOf(partCodeMapToQuantity.get(kit.Parts__r.ProductCode));
                            }else{
                                throw new AuraHandledException(Label.CCM_Portal_EligibilityCheckTips6);
                            }

                            if(kit.Valid_Hours__c != null){
                                partsCostHours += kit.Valid_Hours__c * Integer.valueOf(partCodeMapToQuantity.get(kit.Parts__r.ProductCode));
                            }
                            partsCodeMapToPriceId.put(kit.Parts__c, tpPriceBookId);
                        }

                    }
                }
                partsCostHours = partsCostHours/60;
                partsCostTP = partsCostTP / 1.1;
                // if(org != 'CCA'){
                //     partsCostTP = partsCostTP / 1.1;
                // }else{
                //     partsCostTP = (partsCostTP / 1.1)*1.36;
                // }
                System.debug(LoggingLevel.INFO, '*** test111111111: ');
                System.debug(LoggingLevel.INFO, '*** productCOGSPrice: ' + productCOGSPrice);
                System.debug(LoggingLevel.INFO, '*** shippingCost: ' + shippingCost);
                System.debug(LoggingLevel.INFO, '*** diagnosisFee: ' + diagnosisFee);
                System.debug(LoggingLevel.INFO, '*** partsCostHours: ' + partsCostHours);
                System.debug(LoggingLevel.INFO, '*** laborRate: ' + laborRate);
                System.debug(LoggingLevel.INFO, '*** partsCostTP: ' + partsCostTP);
                System.debug(LoggingLevel.INFO, '*** markupFee: ' + markupFee);
                if(String.isNotBlank(priceBookName)){
                    result.put('Status', 'Fail');
                    result.put('Message', Label.CCM_Portal_EligibilityCheckTips3);
                    return JSON.serialize(result);
                }
                if(productCOGSPrice + shippingCost + diagnosisFee> ( partsCostHours * laborRate + partsCostTP + markupFee * 0.3)){
                    result.put('Status', 'Success');
                    result.put('Message', Label.CCM_Portal_EligibilityCheckTips1);
                    result.put('validHour', partsCostHours * 60);

                }else{
                    result.put('Status', 'Fail');
                    result.put('Message', Label.CCM_Portal_EligibilityCheckTips2);
                }
            }
        }else{
            List<Labor_Rate__c> authBrand1 = [
                                                SELECT Labor_Rate__c, Category__c
                                                FROM Labor_Rate__c WHERE Id != NULL
                                                AND Authorized_Brand__r.Customer__c =: idAccount
                                                AND Authorized_Brand__r.Brands__c =: brand
                                                AND Authorized_Brand__r.RecordTypeId IN :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_IDS
                                                AND Authorized_Brand__r.Approval_Status__c = 'Approved'
                                                AND Authorized_Brand__r.Status__c != 'Inactive'
                                                AND Status__c ='Active'];
            if(authBrand1.size() > 0){
                System.debug(LoggingLevel.INFO, '*** authBrand1: ' + authBrand1);
                laborRate = authBrand1[0].Labor_Rate__c;
            }else{
                throw new AuraHandledException(Label.CCM_Portal_EligibilityCheckTips4);
            }
            List<Pricebook2> priceBookIdList1 = new List<Pricebook2>();
            List<Customer_Brand_Pricebook_Mapping__c> priceBookIdList2 = new List<Customer_Brand_Pricebook_Mapping__c>();
            String tpPriceBookId1 = '';
            String msrpPriceBookId1 = '';
            String priceBookId = '';
            // if(org == 'CCA'){
            //     priceBookIdList1 = [Select Id,Name  FROM Pricebook2 WHERE Name = 'CA Intercompany Price List'  AND IsActive = true LIMIT 1];
            //     if(priceBookIdList1 != null && priceBookIdList1.size() > 0){
            //         tpPriceBookId1 = priceBookIdList1[0].Id;
            //     }
            // }else{
            priceBookIdList1 = [Select Id,Name  FROM Pricebook2 WHERE Name = 'CNA Interco Price List'  AND IsActive = true LIMIT 1];
            if(priceBookIdList1 != null && priceBookIdList1.size() > 0){
                tpPriceBookId1 = priceBookIdList1[0].Id;
            }
            // }
            // 25.1.23: CCA 2nd Tier Dealer取父级Id找Auth Brands
            Id targetAccountId = idAccount;
            List<String> lstCCA2ndTierDealerProfile = new List<String>{
                'Partner Community 2nd Tier Dealer Sales for CA', 
                'Partner Community 2nd Tier Dealer Service for CA',
                'Partner Community 2nd Tier Dealer Sales&Service for CA'
            };
            User currentUser = [SELECT Id, ContactId, Contact.AccountId, Profile.Name FROM User WHERE Id = :UserInfo.getUserId()];
            if (lstCCA2ndTierDealerProfile.contains(currentUser.Profile.Name)) {
                Account obj2ndTierDealer = [SELECT Id, ParentId FROM Account WHERE Id = :idAccount];
                targetAccountId = obj2ndTierDealer.ParentId != null ? obj2ndTierDealer.ParentId : idAccount;
            }
            // 25.1.23 end
            List<Sales_Program__c> brandList = [SELECT Id,Warranty_parts_credit_mark_up__c FROM Sales_Program__c WHERE Customer__c =: targetAccountId AND RecordTypeId IN :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_IDS AND Brands__c =: brand AND Approval_Status__c = 'Approved'];
            List<Account> accList = [SELECT Id,Distributor_or_Dealer__c, ORG_Code__c, AccountNumber FROM Account WHERE Id =: targetAccountId];
            if(CCM_Constants.ORG_CODE_CCA == org) {
                if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')) {
                    priceBookIdList2 = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Name = 'CA-Distributor Price for Parts' AND Type__c = 'Service' AND Is_Active__c = true LIMIT 1];
                    if(priceBookIdList2 != null && priceBookIdList2.size() > 0){
                        priceBookId = priceBookIdList2[0].Price_Book__c;
                    }
                }
                else {
                    priceBookIdList2 = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Name = 'CA-Direct Dealer Price for Parts' AND Type__c = 'Service' AND Is_Active__c = true LIMIT 1];
                    if(priceBookIdList2 != null && priceBookIdList2.size() > 0){
                        priceBookId = priceBookIdList2[0].Price_Book__c;
                    }
                }
            }
            else {
                if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Dealer')){
                    priceBookIdList2 =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Direct Dealer Price for Parts' LIMIT 1];
                    if(priceBookIdList2 != null && priceBookIdList2.size() > 0){
                        priceBookId = priceBookIdList2[0].Price_Book__c;
                    }
                }else if(accList.size() > 0 && accList[0].Distributor_or_Dealer__c.contains('Distributor')){
                    priceBookIdList2 =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Distributor Price for Parts' LIMIT 1];
                    if(priceBookIdList2 != null && priceBookIdList2.size() > 0){
                        priceBookId = priceBookIdList2[0].Price_Book__c;
                    }
                }
            }
            if(piList.size() > 0){
                List<String> partsCodeList1 = new List<String>();
                Map<String,String> partCodeMapToQuantity1 = new Map<String,String>();
                for(Object pi : piList){
                    Map<String, Object> piMap1 = (Map<String, Object>)pi;
                    partsCodeList1.add((String)piMap1.get('ProductCode'));
                    if(partCodeMapToQuantity1.containsKey((String)piMap1.get('ProductCode'))){
                        Integer amount = Integer.valueOf(partCodeMapToQuantity1.get((String)piMap1.get('ProductCode'))) + Integer.valueOf(piMap1.get('quantity'));
                        partCodeMapToQuantity1.put((String)piMap1.get('ProductCode'), String.valueOf(amount));
                    }else{
                        partCodeMapToQuantity1.put((String)piMap1.get('ProductCode'), (String)piMap1.get('quantity'));
                    }
                 }
                List<Kit_Item__c> kitItemsList1 = [SELECT Id,Parts__c,Valid_Hours__c,Parts__r.ProductCode
                                                    FROM Kit_Item__c WHERE Id != NULL
                                                    AND Product__c =: productId
                                                    AND RecordType.Name = 'Products and Parts'
                                                    AND Parts__r.ProductCode IN: partsCodeList1
                                                    AND Status__c = 'A'
                                                    AND (ReferenceId__c LIKE '%Spare part' OR ReferenceId__c LIKE '%Spare part Version%')
                                                    AND Product__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                                    AND Parts__r.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                                                    ];
                Map<Id,String> partsCodeMapToPriceId1 = new Map<Id,String>();
                if(kitItemsList1.size() > 0){
                    for(Kit_Item__c kit : kitItemsList1){
                        if(!partsCodeMapToPriceId1.containsKey(kit.Parts__c)){
                            List<PricebookEntry> prodTpEntryList1 = Util.getPriceBookEntryByProdId(kit.Parts__c, tpPriceBookId1);
                            List<PricebookEntry> prodMsrpEntryList1 = Util.getPriceBookEntryByProdId(kit.Parts__c, priceBookId);
                            if(prodTpEntryList1 != null && prodTpEntryList1.size() > 0){
                                partsCostTP += prodTpEntryList1[0].UnitPrice * Integer.valueOf(partCodeMapToQuantity1.get(kit.Parts__r.ProductCode));
                            }else{
                                priceBookName = '(Transfer Price)';
                                if(clickNum == 0){
                                    sendEmailWhenNoPrice(priceBookName,kit.Parts__r.ProductCode);
                                    result.put('clickNu',true);
                                }
                            }
                            if(prodMsrpEntryList1 != null && prodMsrpEntryList1.size() > 0){
                                markupFee += prodMsrpEntryList1[0].UnitPrice * Integer.valueOf(partCodeMapToQuantity1.get(kit.Parts__r.ProductCode));
                            }else{
                                throw new AuraHandledException(Label.CCM_Portal_EligibilityCheckTips5);
                            }

                            if(kit.Valid_Hours__c != null){
                                partsCostHours += kit.Valid_Hours__c * Integer.valueOf(partCodeMapToQuantity1.get(kit.Parts__r.ProductCode));
                            }
                            partsCodeMapToPriceId1.put(kit.Parts__c, tpPriceBookId1);
                        }

                    }
                    partsCostHours = partsCostHours/60;
                    partsCostTP = partsCostTP / 1.1;
                    // if(org != 'CCA'){
                    //     partsCostTP = partsCostTP / 1.1;
                    // }else{
                    //     partsCostTP = (partsCostTP / 1.1)*1.36;
                    // }
                }
                if(String.isNotBlank(priceBookName)){
                    result.put('Status', 'Fail');
                    result.put('Message', Label.CCM_Portal_EligibilityCheckTips3);
                    return JSON.serialize(result);
                }
                System.debug(LoggingLevel.INFO, '*** test2222222222222: ');
                System.debug(LoggingLevel.INFO, '*** productCOGSPrice: ' + productCOGSPrice);
                System.debug(LoggingLevel.INFO, '*** shippingCost: ' + shippingCost);
                System.debug(LoggingLevel.INFO, '*** diagnosisFee: ' + diagnosisFee);
                System.debug(LoggingLevel.INFO, '*** partsCostHours: ' + partsCostHours);
                System.debug(LoggingLevel.INFO, '*** laborRate: ' + laborRate);
                System.debug(LoggingLevel.INFO, '*** partsCostTP: ' + partsCostTP);
                System.debug(LoggingLevel.INFO, '*** markupFee: ' + markupFee);

                if(brandList.size() > 0){
                    if(brandList[0].Warranty_parts_credit_mark_up__c != null){
                        warrantyCreditMarkUp = brandList[0].Warranty_parts_credit_mark_up__c/100;
                    }
                    System.debug(LoggingLevel.INFO, '*** mark up: ' + brandList[0].Warranty_parts_credit_mark_up__c);
                }else{
                     throw new AuraHandledException(Label.CCM_Portal_EligibilityCheckTips7);
                }
                if(productCOGSPrice + shippingCost> ( partsCostHours * laborRate + partsCostTP + markupFee * warrantyCreditMarkUp)){
                    result.put('Status', 'Success');
                    result.put('Message', Label.CCM_Portal_EligibilityCheckTips1);
                    result.put('validHour', partsCostHours * 60);

                }else{
                    result.put('Status', 'Fail');
                    result.put('Message', Label.CCM_Portal_EligibilityCheckTips2);
                }
            }
        }


    	return JSON.serialize(result);
    }

    public static void sendEmailWhenNoPrice(String priceBookName,String productCode){
        EmailTemplate emailTemplate = [SELECT Id, Subject, Description, HtmlValue, DeveloperName, Body
                                         FROM EmailTemplate
                                         WHERE DeveloperName= 'Eligibility_Check_Price_Not_Found'];
        System.debug(LoggingLevel.INFO, '*** emailTemplate: ' + emailTemplate);
        if( emailTemplate != null ){
            System.debug(LoggingLevel.INFO, '*** test: ');
            Messaging.SingleEmailMessage mail = new  Messaging.SingleEmailMessage();
            mail.setToAddresses(new List<String>{'<EMAIL>','<EMAIL>'});
            mail.setSubject(emailTemplate.Subject);
            String tableBody = emailTemplate.HtmlValue.replace('{!Eligibility_Check_Price__c.Shipping_Cost__c} ', priceBookName)
            .replace('{!Eligibility_Check_Price__c.Product_Code__c}', productCode);
            mail.setHtmlBody(tableBody);
            OrgWideEmailAddress orgWideEmail = [SELECT Id FROM OrgWideEmailAddress WHERE Address = '<EMAIL>' LIMIT 1];
            mail.setOrgWideEmailAddressId(orgWideEmail.Id);

            System.debug(LoggingLevel.INFO, '*** test1: ' +  Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail }));

        }
    }

    public class PartsItem {
        public String itemNumber;
        public String Name;
        public String partsId;
        public Integer quantity;
        public String brand;
        public String productCode;
        public Decimal validHours;
        public Decimal price;

    }

    public class PartsItems {
        public String Name;
        public String ProductCode;
        public Integer itemNumber;
        public String quantity;

    }

    @AuraEnabled
    public static String searchPartsItem(String kitItemId){
        // add haibo: french
        Kit_Item__c ki = [SELECT Id,Parts__r.Item_Number__c,Warranty_Day__c,Wearable_Parts__c,
                                Parts__r.ProductCode,Parts__r.Name,Parts__r.Product_Name_French__c,Parts__c,Valid_Hours__c,
                                Parts__r.Brand__c
                             FROM Kit_Item__c
                             WHERE Id =: kitItemId
                             AND RecordType.Name = 'Products and Parts'];

            PartsItem pi = new PartsItem();
            if(ki != null){

	            pi.itemNumber = ki.Parts__r.Item_Number__c;
                // add haibo: French
                if(UserInfo.getLanguage() == Label.CCM_Portal_French){
                    pi.Name = ki.Parts__r.Product_Name_French__c;
                }else{
                    pi.Name = ki.Parts__r.Name;
                }
	            pi.partsId = ki.Parts__c;
	            pi.quantity = 1;
	            pi.brand = ki.Parts__r.Brand__c;
	            pi.productCode = ki.Parts__r.ProductCode;
	            pi.validHours = ki.Valid_Hours__c;
            }


        Map<String,Object> result = new Map<String,Object>();
        result.put('PartsList', ki);
        return JSON.serialize(result);
    }
    public static void forTestCoverage(){
        Integer a = 200;
        for(Integer i=0;i<a;i++){
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
            i+=1;
        }
    }
}