public without sharing class CCM_Lead_Validation_Rules_Handler implements Triggers.Handler {
    public static boolean isRun = true;
    public void handle() {
        if(!CCM_Lead_Validation_Rules_Handler.isRun) {
            return;
        }
        if (Trigger.isBefore) {
            for(Lead lead : (List<Lead>)Trigger.new) {
                if (lead.Credit_Limit__c != null) {
                    if (lead.Credit_Limit__c == 'N/A') {
                        continue;
                    }
                    try {
                        Decimal value = Decimal.valueOf(lead.Credit_Limit__c);
                        String normalizedInput = lead.Credit_Limit__c.trim();
                        String decimalString = String.valueOf(value);
                        if (!normalizedInput.equals(decimalString)) {
                            lead.addError('Credit_Limit__c', 'Please enter a valid number format or "N/A"');
                        }
                    } catch (Exception e) {
                        lead.addError('Credit_Limit__c', 'Please enter a valid number format or "N/A"');
                    }
                }
            }

            if (Trigger.isInsert) {
                //validateLeadUpdate(Trigger.new, Trigger.newMap, null);
                validateLeadInsert(Trigger.new, Trigger.newMap, null);
            } else if (Trigger.isUpdate) {
                validateLeadUpdate(Trigger.new, Trigger.newMap, Trigger.oldMap);
            }
        }
    }

    /*private void validateLeadInsert(List<sObject> newList, Map<Id, sObject> newMap) {
        List<Lead> leadList = (List<Lead>)newList;
        for (Lead leadRow : leadList) {
            CCM_V_Lead_Validations.requirePhoneOrMobileOrEmail(leadRow);

        }
    }*/
    private void validateLeadInsert(List<sObject> newList, Map<Id, sObject> newMap, Map<Id, sObject> oldMap) {
        List<Lead> leadList = (List<Lead>)newList;
        for (Lead leadRow : leadList) {
            CCM_V_Lead_Validations.requirePhoneOrMobileOrEmail(leadRow);

            CCM_V_Lead_Validations.validateStatusIsOpen(leadRow);
        }
    }

    private void validateLeadUpdate(List<sObject> newList, Map<Id, sObject> newMap, Map<Id, sObject> oldMap) {
        List<Lead> leadList = (List<Lead>)newList;
        Map<Id, Lead> queriedLeadMap = new Map<Id, Lead>([
            select Id, OwnerId, Owner.UserRole.DeveloperName,Director_Approver__c,Customer_Cluster__c,Customer_Sub_Cluster__c,Sales_Channel__c,Sales_Group__c,
                (Select id, Name, Invoice_Methods__c, Email_for_Invoice__c, Ship_Complete__c, Shipment_Priority__c,
                    Payment_Term__c, Payment_Lead_Time__c, Starting_Date_of_Payment_Term__c,
                    Freight_Term__c, Deliver_From__c, Order_Lead_Time__c,
                    Show_Room__c, Business_Volume_Potential__c, Delivery__c, Service__c,
                    Service_Capability__c, Operataion_Hours__c,
                    OwnerId, Director_Approver__c, Approval_Status__c,
                    RecordType.DeveloperName
                from Sales_Program__r),
                (Select Id, Name, OwnerId, Sales_Director__c, Contact__c, RecordTypeId, Email_for_Invoicing__c, EDI__c from Account_Address__r),
                (Select Id, OwnerId, Attachment_Type__c from Attachment_Management__r),
                (Select Id, OwnerId from Contacts__r),
                (Select Id, WhoId From Tasks WHERE Status = 'Completed' Limit 1),
                (Select Id, WhoId From Events Limit 1),
                (Select Id, OwnerId from Customer_Profile__r)
            from Lead
            where Id IN :newMap.keySet()
        ]);
        // get leadToAMsMap
        Map<Id, List<Attachment_Management__c>> leadToAMsMap = new Map<Id, List<Attachment_Management__c>>();
        Set<Id> amIdSet = new Set<Id>();
        for (Lead leadRow : leadList){
            if (queriedLeadMap.get(leadRow.Id).Attachment_Management__r != null) {
                for (Attachment_Management__c am : queriedLeadMap.get(leadRow.Id).Attachment_Management__r) {
                    amIdSet.add(am.Id);
                }
            }
        }

        List<Attachment_Management__c> amList = [
            Select Id, Attachment_Type__c, Prospect__c,
                (Select Id, LinkedEntityId from ContentDocumentLinks)
            from Attachment_Management__c where Id IN : amIdSet
        ];
        for (Attachment_Management__c am : amList) {
            if (leadToAMsMap.containsKey(am.Prospect__c)) {
                leadToAMsMap.get(am.Prospect__c).add(am);
            } else {
                leadToAMsMap.put(am.Prospect__c, new List<Attachment_Management__c>{am});
            }
        }
        System.debug(LoggingLevel.INFO, '*** leadToAMsMap===>: ' + leadToAMsMap);

        /*//Task WhoId
        Set<Id> taskWhoIdSet = new Set<Id>();
        List<Task> taskLst = [SELECT Id, WhoId, IsClosed, Status FROM Task WHERE Status = 'Completed'];
        for(Task task : taskLst){
            taskWhoIdSet.add(task.WhoId);
        }

        //Event WhoId
        Set<Id> eventWhoIdSet = new Set<Id>();
        List<Event> eventLst = [SELECT Id, IsDeleted, WhoId FROM Event];
        System.debug(LoggingLevel.INFO, '*** eventLst: ' + eventLst);
        for(Event event : eventLst){
            eventWhoIdSet.add(event.WhoId);
        }*/

        // address with program
        Map<Id, Map<Id, List<Address_With_Program__c>>> leadToSPsMap = new Map<Id, Map<Id, List<Address_With_Program__c>>>();
        Set<Id> spIdSet = new Set<Id>();
        Map<Id, String> spIdToNameMap = new Map<Id, String>();
        for (Lead leadRow : leadList) {
            leadToSPsMap.put(leadRow.Id, new Map<Id, List<Address_With_Program__c>>());
            if (queriedLeadMap.get(leadRow.Id).Sales_Program__r != null) {
                for (Sales_Program__c sp : queriedLeadMap.get(leadRow.Id).Sales_Program__r) {
                    spIdSet.add(sp.Id);
                    leadToSPsMap.get(leadRow.Id).put(sp.Id, new List<Address_With_Program__c>());
                    spIdToNameMap.put(sp.Id, sp.Name);
                }
            }
        }
        List<Address_With_Program__c> awpList = [
            SELECT Id, Address_Type__c, Program__c, Program__r.Prospect__c
            FROM Address_With_Program__c
            WHERE Program__c In :spIdSet
            AND Address_Type__c = 'Billing Address'
        ];
        for (Address_With_Program__c awp : awpList) {
            if (String.isNotBlank(awp.Program__c) && String.isNotBlank(awp.Program__r.Prospect__c)) {
                leadToSPsMap.get(awp.Program__r.Prospect__c).get(awp.Program__c).add(awp);
            }
        }

        // get new OwnerId
        Set<Id> userIdSet = new Set<Id>();
        Set<Id> queueIdSet = new Set<Id>();
        Set<Id> roleGroupIdSet = new Set<Id>();
        for (Lead leadRow : leadList) {
            if(leadRow.OwnerId.getSobjectType().getDescribe().getName() == 'User'){
                userIdSet.add(leadRow.OwnerId);
            } else if (leadRow.OwnerId.getSobjectType().getDescribe().getName() == 'Group') {
                queueIdSet.add(leadRow.OwnerId);
            }
        }
        Map<Id, User> userMap = new Map<Id, User>([
            Select Id, UserRole.DeveloperName, Profile.Name from User where Id IN : userIdSet
        ]);
        //Additional sales managers
        List<GroupMember> gms = [Select GroupId, Id, SystemModstamp, UserOrGroupId from GroupMember where GroupId IN (Select Id from Group where Type = 'Queue' and DeveloperName = 'Sales_Manager_Additional')];
        Set<Id> additionalManagerSet = new Set<Id>();
        for (GroupMember gm : gms) {
            if (gm.UserOrGroupId .getSobjectType().getDescribe().getName() == 'Group') {
                roleGroupIdSet.add(gm.UserOrGroupId);
            }else{
                additionalManagerSet.add(gm.UserOrGroupId);
            }
        }

        for(Group gr : [Select RelatedId from Group where Id IN : roleGroupIdSet]){
            additionalManagerSet.add(gr.RelatedId);
        }
        // Assignment Rule of CCA
        List<Prospect_Assignment_Rule__c> assignRuleCCAMap = new List<Prospect_Assignment_Rule__c>([
            SELECT Id, User__c FROM Prospect_Assignment_Rule__c WHERE User__c IN: userIdSet AND Org_Code__c = :CCM_Constants.ORG_CODE_CCA
        ]);
        Set<Id> assignRuleCCASalesManagerSet = new Set<Id>();
        for(Prospect_Assignment_Rule__c ar : assignRuleCCAMap) {
            assignRuleCCASalesManagerSet.add(ar.User__c);
        }

        Set<String> allPhotoCatogorySet = new Set<String>();
        allPhotoCatogorySet.addAll(Util.getPicklistValues('Customer_Profile_Photo__c', 'Photo_Category__c'));
        allPhotoCatogorySet.remove('Sync From Call Log');

        for (Lead leadRow : leadList) {
            Lead oldItem = (Lead) oldMap.get(leadRow.Id);

            CCM_V_Lead_Validations.requireBillingAddressWithProgram(leadRow, spIdToNameMap, leadToSPsMap);

            CCM_V_Lead_Validations.requireAddresses(leadRow, queriedLeadMap);

            CCM_V_Lead_Validations.requireAttachment(leadRow, queriedLeadMap, leadToAMsMap);

            CCM_V_Lead_Validations.requireSalesProgram(leadRow, queriedLeadMap);

            // CCM_V_Lead_Validations.requireAllCategoryInCustomerProfile(leadRow, queriedLeadMap, allPhotoCatogorySet);

            Lead theLead = queriedLeadMap.get(leadRow.Id);
            Set<Id> taskWhoIdSet = new Set<Id>();
            for (Task task : theLead.Tasks) {
                taskWhoIdSet.add(task.WhoId);
            }
            Set<Id> eventWhoIdSet = new Set<Id>();
            for(Event event : theLead.Events){
                eventWhoIdSet.add(event.WhoId);
            }
            CCM_V_Lead_Validations.requireActivity(leadRow,taskWhoIdSet,eventWhoIdSet);

            CCM_V_Lead_Validations.validateOwnerIsSalesManager(leadRow, userMap, oldItem, additionalManagerSet, assignRuleCCASalesManagerSet);

            CCM_V_Lead_Validations.validateWhetherCanChangeOwner(leadRow, oldItem, queriedLeadMap);

            CCM_V_Lead_Validations.requirePhoneOrMobileOrEmail(leadRow);
        }
    }

}