import { LightningElement, track, wire } from 'lwc';
import { loadScript, loadStyle } from 'lightning/platformResourceLoader';
import PromotionResource from '@salesforce/resourceUrl/PromotionResource';
import PromotionBackground from '@salesforce/resourceUrl/PromotionBackground';
import getDefaultAll from '@salesforce/apex/CCM_PromotionAnnounceCtl.getDefaultAll';
import getAllPromotions from '@salesforce/apex/CCM_PromotionAnnounceCtl.getAllPromotions';
import { NavigationMixin } from 'lightning/navigation';
import { getObjectInfo } from "lightning/uiObjectInfoApi";
import { getPicklistValuesByRecordType } from "lightning/uiObjectInfoApi";
import PROMOTION2_OBJECT from "@salesforce/schema/Promotion2__c";
import getPickListValueByRecordType from '@salesforce/apex/CCM_PromotionAnnounceCtl.getPickListValueByRecordType';
import getUserProfile from '@salesforce/apex/CCM_PromotionAnnounceCtl.getUserProfile';
import getFirstTireBrands from '@salesforce/apex/CCM_PromotionAnnounceCtl.getFirstTireBrands';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

// 导入 Custom Label
import CCM_Portal_SellinPromotion from "@salesforce/label/c.CCM_Portal_SellinPromotion";
import CCM_Portal_SellthroughPOSCredit from "@salesforce/label/c.CCM_Portal_SellthroughPOSCredit";
import CCM_Portal_PromotionCode from "@salesforce/label/c.CCM_Portal_PromotionCode";
import CCM_Portal_Product from "@salesforce/label/c.CCM_Portal_Product";
import CCM_Portal_PromotionType from "@salesforce/label/c.CCM_Portal_PromotionType";
import CCM_Portal_Search from "@salesforce/label/c.CCM_Portal_Search";
import CCM_Portal_FeaturedPromotions from "@salesforce/label/c.CCM_Portal_FeaturedPromotions";
import CCM_Portal_VIEWDETAIL from "@salesforce/label/c.CCM_Portal_VIEWDETAIL";
import CCM_Portal_ViewAll from "@salesforce/label/c.CCM_Portal_ViewAll";
import CCM_Portal_ComingSoon from "@salesforce/label/c.CCM_Portal_ComingSoon";
import CCM_Portal_SelectAnOption from "@salesforce/label/c.CCM_Portal_SelectAnOption";
import CCM_Portal_CLAIMNOW from "@salesforce/label/c.CCM_Portal_CLAIMNOW";
import CCM_Portal_Noavailablepromotion from "@salesforce/label/c.CCM_Portal_Noavailablepromotion";
import CCM_Portal_Expiresin from "@salesforce/label/c.CCM_Portal_Expiresin";
import CCM_Portal_Days from "@salesforce/label/c.CCM_Portal_Days";
import CCM_Portal_Claimexpiresin from "@salesforce/label/c.CCM_Portal_Claimexpiresin";
import CCM_Portal_Error from "@salesforce/label/c.CCM_Portal_Error";

export default class CcmPortalPromotions extends NavigationMixin(LightningElement) {

    // 定义 label
    @track label = {
        CCM_Portal_SellinPromotion,
        CCM_Portal_SellthroughPOSCredit,
        CCM_Portal_PromotionCode,
        CCM_Portal_Product,
        CCM_Portal_PromotionType,
        CCM_Portal_Search,
        CCM_Portal_FeaturedPromotions,
        CCM_Portal_VIEWDETAIL,
        CCM_Portal_ViewAll,
        CCM_Portal_ComingSoon,
        CCM_Portal_SelectAnOption,
        CCM_Portal_CLAIMNOW,
        CCM_Portal_Noavailablepromotion,
        CCM_Portal_Expiresin,
        CCM_Portal_Days,
        CCM_Portal_Claimexpiresin,
        CCM_Portal_Error
    };
    defalutImgUrl = PromotionResource + '/thumbnail_default.png';

    // sell in, sell through use two template to display, 
    // before, display in one template, result banner and swiper error
    @track
    bigSalesSellIn = [];
    @track
    bigSalesSellThrough = [];

    @track
    ego = [{}];

    @track
    skil = [{}];

    @track
    flex = [{}];

    @track
    comingSoon = [];
    @track
    comingSoonSellIn = [];
    @track
    comingSoonSellThrough = [];

    @track
    allData = {};

    noDataStr = "";//No available promotion.

    isSearch = false;

    isDisplaySellIn = false;
    isDisplaySellThrough = true;
    isSellInActive = false;
    isSellThroughActive = false;

    recordType = 'Sell_In_Promotion';

    hide = true;

    recordTypeId = "";
    
    @track
    optionsSellIn = [];
    @track
    optionsSellThrough = [];

    @track
    isCCA = false;

    @track
    is2ndTierDealer;
    isCcaSecondTierDealer;

    @track
    brands = [];

    @track
    isShowSkil = false;

    @track
    isShowFlex = false;

    @track
    isShowEgo = false;

    // brand header background image url
    featuredPromotionBg = `background-image: url("${PromotionBackground}/PromotionBackground/featured_promotion.png");`
    egoBg = `background-image: url("${PromotionBackground}/PromotionBackground/ego.png");`
    skilBg = `background-image: url("${PromotionBackground}/PromotionBackground/skil.png");`
    flexBg = `background-image: url("${PromotionBackground}/PromotionBackground/flex.png");`
    comingSoonBg = `background-image: url("${PromotionBackground}/PromotionBackground/coming_soom.png");`

    // used to get Promotion_Type_for_External__c picklist
    @wire(getObjectInfo, { objectApiName: PROMOTION2_OBJECT })
    objectInfo({ error, data }) {
        // console.log("wire obj recordtype");
        // console.log(data);
        if (data) {
            this.recordTypeId = data.defaultRecordTypeId;
        }
    }
    // get Promotion_Type_for_External__c picklist
    @wire(getPicklistValuesByRecordType, {
        objectApiName: PROMOTION2_OBJECT,
        recordTypeId: "$recordTypeId"
    })
    setPicklist({ error, data }) {
        console.log(data);
        if (data) {
            const v = data.picklistFieldValues;
            this._options = v.Promotion_Type_for_External__c.values;
            if(this._options.length>0){
                this.options = this._options[0].value;
            }
            // this.wired = true;
        }
    }

    async connectedCallback() {
        const _userProfile = await getUserProfile();
        this.is2ndTierDealer = _userProfile.isSecondTier;
        this.isCcaSecondTierDealer = _userProfile.isCcaSecondTierDealer;

        getFirstTireBrands().then(values =>{ 
            this.brands = values.brandsList;
            if(this.brands.find(values => values === 'Skil')){
                this.isShowSkil = true;
            }
            if(this.brands.find(values => values.toUpperCase() === 'FLEX')){
                this.isShowFlex = true;
            }
            if(this.brands.find(values => values.toUpperCase() === 'EGO')){
                this.isShowEgo = true;
            }
        });
        if (!this.connect) {
            this.connect = true;
            loadStyle(this, PromotionResource + '/promotion.css');
            // use sessionStorage store the activated promotion record type, 
            // back from promotion detail will re-activated promotion record type
            let _promotionRecordType = sessionStorage.getItem("promotionType");
            if(!_promotionRecordType){
                _promotionRecordType = this.isCcaSecondTierDealer ? 'Sell_Through_Promotion' : 'Sell_In_Promotion';
            }
            this.recordType = _promotionRecordType;
            if(!this.isCcaSecondTierDealer){
                this.isDisplaySellIn = true;
            }
            if(_promotionRecordType === 'Sell_In_Promotion'){
                this.isSellInActive = true;
                this.isSellThroughActive = false;
            }
            if(_promotionRecordType === 'Sell_Through_Promotion'){
                this.isSellThroughActive = true;
                this.isSellInActive = false;
            }
            Promise.all([getPickListValueByRecordType(),getAllPromotions({recordType:this.recordType})]).then(values=>{
                if(values[0]){
                    if(values[0] && Array.isArray(values[0])) {
                        let _arraySellIn = [],_arraySellThrough = [];
                        _arraySellIn.push({label:this.label.CCM_Portal_SelectAnOption,value:""});
                        _arraySellThrough.push({label:this.label.CCM_Portal_SelectAnOption,value:""});

                        values[0].forEach(item=>{
                            if(item.recordType==="Sell_In_Promotion"){
                                item.picklistList.forEach((p)=>{
                                    let _o = {
                                        label: p.Name,
                                        value: p.Id
                                    }
                                    _arraySellIn.push(_o);
                                })
                                
                            }
                            if(item.recordType==="Sell_Through_Promotion"){
                                item.picklistList.forEach((p)=>{
                                    let _o = {
                                        label: p.Name,
                                        value: p.Id
                                    }
                                    _arraySellThrough.push(_o);
                                })
                            }
                        })
                        this.optionsSellIn = _arraySellIn;
                        this.optionsSellThrough = _arraySellThrough;
                    }
                }
                if (values[1]) {
                    this.allData = values[1];
                    this.handlePromotions(values[1]);
                    this.ego = values[1].egoList;
                    this.skil = values[1].skilList;
                    this.flex = values[1].flexList;
                    if(this.recordType === 'Sell_In_Promotion'){
                        this.bigSalesSellIn = values[1].bigSalesList;
                        this.comingSoonSellIn = values[1].soonList;
                    }
                    if(this.recordType === 'Sell_Through_Promotion'){
                        this.bigSalesSellThrough = values[1].bigSalesList;
                        this.comingSoonSellThrough = values[1].soonList;
                    }
                    this.noDataStr = this.label.CCM_Portal_Noavailablepromotion;
                    this.isCCA = values[1].isCCA;
                }
            },error=>{
                console.error(error);
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.label.CCM_Portal_Error,
                        message: error.body.message,
                        variant: 'error'
                    })
                );
            });
        }
    }
    /**
     * Allen, Comment on 2021/05/27
     * use third part js framework swiper.js 
    */
    renderedCallback() {
        if (!this.rendered) {
            this.rendered = true;
            let _self = this;

            Promise.all([
                loadStyle(this, PromotionResource + '/swiper-bundle.min.css'),
                loadScript(this, PromotionResource + '/swiper-bundle.min.js')
            ]).then(() => {
                this.swiperJsLoaded = true;
                let _context1 = _self.template.querySelector('.brand-ego');
                let swiperEgo = new Swiper('.swiper-container1', {
                    slidesPerView: 3,
                    spaceBetween: 10,
                    slidesPerGroup: 3,
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    a11y: {
                        enabled: false
                    },
                    context: _context1
                });

                let _context2 = _self.template.querySelector('.brand-skil');
                let swiperSkil = new Swiper('.swiper-container2', {
                    slidesPerView: 3,
                    spaceBetween: 10,
                    slidesPerGroup: 3,
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    a11y: {
                        enabled: false
                    },
                    context: _context2
                });

                let _context3 = _self.template.querySelector('.brand-flex');
                let swiperFlex = new Swiper('.swiper-container3', {
                    slidesPerView: 3,
                    spaceBetween: 10,
                    slidesPerGroup: 3,
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    a11y: {
                        enabled: false
                    },
                    context: _context3
                });
            },error=>{
                console.error(error);
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Error',
                        message: error.body.message,
                        variant: 'error'
                    })
                );
            })
        } else if(this.rendered && this.swiperJsLoaded) {
            let _context1 = this.template.querySelector('.brand-ego');
            let swiperEgo = new Swiper('.swiper-container1', {
                slidesPerView: 3,
                spaceBetween: 10,
                slidesPerGroup: 3,
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                a11y: {
                    enabled: false
                },
                context: _context1
            });

            let _context2 = this.template.querySelector('.brand-skil');
            let swiperSkil = new Swiper('.swiper-container2', {
                slidesPerView: 3,
                spaceBetween: 10,
                slidesPerGroup: 3,
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                a11y: {
                    enabled: false
                },
                context: _context2
            });

            let _context3 = this.template.querySelector('.brand-flex');
            let swiperFlex = new Swiper('.swiper-container3', {
                slidesPerView: 3,
                spaceBetween: 10,
                slidesPerGroup: 3,
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                a11y: {
                    enabled: false
                },
                context: _context3
            });

            if (this.isSearch) {
                let _context4 = this.template.querySelector('.coming-soon');
                let swiperComingSoon = new Swiper('.swiper-container4', {
                    slidesPerView: 3,
                    spaceBetween: 10,
                    slidesPerGroup: 3,
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    a11y: {
                        enabled: false
                    },
                    context: _context4
                });
            }
        }
    }

    /**
     * Allen, Comment on 2021/05/27
     * get sell in promotions
    */
    handleActiveSellIn(){
        this.isSellInActive = true;
        this.isSellThroughActive = false;
        this.isSearch = false;
        this.recordType = 'Sell_In_Promotion';
        sessionStorage.setItem("promotionType","Sell_In_Promotion");
        getAllPromotions({recordType:this.recordType}).then(result => {
            if (result) {
                console.log("--get Sell_In_Promotion--");
                console.log(result);
                this.allData = result;
                this.handlePromotions(result);
                this.bigSalesSellIn = result.bigSalesList;
                this.ego = result.egoList;
                this.skil = result.skilList;
                this.flex = result.flexList;
                this.comingSoonSellIn = result.soonList;
            }
        },error=>{
            console.error(error);
            this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Error',
                    message: error.body.message,
                    variant: 'error'
                })
            );
        })
    }

    /**
     * Allen, Comment on 2021/05/27
     * get sell through promotions
    */
    handleActiveSellThrough(){
        this.isSellThroughActive = true;
        this.isSellInActive = false;
        this.isSearch = false;
        this.recordType = 'Sell_Through_Promotion';
        sessionStorage.setItem("promotionType","Sell_Through_Promotion");
        getAllPromotions({recordType:this.recordType}).then(result => {
            if (result) {
                console.log("--get Sell_Through_Promotion--");
                console.log(result);
                this.allData = result;
                this.handlePromotions(result);
                this.bigSalesSellThrough = result.bigSalesList;
                this.ego = result.egoList;
                this.skil = result.skilList;
                this.flex = result.flexList;
                this.comingSoonSellThrough = result.soonList;
            }
        },error=>{
            console.error(error);
            this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Error',
                    message: error.body.message,
                    variant: 'error'
                })
            );
        })
    }

    /**
     * Allen, Comment on 2021/05/27
     * filter promotions in front end
    */
    handleClickSearch(event) {
        this.isSearch = false;
        let _allData = JSON.parse(JSON.stringify(this.allData));
        let promoCode = this.template.querySelector('.promoCode').value;
        let productName = this.template.querySelector('.product').value;
        let typeforexternal = this.template.querySelector('.typeforexternal').value;
        if (promoCode) {
            this.isSearch = true;
            Object.keys(_allData).forEach(key => {
                if (key !== "bigSalesList") {
                    if (_allData[key] && Array.isArray(_allData[key])) {
                        _allData[key] = _allData[key].filter(item => {
                            if(item.promotionCode){
                                return item.promotionCode.toLowerCase().indexOf(promoCode.toLowerCase()) > -1;
                            }
                            return false;
                        })
                    }
                }
            })
        }
        if (productName) {
            this.isSearch = true;
            Object.keys(_allData).forEach(key => {
                if (key !== "bigSalesList") {
                    if (_allData[key] && Array.isArray(_allData[key])) {
                        _allData[key] = _allData[key].filter(item => {
                            if (item.productList && Array.isArray(item.productList)) {
                                return item.productList.filter(product => {
                                    let _productName = product.productName ? product.productName : '';
                                    let _productCode = product.productCode ? product.productCode : '';
                                    return _productName.toLowerCase().indexOf(productName.toLowerCase()) > -1 ||
                                    _productCode.toLowerCase().indexOf(productName.toLowerCase()) > -1
                                }).length > 0;
                            }
                            return false;
                        })
                    }
                }
            })
        }
        if (typeforexternal) {
            this.isSearch = true;
            Object.keys(_allData).forEach(key => {
                if (key !== "bigSalesList") {
                    if (_allData[key] && Array.isArray(_allData[key])) {
                        _allData[key] = _allData[key].filter(item => {
                            return item.promotionTypeExt === typeforexternal;
                        })
                    }
                }
            })
        }

        this.ego = _allData.egoList;
        this.skil = _allData.skilList;
        this.flex = _allData.flexList;
        if(this.recordType === 'Sell_In_Promotion'){
            this.comingSoonSellIn = _allData.soonList;
        }
        if(this.recordType === 'Sell_Through_Promotion'){
            this.comingSoonSellThrough = _allData.soonList;
        }
    }

    handleClickViewDetail(event){
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: "PromotionDetail__c"
            },
            state: {
                c__windowid: event.currentTarget.dataset.promotionwindowid
            }
        });
    }

    handleClickClaimNow(event){
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: "Sell_Through_Promotion_Claim__c"
            },
            state: {
                c__promotioncode: encodeURI(event.currentTarget.dataset.promotioncode)
            }
        });
    }

    handleViewAll(event){
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: "PromotionList__c"
            },
            state: {
                c__type: event.currentTarget.dataset.recordtype,
                c__brand: event.currentTarget.dataset.brand
            }
        });
    }

    handleClickOrderNow(event){
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: "Edit_Quotation__c"
            },
            state: {
                c__promotioncode: encodeURI(event.currentTarget.dataset.promotioncode)
            }
        });
    }

    /**
     * Allen, Comment on 2021/05/27
     * add thumbnail image, if image is not configure, use default image
    */
    handlePromotions(allData){
        if(allData && allData.egoList && Array.isArray(allData.egoList)){
            allData.egoList.forEach(item => {
                if (item.photoUrl) {
                    item.bg = `background-image: url("${item.photoUrl}");`
                } else {
                    item.bg = `background-image: url("${this.defalutImgUrl}");`
                }
            })
        }
        if(allData && allData.skilList && Array.isArray(allData.skilList)){
            allData.skilList.forEach(item => {
                if (item.photoUrl) {
                    item.bg = `background-image: url("${item.photoUrl}");`
                } else {
                    item.bg = `background-image: url("${this.defalutImgUrl}");`
                }
            })
        }
        if(allData && allData.flexList && Array.isArray(allData.flexList)){
            allData.flexList.forEach(item => {
                if (item.photoUrl) {
                    item.bg = `background-image: url("${item.photoUrl}");`
                } else {
                    item.bg = `background-image: url("${this.defalutImgUrl}");`
                }
            })
        }
    }
}