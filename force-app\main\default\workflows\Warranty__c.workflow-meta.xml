<?xml version="1.0" encoding="UTF-8"?>
<Workflow xmlns="http://soap.sforce.com/2006/04/metadata">
    <alerts>
        <fullName>Austria_Confirmation</fullName>
        <description>Austria Confirmation</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_Austria</template>
    </alerts>
    <alerts>
        <fullName>Email_Completed_Warranty</fullName>
        <description>Email Completed Warranty</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>Hammerhead/Warranty_Confirmation_VF</template>
    </alerts>
    <alerts>
        <fullName>Flex_Warranty</fullName>
        <description>Flex Warranty</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>FLEX/Email_Template_for_FLEX_Warranty_Reg</template>
    </alerts>
    <alerts>
        <fullName>Germany_confirmation</fullName>
        <description>Germany confirmation</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_Germany</template>
    </alerts>
    <alerts>
        <fullName>Receipt_Received_EGO_Registration_Confirmation_Australia</fullName>
        <description>Receipt Received- EGO Registration Confirmation Australia</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Receipt_Received_EGO_Registration_Confirmation</template>
    </alerts>
    <alerts>
        <fullName>Receipt_Received_EGO_Registration_Confirmation_Europe</fullName>
        <description>Receipt Received- EGO Registration Confirmation Europe</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Receipt_Received_EGO_Registration_Confirmation</template>
    </alerts>
    <alerts>
        <fullName>Receipt_Received_EGO_Registration_Confirmation_US</fullName>
        <description>Receipt Received- EGO Registration Confirmation US EGO</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Receipt_Received_EGO_Registration_Confirmation_EGO2</template>
    </alerts>
    <alerts>
        <fullName>Receipt_Received_SkilSaw_Registration_Confirmation_US_SkilSaw</fullName>
        <description>Receipt Received- SkilSaw Registration Confirmation US SkilSaw</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>SkilSaw/Receipt_Received_Registration_Confirmation_SkilSaw</template>
    </alerts>
    <alerts>
        <fullName>Receipt_Received_Skil_Registration_Confirmation_US_Skil</fullName>
        <description>Receipt Received- Skil Registration Confirmation US Skil</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>Skil/Skil_Receipt_Received_Registration_Confirmation</template>
    </alerts>
    <alerts>
        <fullName>Receipt_Received_Unauthorized_Vendor_Warranty_Australia</fullName>
        <description>Receipt Received- Unauthorized Vendor Warranty Australia</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Receipt_Received_Unauthorized_Vendor_Warranty</template>
    </alerts>
    <alerts>
        <fullName>Receipt_Received_Unauthorized_Vendor_Warranty_Australia_Europe</fullName>
        <description>Receipt Received- Unauthorized Vendor Warranty Australia Europe</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Receipt_Received_Unauthorized_Vendor_Warranty</template>
    </alerts>
    <alerts>
        <fullName>Receipt_Received_Unauthorized_Vendor_Warranty_US</fullName>
        <description>Receipt Received- Unauthorized Vendor Warranty US EGO</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Receipt_Received_Unauthorized_Vendor_Warranty</template>
    </alerts>
    <alerts>
        <fullName>Receipt_Received_Unauthorized_Vendor_Warranty_US_Skil</fullName>
        <description>Receipt Received- Unauthorized Vendor Warranty US Skil</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>Skil/Skil_Receipt_Received_Unauthorized_Vendor_Warranty</template>
    </alerts>
    <alerts>
        <fullName>Receipt_Received_Unauthorized_Vendor_Warranty_US_SkilSaw</fullName>
        <description>Receipt Received- Unauthorized Vendor Warranty US SkilSaw</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>SkilSaw/Receipt_Received_Unauthorized_Vendor_Warranty_SkilSaw</template>
    </alerts>
    <alerts>
        <fullName>Resend_confirmation_Austria</fullName>
        <description>Resend confirmation Austria</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_Austria</template>
    </alerts>
    <alerts>
        <fullName>Resend_confirmation_Belgium</fullName>
        <description>Resend confirmation Belgium</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_Belgium</template>
    </alerts>
    <alerts>
        <fullName>Resend_confirmation_Finland</fullName>
        <description>Resend confirmation Finland</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_Finland</template>
    </alerts>
    <alerts>
        <fullName>Resend_confirmation_France</fullName>
        <description>Resend confirmation France</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_France</template>
    </alerts>
    <alerts>
        <fullName>Resend_confirmation_Germany</fullName>
        <description>Resend confirmation Germany</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_Germany</template>
    </alerts>
    <alerts>
        <fullName>Resend_confirmation_Ireland</fullName>
        <description>Resend confirmation Ireland</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_Ireland</template>
    </alerts>
    <alerts>
        <fullName>Resend_confirmation_Italy</fullName>
        <description>Resend confirmation Italy</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_Italy</template>
    </alerts>
    <alerts>
        <fullName>Resend_confirmation_Netherlands</fullName>
        <description>Resend confirmation Netherlands</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_NL</template>
    </alerts>
    <alerts>
        <fullName>Resend_confirmation_Spain</fullName>
        <description>Resend confirmation Spain</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_Spain</template>
    </alerts>
    <alerts>
        <fullName>Resend_confirmation_Switzerland</fullName>
        <description>Resend confirmation Switzerland</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_Switzerland</template>
    </alerts>
    <alerts>
        <fullName>Resend_confirmation_UK</fullName>
        <description>Resend confirmation UK</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_UK</template>
    </alerts>
    <alerts>
        <fullName>Switzerland_Confirmation</fullName>
        <description>Switzerland Confirmation</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Warranty_Completed_EGO_Switzerland</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Complete_Confirmation</fullName>
        <description>Warranty Complete Confirmation EGO</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/EGO_Warranty_Confirmation_VF</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Complete_Confirmation_Skil</fullName>
        <description>Warranty Complete Confirmation  Skil</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>Skil/Skil_Warranty_Comfirmation</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Complete_Confirmation_SkilSaw</fullName>
        <description>Warranty Complete Confirmation  SkilSaw</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>SkilSaw/SkilSaw_Warranty_Comfirmation</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Austria</fullName>
        <description>Warranty Confirmation Austria</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Austria</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Belgium</fullName>
        <description>Warranty Confirmation Belgium</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Belgium</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Belgium_fr</fullName>
        <description>Warranty Confirmation Belgium-French</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Belgium_fr</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Belgium_nl</fullName>
        <description>Warranty Confirmation Belgium-Dutch</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Belgium</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Finland</fullName>
        <description>Warranty Confirmation Finland</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Finland</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_France</fullName>
        <description>Warranty Confirmation France</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_France</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Germany</fullName>
        <description>Warranty Confirmation Germany</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Germany</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_HHOz</fullName>
        <description>Warranty Confirmation HHOz</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>Hammerhead/Warranty_Confirmation_HH_Oz</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Ireland</fullName>
        <description>Warranty Confirmation Ireland</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Ireland</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Italy</fullName>
        <description>Warranty Confirmation Italy</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Italy</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Netherlands</fullName>
        <description>Warranty Confirmation Netherlands</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_NL</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Spain</fullName>
        <description>Warranty Confirmation Spain</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Spain</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Switzerland</fullName>
        <description>Warranty Confirmation Switzerland</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Switzerland</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Switzerland_de</fullName>
        <description>Warranty Confirmation Switzerland-German</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Germany</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Switzerland_fr</fullName>
        <description>Warranty Confirmation Switzerland-French</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Switzerland_fr</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_Switzerland_it</fullName>
        <description>Warranty Confirmation Switzerland-Italian</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_Switzerland_it</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Confirmation_UK</fullName>
        <description>Warranty Confirmation UK</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_UK</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Registration_Email</fullName>
        <description>Warranty Registration Email EGO</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Registration_Email_AUS</fullName>
        <description>Warranty Registration Email (AUS)</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>EGO/Online_Confirmation_AUS</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Registration_Email_H</fullName>
        <description>Warranty Registration Email (H)</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>Hammerhead/Online_Confirmation_H</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Registration_Email_Skil</fullName>
        <description>Warranty Registration Email Skil</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>Skil/Skil_Warranty_Comfirmation</template>
    </alerts>
    <alerts>
        <fullName>Warranty_Registration_Email_SkilSaw</fullName>
        <description>Warranty Registration Email SkilSaw</description>
        <protected>false</protected>
        <recipients>
            <field>CustomerEmail__c</field>
            <type>email</type>
        </recipients>
        <senderAddress><EMAIL></senderAddress>
        <senderType>OrgWideEmailAddress</senderType>
        <template>SkilSaw/SkilSaw_Warranty_Comfirmation</template>
    </alerts>
    <fieldUpdates>
        <fullName>CheckUsedOneTime</fullName>
        <field>Used_One_Time_Exception__c</field>
        <literalValue>1</literalValue>
        <name>CheckUsedOneTime</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Check_One_Time_Exception</fullName>
        <field>One_Time_Exception__c</field>
        <literalValue>1</literalValue>
        <name>Check One Time Exception</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>UnCheckOneTime</fullName>
        <field>One_Time_Exception__c</field>
        <literalValue>0</literalValue>
        <name>UnCheckOneTime</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>UncheckPendingReceipt</fullName>
        <field>Pending__c</field>
        <literalValue>0</literalValue>
        <name>UncheckPendingReceipt</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>UpdateEmail</fullName>
        <field>CustomerEmail__c</field>
        <formula>AccountCustomer__r.PersonContact.Email</formula>
        <name>UpdateEmail</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Update_Resend_Check_Box</fullName>
        <field>Resend_Registration_Confirmation__c</field>
        <literalValue>0</literalValue>
        <name>Update Resend Check Box</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Warranty_Confirmation</fullName>
        <field>Send_confirmation__c</field>
        <literalValue>1</literalValue>
        <name>Warranty Confirmation</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <rules>
        <fullName>ChangeEmailAddress</fullName>
        <actions>
            <name>UpdateEmail</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Account.PersonEmail</field>
            <operation>notEqual</operation>
        </criteriaItems>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>One_Time_Exception</fullName>
        <actions>
            <name>Check_One_Time_Exception</name>
            <type>FieldUpdate</type>
        </actions>
        <active>false</active>
        <criteriaItems>
            <field>Warranty__c.Lost_Receipt__c</field>
            <operation>equals</operation>
            <value>True</value>
        </criteriaItems>
        <triggerType>onCreateOnly</triggerType>
    </rules>
    <rules>
        <fullName>Receipt Lost Australia</fullName>
        <actions>
            <name>Receipt_Received_Unauthorized_Vendor_Warranty_Australia</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND(  Invalid_Receipt__c ,TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Australia&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Receipt Lost Europe</fullName>
        <actions>
            <name>Receipt_Received_Unauthorized_Vendor_Warranty_Australia_Europe</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND(  Invalid_Receipt__c ,TEXT(AccountCustomer__r.Site_Origin__pc) != &apos;Australia&apos;,TEXT(AccountCustomer__r.Site_Origin__pc) != &apos;United States&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Receipt Lost US EGO</fullName>
        <actions>
            <name>Receipt_Received_Unauthorized_Vendor_Warranty_US</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( Invalid_Receipt__c ,TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;) )</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Receipt Lost US Skil</fullName>
        <actions>
            <name>Receipt_Received_Unauthorized_Vendor_Warranty_US_Skil</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( Invalid_Receipt__c ,TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;, AccountCustomer__r.Record_Type_Name__c = &apos;Skil_Customer&apos; )</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Receipt Lost US SkilSaw</fullName>
        <actions>
            <name>Receipt_Received_Unauthorized_Vendor_Warranty_US_SkilSaw</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( Invalid_Receipt__c ,TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;, AccountCustomer__r.Record_Type_Name__c = &apos;SkilSaw_Customer&apos; )</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Receipt Reviewed and Verified Australia</fullName>
        <actions>
            <name>Receipt_Received_EGO_Registration_Confirmation_Australia</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( Receipt_received_and_verified__c ,TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Australia&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Receipt Reviewed and Verified Europe</fullName>
        <actions>
            <name>Receipt_Received_EGO_Registration_Confirmation_Europe</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( Receipt_received_and_verified__c ,TEXT(AccountCustomer__r.Site_Origin__pc) != &apos;Australia&apos;,TEXT(AccountCustomer__r.Site_Origin__pc) != &apos;United States&apos;,AccountCustomer__r.Record_Type_Name__c = &apos;EGO_Customer&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Receipt Reviewed and Verified US EGO</fullName>
        <actions>
            <name>Receipt_Received_EGO_Registration_Confirmation_US</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( Receipt_received_and_verified__c ,TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;, TEXT( Brand_Name__c ) = &apos;EGO&apos; )</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Receipt Reviewed and Verified US Skil</fullName>
        <actions>
            <name>Receipt_Received_Skil_Registration_Confirmation_US_Skil</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( Receipt_received_and_verified__c ,TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;,  TEXT( Brand_Name__c ) = &apos;Skil&apos; )</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Receipt Reviewed and Verified US SkilSaw</fullName>
        <actions>
            <name>Receipt_Received_SkilSaw_Registration_Confirmation_US_SkilSaw</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( Receipt_received_and_verified__c ,TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;,  TEXT( Brand_Name__c ) = &apos;SkilSaw&apos; )</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration %28AUS%29</fullName>
        <actions>
            <name>Warranty_Registration_Email_AUS</name>
            <type>Alert</type>
        </actions>
        <active>false</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Australia&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration %28H%29</fullName>
        <actions>
            <name>Warranty_Registration_Email_H</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Hammerhead&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration Austria</fullName>
        <actions>
            <name>Resend_confirmation_Austria</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Austria&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration Belgium</fullName>
        <actions>
            <name>Resend_confirmation_Belgium</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Belgium&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration EGO</fullName>
        <actions>
            <name>Warranty_Registration_Email</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Update_Resend_Check_Box</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, AccountCustomer__r.Record_Type_Name__c = &apos;EGO_Customer&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration Finland</fullName>
        <actions>
            <name>Resend_confirmation_Finland</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Finland&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration France</fullName>
        <actions>
            <name>Resend_confirmation_France</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;France&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration Germany</fullName>
        <actions>
            <name>Resend_confirmation_Germany</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Germany&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration Ireland</fullName>
        <actions>
            <name>Resend_confirmation_Ireland</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Ireland&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration Italy</fullName>
        <actions>
            <name>Resend_confirmation_Italy</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Italy&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration Netherlands</fullName>
        <actions>
            <name>Resend_confirmation_Netherlands</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Netherlands&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration Skil</fullName>
        <actions>
            <name>Warranty_Registration_Email_Skil</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Update_Resend_Check_Box</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c,  INCLUDES( AccountCustomer__r.Product_Type__c , &apos;Skil&apos;))</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration SkilSaw</fullName>
        <actions>
            <name>Warranty_Registration_Email_SkilSaw</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Update_Resend_Check_Box</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c,  INCLUDES( AccountCustomer__r.Product_Type__c , &apos;SkilSaw&apos;))</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration Spain</fullName>
        <actions>
            <name>Resend_confirmation_Spain</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__pc )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Spain&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration Switzerland</fullName>
        <actions>
            <name>Resend_confirmation_Switzerland</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Switzerland&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Resend Warranty Registration UK</fullName>
        <actions>
            <name>Resend_confirmation_UK</name>
            <type>Alert</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK( AccountCustomer__r.Shopify_ID__pc )), Resend_Registration_Confirmation__c, TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United Kingdom&apos;)</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>TempTestWorkFlow</fullName>
        <actions>
            <name>Warranty_Complete_Confirmation</name>
            <type>Alert</type>
        </actions>
        <active>false</active>
        <formula>AccountCustomer__r.PersonEmail =&apos;<EMAIL>&apos;</formula>
        <triggerType>onCreateOnly</triggerType>
    </rules>
    <rules>
        <fullName>UpdateReceiptStatus</fullName>
        <actions>
            <name>UncheckPendingReceipt</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <booleanFilter>1 AND (2 OR 3 OR 4)</booleanFilter>
        <criteriaItems>
            <field>Warranty__c.Pending__c</field>
            <operation>equals</operation>
            <value>True</value>
        </criteriaItems>
        <criteriaItems>
            <field>Warranty__c.Receipt_received_and_verified__c</field>
            <operation>equals</operation>
            <value>True</value>
        </criteriaItems>
        <criteriaItems>
            <field>Warranty__c.Lost_Receipt__c</field>
            <operation>equals</operation>
            <value>True</value>
        </criteriaItems>
        <criteriaItems>
            <field>Warranty__c.Invalid_Receipt__c</field>
            <operation>equals</operation>
            <value>True</value>
        </criteriaItems>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>UsedOneTimeException</fullName>
        <actions>
            <name>CheckUsedOneTime</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>UnCheckOneTime</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Warranty__c.One_Time_Exception__c</field>
            <operation>equals</operation>
            <value>True</value>
        </criteriaItems>
        <criteriaItems>
            <field>Warranty__c.Order_Times__c</field>
            <operation>greaterThan</operation>
            <value>0</value>
        </criteriaItems>
        <criteriaItems>
            <field>Warranty__c.ClosedCase__c</field>
            <operation>equals</operation>
            <value>True</value>
        </criteriaItems>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Completed</fullName>
        <active>false</active>
        <formula>AND(AccountCustomer__r.RecordType.Name =&apos;EGO Customer&apos;,
TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;)</formula>
        <triggerType>onCreateOnly</triggerType>
        <workflowTimeTriggers>
            <actions>
                <name>Warranty_Complete_Confirmation</name>
                <type>Alert</type>
            </actions>
            <actions>
                <name>Warranty_Confirmation</name>
                <type>FieldUpdate</type>
            </actions>
            <offsetFromField>Warranty__c.SendRegistrationConfirmEmail__c</offsetFromField>
            <timeLength>1</timeLength>
            <workflowTimeTriggerUnit>Hours</workflowTimeTriggerUnit>
        </workflowTimeTriggers>
    </rules>
    <rules>
        <fullName>Warranty Completed EGO%28US%29</fullName>
        <actions>
            <name>Warranty_Complete_Confirmation</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;,   INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;), TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;, Items_Inserted__c, !Send_confirmation__c, TEXT(Brand_Name__c) = &apos;EGO&apos;)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Completed FLEX%28US%29</fullName>
        <actions>
            <name>Flex_Warranty</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( INCLUDES( AccountCustomer__r.Product_Type__c , &apos;FLEX&apos;),  TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;,  Items_Inserted__c,!Send_confirmation__c,TEXT(Brand_Name__c) = &apos;FLEX&apos;)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Completed Skil%28US%29</fullName>
        <actions>
            <name>Warranty_Complete_Confirmation_Skil</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>false</active>
        <formula>AND( INCLUDES( AccountCustomer__r.Product_Type__c , &apos;Skil&apos;), 
TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;, 
Items_Inserted__c,!Send_confirmation__c,TEXT(Brand_Name__c) = &apos;Skil&apos;)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Completed SkilSaw%28US%29</fullName>
        <actions>
            <name>Warranty_Complete_Confirmation_SkilSaw</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( INCLUDES( AccountCustomer__r.Product_Type__c , &apos;SkilSaw&apos;), 
TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;, 
Items_Inserted__c,!Send_confirmation__c,TEXT(Brand_Name__c) = &apos;SkilSaw&apos;)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Completed_HammerHead</fullName>
        <active>false</active>
        <formula>AccountCustomer__r.RecordType.Name =&apos;Hammerhead Customer&apos;</formula>
        <triggerType>onCreateOnly</triggerType>
        <workflowTimeTriggers>
            <actions>
                <name>Warranty_Confirmation</name>
                <type>FieldUpdate</type>
            </actions>
            <offsetFromField>Warranty__c.SendRegistrationConfirmEmail__c</offsetFromField>
            <timeLength>1</timeLength>
            <workflowTimeTriggerUnit>Hours</workflowTimeTriggerUnit>
        </workflowTimeTriggers>
    </rules>
    <rules>
        <fullName>Warranty Completed_HammerHead New</fullName>
        <actions>
            <name>Email_Completed_Warranty</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( INCLUDES( AccountCustomer__r.Product_Type__c , &apos;Hammerhead&apos;),
TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Hammerhead&apos;,
Items_Inserted__c,!Send_confirmation__c)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration</fullName>
        <actions>
            <name>Warranty_Registration_Email</name>
            <type>Alert</type>
        </actions>
        <active>false</active>
        <formula>AND(NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )), TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;United States&apos;)</formula>
        <triggerType>onCreateOnly</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration %28AUS%29</fullName>
        <actions>
            <name>Warranty_Registration_Email_AUS</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>false</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;), OR(TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Australia&apos;,  TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;New Zealand&apos;), Items_Inserted__c, !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration %28H%29</fullName>
        <actions>
            <name>Warranty_Registration_Email_H</name>
            <type>Alert</type>
        </actions>
        <active>false</active>
        <formula>AND(NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )),  TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Hammerhead&apos;)</formula>
        <triggerType>onCreateOnly</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration %28HHOz%29</fullName>
        <actions>
            <name>Warranty_Confirmation_HHOz</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND(NOT(ISBLANK( AccountCustomer__r.Shopify_ID__c )),  TEXT(AccountCustomer__r.Site_Origin__pc) = &apos;Hammerhead Australia&apos;,
Items_Inserted__c,!Send_confirmation__c)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Austria</fullName>
        <actions>
            <name>Warranty_Confirmation_Austria</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),     TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Austria&apos;,      Items_Inserted__c,      !Send_confirmation__c  )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Belgium</fullName>
        <actions>
            <name>Warranty_Confirmation_Belgium</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;), TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Belgium&apos;,      Items_Inserted__c,      !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Belgium-Dutch</fullName>
        <actions>
            <name>Warranty_Confirmation_Belgium_nl</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),       TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Belgium-Dutch&apos;,      Items_Inserted__c,      !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Belgium-French</fullName>
        <actions>
            <name>Warranty_Confirmation_Belgium_fr</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),      TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Belgium-French&apos;,      Items_Inserted__c,      !Send_confirmation__c  )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Finland</fullName>
        <actions>
            <name>Warranty_Confirmation_Finland</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),   TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Finland&apos;,      Items_Inserted__c,      !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration France</fullName>
        <actions>
            <name>Warranty_Confirmation_France</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND(      TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;France&apos;,      Items_Inserted__c,      !Send_confirmation__c  )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Germany</fullName>
        <actions>
            <name>Warranty_Confirmation_Germany</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),     TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Germany&apos;,      Items_Inserted__c,      !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Ireland</fullName>
        <actions>
            <name>Warranty_Confirmation_Ireland</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND(AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),  TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Ireland&apos;,      Items_Inserted__c, !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Italy</fullName>
        <actions>
            <name>Warranty_Confirmation_Italy</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),     TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Italy&apos;,      Items_Inserted__c,      !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Netherlands</fullName>
        <actions>
            <name>Warranty_Confirmation_Netherlands</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND(AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),       TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Netherlands&apos;,      Items_Inserted__c,      !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Spain</fullName>
        <actions>
            <name>Warranty_Confirmation_Spain</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),      TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Spain&apos;,      Items_Inserted__c,      !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Switzerland</fullName>
        <actions>
            <name>Warranty_Confirmation_Switzerland</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND(  AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),     TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Switzerland&apos;,      Items_Inserted__c,      !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Switzerland-French</fullName>
        <actions>
            <name>Warranty_Confirmation_Switzerland_fr</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),     TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Switzerland-French&apos;,      Items_Inserted__c,      !Send_confirmation__c  )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Switzerland-German</fullName>
        <actions>
            <name>Warranty_Confirmation_Switzerland_de</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),      TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Switzerland-German&apos;,      Items_Inserted__c,      !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration Switzerland-Italian</fullName>
        <actions>
            <name>Warranty_Confirmation_Switzerland_it</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND(AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;, INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),      TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;Switzerland-Italian&apos;,      Items_Inserted__c,      !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Warranty Registration UK</fullName>
        <actions>
            <name>Warranty_Confirmation_UK</name>
            <type>Alert</type>
        </actions>
        <actions>
            <name>Warranty_Confirmation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND(   AccountCustomer__r.RecordType.DeveloperName =&apos;PersonAccount&apos;,   INCLUDES(AccountCustomer__r.Product_Type__c, &apos;EGO&apos;),    TEXT( AccountCustomer__r.Site_Origin__pc ) = &apos;United Kingdom&apos;,      Items_Inserted__c,      !Send_confirmation__c )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
</Workflow>
