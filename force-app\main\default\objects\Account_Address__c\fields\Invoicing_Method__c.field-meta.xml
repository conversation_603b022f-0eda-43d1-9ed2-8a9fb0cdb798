<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
<<<<<<<< HEAD:force-app/main/default/objects/Account/fields/JSS_TR__c.field-meta.xml
    <fullName>JSS_TR__c</fullName>
    <deleteConstraint>SetNull</deleteConstraint>
    <externalId>false</externalId>
    <label>JSS/TR</label>
    <referenceTo>Territory_User__c</referenceTo>
    <relationshipLabel>Customers</relationshipLabel>
    <relationshipName>Customers</relationshipName>
    <required>false</required>
    <trackFeedHistory>false</trackFeedHistory>
    <trackHistory>false</trackHistory>
    <type>Lookup</type>
========
    <fullName>Invoicing_Method__c</fullName>
    <externalId>false</externalId>
    <formula>IF( ISBLANK( Customer__c ) ,  TEXT(Prospect__r.Invoicing_Method__c) ,   TEXT(Customer__r.Invoicing_Method__c))</formula>
    <label>Invoicing Method</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Text</type>
    <unique>false</unique>
>>>>>>>> ReverseOrderListUpdate:force-app/main/default/objects/Account_Address__c/fields/Invoicing_Method__c.field-meta.xml
</CustomField>
