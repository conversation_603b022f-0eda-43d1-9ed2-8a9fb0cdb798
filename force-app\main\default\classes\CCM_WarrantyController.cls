/**
About
-----
Description: This Class is used for create/edit warranty in lightning .

Created for: Chervon classic to lightning
Created: 06 05 2019

Update History
--------------
Created: 06 11 2019 – <EMAIL>
-------------
**/
public class CCM_WarrantyController {

    @AuraEnabled public static Warranty__c thisWarranty {get; set;}
    @AuraEnabled public static List<Product3> proList {get; set;}
    @AuraEnabled public static Boolean withinStorePolicy {set;get;}

    public static WarrantyService warrService;
    public static Id masterProductOld;
    public static Id lastMasterProduct;
    public static List<Warranty_Item__c> warrantyItemList;//用来存储修改masterproduct之前的明细

    //Add bullet 存储当前brand name
    //public static String warrantyBrandName {get;set;}
    private static String imgID;
    private static Integer CHANGESNDATE = 1911;



    @AuraEnabled
    public static String init(String accId, String caseId, String warrantyId) {
        warrService       = new WarrantyService();
        proList           = new List<Product3>();
        warrantyItemList  = new List<Warranty_Item__c>();

        //Distinguish between create and edit page
        String returnResult;
        Account customer;
        if (warrantyId == NULL || warrantyId == '') {
            //create page
            thisWarranty = new Warranty__c();

            if (accId != null && accId != '') {
                customer = AccountService.getAccountByID(accId);
            } else if (caseId != null && caseId != '') {
                Case cas = [SELECT Id, AccountId FROM Case WHERE Id = :caseId];
                customer = AccountService.getAccountByID(cas.AccountId);
            }
            returnResult = JSON.serialize(customer);
            thisWarranty.AccountCustomer__r = customer;
        } else {

            thisWarranty   = WarrantyService.getWarrantyByID(warrantyId);

            //获取MasterProduct的产品Code
            if (thisWarranty.Master_Product__c != null){
                masterProductOld  = thisWarranty.Master_Product__c;
                //lastMasterProduct = initWarranty.Master_Product__c;
            }


            //初始化产品列表
            for (Warranty_Item__c warrantyItem : thisWarranty.Warranty_Items__r){
                warrantyItem.Product_Type__c    = warrantyItem.Product__r.Type__c;
                warrantyItem.Product_Code__c    = warrantyItem.Product__r.ProductCode;
                warrantyItem.Product_Model__c   = warrantyItem.Product__r.ProductModel__c;
                warrantyItem.Product_Name__c    = warrantyItem.Product__r.Name;
                if(warrantyItem.ActualIndicator__c != null){
                     warrantyItem.Indicator__c  = warrantyItem.ActualIndicator__c;
                }else {
                    warrantyItem.Indicator__c  = null;
                }

                if(warrantyItem.Expiration_Date_New__c != null){
                    warrantyItem.Expiration_Date_New__c = warrantyItem.Expiration_Date_New__c;
                }else{
                    warrantyItem.Expiration_Date_New__c = warrantyItem.Expiration_Date__c;
                }
                //需要将过期日期公式字段赋值给过期日期展示字段
                proList.add(new Product3(warrantyItem));
                warrantyItemList.add(warrantyItem);
            }
            warrService.warrantyItemList    = thisWarranty.Warranty_Items__r;
            warrService.warrantyItemOldList = thisWarranty.Warranty_Items__r;

            returnResult = JSON.serialize(new WarrantyItemWrapper('', proList));
        }

        return returnResult;
    }


    @AuraEnabled
    public static Boolean isCustomerVisible(){
        Boolean isCustomerVisible = true;
        List<Profile> profiles = [SELECT Name FROM Profile WHERE Id = :UserInfo.getProfileId()];
        if(!profiles.isEmpty()) {
            if(profiles[0].Name == 'Engineer Profile') {
                isCustomerVisible = false;
            }
        }
        return isCustomerVisible;
    }


    @AuraEnabled
    public static Boolean isCustomerBrand(String accId, String brand){

        Account acc = [SELECT Product_Type__c FROM Account WHERE Id =: accId LIMIT 1];
        if (String.isNotEmpty(acc.Product_Type__c)) {
                if(acc.Product_Type__c.contains(brand)){
                return true;
            }
            if (acc.Product_Type__c.contains('Skil')) {
                if (brand == 'SkilSaw') {
                    return true;
                }
            }
        }
        return false;
    }

    //变更保修时间
    /*@AuraEnabled
    public static String changeMasterProduct(String productId, String warrantyId) {

       if (productId == null || productId == '') {
            Map<String,String> jsonMap = new Map<String,String>();
            jsonMap.put('Message', 'Product Id can not be null');
            return Json.serialize(jsonMap);
        }

        warrService = new WarrantyService();
        warrService.warrantyItemList = new List<Warranty_Item__c>();

        Warranty__c innerWarranty;
        if (warrantyId <> null && warrantyId <> '') {
            innerWarranty = WarrantyService.getWarrantyByID(warrantyId);
            if (innerWarranty <> null) {
                masterProductOld = innerWarranty.Master_Product__c;
            }

        }

        proList = new List<Product3>();
        Product2 masterProduct = ProductService.getProductKitByID(productId);
        //判断masterproduct是否发生变更，若没有，继续采用之前生成的warranty items；若有，则根据
        if (masterProduct.Id == masterProductOld && innerWarranty.Warranty_Items__r.size() > 0){
            system.debug('warrantyItemList:'+innerWarranty.Warranty_Items__r);
            for(Warranty_Item__c warrantyItem : innerWarranty.Warranty_Items__r){
                proList.add(new Product3(warrantyItem));
                warrService.warrantyItemList.add(warrantyItem);
            }
        } else{
            //kit下所有item的信息
            for (Kit_Item__c kitItem : masterProduct.Kit_Items__r){
                Warranty_Item__c warrantyItem = new Warranty_Item__c();
                warrantyItem.Product__c = kitItem.Product__c;
                warrantyItem.Warranty__c = innerWarranty == null ? null : innerWarranty.Id;
                warrantyItem.Product_Code__c  = kitItem.Product__r.ProductCode;
                warrantyItem.Product_Type__c  = kitItem.Product__r.Type__c;
                warrantyItem.Product_Model__c = kitItem.Product__r.ProductModel__c;
                warrantyItem.Product_Name__c  = kitItem.Product__r.Name;
                proList.add(new Product3(warrantyItem));
                system.debug('warrantyItem==>'+warrantyItem);
                warrService.warrantyItemList.add(warrantyItem);
            }
        }


        if (innerWarranty <> null) {
            //修改保修时间
            changeExpirationDate(innerWarranty, warrService.warrantyItemList);
        }

        //修改维修金额与建议
        countCost();

        return JSON.serialize(new WarrantyItemWrapper(masterProduct.ProductCode, proList));
    }*/
    @AuraEnabled
    public static String changeMasterProduct(String productId, String warrantyId) {

       if (productId == null || productId == '') {
            Map<String,String> jsonMap = new Map<String,String>();
            jsonMap.put('Message', 'Product Id can not be null');
            return Json.serialize(jsonMap);
        }

        warrService = new WarrantyService();
        warrService.warrantyItemList = new List<Warranty_Item__c>();

        Warranty__c innerWarranty;
        if (warrantyId <> null && warrantyId <> '') {
            innerWarranty = WarrantyService.getWarrantyByID(warrantyId);
            if (innerWarranty <> null) {
                masterProductOld = innerWarranty.Master_Product__c;
            }

        }

        proList = new List<Product3>();
        Product2 masterProduct = ProductService.getProductKitByID2(productId);

        //joshua
        List<Kit_Item__c> hasReplacelist = new List<Kit_Item__c>();
        List<String> replaceStrlist = new List<String>();
        Map<String,Kit_Item__c> kiMap = new Map<String,Kit_Item__c>();
        for(Kit_Item__c kitItem : masterProduct.Kit_Items__r){
            if(String.isNotBlank(kitItem.Sequence__c)){
                hasReplacelist.add(kitItem);
                replaceStrlist.add(kitItem.Sequence__c);
            }
            kiMap.put(kitItem.Product__c, kitItem);
        }

        Map<String,List<Kit_Item__c>> replaceMap = new Map<String,List<Kit_Item__c>>();
        List<Kit_Item__c> replacelist = [select id,Product__c,Product__r.ProductCode,
                                         Product__r.Type__c,Product__r.Name,
                                         Product__r.ProductModel__c,Sequence__c
                                         from Kit_Item__c
                                         where Sequence__c in :replaceStrlist
                                         and kit__c = :productId];
        for(Kit_Item__c ki : replacelist){
            if(replaceMap.containsKey(ki.Sequence__c)){
                replaceMap.get(ki.Sequence__c).add(ki);
            }else{
                List<Kit_Item__c> kilist2 = new List<Kit_Item__c>();
                kilist2.add(ki);
                replaceMap.put(ki.Sequence__c, kilist2);
            }
        }

        //判断masterproduct是否发生变更，若没有，继续采用之前生成的warranty items；若有，则根据
        if (masterProduct.Id == masterProductOld && innerWarranty.Warranty_Items__r.size() > 0){
            system.debug('warrantyItemList:'+innerWarranty.Warranty_Items__r);


            for(Warranty_Item__c warrantyItem : innerWarranty.Warranty_Items__r){
                Product3 pro3 = new Product3(warrantyItem);
                if(kiMap.get(warrantyItem.Product__c) != null){
                    Kit_Item__c ki = kiMap.get(warrantyItem.Product__c);
                    if(String.isNotBlank(ki.Sequence__c) && replaceMap.get(ki.Sequence__c) != null){
                        List<comboBoxOptions> codelist = new List<comboBoxOptions>();
                        System.debug(LoggingLevel.INFO, '*** replaceMap.get(ki.Sequence__c): ' + replaceMap.get(ki.Sequence__c));
                        for(Kit_Item__c ki2 : replaceMap.get(ki.Sequence__c)){
                            comboBoxOptions cbo = new comboBoxOptions();
                            cbo.label = ki2.Product__r.ProductCode;
                            cbo.value = /*kitItem.Id*/ki2.Product__r.ProductCode;
                            codelist.add(cbo);
                            // .add(ki.Id);

                            if(ki2.Product__c != warrantyItem.Product__c){
                                Warranty_Item__c wi = new Warranty_Item__c();
                                wi.Id = warrantyItem.Id;
                                wi.Product__c = ki2.Product__c;
                                //wi.Warranty__c = innerWarranty == null ? null : innerWarranty.Id;
                                wi.Product_Code__c  = ki2.Product__r.ProductCode;
                                wi.Product_Type__c  = ki2.Product__r.Type__c;
                                wi.Product_Model__c = ki2.Product__r.ProductModel__c;
                                wi.Product_Name__c  = ki2.Product__r.Name;
                                wi.Historical_SN__c = warrantyItem.Historical_SN__c;
                                pro3.replaceProMap.put(/*kitItem.Id*/ki2.Product__r.ProductCode, wi);
                            }else{
                                pro3.replaceProMap.put(/*kitItem.Id*/ki2.Product__r.ProductCode, warrantyItem);
                            }

                        }

                        if(codelist.size() > 1){
                            pro3.hasReplace = true;
                        }
                        pro3.replaceCodeList = codelist;
                    }
                }
                proList.add(pro3);
                warrService.warrantyItemList.add(warrantyItem);
            }
        } else{
            //kit下所有item的信息
            Set<String> productCodeSet = new Set<String>();
            Set<String> productSequenceSet = new Set<String>();
            for (Kit_Item__c kitItem : masterProduct.Kit_Items__r){
                Warranty_Item__c warrantyItem = new Warranty_Item__c();
                warrantyItem.Product__c = kitItem.Product__c;
                warrantyItem.Warranty__c = innerWarranty == null ? null : innerWarranty.Id;
                warrantyItem.Product_Code__c  = kitItem.Product__r.ProductCode;
                warrantyItem.Product_Type__c  = kitItem.Product__r.Type__c;
                warrantyItem.Product_Model__c = kitItem.Product__r.ProductModel__c;
                warrantyItem.Product_Name__c  = kitItem.Product__r.Name;
                Product3 pro3 = new Product3(warrantyItem);
                if(String.isNotBlank(kitItem.Sequence__c) && replaceMap.get(kitItem.Sequence__c) != null){
                    List<comboBoxOptions> codelist = new List<comboBoxOptions>();
                    for(Kit_Item__c ki2 : replaceMap.get(kitItem.Sequence__c)){
                        if(!productCodeSet.contains(kitItem.Id)){
                            comboBoxOptions cbo = new comboBoxOptions();
                            cbo.label = kitItem.Product__r.ProductCode;
                            cbo.value = /*kitItem.Id*/kitItem.Product__r.ProductCode;
                            codelist.add(cbo);
                            productCodeSet.add(kitItem.Id);

                            Warranty_Item__c wi = new Warranty_Item__c();
                            wi.Product__c = kitItem.Product__c;
                            wi.Warranty__c = innerWarranty == null ? null : innerWarranty.Id;
                            wi.Product_Code__c  = kitItem.Product__r.ProductCode;
                            wi.Product_Type__c  = kitItem.Product__r.Type__c;
                            wi.Product_Model__c = kitItem.Product__r.ProductModel__c;
                            wi.Product_Name__c  = kitItem.Product__r.Name;
                            pro3.replaceProMap.put(/*kitItem.Id*/kitItem.Product__r.ProductCode, wi);
                        }
                        if(!productCodeSet.contains(ki2.Id)){
                            comboBoxOptions cbo = new comboBoxOptions();
                            cbo.label = ki2.Product__r.ProductCode;
                            cbo.value = /*ki2.Id*/ki2.Product__r.ProductCode;
                            codelist.add(cbo);
                            productCodeSet.add(ki2.Id);

                            Warranty_Item__c wi = new Warranty_Item__c();
                            wi.Product__c = ki2.Product__c;
                            wi.Warranty__c = innerWarranty == null ? null : innerWarranty.Id;
                            wi.Product_Code__c  = ki2.Product__r.ProductCode;
                            wi.Product_Type__c  = ki2.Product__r.Type__c;
                            wi.Product_Model__c = ki2.Product__r.ProductModel__c;
                            wi.Product_Name__c  = ki2.Product__r.Name;
                            pro3.replaceProMap.put(/*ki2.Id*/ki2.Product__r.ProductCode, wi);
                        }
                    }
                    if(codelist.size() > 1){
                        pro3.hasReplace = true;
                    }
                    pro3.replaceCodeList = codelist;
                }

                if(!productSequenceSet.contains(kitItem.Sequence__c)){
                    proList.add(pro3);
                    if(String.isNotBlank(kitItem.Sequence__c)){
                        productSequenceSet.add(kitItem.Sequence__c);
                    }
                }
                system.debug('warrantyItem==>'+warrantyItem);
                warrService.warrantyItemList.add(warrantyItem);
            }
        }
        System.debug(LoggingLevel.INFO, '*** Json.serialize(proList): ' + Json.serialize(proList));

        if (innerWarranty <> null) {
            //修改保修时间
            changeExpirationDate(innerWarranty, warrService.warrantyItemList);
        }

        //修改维修金额与建议
        countCost();

        return JSON.serialize(new WarrantyItemWrapper(masterProduct.ProductCode, proList));
    }

    //Check Serial Number format and update Indicator
    @AuraEnabled
    public static String checkSNAndUpdateIndicator(String productId, String purchaseDate, String useType, String placeofPurchasePicklist, String warrantyBrandName, String proListStr, Boolean isEdit) {
        if (productId == null || productId == '') {
            return 'Master Product ID Can not be null';
        }
        Boolean boolMatch;
        Warranty__c warr = new Warranty__c();
        warr.Master_Product__c             = productId;
        warr.Purchase_Date__c              = Date.newInstance(Integer.valueOf(purchaseDate.split('-')[0]), Integer.valueOf(purchaseDate.split('-')[1]), Integer.valueOf(purchaseDate.split('-')[2]));
        warr.Product_Use_Type2__c          = useType;
        warr.Place_of_Purchase_picklist__c = placeofPurchasePicklist;
        warr.Brand_Name__c                 = warrantyBrandName;

        //所有warranty item
        List<Warranty_Item__c> items = new List<Warranty_Item__c>();
        WarrantyItemWrapper product3s = (WarrantyItemWrapper)JSON.deserialize(proListStr, WarrantyItemWrapper.class);
        Map<String,Warranty_Rules__c> codeMapToWR = new Map<String,Warranty_Rules__c>();
        for(Warranty_Rules__c wr : [SELECT Code_in_Serial__c,NA_FC_Model__c,NA_Model__c FROM  Warranty_Rules__c WHERE RecordType.Name = 'Model# - Code']){
            // codeMapToWR.put(wr.Code_in_Serial__c, wr);
            if(String.isNotBlank(wr.NA_FC_Model__c)) {
                codeMapToWR.put(wr.NA_FC_Model__c, wr);
            }
            if(String.isNotBlank(wr.NA_Model__c)) {
                codeMapToWR.put(wr.NA_Model__c, wr);
            }
        }
        Integer intSerialNumberSum;
        String strSerialNumber;
        Set<Id> setEuropeanProduct = getEuropeanProduct(product3s.proList);
        for (Product3 pro3 : product3s.proList) {
            pro3.isFormatCorrect      = true;
            pro3.snFormatErrorMessage = '';
            items.add(pro3.warrantyItem);

            if (!setEuropeanProduct.contains(pro3.warrantyItem?.Product__c) && !String.isBlank(pro3.warrantyItem.Serial_Number__c) && pro3.isSelect) {
                if(warrantyBrandName != null) {
                    system.debug('pro3.warrantyItem.Serial_Number__c.length()='+pro3.warrantyItem.Serial_Number__c.length());
                    if(warrantyBrandName.equalsIgnoreCase('Skil')) {
                        //如果warrantyitem的 新老没有值  默认是new的
                        if(pro3.warrantyItem.Serial_Number__c.length() != 9 && pro3.warrantyItem.Serial_Number__c.length() != 3){
                          pro3.snFormatErrorMessage = 'The length of the SN should be 3 or 9 characters.';
                          pro3.isFormatCorrect      = false;
                          continue;
                        }
                    } else if(warrantyBrandName.equalsIgnoreCase('SkilSaw')) {
                        if(pro3.warrantyItem.Serial_Number__c.length() != 9) {
                          pro3.snFormatErrorMessage = 'The length of the SN should be 9 characters.';
                          pro3.isFormatCorrect      = false;
                          continue;
                        }
                    } else if (warrantyBrandName.equalsIgnoreCase('Hammerhead')) {
                        if (pro3.warrantyItem.Serial_Number__c?.length() != 9 && pro3.warrantyItem.Serial_Number__c?.length() != 3) {
                            pro3.snFormatErrorMessage = 'The length of the SN should be 3 or 9 characters.';
                            pro3.isFormatCorrect = false;
                            continue;
                        }
                    } else if(warrantyBrandName.equalsIgnoreCase('EGO')) {
                        if(pro3.warrantyItem.Serial_Number__c.length() != 10 && pro3.warrantyItem.Serial_Number__c.length() != 14 &&
                          pro3.warrantyItem.Serial_Number__c.length() != 15 && pro3.warrantyItem.Serial_Number__c.length() != 16) {
                          pro3.snFormatErrorMessage = 'The length of the SN should be 10 or 14 or 15 or 16 characters.';
                          pro3.isFormatCorrect      = false;
                          continue;
                        }

                        if(pro3.warrantyItem.Serial_Number__c.length() == 14 ||
                            pro3.warrantyItem.Serial_Number__c.length() == 15) {
                            if(pro3.warrantyItem.Serial_Number__c.startsWith('R')) {
                                pro3.snFormatErrorMessage = 'The length of the SN should be 16 characters.';
                            }
                        }

                        if((pro3.warrantyItem.Serial_Number__c.length() == 14 ||
                          pro3.warrantyItem.Serial_Number__c.length() == 15
                          ) && !pro3.warrantyItem.IsReplacement__c){
                            Date manufactureDate = null;
                            // 区分情况
                            if(pro3.warrantyItem.Serial_Number__c.startsWith('R')){
                                if(Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(6,10)) <= CHANGESNDATE ){
                                    manufactureDate = Date.newInstance(Integer.valueOf('20'+pro3.warrantyItem.Serial_Number__c.subString(6,8)), Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(8,10)), 28);
                                }else{
                                    Integer weekToMonth = (Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(8,10))/4);
                                    manufactureDate = Date.newInstance(Integer.valueOf('20'+pro3.warrantyItem.Serial_Number__c.subString(6,8)),weekToMonth, 28);
                                }
                            }else{
                                if(Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(5,9)) <= CHANGESNDATE ){
                                    manufactureDate = Date.newInstance(Integer.valueOf('20'+pro3.warrantyItem.Serial_Number__c.subString(5,7)), Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(7,9)), 28);
                                }else{
                                    Integer weekToMonth = (Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(7,9))/4);
                                    manufactureDate = Date.newInstance(Integer.valueOf('20'+pro3.warrantyItem.Serial_Number__c.subString(5,7)),weekToMonth, 28);
                                }
                            }
                            

                            system.debug(manufactureDate);
                            system.debug(warr.Purchase_Date__c);
                            if(manufactureDate > warr.Purchase_Date__c){
                                pro3.snFormatErrorMessage = 'Purchase Date CANNOT be earlier than Manufactured Date.';
                                pro3.isFormatCorrect      = false;
                                continue;
                            }
                        }

                        if(pro3.warrantyItem.Serial_Number__c.length() == 14 ||
                          pro3.warrantyItem.Serial_Number__c.length() == 15){
                            String productCodeInSN = '';
                            if(pro3.warrantyItem.Serial_Number__c.startsWith('R')){
                                productCodeInSN = pro3.warrantyItem.Serial_Number__c.subString(2,6).toUpperCase();
                            }else{
                                productCodeInSN = pro3.warrantyItem.Serial_Number__c.subString(1,5).toUpperCase();
                            }
                            
                            if(codeMapToWR.containsKey(pro3.warrantyItem.Product_Code__c)) {
                                List<String> serialCodeList = new List<String>();
                                if(String.isNotBlank(codeMapToWR.get(pro3.warrantyItem.Product_Code__c).Code_in_Serial__c)){
                                    serialCodeList.addAll(codeMapToWR.get(pro3.warrantyItem.Product_Code__c).Code_in_Serial__c.split(';'));
                                }
                                if(!serialCodeList.contains(productCodeInSN)) {
                                    pro3.snFormatErrorMessage = 'Warning: Entered SN may not match the product model, please double confirm.';
                                }
                            }

                            // if(codeMapToWR.get(productCodeInSN) != null &&
                            //     pro3.warrantyItem.Product_Code__c != codeMapToWR.get(productCodeInSN).NA_FC_Model__c &&
                            //     pro3.warrantyItem.Product_Code__c != codeMapToWR.get(productCodeInSN).NA_Model__c){

                            //     pro3.snFormatErrorMessage = 'Warning: Entered SN may not match the product model, please double confirm.';
                            // }

                        }else if(pro3.warrantyItem.Serial_Number__c.length() == 16){

                            if(isV5OrFC2(pro3.warrantyItem.Serial_Number__c)) {

                                Date manufactureDate = null;
                                if(Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(5,9)) <= CHANGESNDATE ){
                                    manufactureDate = Date.newInstance(Integer.valueOf('20'+pro3.warrantyItem.Serial_Number__c.subString(5,7)), Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(7,9)), 28);
                                }else{
                                    Integer weekToMonth = (Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(7,9))/4);
                                    manufactureDate = Date.newInstance(Integer.valueOf('20'+pro3.warrantyItem.Serial_Number__c.subString(5,7)),weekToMonth, 28);
                                }

                                if(manufactureDate > warr.Purchase_Date__c && !pro3.warrantyItem.IsReplacement__c){
                                    pro3.snFormatErrorMessage = 'Purchase Date CANNOT be earlier than Manufactured Date.';
                                    pro3.isFormatCorrect      = false;
                                    continue;
                                }

                                String productCodeInSN = pro3.warrantyItem.Serial_Number__c.subString(1,5).toUpperCase();
                                if(codeMapToWR.containsKey(pro3.warrantyItem.Product_Code__c)) {
                                    List<String> serialCodeList = new List<String>();
                                    if(String.isNotBlank(codeMapToWR.get(pro3.warrantyItem.Product_Code__c).Code_in_Serial__c)){
                                        serialCodeList.addAll(codeMapToWR.get(pro3.warrantyItem.Product_Code__c).Code_in_Serial__c.split(';'));
                                    }
                                    if(!serialCodeList.contains(productCodeInSN)) {
                                        pro3.snFormatErrorMessage = 'Warning: Entered SN may not match the product model, please double confirm.';
                                    }
                                }

                                // if(codeMapToWR.get(productCodeInSN) != null &&
                                //     pro3.warrantyItem.Product_Code__c != codeMapToWR.get(productCodeInSN).NA_FC_Model__c &&
                                //     pro3.warrantyItem.Product_Code__c != codeMapToWR.get(productCodeInSN).NA_Model__c){

                                //     pro3.snFormatErrorMessage = 'Warning: Entered SN may not match the product model, please double confirm.';
                                // }
                            }
                            else {
                                String productCodeInSN = pro3.warrantyItem.Serial_Number__c.subString(2,6);
                                if(codeMapToWR.containsKey(pro3.warrantyItem.Product_Code__c)) {
                                    List<String> serialCodeList = new List<String>();
                                    if(String.isNotBlank(codeMapToWR.get(pro3.warrantyItem.Product_Code__c).Code_in_Serial__c)){
                                        serialCodeList.addAll(codeMapToWR.get(pro3.warrantyItem.Product_Code__c).Code_in_Serial__c.split(';'));
                                    }
                                    if(!serialCodeList.contains(productCodeInSN)) {
                                        pro3.snFormatErrorMessage = 'Warning: Entered SN may not match the product model, please double confirm.';
                                    }
                                }

                                // if(codeMapToWR.get(productCodeInSN) != null &&
                                //     pro3.warrantyItem.Product_Code__c != codeMapToWR.get(productCodeInSN).NA_FC_Model__c &&
                                //     pro3.warrantyItem.Product_Code__c != codeMapToWR.get(productCodeInSN).NA_Model__c){

                                //     pro3.snFormatErrorMessage = 'Warning: Entered SN may not match the product model, please double confirm.';
                                // }
                            }
                        }
                    } else if (warrantyBrandName.equalsIgnoreCase('FLEX')) {
                        strSerialNumber = pro3.warrantyItem.Serial_Number__c;
                        if (String.isBlank(strSerialNumber) || strSerialNumber.length() < 5 || strSerialNumber.length() > 13) {
                            pro3.snFormatErrorMessage = Label.CCM_FLEX_Warranty_Serial_Number_Error_Invalid_Length;
                            pro3.isFormatCorrect = false;
                            continue;
                        }
                        if (strSerialNumber.length() < 13) {
                            boolMatch = CCM_RegularExpressionUtils.test(Label.CCM_FLEX_Warranty_Serial_Number_Rule, strSerialNumber);
                            if (boolMatch == false) {
                                pro3.snFormatErrorMessage = 'This SN is incorrect.';
                                pro3.isFormatCorrect = false;
                                continue;
                            }
                        } else {
                            if (strSerialNumber.contains('/')) {
                                if (strSerialNumber.substringAfter('/').length() != 4) {
                                    pro3.snFormatErrorMessage = 'This SN is incorrect.';
                                    pro3.isFormatCorrect = false;
                                    continue;
                                }
                            } else {
                                boolMatch = CCM_RegularExpressionUtils.test(Label.CCM_FLEX_Warranty_Serial_Number_Rule_13, strSerialNumber);
                                if (boolMatch == false) {
                                    pro3.snFormatErrorMessage = 'This SN is incorrect.';
                                    pro3.isFormatCorrect = false;
                                    continue;
                                }
                                if (Integer.valueOf(strSerialNumber.substring(4, 7)) > 366 || Integer.valueOf(strSerialNumber.substring(4, 7)) < 1) {
                                    pro3.snFormatErrorMessage = 'This SN is incorrect.2';
                                    pro3.isFormatCorrect = false;
                                    continue;
                                }
                                intSerialNumberSum = 0;
                                for (Integer intI = 0; intI < strSerialNumber.length() - 1; intI++) {
                                    intSerialNumberSum += Integer.valueOf(strSerialNumber.substring(intI, intI + 1)) * (Math.mod(intI, 2) == 0 ? 1 : 3);
                                }
                                if (Math.ceil(intSerialNumberSum / 10.0) * 10 - intSerialNumberSum != Integer.valueOf(strSerialNumber.substring(12))) {
                                    pro3.snFormatErrorMessage = 'This SN is incorrect.3';
                                    pro3.isFormatCorrect = false;
                                    continue;
                                }
                            }
                        }
                    }
                }
                if (!WarrantyService.verifySerialNumber(warrantyBrandName, pro3.warrantyItem.Serial_Number__c.toUpperCase(), pro3.warrantyItem.Product_Code__c, isEdit) && warrantyBrandName != 'FLEX' && warrantyBrandName != 'Kobalt') {
                    pro3.snFormatErrorMessage = 'The SN format is not correct!';
                    pro3.isFormatCorrect      = false;
                }
                List<John_Deere_SN__c> jdSNList = [SELECT Id, Serial_Number__c FROM John_Deere_SN__c WHERE Serial_Number__c = :pro3.warrantyItem.Serial_Number__c];
                if (!jdSNList.isEmpty()) {
                    pro3.snFormatErrorMessage = 'This item was purchased with a John Deere product. Warranty is not fulfilled through EGO, direct customer back to John Deere for assistance!';
                    pro3.isFormatCorrect      = false;
                }

                //Check the serial number entered by the user is in the recalled project
                if (pro3.isFormatCorrect) {
                    List<Project_SN__c> projectList = findProjectsAccordingSerialNum(pro3.warrantyItem.Serial_Number__c, pro3.warrantyItem.Product_Code__c, warrantyBrandName);
                    if (!projectList.isEmpty()) {
                        pro3.snFormatErrorMessage = 'Warning: This product is in recall queue!';
                        pro3.inRecallProject      = true;
                    }
                }
            }
        }

        if(warr.Place_of_Purchase_picklist__c == null || warr.Purchase_Date__c == null) {
            return 'Purchase Date / Place of Purchase can not be null';
        }
        setWarrantyItemExpirationDate(warr, items);

        return JSON.serialize(product3s);

    }

    private static Boolean isV5OrFC2(String serialNumber) {
        Boolean isV5orFC2 = false;
        if(String.isNotBlank(serialNumber)) {
            serialNumber = serialNumber.substring(3,4);
            isV5orFC2 = serialNumber.isNumeric();
        }
        return isV5orFC2;
    }


    public static void setWarrantyItemExpirationDate(Warranty__c warr, List<Warranty_Item__c> items) {

        //找出当前warrantyItem中包含的product对应的warranty year
        Map<String, Decimal> product_warrantyYear_map = new Map<String, Decimal>();
        Set<String> productSet = new Set<String>();
        for (Warranty_Item__c item : items) {
            productSet.add(item.Product__c);
        }
        for (Product2 pro : [SELECT Id, Warranty_Year__c FROM Product2 WHERE Id IN :productSet]) {
            Decimal year = pro.Warranty_Year__c == null ? 99 : pro.Warranty_Year__c;
            product_warrantyYear_map.put(pro.Id, year);
        }
        //设置产品明细的过期时间  不同品牌有不同的过期日期处理逻辑
        if (warr.Brand_Name__c == 'Skil' || warr.Brand_Name__c == 'SkilSaw') {
            for (Warranty_Item__c warrantyItem : items) {
                setWarrantyItemExpirationDateForSkilAndSkilSaw(warr, warrantyItem, product_warrantyYear_map);
            }
        } else {
            for (Warranty_Item__c warrantyItem : items) {
                setWarrantyItemExpirationDateForEGO(warr, warrantyItem);
            }
        }
        //处理完所有过期日期之后  设置指示灯
        setWarrantyItemIndicator(warr, items);

    }

    public static void setWarrantyItemExpirationDateForSkilAndSkilSaw(Warranty__c warr, Warranty_Item__c warrantyItem, Map<String,Decimal> product_warrantyYear_map) {
        if (warr.Purchase_Date__c==null) {
            return;
        }
        //商用  skil->90天  skil->1 year
        //民用  根据warranty year
        if (warr.Product_Use_Type2__c == 'Residential' && product_warrantyYear_map.containsKey(warrantyItem.Product__c)) {
            Integer year = Integer.valueOf(product_warrantyYear_map.get(warrantyItem.Product__c) );
            warrantyItem.Expiration_Date_New__c = warr.Purchase_Date__c.addYears(year);
        } else if (warr.Product_Use_Type2__c == 'Professional/Commercial'){
            //商用的
            if (warr.Brand_Name__c == 'Skil'){
                warrantyItem.Expiration_Date_New__c = warr.Purchase_Date__c.addDays(90);
            } else if (warr.Brand_Name__c == 'SkilSaw') {
                warrantyItem.Expiration_Date_New__c = warr.Purchase_Date__c.addYears(1);
            }
        }
    }

    public static void setWarrantyItemExpirationDateForEGO(Warranty__c warr, Warranty_Item__c warrantyItem) {
        //ADD BULLET
        if (warr.Purchase_Date__c==null) {
            return;
        }
        if (warr.Product_Use_Type2__c == 'Residential') {
            if (warrantyItem.Product_Model__c == '3') {
                warrantyItem.Expiration_Date_New__c = warr.Purchase_Date__c.addYears(1);
            } else {
                if(warrantyItem.Product_Type__c == 'Product') {
                    warrantyItem.Expiration_Date_New__c = warr.Purchase_Date__c.addYears(5);
                } else {
                    warrantyItem.Expiration_Date_New__c = warr.Purchase_Date__c.addYears(3);
                }
            }
        } else if (warr.Product_Use_Type2__c == 'Professional/Commercial'){
            //如果是翻新机，这过保日期为90天
            if (warrantyItem.Product_Model__c == '3') {
                warrantyItem.Expiration_Date_New__c = warr.Purchase_Date__c.addDays(90);
            } else {
                warrantyItem.Expiration_Date_New__c = warr.Purchase_Date__c.addYears(1);
            }
        }
    }

    public static void setWarrantyItemIndicator(Warranty__c warr, List<Warranty_Item__c> items) {
        //判断购买地是否授权
        String isAuthorized = getPurchasePlaceIsAuthorized(warr);
        System.debug('isAuthorized='+isAuthorized);
        //设置warranty明细的指示灯
        for (Warranty_Item__c warrantyItem : items) {
            //购买地未授权
            if (isAuthorized == 'Unauthorized') {
                System.debug('isAuthorized11111111');
                warrantyItem.Indicator__c = 'Out of Warranty';//购买地状态为授权
                continue;
            }
            system.debug(warr.Receipt_received_and_verified__c);
            system.debug(warr.Lost_Receipt__c);
            system.debug(warr.Pending__c);

            //过期
            if (warrantyItem.Expiration_Date_New__c != null && warrantyItem.Expiration_Date_New__c.daysBetween(Date.today()) > 0) {
                warrantyItem.Indicator__c = 'Out of Warranty';//过期
            } else {
                if (isAuthorized == 'Unknown') {
                    warrantyItem.Indicator__c = 'Pending';//未过期，但购买地状态为Unknown
                } else {
                    if (warr.Receipt_received_and_verified__c) {
                        if (!String.isBlank(warrantyItem.Serial_Number__c)) {
                            warrantyItem.Indicator__c = 'Vailid Warranty';//购买地状态为Authorized，未过期，发票上传且验证过
                        } else {
                            warrantyItem.Indicator__c = 'Pending';
                        }
                    } else {
                        if (warr.Lost_Receipt__c) {
                            if (warr.One_Time_Exception__c) {
                                warrantyItem.Indicator__c = 'Vailid Warranty';//购买地状态为Authorized，未过期，未上传或验证发票，发票丢失了
                            } else {
                                warrantyItem.Indicator__c = 'Out of Warranty';
                            }
                        } else if(warr.Pending__c){
                            warrantyItem.Indicator__c = 'Pending';
                        } else{
                            warrantyItem.Indicator__c = 'Out of Warranty';//购买地状态为Authorized，未过期，未上传或验证发票，发票未丢失
                        }
                    }
                }
            }
        }
    }

    public static String getPurchasePlaceIsAuthorized(Warranty__c warr) {
        // 将授权的购买地通过配置形式保存
        if (warr.Place_of_Purchase_picklist__c.containsIgnoreCase('Unauthorized')) {
            return 'Unauthorized';
        } else if (warr.Place_of_Purchase_picklist__c.containsIgnoreCase('Unknown') || warr.Place_of_Purchase_picklist__c.containsIgnoreCase('Other')) {
            return 'Unknown';
        } else {
            return 'Authorized';
        }
    }
@TestVisible
private static Set<Id> getEuropeanProduct(List<Product3> lstProduct3) {
    Set<Id> setProductId = new Set<Id>();
    Set<Id> setEuropeanProduct = new Set<Id>();
    // prettier-ignore
    if (lstProduct3 == null || lstProduct3.isEmpty()) return setEuropeanProduct;
    for (Product3 objP : lstProduct3) {
        // prettier-ignore
        if (objP.warrantyItem == null || String.isBlank(objP.warrantyItem.Product__c)) continue;
        setProductId.add(objP.warrantyItem.Product__c);
    }
    if (!setProductId.isEmpty()) {
        for (Product2 objP : [
            SELECT Id
            FROM Product2
            WHERE Id IN :setProductId AND Country_of_Origin__c IN :Label.CCM_Product_Country_Origin_European_List.split(CCM_Constants.SEMICOLON_REGEXP_SPACE_IGNORED)
        ]) {
            setEuropeanProduct.add(objP.Id);
        }
    }
    return setEuropeanProduct;
}
    //保存Warranty
    public Static void getWarrantyItemMap( Map<String,List<Warranty_Item__c>> warrantyItemMap,String key,Warranty_Item__c warrantyItem){
        if (!warrantyItemMap.containsKey(key)) {
            warrantyItemMap.put(key, new List<Warranty_Item__c>());
            warrantyItemMap.get(key).add(warrantyItem);
        }else {
            warrantyItemMap.get(key).add(warrantyItem);
        }
    }
    @AuraEnabled
    public static String save(String warranty, String proListStr, String caseId) {

        Warranty__c innerWarranty = (Warranty__c)JSON.deserialize(warranty, Warranty__c.class);
        System.debug(LoggingLevel.INFO, '*** Json.serialize(innerWarranty): ' + Json.serialize(innerWarranty));
        WarrantyItemWrapper product3s = (WarrantyItemWrapper)JSON.deserialize(proListStr, WarrantyItemWrapper.class);
        System.debug(LoggingLevel.INFO, '*** Json.serialize(product3s): ' + Json.serialize(product3s));
        proList = product3s.proList;

        //System.debug('保存warranty:'+isInConsole);


        Boolean isInfoNotRight = FALSE;
        Map<String,List<Warranty_Item__c>> warrantyItemMap = new Map<String,List<Warranty_Item__c>>();
        Set<Id> setEuropeanProduct = getEuropeanProduct(proList);
        List<Warranty_Item__c> warrantyItemNewList = new List<Warranty_Item__c>();
        List<Warranty_Item__c> wis = new List<Warranty_Item__c>();
        List<String> warrantyItemSkilSNList = new List<String>();
        List<String> warrantyItemEGOSNList = new List<String>();
        List<String> warrantyItemFLEXSNList = new List<String>();
        for(Product3 pro3 : proList) {

            //选中的产品必须填写SN号
            pro3.isFormatCorrect = true;
            if (pro3.isSelect == true && String.isBlank(pro3.warrantyItem.Serial_Number__c) ) {
                isInfoNotRight = TRUE;
            }

            if (pro3.isSelect ){
                wis.add(pro3.warrantyItem);
            }else if(!pro3.isSelect && String.isBlank(pro3.warrantyItem.Serial_Number__c) ){
                wis.add(pro3.warrantyItem);
            }

            if (!setEuropeanProduct.contains(pro3.warrantyItem?.Product__c) && !String.isBlank(pro3.warrantyItem.Serial_Number__c)) {
                if (!WarrantyService.verifySerialNumber(innerWarranty.Brand_Name__c, pro3.warrantyItem.Serial_Number__c.toUpperCase(), pro3.warrantyItem.Product_Code__c) && innerWarranty.Brand_Name__c != 'FLEX' && innerWarranty.Brand_Name__c != 'Kobalt') {
                    pro3.snFormatErrorMessage = 'The SN format is not correct!';
                    pro3.isFormatCorrect      = false;
                    isInfoNotRight            = TRUE;
                }

                if (pro3.warrantyItem.IsReplacement__c == false && !'FLEX'.equalsIgnoreCase(innerWarranty.Brand_Name__c)) {
                    System.debug('*********pro3.warrantyItem.Serial_Number__c.length()' + pro3.warrantyItem.Serial_Number__c.length());
                    Integer size = pro3.warrantyItem.Serial_Number__c.length();
                    if(size == 10){
                            warrantyItemEGOSNList.add('20'+pro3.warrantyItem.Serial_Number__c.subString(1,5));
                            getWarrantyItemMap(warrantyItemMap,'20'+pro3.warrantyItem.Serial_Number__c.subString(1,5),pro3.warrantyItem);
                    }else if(size == 14 || size == 15){
                        if(Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(5,9)) <= CHANGESNDATE ){
                             warrantyItemEGOSNList.add('20'+pro3.warrantyItem.Serial_Number__c.subString(5,9));
                             getWarrantyItemMap(warrantyItemMap,'20'+pro3.warrantyItem.Serial_Number__c.subString(5,9),pro3.warrantyItem);
                        }else{
                            Integer weekToMonth = (Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(7,9))/4);
                            if(weekToMonth < 1){
                                weekToMonth = 1;
                            }
                            if(String.valueOf(weekToMonth).length() == 1 ){
                                weekToMonth = Integer.valueOf('0'+ String.valueOf(weekToMonth));
                            }
                            warrantyItemEGOSNList.add('20'+pro3.warrantyItem.Serial_Number__c.subString(5,7)+ weekToMonth);
                            getWarrantyItemMap(warrantyItemMap,'20'+pro3.warrantyItem.Serial_Number__c.subString(5,7)+ weekToMonth,pro3.warrantyItem);
                        }

                        System.debug('*********pro3.warrantyItem.Serial_Number__c.subString(5,9):' + pro3.warrantyItem.Serial_Number__c.subString(5,9));
                    }else if (size == 16){
                        if(isV5OrFC2(pro3.warrantyItem.Serial_Number__c)) {
                            if(Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(5,9)) <= CHANGESNDATE ){
                                 warrantyItemEGOSNList.add('20'+pro3.warrantyItem.Serial_Number__c.subString(5,9));
                                 getWarrantyItemMap(warrantyItemMap,'20'+pro3.warrantyItem.Serial_Number__c.subString(5,9),pro3.warrantyItem);
                            }else{
                                Integer weekToMonth = (Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(7,9))/4);
                                if(weekToMonth < 1){
                                    weekToMonth = 1;
                                }
                                if(String.valueOf(weekToMonth).length() == 1 ){
                                    weekToMonth = Integer.valueOf('0'+ String.valueOf(weekToMonth));
                                }
                                warrantyItemEGOSNList.add('20'+pro3.warrantyItem.Serial_Number__c.subString(5,7)+ weekToMonth);
                                getWarrantyItemMap(warrantyItemMap,'20'+pro3.warrantyItem.Serial_Number__c.subString(5,7)+ weekToMonth,pro3.warrantyItem);
                            }
                        }
                        else {
                            warrantyItemEGOSNList.add('20'+pro3.warrantyItem.Serial_Number__c.subString(6,10));
                            getWarrantyItemMap(warrantyItemMap,'20'+pro3.warrantyItem.Serial_Number__c.subString(6,10),pro3.warrantyItem);
                        }
                    }else{
                        // SN以420开头的特定产品按430算
                        Integer intSN = 0;
                        Integer intSN2 = 0;
                        if(pro3.warrantyItem.Product_Code__c =='SC535801' && pro3.warrantyItem.Serial_Number__c.length() <= 9){
                            intSN = Integer.valueof(pro3.warrantyItem.Serial_Number__c);
                            System.debug('222222222  '+ intSN);
                        }else if(pro3.warrantyItem.Product_Code__c =='NA1800B-00' && pro3.warrantyItem.Serial_Number__c.length() <= 9){
                            intSN2 = Integer.valueof(pro3.warrantyItem.Serial_Number__c);
                        }
                        if((pro3.warrantyItem.Product_Code__c == 'SC535801' 
                                && intSN >= Integer.valueof('420000001') 
                                && intSN <= Integer.valueof('420015000')) 
                            || (pro3.warrantyItem.Product_Code__c == 'NA1800B-00' 
                                && intSN2 >= Integer.valueof('420000001') 
                                && intSN2 <= Integer.valueof('420001848'))){
                            warrantyItemSkilSNList.add('430');
                            getWarrantyItemMap(warrantyItemMap,'430',pro3.warrantyItem);
                        }else{
                            warrantyItemSkilSNList.add(pro3.warrantyItem.Serial_Number__c.subString(0,3));
                            getWarrantyItemMap(warrantyItemMap,pro3.warrantyItem.Serial_Number__c.subString(0,3),pro3.warrantyItem);
                        }

                    }
                }

            }

            Integer size = null;
            if(pro3.warrantyItem.Serial_Number__c != null){
                size = pro3.warrantyItem.Serial_Number__c.length();
            }
            if(innerWarranty.Brand_Name__c == 'FLEX' && size > 7 ){
                if (pro3.warrantyItem.Serial_Number__c.contains('/') && pro3.warrantyItem.Serial_Number__c.substringAfter('/').length() == 4) {
                    String dayToYear = pro3.warrantyItem.Serial_Number__c.substringAfter('/');
                    String snBefore = pro3.warrantyItem.Serial_Number__c.substringBefore('/');
                    String toMonth = snBefore.subString(snBefore.length()-2,snBefore.length());
                    warrantyItemFLEXSNList.add(dayToYear + toMonth);
                    getWarrantyItemMap(warrantyItemMap,dayToYear + toMonth,pro3.warrantyItem);
                }else{
                    Integer dayToMonth = (Integer.valueOf(pro3.warrantyItem.Serial_Number__c.subString(4,7))/30);
                    if(dayToMonth < 1){
                        dayToMonth = 1;
                    }
                    system.debug(dayToMonth);
                    system.debug(String.valueOf(dayToMonth).length());
                     if(String.valueOf(dayToMonth).length() == 1 ){
                        dayToMonth = Integer.valueOf('0'+ String.valueOf(dayToMonth));
                    }
                    system.debug(dayToMonth);
                    warrantyItemFLEXSNList.add('20'+pro3.warrantyItem.Serial_Number__c.subString(2,4)+ dayToMonth);
                    getWarrantyItemMap(warrantyItemMap,'20'+pro3.warrantyItem.Serial_Number__c.subString(2,4)+ dayToMonth,pro3.warrantyItem);
                }

            }

            warrantyItemNewList.add(pro3.warrantyItem);
        }

        //变更保修时间
        changeExpirationDate(innerWarranty, wis);

        if(isInfoNotRight && !Test.isRunningTest()){
            return 'The SN format is not correct!';
        }


        Integer todayYear = Date.today().year();
        Integer todayMonth = Date.today().month();

        System.debug('*********warrantyItemSkilSNList: ' + warrantyItemSkilSNList);
        System.debug('*********warrantyItemEGOSNList: ' + warrantyItemEGOSNList);
        if(warrantyItemSkilSNList.size() > 0 && innerWarranty.Lost_Receipt__c == TRUE && innerWarranty.Used_One_Time_Exception__c == false){
            for(Warranty_Rules__c wr : [SELECT Name, Year_MD__c, Month_MD__c FROM Warranty_Rules__c
                                        WHERE Name IN: warrantyItemSkilSNList]){

                    System.debug('*********: break');
                    Date dat = Date.newInstance(Integer.valueOf(wr.Year_MD__c), Integer.valueOf(wr.Month_MD__c), 1);
                    innerWarranty.Production_Date__c = dat.addDays(180);
                    innerWarranty.One_Time_Exception__c = true;
                    for(Warranty_Item__c wi : warrantyItemMap.get(wr.Name)){
                        wi.Production_Date__c = dat.addDays(180);
                    }
                    break;

            }
        }else if(warrantyItemEGOSNList.size() > 0 && innerWarranty.Lost_Receipt__c == TRUE && innerWarranty.Used_One_Time_Exception__c == false){
            for(String yearMonth : warrantyItemEGOSNList){
                 Date dat = Date.newInstance(1900,1,1);
                if(yearMonth.length() == 6){
                    dat = Date.newInstance(Integer.valueOf(yearMonth.substring(0, 4)), Integer.valueOf(yearMonth.substring(4, 6)), 1);
                }else{
                    dat = Date.newInstance(Integer.valueOf(yearMonth.substring(0, 4)), Integer.valueOf(yearMonth.substring(4, 5)), 1);
                }
                for(Warranty_Item__c wi : warrantyItemMap.get(yearMonth)){
                    wi.Production_Date__c = dat.addDays(180);
                }
                innerWarranty.Production_Date__c = dat.addDays(180);
                innerWarranty.One_Time_Exception__c = true;
                break;

            }
        }else if(warrantyItemFLEXSNList.size() > 0 && innerWarranty.Lost_Receipt__c == TRUE && innerWarranty.Used_One_Time_Exception__c == false){
            for(String yearMonth : warrantyItemFLEXSNList){
                 Date dat = Date.newInstance(1900,1,1);
                 system.debug(yearMonth);
                if(yearMonth.length() == 6){
                    dat = Date.newInstance(Integer.valueOf(yearMonth.substring(0, 4)), Integer.valueOf(yearMonth.substring(4, 6)), 1);
                }else{
                    dat = Date.newInstance(Integer.valueOf(yearMonth.substring(0, 4)), Integer.valueOf(yearMonth.substring(4, 5)), 1);
                }
                for(Warranty_Item__c wi : warrantyItemMap.get(yearMonth)){
                    wi.Production_Date__c = dat.addDays(180);
                }
                innerWarranty.Production_Date__c = dat.addDays(180);
                innerWarranty.One_Time_Exception__c = true;
                break;

            }
        }else if(innerWarranty.Lost_Receipt__c == FALSE){
            innerWarranty.Production_Date__c = innerWarranty.Purchase_Date__c;
            innerWarranty.One_Time_Exception__c = false;
        }else if(innerWarranty.Lost_Receipt__c == true && innerWarranty.Brand_Name__c == 'FLEX'){

            innerWarranty.One_Time_Exception__c = true;
        }else{
            innerWarranty.Production_Date__c = innerWarranty.Purchase_Date__c;
        }
        //update Warranty and Case
        try {

            //innerWarranty.Receipt_upload__c = hasUploaded;//TODO
            upsert innerWarranty;

            //若没有修改master product，更新warrant item
            //若修改或新增master product，则插入新的warranty item，并删除旧的
            System.debug('*********wis: ' + wis);
            if (wis.size() == 0) {
                return 'Warranty Item can not be null';
            }
            saveWarrantyItem(innerWarranty, wis);

            //relate to case
            // if (caseId != null && caseId != '') {
            //     Case thisCase = [SELECT Id, Warranty__c FROM Case WHERE Id =: caseId LIMIT 1];
            //     thisCase.Warranty__c = innerWarranty.Id;
            //     update thisCase;
            // }

            /*if(hasUploaded) { //TODO
                changeAttParent();
            }*/
        } catch(Exception e) {
            return 'Save Warranty Failed ' + e.getMessage() + ' In Line: ' + e.getLineNumber();
        }


        return 'success';
    }


    public static void saveWarrantyItem(Warranty__c warranty, List<Warranty_Item__c> warrantyItemList) {

        List<Warranty_Item__c> insertNewWarrantyList = new List<Warranty_Item__c>();

        //将warranty item与warranty建立关系
        for(Warranty_Item__c warrantyItem : warrantyItemList){
            system.debug('warrantyItem.Warranty__c========>'+warranty.Id);
            Warranty_Item__c wi = new Warranty_Item__c();
            wi = warrantyItem.clone();
            if(warrantyItem.Id != null){
                wi.Id = warrantyItem.Id;

            }else {
                wi.Warranty__c = warranty.Id;
            }

            insertNewWarrantyList.add(wi);

        }

        //插入新的
        if(insertNewWarrantyList.size() > 0) {
            UPSERT insertNewWarrantyList;
        }

    }
    /**
     * @description This wrapper class is used to struct Question and Choices together.
     */
    public class QuestionChoiceWrapper {
        public Id id;
        public String type;
        public List<ChoiceWrapper> choices;
    }
    /**
     * @description This class is used to wrap Choices.
     */
    public class ChoiceWrapper {
        public Id id;
        public Boolean checked;
        public String answerText;
    }
    /**
     * @description This method is used to save a Survey.
     */
    @AuraEnabled
    public static void saveSurvey(Id idSurvey, Id idCustomer, Id idWarranty, String strResponseJSON) {
        CCM_SurveyProcessor.saveSurvey(idSurvey, idCustomer, idWarranty, strResponseJSON);
    }
    /**
     * <AUTHOR> Dou
     * @date 2020/10/28
     * @description Get if the product have survey.
     */
    @AuraEnabled
    public static Product2 getProductIfHaveSurvey(String productId) {
        try {
            Product2 product = [SELECT Id, Chervon_Survey__c FROM Product2 WHERE Id = :productId][0];
            return product;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    //变更保修时间
    @AuraEnabled
    public static String changeExpirationDate(Warranty__c innerWarranty, String proListStr){
        system.debug('innerWarranty==>'+innerWarranty);
        system.debug('proListStr==>'+proListStr);
        WarrantyItemWrapper product3s = (WarrantyItemWrapper)JSON.deserialize(proListStr, WarrantyItemWrapper.class);
        proList = product3s.proList;
        List<Warranty_Item__c> warrantyItems = new List<Warranty_Item__c>();
        for (Product3 pro3 : proList) {
            warrantyItems.add(pro3.warrantyItem);
        }

        //setReceiptStatus();
        //设置根据购买时间和购买地，设置Store_return_exchange_policy__c的值(存储返回/更换日期)
        withinStorePolicy = false;
        setStoreReturnExchangePolicy(innerWarranty);
        //判断是否在存储政策日期内
        if(innerWarranty.Store_return_exchange_policy__c != null &&
            innerWarranty.Store_return_exchange_policy__c.daysBetween(Date.today()) <= 0) {
            withinStorePolicy = true;
        }
        innerWarranty.Within_Store_Policy__c = withinStorePolicy;

        //通过 购买地、购买日期、Master产品、下属产品 确定保修时间，所以不能为空
        if(innerWarranty.Place_of_Purchase_picklist__c == null || innerWarranty.Purchase_Date__c == null || innerWarranty.Master_Product__c == null || warrantyItems.size() == 0){
            Map<String,String> resultJson = new Map<String,String>();
            resultJson.put('Message','Place of Purchase / Purchase Date / Master Product / Warranty Item can not be null');
            return Json.serialize(resultJson);
        }

        //设置产品明细的过期时间
        if (warrantyItems.size() > 0) {
            setWarrantyItemExpirationDate(innerWarranty, warrantyItems);
        }

        //变更修理退货费用
        countCost();
        System.debug('******changeExpirationDate: ' + JSON.serialize(product3s));
        return JSON.serialize(product3s);
    }

    //变更保修时间
    public static void changeExpirationDate(Warranty__c innerWarranty, List<Warranty_Item__c> warrantyItems){
        //setReceiptStatus();
        //设置根据购买时间和购买地，设置Store_return_exchange_policy__c的值(存储返回/更换日期)
        withinStorePolicy = false;
        setStoreReturnExchangePolicy(innerWarranty);
        //判断是否在存储政策日期内
        if(innerWarranty.Store_return_exchange_policy__c != null &&
            innerWarranty.Store_return_exchange_policy__c.daysBetween(Date.today()) <= 0) {
            withinStorePolicy = true;
        }
        innerWarranty.Within_Store_Policy__c = withinStorePolicy;

        //通过 购买地、购买日期、Master产品、下属产品 确定保修时间，所以不能为空
        if(innerWarranty.Place_of_Purchase_picklist__c == null || innerWarranty.Purchase_Date__c == null || innerWarranty.Master_Product__c == null || warrantyItems.size() == 0){
            System.debug('******changeExpirationDate if');
            return;
        }

        //设置产品明细的过期时间
        if (warrantyItems.size() > 0) {
            setWarrantyItemExpirationDate(innerWarranty, warrantyItems);
        }

        //变更修理退货费用
        countCost();
    }

    public static void setStoreReturnExchangePolicy(Warranty__c innerWarranty) {
        innerWarranty.Store_return_exchange_policy__c = null;

        if(innerWarranty.Purchase_Date__c != null &&
            String.isNotBlank(innerWarranty.Place_of_Purchase_picklist__c)) {

            if(innerWarranty.Brand_Name__c == 'Skil') {
                if(innerWarranty.Place_of_Purchase_picklist__c == 'Home Depot' ||
                    innerWarranty.Place_of_Purchase_picklist__c == 'Lowes' ||
                    innerWarranty.Place_of_Purchase_picklist__c == 'Menards' ||
                    innerWarranty.Place_of_Purchase_picklist__c == 'Meijer' ||
                    innerWarranty.Place_of_Purchase_picklist__c == 'Wal-mart' ) {
                    innerWarranty.Store_return_exchange_policy__c = innerWarranty.Purchase_Date__c.addDays(90).addDays(1);
                } else if(innerWarranty.Place_of_Purchase_picklist__c == 'Amazon LLC' ||
                            innerWarranty.Place_of_Purchase_picklist__c == 'White Cap' ||
                            innerWarranty.Place_of_Purchase_picklist__c == 'Tool Barn' ||
                            innerWarranty.Place_of_Purchase_picklist__c == 'Acme Tools' ||
                            innerWarranty.Place_of_Purchase_picklist__c == 'Authorized Dealer') {
                    innerWarranty.Store_return_exchange_policy__c = innerWarranty.Purchase_Date__c.addDays(30).addDays(1);
                } else {
                    innerWarranty.Store_return_exchange_policy__c = innerWarranty.Purchase_Date__c.addDays(-1);
                }
            } else if(innerWarranty.Brand_Name__c == 'SkilSaw') {
                if(innerWarranty.Place_of_Purchase_picklist__c == 'Home Depot' ||
                    innerWarranty.Place_of_Purchase_picklist__c == 'Lowes' ||
                    innerWarranty.Place_of_Purchase_picklist__c == 'Menards' ) {
                    innerWarranty.Store_return_exchange_policy__c = innerWarranty.Purchase_Date__c.addDays(90).addDays(1);
                } else if(innerWarranty.Place_of_Purchase_picklist__c == 'Ferguson' ) {
                    innerWarranty.Store_return_exchange_policy__c = innerWarranty.Purchase_Date__c.addDays(60).addDays(1);
                } else if(innerWarranty.Place_of_Purchase_picklist__c == 'Amazon LLC' ||
                            innerWarranty.Place_of_Purchase_picklist__c == 'Fastenal' ||
                            innerWarranty.Place_of_Purchase_picklist__c == 'Grainger' ||
                            innerWarranty.Place_of_Purchase_picklist__c == 'White Cap' ||
                            innerWarranty.Place_of_Purchase_picklist__c == 'Authorized Dealer') {
                    innerWarranty.Store_return_exchange_policy__c = innerWarranty.Purchase_Date__c.addDays(30).addDays(1);
                } else {
                    innerWarranty.Store_return_exchange_policy__c = innerWarranty.Purchase_Date__c.addDays(-1);
                }
            } else {
                if(innerWarranty.Place_of_Purchase_picklist__c == 'Home Depot') {
                    innerWarranty.Store_return_exchange_policy__c = innerWarranty.Purchase_Date__c.addDays(90).addDays(1);
                } else if(innerWarranty.Place_of_Purchase_picklist__c == 'Amazon LLC' ||
                            innerWarranty.Place_of_Purchase_picklist__c == 'Authorized Dealer' ||
                            innerWarranty.Place_of_Purchase_picklist__c == 'Ace Hardware') {
                    innerWarranty.Store_return_exchange_policy__c = innerWarranty.Purchase_Date__c.addDays(30).addDays(1);
                } else {
                    innerWarranty.Store_return_exchange_policy__c = innerWarranty.Purchase_Date__c.addDays(-1);
                }
            }
        }
    }




    //变更修理退货费用
    public static void countCost() {
        //初始化
        /*thisWarranty.Store_Policy_Cost__c = null;
        thisWarranty.Replacement_Cost__c = null;
        thisWarranty.Recommendation_of_Warranty_Issue__c = '';

        //根据产品购买地计算费用
        if(thisWarranty.Place_of_Purchase_picklist__c == null || thisWarranty.Purchase_Date__c == null) {
          return;
        }
        //计算修理/退货费用
        warrService.countCost();*/
    }


    // 检查用户输入的serial number是否处于召回的项目中
    @TestVisible
    public static List<Project_SN__c> findProjectsAccordingSerialNum(String serialNumber, String productCode, String warrantyBrandName) {
        List<Project_SN__c> projectList = new List<Project_SN__c>();
        Date today = Date.today();
        // for (Project__c eachPro : [SELECT Id, Star_Time__c, Deadline__c,
        //                             (SELECT Id, Star_SN__c, End_SN__c FROM Project_SNs__r)
        //                             FROM Project__c WHERE Deadline__c >= :today
        //                             AND Brand_Name__c =: warrantyBrandName
        //                             AND Product__r.ProductCode =: productCode]) {
           // List<Project_SN__c> proSns = eachPro.Project_SNs__r;
           // if (proSns == null || proSns.isEmpty()) {
           //     continue;
           // }

        //    for (Project_SN__c eachProSN :  eachPro.Project_SNs__r) {
        //      String startSn = eachProSN.Star_SN__c;
        //      String endSn = eachProSN.End_SN__c;

        //      // 判断传入的serial number是否在区间内
        //     // this.recallProject = eachPro;
        //     if (serialNumber >= startSn && serialNumber <= endSn) {
        //       projectList.add(eachPro);
        //     }
        //    }
        // }
        // ApexPages.Message msg = new ApexPages.Message(ApexPages.Severity.INFO, '您所填写的Serial Number所代表的设备正在被召回！');
        // ApexPages.addMessage(msg);
        projectList = [ SELECT Id, Star_SN__c, End_SN__c,Project__c,Project__r.Star_Time__c,Project__r.Deadline__c
                            FROM Project_SN__c
                            WHERE Project__r.Deadline__c >= :today
                            // AND Project__r.Brand_Name__c ='EGO'
                            AND Project__r.Product__r.ProductCode =:productCode
                            AND End_SN__c >=:serialNumber
                            AND Star_SN__c <= :serialNumber];
        return projectList;
    }
    @AuraEnabled
    public static Boolean getAccountSiteOriginOutsideEuropeStatus(Id idAccount) {
        Boolean boolIsOutsideEurope = false;
        String strSiteOrigin;
        if (String.isNotBlank(idAccount)) {
            for (Account objA : [SELECT Site_Origin__c FROM Account WHERE Id = :idAccount]) {
                strSiteOrigin = objA.Site_Origin__c;
            }
        }
        if (String.isNotBlank(strSiteOrigin)) {
            boolIsOutsideEurope = Label.CCM_Site_Origin_Outside_Europe.contains(strSiteOrigin);
        }
        return boolIsOutsideEurope;
    }
    public class Product3 {
        public String snFormatErrorMessage     {set; get;}
        public Boolean isSelect         {get; set;}
        public Boolean hasReplace         {get; set;}
        public List<comboBoxOptions> replaceCodeList     {get; set;}
        public Map<String,Warranty_Item__c> replaceProMap    {get; set;}
        public Boolean isFormatCorrect       {get; set;}
        public Boolean inRecallProject       {get; set;}
        public Warranty_Item__c warrantyItem   {get; set;}
        public Product3(Warranty_Item__c warrantyItem){
            this.warrantyItem         = warrantyItem;
            this.isSelect             = true;
            this.isFormatCorrect      = true;
            this.inRecallProject     = false;
            this.hasReplace = false;
            this.replaceCodeList = new List<comboBoxOptions>();
            this.replaceProMap = new Map<String,Warranty_Item__c>();
        }
    }

    public class WarrantyItemWrapper {
        public String productCode {get; set;}
        public List<Product3> proList {get; set;}
        public WarrantyItemWrapper (String productCode, List<Product3> proList) {
            this.productCode = productCode;
            this.proList = proList;
        }
    }

    public class comboBoxOptions {
        public String label;
        public String value;
    }


}