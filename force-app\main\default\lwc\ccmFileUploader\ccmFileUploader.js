/**
 * <AUTHOR>
 * @date 2021-10-09
 * @description This is custom component for file uploading.
 */
import { api, track, LightningElement } from "lwc";
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import { NavigationMixin } from "lightning/navigation";
import deleteFile from "@salesforce/apex/CCM_FileUploaderCtrl.deleteFile";
import linkFilesWithRecord from "@salesforce/apex/CCM_FileUploaderCtrl.linkFilesWithRecord";
import fetchExistingFiles from "@salesforce/apex/CCM_FileUploaderCtrl.fetchExistingFiles";

// 导入 Custom Label
import CCM_Portal_Delete from "@salesforce/label/c.CCM_Portal_Delete";
import CCM_Portal_Attachment from "@salesforce/label/c.CCM_Portal_Attachment";
import CCM_Portal_Youarerequiredtouploadatleastonefile from "@salesforce/label/c.CCM_Portal_Youarerequiredtouploadatleastonefile";
import CCM_Portal_Errorhappenedduringlinkingfileswiththetargetrecord from "@salesforce/label/c.CCM_Portal_Errorhappenedduringlinkingfileswiththetargetrecord";
import CCM_Portal_Errorhappenedduringfetchingfiles from "@salesforce/label/c.CCM_Portal_Errorhappenedduringfetchingfiles";
import CCM_Portal_Error from "@salesforce/label/c.CCM_Portal_Error";

export default class CcmFileUploader extends NavigationMixin(LightningElement) {

    // 定义 label
    // 24.11.14: 修改变量名称为"customLabel"，原变量名称为"label"，与其他变量重名导致报错
    @track customLabel = {
        CCM_Portal_Delete,
        CCM_Portal_Attachment,
        CCM_Portal_Youarerequiredtouploadatleastonefile,
        CCM_Portal_Errorhappenedduringlinkingfileswiththetargetrecord,
        CCM_Portal_Errorhappenedduringfetchingfiles,
        CCM_Portal_Error
    };
    // 24.11.14 end

    @api label = this.customLabel.CCM_Portal_Attachment;
    @api multiple = false;
    @api disabled = false;
    @api recordId;
    @api required = false;
    @api requiredMessage = this.customLabel.CCM_Portal_Youarerequiredtouploadatleastonefile;
    @api acceptedFormats;
    @api files = [];
    isLoading = false;
    showRequiredMessage = false;
    @api
    getUploadedFiles() {
        return this.files;
    }
    @api
    validate() {
        if (this.required === false) {
            return true;
        }
        if (this.files.length === 0) {
            this.showRequiredMessage = true;
        }
        return this.files.length > 0;
    }
    @api
    linkFilesWithRecord() {
        if (this.recordId && this.files && this.files.length > 0) {
            this.isLoading = true;
            let lstDocumentId = [];
            this.files.forEach((f) => {
                if (f.documentId) {
                    lstDocumentId.push(f.documentId);
                }
            });
            linkFilesWithRecord({ idRecord: this.recordId, strDocumentIdList: JSON.stringify(lstDocumentId) })
                .then(() => {
                    console.log("Files were linked successfully.");
                })
                .catch((error) => {
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: this.customLabel.CCM_Portal_Error,
                            variant: "error",
                            message: this.customLabel.CCM_Portal_Errorhappenedduringfetchingfiles
                        })
                    );
                })
                .finally(() => {
                    this.isLoading = false;
                });
        }
    }
    connectedCallback() {
        if (this.recordId) {
            fetchExistingFiles({ idRecord: this.recordId })
                .then((response) => {
                    this.files = response;
                    this.handleFilesUpdate();
                })
                .catch((error) => {
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: this.customLabel.CCM_Portal_Error,
                            variant: "error",
                            message: this.customLabel.CCM_Portal_Errorhappenedduringfetchingfiles,
                        })
                    );
                });
        }
        this.acceptedFormats = !this.acceptedFormats
            ? []
            : this.acceptedFormats
                  .replace(/\s/g, "")
                  .split(",")
                  .filter((i) => i);
    }
    handleUploadFinished(event) {
        let { files } = event.detail || [];
        this.files = [...this.files, ...files];
        this.dispatchEvent(new CustomEvent('finish', { detail: this.files }));// Added by Anony on 23.8.2
        this.showRequiredMessage = false;
        this.handleFilesUpdate();
    }
    handleFilesUpdate() {
        let files = Array.isArray(this.files) ? this.files : [];
        this.dispatchEvent(new CustomEvent("update", { detail: { files } }));
    }
    handleFileDeletion(event) {
        this.isLoading = true;
        let idDocument = event.currentTarget && event.currentTarget.dataset && event.currentTarget.dataset.id;
        if (typeof idDocument === "string" && idDocument.trim() !== "") {
            deleteFile({ idDocument })
                .then(() => {
                    this.files = this.files.filter((f) => f.documentId !== idDocument);
                    this.handleFilesUpdate();
                })
                .catch((error) => {
                    let { body, message } = error,
                        { message: bodyMessage, pageErrors } = body || {},
                        { message: pageErrorMessage } = Array.isArray(pageErrors) ? pageErrors[0] : {};
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: this.customLabel.CCM_Portal_Error,
                            message: message || bodyMessage || pageErrorMessage,
                            variant: "error"
                        })
                    );
                })
                .finally(() => {
                    this.isLoading = false;
                });
        }
    }
    handlePreview(event) {
        this[NavigationMixin.Navigate]({
            type: "standard__namedPage",
            attributes: {
                pageName: "filePreview"
            },
            state: {
                selectedRecordId: event.currentTarget.dataset.id
            }
        });
    }
}