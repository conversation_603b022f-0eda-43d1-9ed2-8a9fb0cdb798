/**
 * Created by gluo006 on 9/24/2019.
 */
({
    init:function(component, event, helper){
        var userInfo = $A.get("$SObjectType.CurrentUser");
        if(userInfo.Id){
            var userId = userInfo.Id.substr(0,15);
            component.set('v.userId', userId);
        }

        var action = component.get('c.getUserInfo');
        action.setCallback(this, $A.getCallback(function(response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var ret = response.getReturnValue();
                var data = JSON.parse(ret);
                if (data.profileName.includes('Distributor')) {
                    component.set("v.isDistributor", true);
                }
                component.set("v.isShowWarrantyModel", data.isShowWarrantyModel);

                 if(component.get("v.isDistributor")){
                    var columns = [
                        {label: $A.get("$Label.c.CCM_Portal_OrderNumber"), fieldName:'OrderNumber'},
                        {label: $A.get("$Label.c.CCM_Portal_PONumber"), fieldName: 'PO_No__c'},
                        {label: $A.get("$Label.c.CCM_Portal_SubmitDate"), fieldName: 'Submit_Date__c'},
                        {label: $A.get("$Label.c.CCM_Portal_Status"), fieldName: 'Status'},
                        /*{label: 'Totals',
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${TotalAmount}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },*/
                    ];
                    var claimColumns = [
                        {
                            label: $A.get("$Label.c.CCM_Portal_Action"),
                            width: '80px',
                            children:[
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:preview",
                                    alternativeText:"View",
                                    class: "${viewStyleCss}",
                                    onclick: component.getReference('c.doView')
                                }
                            },
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:edit",
                                    alternativeText:"Edit",
                                    class: "${editStyleCss}",
                                    onclick: component.getReference("c.doEdit")
                                }
                            },
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:delete",
                                    alternativeText:"Delete",
                                    class: "${editStyleCss}",
                                    onclick: component.getReference("c.doDelete")
                                }
                            }
                        ]},
                        {label: $A.get("$Label.c.CCM_Portal_Name"), fieldName:'claim.Name'},
                        // {label: 'Status', fieldName: 'claim.Status__c'},
                        {label: $A.get("$Label.c.CCM_Portal_Status"),
                            children: [
                                {
                                    type: "lightning:formattedText",
                                    attributes:{
                                        value: "${claim.Status__c}"
                                    }
                                },
                                {
                                    type: "lightning:buttonIcon",
                                    attributes:{
                                        value: "${rejectComments}",
                                        variant:"bare",
                                        iconName:"utility:info",
                                        alternativeText:"Reject Comments",
                                        class: "${rejectStypleCss}",
                                        onclick: component.getReference("c.showRejectComments")
                                    }
                                }
                            ]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_PaymentStatus"), fieldName: 'claim.Payment_Status__c'},
                        {label: $A.get("$Label.c.CCM_Portal_LaborCost"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Labor_Cost_Summary__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        /*{label: 'Material Cost',
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Parts_Cost__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: 'Total',
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Total__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },*/
                        {label: $A.get("$Label.c.CCM_Portal_InvoiceNumber"), fieldName: 'claim.Invoice_Item__r.Invoice__r.Name'},
                        {label: $A.get("$Label.c.CCM_Portal_TrackingNumber"), fieldName: 'trackingNumber'},
                        ];
                    component.set('v.columns',columns);
                    component.set('v.claimColumns',claimColumns);
            	}else if(data.companyName == "Hayward Distributing Co"){
                    var columns = [
                        {label: $A.get("$Label.c.CCM_Portal_OrderNumber"), fieldName:'OrderNumber'},
                        {label: $A.get("$Label.c.CCM_Portal_PONumber"), fieldName: 'PO_No__c'},
                        {label: $A.get("$Label.c.CCM_Portal_SubmitDate"), fieldName: 'Submit_Date__c'},
                        {label: $A.get("$Label.c.CCM_Portal_Status"), fieldName: 'Status'},
                        {label: $A.get("$Label.c.CCM_Portal_Totals"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${TotalAmount}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                    ];
                    var claimColumns = [
                        {
                            label: $A.get("$Label.c.CCM_Portal_Action"),
                            width: '80px',
                            children:[
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:preview",
                                    alternativeText:"View",
                                    class: "${viewStyleCss}",
                                    onclick: component.getReference('c.doView')
                                }
                            },
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:edit",
                                    alternativeText:"Edit",
                                    class: "${editStyleCss}",
                                    onclick: component.getReference("c.doEdit")
                                }
                            },
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:delete",
                                    alternativeText:"Delete",
                                    class: "${editStyleCss}",
                                    onclick: component.getReference("c.doDelete")
                                }
                            }
                        ]},
                        {label: $A.get("$Label.c.CCM_Portal_Name"), fieldName:'claim.Name'},
                        // {label: 'Status', fieldName: 'claim.Status__c'},
                        {label: $A.get("$Label.c.CCM_Portal_Status"),
                            children: [
                                {
                                    type: "lightning:formattedText",
                                    attributes:{
                                        value: "${claim.Status__c}"
                                    }
                                },
                                {
                                    type: "lightning:buttonIcon",
                                    attributes:{
                                        value: "${rejectComments}",
                                        variant:"bare",
                                        iconName:"utility:info",
                                        alternativeText:"Reject Comments",
                                        class: "${rejectStypleCss}",
                                        onclick: component.getReference("c.showRejectComments")
                                    }
                                }
                            ]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_PaymentStatus"), fieldName: 'claim.Payment_Status__c'},
                        {label: $A.get("$Label.c.CCM_Portal_LaborCost"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Labor_Cost_Summary__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_MaterialCost"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Parts_Cost__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_Total"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Total__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_InvoiceNumber"), fieldName: 'claim.Invoice_Item__r.Invoice__r.Name'},
                        {label: $A.get("$Label.c.CCM_Portal_ServicePartner"), fieldName:'claim.Service_Partner__r.Name'},
                     ];
            		component.set('v.columns',columns);
                    component.set('v.claimColumns',claimColumns);
                }else{
                    var columns = [
                        {label: $A.get("$Label.c.CCM_Portal_OrderNumber"), fieldName:'OrderNumber'},
                        {label: $A.get("$Label.c.CCM_Portal_PONumber"), fieldName: 'PO_No__c'},
                        {label: $A.get("$Label.c.CCM_Portal_SubmitDate"), fieldName: 'Submit_Date__c'},
                        {label: $A.get("$Label.c.CCM_Portal_Status"), fieldName: 'Status'},
                        {label: $A.get("$Label.c.CCM_Portal_Totals"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${TotalAmount}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                    ];
                    var claimColumns = [
                        {
                            label: $A.get("$Label.c.CCM_Portal_Action"),
                            width: '80px',
                            children:[
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:preview",
                                    alternativeText:"View",
                                    class: "${viewStyleCss}",
                                    onclick: component.getReference('c.doView')
                                }
                            },
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:edit",
                                    alternativeText:"Edit",
                                    class: "${editStyleCss}",
                                    onclick: component.getReference("c.doEdit")
                                }
                            },
                            {
                                type: "lightning:buttonIcon",
                                attributes:{
                                    value: "${claim.Id}",
                                    variant:"bare",
                                    iconName:"utility:delete",
                                    alternativeText:"Delete",
                                    class: "${editStyleCss}",
                                    onclick: component.getReference("c.doDelete")
                                }
                            }
                        ]},
                        {label: $A.get("$Label.c.CCM_Portal_Name"), fieldName:'claim.Name'},
                        // {label: 'Status', fieldName: 'claim.Status__c'},
                        {label: $A.get("$Label.c.CCM_Portal_Status"),
                            children: [
                                {
                                    type: "lightning:formattedText",
                                    attributes:{
                                        value: "${claim.Status__c}"
                                    }
                                },
                                {
                                    type: "lightning:buttonIcon",
                                    attributes:{
                                        value: "${rejectComments}",
                                        variant:"bare",
                                        iconName:"utility:info",
                                        alternativeText:"Reject Comments",
                                        class: "${rejectStypleCss}",
                                        onclick: component.getReference("c.showRejectComments")
                                    }
                                }
                            ]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_PaymentStatus"), fieldName: 'claim.Payment_Status__c'},
                        {label: $A.get("$Label.c.CCM_Portal_LaborCost"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Labor_Cost_Summary__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_MaterialCost"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Parts_Cost__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_Total"),
                            children:[
                            {
                                type: "lightning:formattedNumber",
                                attributes:{
                                    value: "${claim.Total__c}",
                                    currencyCode: $A.get("$Locale.currencyCode"),
                                    currencyDisplayAs:"code",
                                    style:"currency"
                                }
                            }]
                        },
                        {label: $A.get("$Label.c.CCM_Portal_InvoiceNumber"), fieldName: 'claim.Invoice_Item__r.Invoice__r.Name'},
                        {label: $A.get("$Label.c.CCM_Portal_TrackingNumber"), fieldName: 'trackingNumber'},
                    ];
            component.set('v.columns',columns);
            component.set('v.claimColumns',claimColumns);
        }


                //get all the info
                helper.getInit(component, 'claim');
                //get document
                helper.getDocuments(component);
                helper.getBannerDocument(component);
            }
        }));
		$A.enqueueAction(action);
    },
    doView: function(component, event){
        var id = event.getSource().get('v.value');
        var url = '/s/warrantyclaim?recordId=' + id + '&view=true';
        window.open(url);
    },
    doEdit: function(component, event){
        var id = event.getSource().get('v.value');
        var url = '/s/warrantyclaim?recordId=' + id + '&edit=true';
        window.open(url);
    },
    doDelete: function(component, event, helper) {
        var claimId = event.getSource().get('v.value');
        helper.doDeleteClaim(component, claimId);
    },
    showRejectComments: function(component, event, helper) {
        let comments = event.getSource().get('v.value');
        component.set("v.isShowRejectComments", false);
        component.set("v.rejectedComments", comments);

        setTimeout(()=>{
            let box = event.target.getBoundingClientRect();
            let scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            let clientTop = document.documentElement.clientTop || document.body.clientTop;
            component.set("v.isShowRejectComments", true);
            let elementHeight = document.querySelector('#help').clientHeight;
            console.log(elementHeight);
            component.set("v.tooltipTop", box.top + scrollTop - clientTop - elementHeight/2 - 15);
            component.set("v.tooltipLeft", box.left + 8);
        }, 100);
    },
    closeTooltip: function(component, event) {
        component.set("v.isShowRejectComments", false);
    },
    handleChange: function(component, event, helper){
        var selected = component.get("v.tabId");
        component.set('v.tabId', selected);
        if(selected == 'Claim'){
            helper.getInit(component, 'claim')
        }else{
            helper.getInit(component, 'order')
        }
    },
    openDocument: function(component, event, helper){
        var recordId = event.currentTarget.getAttribute('data-fileId');
        $A.get('e.lightning:openFiles').fire({
            recordIds: [recordId]
        });
    },
    pageChange : function(component, event, helper) {
        var pageNumber = event.getParam("pageNumber");
        console.log('PageNumber' + pageNumber);
        if(component.get('v.tabId') == 'Claim'){
            component.set("v.claimPageNumber",pageNumber);
            helper.getInit(component, 'claim');
        }
        if(component.get('v.tabId') == 'order'){
            component.set("v.orderPageNumber",pageNumber);
            helper.getInit(component, 'order');
        }
        event.stopPropagation();
    },
    pageCountChange : function(component, event, helper){
        var pageCount = event.getParam("pageCount");
        console.log('pageCount' + pageCount);
        if(component.get('v.tabId') == 'Claim'){
            component.set("v.claimPageCount",pageCount);
            helper.getInit(component, 'claim');
        }
        if(component.get('v.tabId') == 'order'){
            component.set("v.orderPageCount",pageCount);
            helper.getInit(component, 'order');
        }
        component.set("v.pageCount",pageCount);
        component.set("v.claimPageNumber", 1);
        component.set("v.orderPageNumber", 1);
        helper.getInit(component,event, helper);
        event.stopPropagation();
    },
    openImageDocument : function(component, event, helper){
        var recordId = component.get('v.bannerDocument');
        $A.get('e.lightning:openFiles').fire({
            recordIds: [recordId]
        });
    }
})