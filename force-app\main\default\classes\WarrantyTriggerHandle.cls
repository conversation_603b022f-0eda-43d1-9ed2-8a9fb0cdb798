/**
* @Author: <PERSON>
* @Date: 2017-12-11 13:08:07
* @Description:  Warranty trigger handle class
* @Test_Class: WarrantyTriggerTest
* @Related_Class: 
---------------------------------------------------
* @Last_Modified_by:    Zack
* @Last_Modified_time:  2019-03-14
* @Modifiy_Purpose:     删除Warranty之后，WarrantyItem上面的触发器不执行。解决办法，在Warranty的触发器中查询WarrantyItem，便可执行
*/
public without sharing class WarrantyTriggerHandle {

    /**
     *
     * @Function: 将客户的warranty从纵向展示换成横向展示——将多个warranty和一个客户存在一条记录中（customer Product）
     *
    */
    public class ToCustomProduct implements Triggers.Handler {
        public void handle() {
            List<Warranty__c> warrantyList = (List<Warranty__c>)Trigger.new;

            //获取warranty相关account的Id集合
            Set<String> set_accids = new Set<String>();
            for(Warranty__c wt : warrantyList) {
                set_accids.add(wt.AccountCustomer__c);
            }

            //通过account的Id 集合，获取其相关的customer product,并创建建立account和customer product的关系map
            Map<Id,Customer_Product__c> productmap = new Map<Id,Customer_Product__c>();
            for(Customer_Product__c cp:[SELECT Id,Customer__c,Warranty1__c,Warranty2__c,Warranty3__c,Warranty4__c
                                        FROM Customer_Product__c
                                        WHERE Customer__c IN:set_accids]) {
                productmap.put(cp.Customer__c, cp);
            }


            for(Warranty__c wt : warrantyList) {
                //控制account下只有一个customer product
                if(!productmap.containsKey(wt.AccountCustomer__c)) {
                    productmap.put(wt.AccountCustomer__c, new Customer_Product__c(Customer__c=wt.AccountCustomer__c));
                }

                Customer_Product__c cp = productmap.get(wt.AccountCustomer__c);
                if(cp.Warranty1__c == null) {
                    cp.Warranty1__c = wt.Id;
                    continue;
                }

                if(cp.Warranty2__c == null) {
                    cp.Warranty2__c = wt.Id;
                    continue;
                }

                if(cp.Warranty3__c == null) {
                    cp.Warranty3__c = wt.Id;
                    continue;
                }

                if(cp.Warranty4__c == null) {
                    cp.Warranty4__c = wt.Id;
                    continue;
                }
            }
            try {
                Database.upsert((List<Customer_Product__c>)productmap.values());
            }
            catch(Exception ex){}
        }
    }

    /**
     *
     * @Function: 文本类型的购买地，同步给选项列表的购买地字段
     *
    */
    public class UnifyPlaceOfPurchase implements Triggers.Handler {
        public void handle() {
            List<Warranty__c> warrantyList = (List<Warranty__c>)Trigger.new;
            Map<Id,Warranty__c> warrantyOldMap;
            if(Trigger.isUpdate) {
                warrantyOldMap = (Map<Id,Warranty__c>)Trigger.oldMap;
            }

            //add by mike 20180410 for 当网站同步过来的购买地为Place_of_Purchase_picklist__c选项列表中的值时，直接复制到Place_of_Purchase_picklist__c上，
            //否则置为“Other”
            Set<String> placePurchaseValueSet = QueryUtils.getPicklistValues('Warranty__c', 'Place_of_Purchase_picklist__c');

            for(Warranty__c wt : (List<Warranty__c>)Trigger.new) {
                Warranty__c warrantyOld;
                if(Trigger.isUpdate) {
                    warrantyOld = warrantyOldMap.get(wt.Id);
                    if(wt.Place_of_Purchase_picklist__c == null && wt.Place_of_Purchase__c != null && wt.Place_of_Purchase__c != '') {
                        if(placePurchaseValueSet.contains(wt.Place_of_Purchase__c)) {
                            wt.Place_of_Purchase_picklist__c = wt.Place_of_Purchase__c;
                        } else if(wt.Place_of_Purchase__c.equalsIgnoreCase('Home_depot')){
                            wt.Place_of_Purchase_picklist__c = 'Home Depot';
                        } else if(wt.Place_of_Purchase__c.equalsIgnoreCase('Amazon')) {
                            wt.Place_of_Purchase_picklist__c = 'Amazon LLC';
                        }else if(wt.Place_of_Purchase__c.equalsIgnoreCase('Ace Hardware')) {
                            wt.Place_of_Purchase_picklist__c = 'ACE';
                        } else {
                            wt.Place_of_Purchase_picklist__c = 'Other';
                        }
                    }
                } else {
                    if(wt.Place_of_Purchase_picklist__c == null && wt.Place_of_Purchase__c != null && wt.Place_of_Purchase__c != '') {
                        if(placePurchaseValueSet.contains(wt.Place_of_Purchase__c)) {
                            wt.Place_of_Purchase_picklist__c = wt.Place_of_Purchase__c;
                        } else if(wt.Place_of_Purchase__c.equalsIgnoreCase('Home_depot')){
                            wt.Place_of_Purchase_picklist__c = 'Home Depot';
                        } else if(wt.Place_of_Purchase__c.equalsIgnoreCase('Amazon')) {
                            wt.Place_of_Purchase_picklist__c = 'Amazon LLC';
                        }else if(wt.Place_of_Purchase__c.equalsIgnoreCase('Ace Hardware')) {
                            wt.Place_of_Purchase_picklist__c = 'ACE';
                        } else {
                            wt.Place_of_Purchase_picklist__c = 'Other';
                        }
                    }
                }
            }
        }
    }

    // add by Zack on 2019-03-14，删除Warranty后，同步删除Heroku中数据
    public class SyncToHerokuBeforeDelete implements Triggers.Handler {
        public void handle() {
            System.debug('----WarrantyTriggerHandle SyncToHerokuAfterDelete');
            Map<Id, Warranty__c> warrantyMap = (Map<Id, Warranty__c>) Trigger.oldMap;

            // Warranty id集合
            Set<Id> warrantyIdSet = warrantyMap.keySet();

            // 根据Warranty id查询Warranty下的WarrantyItem
            List<Id> warrantyItemIdList = new List<Id>();
            for (Warranty_Item__c eachWarItem : [SELECT Id, Product_Code__c, Serial_Number__c, 
                                                    Warranty__r.AccountCustomer__c
                                                 FROM Warranty_Item__c
                                                 WHERE Warranty__c IN :warrantyIdSet]) {
                warrantyItemIdList.add(eachWarItem.Id);
            }

            // 调用WarrantyItemTriggerHandle方法删除Heroku中数据
            WarrantyItemTriggerHandle.syncToHerokku(warrantyItemIdList, 'delete');
        }
    }

    //START:Add by John Jiang on 2019-07-17 update warranty item, when edit warranty
    public class DeleteDuplicateWarrantyItems implements Triggers.Handler {
        public void handle() {
            Map<Id,Warranty__c> warrantyNewMap = (Map<Id, Warranty__c>) Trigger.newMap;
            Map<Id,Warranty__c> warrantyOldMap = (Map<Id, Warranty__c>) Trigger.oldMap;
            List<Warranty_Item__c> warrantyItemList = new List<Warranty_Item__c>();
            for(Warranty_Item__c wiLst : [SELECT Id,Warranty__c,Serial_Number__c,Product_Code__c,
                                                Expiration_Date_New__c,Counting_Process__c,Store_Policy_Date__c
                                            FROM Warranty_Item__c 
                                            WHERE Warranty__c IN: warrantyNewMap.keySet()]){
                warrantyItemList.add(wiLst);
            }

            List<Warranty_Item__c> deleteOldWarrantyItem = new List<Warranty_Item__c>();
            for(Warranty__c warranty : warrantyNewMap.values()){
                if(warrantyOldMap.get(warranty.Id) != null && warranty.Master_Product__c != warrantyOldMap.get(warranty.Id).Master_Product__c){
                    for(Warranty_Item__c wi : warrantyItemList){
                        if(wi.Warranty__c == warranty.Id){
                            deleteOldWarrantyItem.add(wi);
                        }
                    }
                    
                }
            }

            DELETE deleteOldWarrantyItem;
        }  
    }
    //END:Add by John Jiang on 2019-07-17 update warranty item, when edit warranty
}