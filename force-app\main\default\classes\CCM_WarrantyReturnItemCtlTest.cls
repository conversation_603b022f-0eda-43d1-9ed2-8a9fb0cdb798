@IsTest
public class CCM_WarrantyReturnItemCtlTest {
    @TestSetup
    static void makeData(){
        Test.startTest();
        // customer info
        String recordId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName = 'Channel' LIMIT 1].Id;
        Account account = new Account();
        account.Name = 'TestCustomer';
        account.Distributor_or_Dealer__c = '2nd Tier Dealer';
        account.RecordTypeId = recordId;
        account.AccountNumber = '12345';
        insert account;
        
        Contact contact = new Contact();
        contact.AccountId = account.Id;
        contact.LastName = 'xxx';
        contact.FirstName = 'YYY';
        contact.OwnerId = UserInfo.getUserId();
        insert contact;

        // get user profile info
        Profile userProfile = [SELECT Id, Name FROM Profile WHERE Id = :UserInfo.getProfileId()];
        
        Account_Address__c shippingAddress = new Account_Address__c();
        shippingAddress.Customer__c = account.Id;
        shippingAddress.X2nd_Tier_Dealer__c = account.Id;
        shippingAddress.Active__c = true;
        shippingAddress.Approval_Status__c = 'Approved';
        shippingAddress.Contact__c = contact.Id;
        shippingAddress.RecordTypeId = CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID;

        Account_Address__c billingAddress = new Account_Address__c();
        billingAddress.Customer__c = account.Id;
        billingAddress.X2nd_Tier_Dealer__c = account.Id;
        billingAddress.Active__c = true;
        billingAddress.Approval_Status__c = 'Approved';
        billingAddress.Contact__c = contact.Id;
        billingAddress.RecordTypeId = CCM_Constants.ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_ID;

        insert new List<Account_Address__c> {shippingAddress, billingAddress};

        // pricebook info
        Pricebook2 pb = new Pricebook2();
        pb.Name = 'Standard Price Book';
        pb.IsActive = true;
        pb.Contract_Price_Book_OracleID__c = '11';
        pb.Price_Book_OracleID__c = '11';
        insert pb;
        
        Product2 pro = new Product2();
        pro.Name = 'TestProduct111';
        pro.Brand_Name__c = 'EGO';
        pro.ProductCode = '1234567';
        pro.IsActive = true;
        pro.Source__c = 'PIM';
        pro.Description = pro.SF_Description__c;
        insert pro;

        Product2 pro2 = new Product2();
        pro2.Name = 'TestProduct222';
        pro2.Brand_Name__c = 'EGO';
        pro2.ProductCode = '1234567';
        pro2.IsActive = true;
        pro2.Source__c = 'EBS';
        insert pro2;
        pro2.Description = pro2.SF_Description__c;
        update pro2;

        // pricebookEntry info
        PricebookEntry pbe = new PricebookEntry();
        pbe.IsActive = true;
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 1000;
        pbe.Pricebook2Id = pb.Id;
        pbe.UseStandardPrice = false;
        insert pbe;

        // authorized brand info
        Sales_Program__c salesProgram = new Sales_Program__c();
        salesProgram.Customer__c = account.Id;
        salesProgram.Approval_Status__c = 'Approved';
        salesProgram.Price_Book__c = pb.Id;
        salesProgram.Authorized_Brand_Name_To_Oracle__c = 'EGO_2020_Test';
        salesProgram.RecordTypeId = CCM_Contants.SALES_STANDARD_PROGRAM_RECORDTYPEID;
        salesProgram.Brands__c = 'EGO';
        insert salesProgram;

        Invoice__c invoice = new Invoice__c();
        invoice.Customer__c = account.Id;
        invoice.Invoice_Number__c = '112233';
        invoice.Total_Due__c = 100;
        insert invoice;

        Warranty_Return_Claim__c objWarrantyReturnClaim = new Warranty_Return_Claim__c();
        objWarrantyReturnClaim.Customer__c = account.Id;
        objWarrantyReturnClaim.Name = 'RA00001111';
        objWarrantyReturnClaim.Chervon_or_Warehouse_Purchase__c = 'Chervon';
        objWarrantyReturnClaim.Approved_Date__c = Date.today();
        objWarrantyReturnClaim.Contact_Name__c = 'test';
        objWarrantyReturnClaim.Contact_Phone_Number__c = '********';
        objWarrantyReturnClaim.Contact_Email_Address__c = '<EMAIL>';
        objWarrantyReturnClaim.Customer_Reference_Number__c = '************';
        objWarrantyReturnClaim.Notes__c = 'test notes';
        objWarrantyReturnClaim.Billing_Address__c = billingAddress.Id;
        objWarrantyReturnClaim.Shipping_Address__c = shippingAddress.Id;
        objWarrantyReturnClaim.Warehouse_Location__c = shippingAddress.Id;
        objWarrantyReturnClaim.Store_Location__c = shippingAddress.Id;
        objWarrantyReturnClaim.Store_Address__c = shippingAddress.Id;
        objWarrantyReturnClaim.Is_Alternative_Address__c = false;
        objWarrantyReturnClaim.Is_Alternative_Store_Address__c = false;
        objWarrantyReturnClaim.Payment_Method__c = 'Credit Memo';
        objWarrantyReturnClaim.Debit_Memo_Number__c = invoice.Id;
        objWarrantyReturnClaim.IsFinanceApproved__c = false;
        insert objWarrantyReturnClaim;

        Warranty_Return_Claim_Item__c objWarrantyItem = new Warranty_Return_Claim_Item__c();
        objWarrantyItem.Warranty_Return_Claim__c = objWarrantyReturnClaim.Id;
        objWarrantyItem.Model__c = pro.Id;
        objWarrantyItem.Quantity__c = 1;
        objWarrantyItem.DIF_RTV__c = 'DIF';
        objWarrantyItem.Serial_Number__c = '12121212121';
        objWarrantyItem.End_Consumer_Purchase_Date__c = Date.newInstance(2022, 11, 22);
        objWarrantyItem.End_Consumer_Return_Date__c = Date.newInstance(2022, 11, 25);
        objWarrantyItem.Index__c = '0';
        insert objWarrantyItem;

        
        /*Warranty_Return_Claim_Item_Attachment__c attachment = new Warranty_Return_Claim_Item_Attachment__c();
        attachment.Attachment_Type__c = 'End Customer Purchase Date';
        attachment.Warranty_Return_Claim__c = objWarrantyReturnClaim.Id;
        attachment.Warranty_Return_Claim_Item__c = objWarrantyItem.Id;
        insert attachment;

        Warranty_Return_Claim_Item_Attachment__c attachment1 = new Warranty_Return_Claim_Item_Attachment__c();
        attachment1.Attachment_Type__c = 'End Customer Return Date';
        attachment1.Warranty_Return_Claim__c = objWarrantyReturnClaim.Id;
        attachment1.Warranty_Return_Claim_Item__c = objWarrantyItem.Id;
        insert attachment1;

        Warranty_Return_Claim_Item_Attachment__c attachment2 = new Warranty_Return_Claim_Item_Attachment__c();
        attachment2.Attachment_Type__c = 'Return Reason';
        attachment2.Warranty_Return_Claim__c = objWarrantyReturnClaim.Id;
        attachment2.Warranty_Return_Claim_Item__c = objWarrantyItem.Id;
        insert attachment2;*/

        Warranty_Return_Policy__c objPolicy = new Warranty_Return_Policy__c();
        objPolicy.Name = 'test policy';
        objPolicy.Active_Date__c = Date.today() - 1;
        objPolicy.Expiry_Date__c = Date.today();
        objPolicy.Approval_Status__c = 'Approved';
        objPolicy.DIF_RTV_Threshold__c = 100;
        insert objPolicy;

        Warranty_Return_Applicable_Customer__c objWarrantyReturnApplicableCustomer = new Warranty_Return_Applicable_Customer__c();
        objWarrantyReturnApplicableCustomer.Warranty_Return_Policy__c = objPolicy.Id;
        objWarrantyReturnApplicableCustomer.Customer__c = account.Id;
        insert objWarrantyReturnApplicableCustomer;

        Test.stopTest();
    }

    @isTest
    static void testGetBillingShipping() {
        String accountNumber = [SELECT Id, AccountNumber FROM Account LIMIT 1].AccountNumber;
        String billto = '';
        String shipto = '';
        CCM_WarrantyReturnItemCtl.getBillingShipping(accountNumber, billto, shipto);
    }

    @isTest
    static void testInsertWarrantyReturnItem() {
        String warrantyReturnItemStr = '[{"line":1,"accountNumber":"0376","contactEmailAddress":"<EMAIL>;<EMAIL>","billTo":"BAWB-********","shipTo":"BAWB-********","paymentMethod":"Deduction","debitMemoNumber":"OCQ260672733","productCode":"BA5600T","invoicePrice":100,"quantity":1,"DIFRTV":"RTV","returnReason":"Defective"},{"productCode":"CHX5500","invoicePrice":100,"quantity":2,"DIFRTV":"DIF","returnReason":"Customer Satisfaction"}]';
        CCM_WarrantyReturnItemCtl.insertWarrantyReturnItem(warrantyReturnItemStr);
    }
}