public without sharing class Util {

    /**
     * isNullOrBlank
     * @description
     * @param integer, decimal, string, date, datetime, time, blob, double, id, long
     * @return boolean true if input null or blank
     */
    public static boolean isNullOrBlank (integer input) {return (input==null)?true:false;}
    public static boolean isNullOrBlank (decimal input) {return (input==null)?true:false;}
    public static boolean isNullOrBlank (string input)  {return (input==null || input.trim().length()==0)?true:false;}
    public static boolean isNullOrBlank (date input)    {return (input==null)?true:false;}
    public static boolean isNullOrBlank (datetime input){return (input==null)?true:false;}
    public static boolean isNullOrBlank (time input)    {return (input==null)?true:false;}
    public static boolean isNullOrBlank (blob input)    {return (input==null || input.size()==0)?true:false;}
    public static boolean isNullOrBlank (double input)  {return (input==null)?true:false;}
    public static boolean isNullOrBlank (id input)      {return (input==null)?true:false;}
    public static boolean isNullOrBlank (long input)    {return (input==null)?true:false;}
    public static boolean isNullOrBlank (boolean input) {return (input==null)?true:false;}

    /**
     * fixNull
     * @description
     * @param integer, decimal, string, date, datetime, time, blob, double, id, long
     * @return integer, decimal, double, long inputs return 0
     * @return string input returns '' (empty string)
     * @return date returns system.today()
     * @return datetime returns system.now()
     * @return id inputs returns null
     * @return time input returns 0:00:00.0
     * @return blob input returns blob.valueof('') (empty string)
     */
    public static integer fixNull (integer input)   {return (isNullOrBlank(input))?0:input;}
    public static decimal fixNull (decimal input)   {return (isNullOrBlank(input))?0:input;}
    public static string fixNull (string input)     {return (isNullOrBlank(input))?'':input;}
    public static date fixNull (date input)         {return (isNullOrBlank(input))?system.today():input;}
    public static datetime fixNull (datetime input) {return (isNullOrBlank(input))?system.now():input;}
    public static time fixNull (time input)         {return (isNullOrBlank(input))?time.newinstance(0,0,0,0):input;}
    public static blob fixNull (blob input)         {return (isNullOrBlank(input))?blob.valueof(''):input;}
    public static double fixNull (double input)     {return (isNullOrBlank(input))?0:input;}
    public static id fixNull (id input)             {return (isNullOrBlank(input))?null:input;} //can't fix a null id
    public static long fixNull (long input)         {return (isNullOrBlank(input))?0:input;}
    public static boolean fixNull (boolean input)   {return (isNullOrBlank(input))?false:input;}

    /**
     * validateEmail
     * @description
     * @param string
     * @return true if valid email, false if not
     */
    public static Boolean validateEmail(string email) {
        if ( !isNullOrBlank(email) ) {
            String pat = '[a-zA-Z0-9\\.\\!\\#\\$\\%\\&\\*\\/\\=\\?\\^\\_\\+\\-\\`\\{\\|\\}\\~\'._%+-]+@[a-zA-Z0-9\\-.-]+\\.[a-zA-Z]+';
            Boolean test = Pattern.matches(pat, email);
            return test;
        } else {
            return false;
        }
    }

    /**
     * getUSStateListOptions
     * @description
     * @return list<SelectOption> with state abbreviations (USA)
     */
    public static list<selectoption> getUSStateListOptions(){
        list<SelectOption> statelist = new list<Selectoption>();
        statelist.add(new selectoption('','--'));
        statelist.add(new selectoption('AL','AL'));
        statelist.add(new selectoption('AK','AK'));
        statelist.add(new selectoption('AZ','AZ'));
        statelist.add(new selectoption('AR','AR'));
        statelist.add(new selectoption('CA','CA'));
        statelist.add(new selectoption('CO','CO'));
        statelist.add(new selectoption('CT','CT'));
        statelist.add(new selectoption('DE','DE'));
        statelist.add(new selectoption('FL','FL'));
        statelist.add(new selectoption('GA','GA'));
        statelist.add(new selectoption('HI','HI'));
        statelist.add(new selectoption('ID','ID'));
        statelist.add(new selectoption('IL','IL'));
        statelist.add(new selectoption('IN','IN'));
        statelist.add(new selectoption('IA','IA'));
        statelist.add(new selectoption('KS','KS'));
        statelist.add(new selectoption('KY','KY'));
        statelist.add(new selectoption('LA','LA'));
        statelist.add(new selectoption('ME','ME'));
        statelist.add(new selectoption('MD','MD'));
        statelist.add(new selectoption('MA','MA'));
        statelist.add(new selectoption('MI','MI'));
        statelist.add(new selectoption('MN','MN'));
        statelist.add(new selectoption('MS','MS'));
        statelist.add(new selectoption('MO','MO'));
        statelist.add(new selectoption('MT','MT'));
        statelist.add(new selectoption('NE','NE'));
        statelist.add(new selectoption('NV','NV'));
        statelist.add(new selectoption('NH','NH'));
        statelist.add(new selectoption('NJ','NJ'));
        statelist.add(new selectoption('NM','NM'));
        statelist.add(new selectoption('NY','NY'));
        statelist.add(new selectoption('NC','NC'));
        statelist.add(new selectoption('ND','ND'));
        statelist.add(new selectoption('OH','OH'));
        statelist.add(new selectoption('OK','OK'));
        statelist.add(new selectoption('OR','OR'));
        statelist.add(new selectoption('PA','PA'));
        statelist.add(new selectoption('RI','RI'));
        statelist.add(new selectoption('SC','SC'));
        statelist.add(new selectoption('SD','SD'));
        statelist.add(new selectoption('TN','TN'));
        statelist.add(new selectoption('TX','TX'));
        statelist.add(new selectoption('UT','UT'));
        statelist.add(new selectoption('VT','VT'));
        statelist.add(new selectoption('VA','VA'));
        statelist.add(new selectoption('WA','WA'));
        statelist.add(new selectoption('WV','WV'));
        statelist.add(new selectoption('WI','WI'));
        statelist.add(new selectoption('WY','WY'));
        return statelist;
    }

    /**
     * getSchemaPicklist
     * @description
     * @param string objectname, string fieldname
     * @return list<SelectOption> List of selectoptions from the specified object's picklist field
     */
    public static list<SelectOption> getSchemaPicklist(string objectname, string fieldname){
        list<SelectOption> selectoptionslist = new list<SelectOption>();
        selectoptionslist.add(new selectoption('', '--Select One--'));
        for (Schema.Picklistentry ple : schema.getGlobalDescribe().get(objectname.toLowerCase()).getDescribe().fields.getMap().get(fieldname.toLowerCase()).getDescribe().getPickListValues()) {
            selectoptionslist.add(new selectoption(ple.getvalue(), ple.getlabel()));
        }
        return selectoptionslist;
    }

    /**
     * reverseList
     * @description Takes an input of a list of sObject, and reverses their order.
     * @param list<sObject> input
     * @return list<sObject> reversed list
     */
    public static list<string> reverseList (list<string> input) {
        list<string> output = new list<string>();
        for (integer i=input.size()-1; i>=0; i--) {
            output.add(input[i]);
        }
        return output;
    }

    /**
     * saveCookie
     * @description
     * @param string name
     * @param string cookieval
     * @param integer ttl
     * @param boolean h24
     */
    public static void saveCookie (string name, string cookieval, integer ttl, boolean h24) {
        Cookie cook = new Cookie(name,cookieval,null,((h24)?86400:ttl),false);
        Apexpages.currentPage().setcookies(new list<cookie>{cook});
    }

    /**
     * loadCookie
     * @description
     * @param string name
     * @return string cookieValue or 'Not Found'
     */
    public static string loadCookie (string name){
        return (apexpages.currentPage().getCookies().get(name) != null)?apexpages.currentPage().getCookies().get(name).getValue():'Not Found';
    }

    /**
     * queryAllFields
     * @description Returns list of query results for the specified object with all of its fields, filtered
     * @param string objectname
     * @param string filtersLimits
     * @return list<sObject>
     */
    public static list<sObject> queryAllFields (string objectName, string filtersLimits) {
        string queryString = 'Select ';
        for (string s : schema.getGlobalDescribe().get(objectname.toLowerCase()).getDescribe().fields.getMap().keySet()){
            if(s.contains('otheraddress')||s.contains('mailingaddress'))continue;
            queryString += s+', ';
        }
        queryString = queryString.trim();
        queryString = queryString.substring(0,queryString.length()-1);
        queryString+= ' FROM '+objectname;
        if (filtersLimits != null) queryString+= ' where '+filtersLimits;

        return database.query(queryString);
    }

    public static string twoDigitIntegerString(integer input){
        if (input < 10){
            return '0'+input;
        }
        else return string.valueof(input);
    }

    public static boolean isSandbox(){
        string temp = URL.getSalesforceBaseUrl().toExternalForm();
        temp = temp.substringbefore('.force.com');
        boolean sandbox = (temp.contains('.cs'))?true:false;
        return sandbox;
    }

    public static Boolean isSandboxBasedOnUser(){
        String orgId = UserInfo.getOrganizationId();
        Organization org = [SELECT Id, IsSandbox FROM Organization WHERE Id = :orgId];
        return org.IsSandbox;
    }

    public static void pagemsg(string severity, string msg){
        if (severity == 'info') apexpages.addmessage(new apexpages.message(apexpages.severity.info, msg));
        else if (severity == 'error') apexpages.addmessage(new apexpages.message(apexpages.severity.error, msg));
        else if (severity == 'confirm') apexpages.addmessage(new apexpages.message(apexpages.severity.confirm, msg));
    }

    //add by mike
    // 通过sObjectName和指定字段获取PickList的value值
    public static List<String> getPicklistValues(String sObjectName, String sFieldName)
    {
        List<String> valueList = new List<String>();
        Map<String, object> picValues= new Map<String, object>();
        //获取所有的对象
        Map<String, Schema.SObjectType> sObjectDescribeMap = Schema.getGlobalDescribe();
        if (sObjectDescribeMap.containsKey(sObjectName))
        {
            Map<String, Schema.SobjectField> sObjectFieldsMap =
               sObjectDescribeMap.get(sObjectName).getDescribe().fields.getMap();
            if (sObjectFieldsMap.containsKey(sFieldName))
            {
                Schema.DescribeFieldResult sObjectFieldDescribe = sObjectFieldsMap.get(sFieldName).getDescribe();
                List<Schema.PicklistEntry> lPickEntryList = sObjectFieldDescribe.getPicklistValues();
                for (Schema.PicklistEntry tPicklistEntry : lPickEntryList)
                {
                    valueList.add(String.valueOf(tPicklistEntry.getValue()));
                }
            } else
            {
                //TODO 不存在此字段的执行
            }
        } else
        {
            // TODO 不存在此sObject的执行
        }
        return valueList;
    }

      // 通过sObjectName和指定字段获取PickList的label值
    public static List<String> getPicklistlabel(String sObjectName, String sFieldName)
    {
        List<String> valueList = new List<String>();
        Map<String, object> picValues= new Map<String, object>();
        //获取所有的对象
        Map<String, Schema.SObjectType> sObjectDescribeMap = Schema.getGlobalDescribe();
        if (sObjectDescribeMap.containsKey(sObjectName))
        {
            Map<String, Schema.SobjectField> sObjectFieldsMap =
               sObjectDescribeMap.get(sObjectName).getDescribe().fields.getMap();
            if (sObjectFieldsMap.containsKey(sFieldName))
            {
                Schema.DescribeFieldResult sObjectFieldDescribe = sObjectFieldsMap.get(sFieldName).getDescribe();
                List<Schema.PicklistEntry> lPickEntryList = sObjectFieldDescribe.getPicklistValues();
                for (Schema.PicklistEntry tPicklistEntry : lPickEntryList)
                {
                    valueList.add(String.valueOf(tPicklistEntry.getLabel()));
                }
            } else
            {
                //TODO 不存在此字段的执行
            }
        } else
        {
            // TODO 不存在此sObject的执行
        }
        return valueList;
    }

    //返回一个标准记录的页面
    public static PageReference returnPage(ID strid){
        String baseUrl=String.valueOf(System.Url.getSalesforceBaseUrl());
        baseUrl=baseUrl.substring(baseUrl.indexOf('=')+1,baseUrl.length()-1);
        baseUrl=baseUrl+'/'+strid;
        PageReference pr=new PageReference(baseUrl);
        return pr;
    }

    //将年月字符串转换为日期类型  例： 2017-01(String 类型)  ——> 2017-01-01(Date 类型)
    public static Date valueOfDate(String dateStr){
        String[] dateStrList = dateStr.split('-');
        return Date.newInstance(Integer.valueOf(dateStrList[0]), Integer.valueOf(dateStrList[1]),1);
    }

    public class ApprovalException extends Exception {}

    public static Boolean doApprovalAction(Id targetObjectId, String action, String comment){
        // Instantiate the new ProcessWorkitemRequest object and populate it
        Approval.ProcessWorkItemRequest req =
            new Approval.ProcessWorkItemRequest();
        req.setComments(comment);
        req.setAction(action);

        List<ProcessInstanceWorkitem> items = [
            SELECT Id, OriginalActorId FROM ProcessInstanceWorkitem
            WHERE ProcessInstance.Status = 'Pending' AND ProcessInstance.TargetObjectId = :targetObjectId LIMIT 1
        ];

        ProcessInstanceWorkitem itemToDeal;
        if(items != null && items.size() > 0){
            System.debug(LoggingLevel.INFO, '*** items: ' + jSON.serialize(items));
            for(ProcessInstanceWorkitem item : items){
                if(item.OriginalActorId.equals(UserInfo.getUserId())){
                    itemToDeal = item;
                }
            }
            if(itemToDeal == null){
                itemToDeal = items[0];
            }
        }else{
            return false;
        }
        // Approve the request
        req.setWorkitemId(itemToDeal.Id);
        Approval.ProcessResult result =  Approval.process(req);
        // If approval failed, throw the exception
        if (!result.isSuccess()) {
            List<String> errorMessages = new List<String>();
            for (Database.Error err : result.getErrors()) {
                errorMessages.add(err.getMessage());
            }

            if (errorMessages.size() > 0) {
                throw new ApprovalException(String.join(errorMessages, ';'));
            }
        }
        return result.isSuccess();
    }

    //Do submit the approval process
    public static Boolean doSubmitAction(Id targetObjectId, String comment) {
        Approval.ProcessSubmitRequest request = new Approval.ProcessSubmitRequest();
        comment = String.isBlank(comment) ? 'Submit pending approval' : Comment;
        request.setComments(comment);
        request.setObjectId(targetObjectId);
        Approval.ProcessResult result = Approval.process(request);
        if (result.isSuccess() && !Test.isRunningTest()) {
            System.debug('Submitted for approval successfully: '+result.isSuccess());
       }
        return result.isSuccess();
    }

    //Get Sobject RecordId
    public static Map<String, String> getSobjectRecordTypeIdMap(String sobjectName) {
        Map<String, String> returnMap = new  Map<String, String>();
        for (RecordType rt : [SELECT Id, DeveloperName FROM RecordType WHERE SobjectType = :sobjectName]) {
            returnMap.put(rt.Id,rt.DeveloperName);
        }
        return returnMap;
    }

    //Get Sobject Recort Type
    public static Map<String, String> getSobjectRecordTypeNameMap(String sobjectName) {
        Map<String, String> returnMap = new  Map<String, String>();
        for (RecordType rt : [SELECT Id, DeveloperName FROM RecordType WHERE SobjectType = :sobjectName]) {
            returnMap.put(rt.DeveloperName,rt.Id);
        }
        return returnMap;
    }

    //Payment Term Map
    public static Map<String, Payment_Term__mdt> getPaymentTermMap(){
        Map<String, Payment_Term__mdt> paymentTermMap = new Map<String, Payment_Term__mdt>();
        // add haibo: french
        for(Payment_Term__mdt ptSetting : [SELECT MasterLabel, Description__c, Description_French__c,
                                                    Name__c, Payment_Discount__c,
                                                   Payment_Leadtime__c,Criteria_From__c,
                                                   Criteria_To__c,IsReCalculate__c
                                             FROM Payment_Term__mdt]) {
            paymentTermMap.put(ptSetting.Name__c, ptSetting);
        }

        return paymentTermMap;
    }

    //Freight Term Map
    public static Map<String, Freight_Term__mdt> getFreightTermMap(){
        Map<String, Freight_Term__mdt> freigntTermMap = new Map<String, Freight_Term__mdt>();
        for(Freight_Term__mdt ftSetting : [SELECT MasterLabel, Description__c,
                                                    Name__c, Freight_Fee__c,
                                                   Currency__c
                                             FROM Freight_Term__mdt]) {
            freigntTermMap.put(ftSetting.Name__c, ftSetting);
        }

        return freigntTermMap;
    }

    public static Boolean isAdmin(){
        User runningUser = [
            SELECT LanguageLocaleKey, ContactID, Profile.UserType, profile.Name
                FROM User
                WHERE Id = : UserInfo.getUserId()];
            if(runningUser.Profile.Name == '系统管理员' || runningUser.Profile.Name == 'System Administrator'){
                return true;
            }
        return false;
    }

    @AuraEnabled
    public static String getCurrentUserInfo() {
        User u = getUserInfo(UserInfo.getUserId());
        if(u != null) {
            return JSON.serialize(u);
        }
        return null;
    }

    // Fetch the current user information
    public static User getUserInfo(String userId){
        List<User> usr = [SELECT Id,
                                 Contact.AccountId,
                                 ContactId,
                                 Name,
                                 Phone,
                                 Email,
                                 UserRoleId,
                                 UserRole.DeveloperName,
                                 SmallPhotoUrl,
                                 FullPhotoUrl,
                                 LocaleSidKey,
                                 Profile.Name,
                                 LanguageLocaleKey FROM User WHERE Id = :userId];
        if (usr.size() > 0 && usr != null){
            return usr[0];
        }
        return null;
    }

    public static List<SelectItem> getSelectOptions(sObject objObject, String fld) {
        List<SelectItem> allOpts = new List<SelectItem>();
        Schema.SObjectType objType = objObject.getSObjectType();
        Schema.DescribeSObjectResult objDescribe = objType.getDescribe();
        Map<String,Schema.SObjectField > fieldMap = objDescribe.fields.getMap();
        List<Schema.PicklistEntry> entries =
        fieldMap.get(fld).getDescribe().getPickListValues();

        for (Schema.PicklistEntry entry : entries) {
            SelectItem item = new SelectItem();
            item.value = entry.getValue();
            item.label = entry.getLabel();
            allOpts.add(item);
        }
        system.debug('allOpts ---->' + allOpts);
        return allOpts;
     }

     public static Map<String, SelectItem> getSelectOptMap(sObject objObject, String fld) {
        Map<String, SelectItem> allOptMap = new Map<String, SelectItem>();
        List<SelectItem> allOpts = new List<SelectItem>();
        Schema.SObjectType objType = objObject.getSObjectType();
        Schema.DescribeSObjectResult objDescribe = objType.getDescribe();
        Map<String,Schema.SObjectField > fieldMap = objDescribe.fields.getMap();
        List<Schema.PicklistEntry> entries =
        fieldMap.get(fld).getDescribe().getPickListValues();

        for (Schema.PicklistEntry entry : entries) {
            SelectItem item = new SelectItem();
            item.value = entry.getValue();
            item.label = entry.getLabel();
            allOptMap.put(item.value, item);
        }
        system.debug('allOptMap ---->' + allOptMap);
        return allOptMap;
     }

    public class SelectItem {
        @AuraEnabled public String label {get; set;}
        @AuraEnabled public String value {get; set;}
    }

    // Get product price book entries
    public static List<PricebookEntry> getPriceBookEntryByProdId(String prodId, String priceBookId){
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        List<Pricebook2> priceBook = [
                SELECT IsStandard, Name, IsActive, Id
                    FROM Pricebook2
                    WHERE IsStandard = false
                    AND Id =: priceBookId
                    AND IsActive = true LIMIT 1];
        if (priceBook != null && priceBook.size() > 0){
            List<PricebookEntry> priceEntries = [
                    SELECT IsActive,IsDeleted,convertCurrency(UnitPrice),Product2Id,Name,
                           UseStandardPrice,Pricebook2Id
                        FROM PricebookEntry
                        WHERE IsActive = true
                        AND IsDeleted = false
                        AND Pricebook2Id =: priceBook[0].Id
                        AND Product2Id =: prodId
                        AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                    ];
            if (priceEntries != null && priceEntries.size() > 0){
                PricebookEntry prodEntry = priceEntries[0];
                prodEntryList.add(prodEntry);
            }
        }

        return prodEntryList;
    }
 // Get product price book entries with out convertCurrency
    public static List<PricebookEntry> getPriceBookEntryByProdIdWithOutConvertCurrency(String prodId, String priceBookId){
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        List<Pricebook2> priceBook = [
                SELECT IsStandard, Name, IsActive, Id
                    FROM Pricebook2
                    WHERE IsStandard = false
                    AND Id =: priceBookId
                    AND IsActive = true LIMIT 1];
        if (priceBook != null && priceBook.size() > 0){
            List<PricebookEntry> priceEntries = [
                    SELECT IsActive,IsDeleted,UnitPrice,Product2Id,Name,
                           UseStandardPrice,Pricebook2Id
                        FROM PricebookEntry
                        WHERE IsActive = true
                        AND IsDeleted = false
                        AND Pricebook2Id =: priceBook[0].Id
                        AND Product2Id =: prodId
                        AND Product2.Is_History_Product__c = :CCM_Constants.blHistoryProduct
                    ];
            if (priceEntries != null && priceEntries.size() > 0){
                PricebookEntry prodEntry = priceEntries[0];
                prodEntryList.add(prodEntry);
            }
        }
        return prodEntryList;
    }
    // Get product price book entries -- Update By Abby On 7/9/2020 针对特殊用户有特殊价格册产品
    public static List<PricebookEntry> getPriceBookEntryByProdId(String prodId, String priceBookId, Boolean isContract){
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        Set<String> oracleIdSet = new Set<String>(); //主价格册下面挂的副价格册的Oracle Id
        Set<String> contracrOracleIdSet = new Set<String>(); //All contract price list oracle Id set
        Map<String, String> contractPriceBookMap = new Map<String, String>(); //Contract Price List Map<Price Book Oracle Id, Price Book Id>
        Set<String> contarctPriceIdSet = new Set<String>(); //Contract Price List RecId
        List<Pricebook2> priceBook = [
                SELECT IsStandard, Name, IsActive, Id,
                    Price_Book_OracleID__c,
                    Contract_Price_Book_OracleID__c
                    FROM Pricebook2
                    WHERE IsStandard = false
                    AND Id =: priceBookId
                    AND IsActive = true LIMIT 1];
        if (priceBook != null && priceBook.size() > 0){
            if (!isContract){
                List<PricebookEntry> priceEntries = [
                    SELECT IsActive,IsDeleted,convertCurrency(UnitPrice),Product2Id,Name,
                           UseStandardPrice,Pricebook2Id
                        FROM PricebookEntry
                        WHERE IsActive = true
                        AND IsDeleted = false
                        AND Pricebook2Id =: priceBook[0].Id
                        AND Product2Id =: prodId];
                if (priceEntries != null && priceEntries.size() > 0){
                    PricebookEntry prodEntry = priceEntries[0];
                    prodEntryList.add(prodEntry);
                }
            }else{
                //先加入对应的Contract Price List下面的主价格册
                contractPriceBookMap.put(priceBook[0].Price_Book_OracleID__c, priceBook[0].Id);
                contarctPriceIdSet.add(priceBook[0].Id);
                contracrOracleIdSet.add(priceBook[0].Price_Book_OracleID__c);
                //找到对应的Contract Price List下面的副价格册
                if (String.isNotBlank(priceBook[0].Contract_Price_Book_OracleID__c)){
                    oracleIdSet.addAll(priceBook[0].Contract_Price_Book_OracleID__c.split(','));
                    contracrOracleIdSet.addAll(priceBook[0].Contract_Price_Book_OracleID__c.split(','));

                    List<Pricebook2> contractPriceBookList = [SELECT Id, IsActive, Price_Book_OracleID__c FROM Pricebook2 WHERE Price_Book_OracleID__c IN: oracleIdSet AND IsActive = true];
                    if (contractPriceBookList != null && contractPriceBookList.size() > 0){
                        for (Pricebook2 pbItem : contractPriceBookList){
                            contractPriceBookMap.put(pbItem.Price_Book_OracleID__c, pbItem.Id);
                            contarctPriceIdSet.add(pbItem.Id);
                        }
                    }
                }

                Map<String, PricebookEntry> entryMap = new Map<String, PricebookEntry>();
                List<PricebookEntry> priceEntries = [
                            SELECT IsActive,IsDeleted,convertCurrency(UnitPrice),Product2Id,Name,
                                   UseStandardPrice,Pricebook2Id
                                FROM PricebookEntry
                                WHERE IsActive = true
                                AND IsDeleted = false
                                AND Pricebook2Id IN: contarctPriceIdSet
                                AND Product2Id =: prodId];
                if (priceEntries != null && priceEntries.size() > 0){
                    for (PricebookEntry entry : priceEntries){
                        entryMap.put(entry.Pricebook2Id, entry);
                    }
                }

                //依次去找对应的价格册
                if (contracrOracleIdSet != null && contracrOracleIdSet.size() > 0){
                    PricebookEntry pbEntry;
                    for (String orId : contracrOracleIdSet){
                        String priceBookRecId = contractPriceBookMap.get(orId);
                        if (entryMap.get(priceBookRecId) != null){
                            pbEntry = entryMap.get(priceBookRecId);
                            break;
                        }
                    }
                    if(pbEntry != null){
                        prodEntryList.add(pbEntry);
                    }
                }
            }
        }
        return prodEntryList;
    }

    public static List<PricebookEntry> getPriceBookEntryByProdIdWithoutConvertCurrency(String prodId, String priceBookId, Boolean isContract){
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        Set<String> oracleIdSet = new Set<String>(); //主价格册下面挂的副价格册的Oracle Id
        Set<String> contracrOracleIdSet = new Set<String>(); //All contract price list oracle Id set
        Map<String, String> contractPriceBookMap = new Map<String, String>(); //Contract Price List Map<Price Book Oracle Id, Price Book Id>
        Set<String> contarctPriceIdSet = new Set<String>(); //Contract Price List RecId
        List<Pricebook2> priceBook = [
                SELECT IsStandard, Name, IsActive, Id,
                    Price_Book_OracleID__c,
                    Contract_Price_Book_OracleID__c
                    FROM Pricebook2
                    WHERE IsStandard = false
                    AND Id =: priceBookId
                    AND IsActive = true LIMIT 1];
        if (priceBook != null && priceBook.size() > 0){
            if (!isContract){
                List<PricebookEntry> priceEntries = [
                    SELECT IsActive,IsDeleted,UnitPrice,Product2Id,Name,
                           UseStandardPrice,Pricebook2Id
                        FROM PricebookEntry
                        WHERE IsActive = true
                        AND IsDeleted = false
                        AND Pricebook2Id =: priceBook[0].Id
                        AND Product2Id =: prodId];
                if (priceEntries != null && priceEntries.size() > 0){
                    PricebookEntry prodEntry = priceEntries[0];
                    prodEntryList.add(prodEntry);
                }
            }else{
                //先加入对应的Contract Price List下面的主价格册
                contractPriceBookMap.put(priceBook[0].Price_Book_OracleID__c, priceBook[0].Id);
                contarctPriceIdSet.add(priceBook[0].Id);
                contracrOracleIdSet.add(priceBook[0].Price_Book_OracleID__c);
                //找到对应的Contract Price List下面的副价格册
                if (String.isNotBlank(priceBook[0].Contract_Price_Book_OracleID__c)){
                    oracleIdSet.addAll(priceBook[0].Contract_Price_Book_OracleID__c.split(','));
                    contracrOracleIdSet.addAll(priceBook[0].Contract_Price_Book_OracleID__c.split(','));

                    List<Pricebook2> contractPriceBookList = [SELECT Id, IsActive, Price_Book_OracleID__c FROM Pricebook2 WHERE Price_Book_OracleID__c IN: oracleIdSet AND IsActive = true];
                    if (contractPriceBookList != null && contractPriceBookList.size() > 0){
                        for (Pricebook2 pbItem : contractPriceBookList){
                            contractPriceBookMap.put(pbItem.Price_Book_OracleID__c, pbItem.Id);
                            contarctPriceIdSet.add(pbItem.Id);
                        }
                    }
                }

                Map<String, PricebookEntry> entryMap = new Map<String, PricebookEntry>();
                List<PricebookEntry> priceEntries = [
                            SELECT IsActive,IsDeleted,UnitPrice,Product2Id,Name,
                                   UseStandardPrice,Pricebook2Id
                                FROM PricebookEntry
                                WHERE IsActive = true
                                AND IsDeleted = false
                                AND Pricebook2Id IN: contarctPriceIdSet
                                AND Product2Id =: prodId];
                if (priceEntries != null && priceEntries.size() > 0){
                    for (PricebookEntry entry : priceEntries){
                        entryMap.put(entry.Pricebook2Id, entry);
                    }
                }

                //依次去找对应的价格册
                if (contracrOracleIdSet != null && contracrOracleIdSet.size() > 0){
                    PricebookEntry pbEntry = new PricebookEntry();
                    for (String orId : contracrOracleIdSet){
                        String priceBookRecId = contractPriceBookMap.get(orId);
                        if (entryMap.get(priceBookRecId) != null){
                            pbEntry = entryMap.get(priceBookRecId);
                            break;
                        }
                    }

                    prodEntryList.add(pbEntry);
                }
            }
        }
        return prodEntryList;
    }

    // Get product price book entries
    public static List<PricebookEntry> getPriceBookEntryByProdIdForClaim(String prodId, String priceBookId){
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        List<Pricebook2> priceBook = [
                SELECT IsStandard, Name, IsActive, Id
                    FROM Pricebook2
                    WHERE IsStandard = false
                    AND Id =: priceBookId
                    AND IsActive = true LIMIT 1];
        if (priceBook != null && priceBook.size() > 0){
            List<PricebookEntry> priceEntries = [
                    SELECT IsActive,IsDeleted,convertCurrency(UnitPrice),Product2Id,Name,
                           UseStandardPrice,Pricebook2Id
                        FROM PricebookEntry
                        WHERE IsDeleted = false
                        AND Pricebook2Id =: priceBook[0].Id
                        AND Product2Id =: prodId];
            if (priceEntries != null && priceEntries.size() > 0){
                PricebookEntry prodEntry = priceEntries[0];
                prodEntryList.add(prodEntry);
            }
        }

        return prodEntryList;
    }

    // Get Standard price book Information
    //If you can't find the corresponding price book, look for the standard price book corresponding to the current brand.
    public static List<PricebookEntry> getStandardPricebookEntryByProdId(String prodId, String brandName, String customerType, String type){
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        if (String.isNotBlank(prodId) && String.isNotBlank(brandName)){
            List<Customer_Brand_Pricebook_Mapping__c> bpList = [
                    SELECT Id,
                           Name,
                           Brand__c,
                           Customer_Type__c,
                           Price_Book__c,
                           Type__c
                    FROM Customer_Brand_Pricebook_Mapping__c
                    WHERE Brand__c = :brandName
                    AND Customer_Type__c = :customerType
                    AND Authorized_Brand_Type__c = 'Standard'
                    AND Type__c = :type
                    AND Special_Dropship_Address__c != true];
            if (bpList != null && bpList.size() > 0){
                String priceBookId = bpList[0].Price_Book__c;
                List<PricebookEntry> priceEntries = [
                    SELECT IsActive,IsDeleted,convertCurrency(UnitPrice),Product2Id,Name,
                           UseStandardPrice,Pricebook2Id
                        FROM PricebookEntry
                        WHERE IsActive = true
                        AND IsDeleted = false
                        AND Pricebook2Id =: priceBookId
                        AND Product2Id =: prodId];
                if (priceEntries != null && priceEntries.size() > 0){
                    PricebookEntry prodEntry = priceEntries[0];
                    prodEntryList.add(prodEntry);
                }
            }
        }
        return prodEntryList;
    }

    public static List<PricebookEntry> getStandardPricebookEntryByProdIdWithoutConvertCurrency(String prodId, String brandName, String customerType, String type){
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        if (String.isNotBlank(prodId) && String.isNotBlank(brandName)){
            List<Customer_Brand_Pricebook_Mapping__c> bpList = [
                    SELECT Id,
                           Name,
                           Brand__c,
                           Customer_Type__c,
                           Price_Book__c,
                           Type__c
                    FROM Customer_Brand_Pricebook_Mapping__c
                    WHERE Brand__c = :brandName
                    AND Customer_Type__c = :customerType
                    AND Authorized_Brand_Type__c = 'Standard'
                    AND Type__c = :type
                    AND Special_Dropship_Address__c != true];
            if (bpList != null && bpList.size() > 0){
                String priceBookId = bpList[0].Price_Book__c;
                List<PricebookEntry> priceEntries = [
                    SELECT IsActive,IsDeleted,UnitPrice,Product2Id,Name,
                           UseStandardPrice,Pricebook2Id
                        FROM PricebookEntry
                        WHERE IsActive = true
                        AND IsDeleted = false
                        AND Pricebook2Id =: priceBookId
                        AND Product2Id =: prodId];
                if (priceEntries != null && priceEntries.size() > 0){
                    PricebookEntry prodEntry = priceEntries[0];
                    prodEntryList.add(prodEntry);
                }
            }
        }
        return prodEntryList;
    }

    //add by austin，获取标准价格册
    public static List<PricebookEntry> getStandardPricebookEntry(String prodId,String brandName,String customerId){
        Set<String> priceBookIds = new Set<String>();
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        Account acc = [SELECT Id,Name,ORG_Code__c FROM Account WHERE Id = :customerId];
        Set<String> orgSet = new  Set<String>();
        if(acc.ORG_Code__c == 'CNA' || String.isEmpty(acc.ORG_Code__c)){
            orgSet.add('CNA');
            orgSet.add(null);
        }else{
            orgSet.add('CCA');
        }
        if (String.isNotBlank(prodId)){
            List<Customer_Brand_Pricebook_Mapping__c> bpList2 = [
                            SELECT Id,
                                   Name,
                                   Brand__c,
                                   Customer_Type__c,
                                   Price_Book__c,
                                   Type__c
                            FROM Customer_Brand_Pricebook_Mapping__c
                            WHERE Brand__c = :brandName
                            AND Type__c = 'Dropship'
                            AND Special_Dropship_Address__c != true
                            AND ORG_Code__c IN :orgSet];
            for (Customer_Brand_Pricebook_Mapping__c book2 : bpList2){
                if (!priceBookIds.contains(book2.Price_Book__c)){
                    priceBookIds.add(book2.Price_Book__c);
                }
            }
            prodEntryList = [SELECT IsActive,IsDeleted,convertCurrency(UnitPrice),Product2Id,Name,
            UseStandardPrice,Pricebook2Id FROM PricebookEntry
                                WHERE IsActive = true
                                AND IsDeleted = false
                                AND UnitPrice > 0
                                AND Pricebook2Id IN :priceBookIds
                                AND Product2Id =:prodId
                                ORDER BY UnitPrice];
        }
        return prodEntryList;
    }

    //Get DropShip PriceBook by Abby
    public static List<PricebookEntry> getDropShipPricebookEntryByProdId(String prodId, String brandName, String customerId){
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        if (String.isNotBlank(prodId) && String.isNotBlank(brandName) && String.isNotBlank(customerId)){
            Boolean isSpecial = false;
            String dropShipBillingId = '';
            List<Sales_Program__c> authBrandList = [
                    SELECT Id, Customer__c, Brands__c,Approval_Status__c,
                            IsDeleted, RecordType.DeveloperName,
                            (SELECT Id, Address_Type__c,Special_Dropship_Address__c, Customer_Line_Oracle_ID__c
                                FROM Addresses_With_Program__r
                                WHERE Address_Type__c = 'Dropship Billing Address' AND Special_Dropship_Address__c = true
                                AND Customer_Line_Oracle_ID__c != null)
                    FROM Sales_Program__c
                    WHERE Customer__c =: customerId
                    AND Approval_Status__c = 'Approved'
                    AND Brands__c =: brandName
                    AND RecordType.DeveloperName != 'Service'
                    AND IsDeleted = false];
            if (authBrandList != null && authBrandList.size() > 0){
                Sales_Program__c authBrand = authBrandList[0];
                if (authBrand.Addresses_With_Program__r != null && authBrand.Addresses_With_Program__r.size() > 0){
                    isSpecial = true;
                    dropShipBillingId = authBrand.Addresses_With_Program__r[0].Id;
                }
            }
            System.debug(LoggingLevel.INFO, '*** dropShipBillingId: ' + dropShipBillingId);
            String sqlString = 'SELECT Id, Name, Brand__c, Customer_Type__c, Price_Book__c, Type__c, '
                               +'Special_Dropship_Address__c, Billing_Address_With_Authorized_Brand__c, '
                               +'Price_Book__r.Contract_Price_Book_OracleID__c, Price_Book__r.Price_Book_OracleID__c '
                               +'FROM Customer_Brand_Pricebook_Mapping__c '
                               +'WHERE Type__c = \'Dropship\' '
                               +'AND Price_Book__c != null';
            if (isSpecial){
                sqlString += ' AND Billing_Address_With_Authorized_Brand__c = \'' + dropShipBillingId + '\' AND Special_Dropship_Address__c = true';
            }else{
                sqlString += ' AND Brand__c = \'' + brandName +'\' AND Special_Dropship_Address__c = false';
            }

            Account customer = [SELECT Id, ORG_Code__c FROM Account WHERE Id=:customerId];
            if(customer.ORG_Code__c == 'CCA'){
                sqlString += ' AND ORG_Code__c = \'CCA\' ';
            }else{
                sqlString += ' AND ORG_Code__c != \'CCA\' ';
            }

            List<Customer_Brand_Pricebook_Mapping__c> bpList = Database.query(sqlString);
            System.debug(LoggingLevel.INFO, '*** bpList: ' + JSON.serialize(bpList));
            if (bpList != null && bpList.size() > 0){
                Set<String> priceBookIdSet = new Set<String>();//收集主副价格册上的PriceId信息
                Set<String> priceBookOracleIdSet = new Set<String>(); //副价格册的Price Book Oracle Id
                Set<String> allPriceBookOracleIdSet = new Set<String>();//主副价格册的Price Book Oracle Id
                Map<String, String> pbMap = new Map<String, String>(); //Map<Price Book Oracle Id, Price Book Id>
                Map<String, PricebookEntry> pbEntryMap = new Map<String, PricebookEntry>();//Map<Price Book Id, PriceBookEntry>
                String priceBookId = bpList[0].Price_Book__c;
                priceBookIdSet.add(priceBookId);
                allPriceBookOracleIdSet.add(bpList[0].Price_Book__r.Price_Book_OracleID__c);

                //判断该本价格册上是否有特殊价格册的信息
                if (String.isNotBlank(priceBookId)){
                    List<Pricebook2> pbList = [SELECT Id, Contract_Price_Book_OracleID__c, Price_Book_OracleID__c FROM Pricebook2 WHERE Id =: priceBookId];
                    pbMap.put(pbList[0].Price_Book_OracleID__c, pbList[0].Id);
                    if (String.isNotBlank(pbList[0].Contract_Price_Book_OracleID__c)){
                        priceBookOracleIdSet.addAll(pbList[0].Contract_Price_Book_OracleID__c.split(','));
                        List<Pricebook2> priceBookList = [SELECT Id, Price_Book_OracleID__c FROM Pricebook2 WHERE Price_Book_OracleID__c IN: priceBookOracleIdSet];
                        if (priceBookList != null && priceBookList.size() > 0){
                            for (Pricebook2 pb : priceBookList){
                                pbMap.put(pb.Price_Book_OracleID__c, pb.Id);
                                allPriceBookOracleIdSet.add(pb.Price_Book_OracleID__c);
                                priceBookIdSet.add(pb.Id);
                            }
                        }
                    }
                }

                List<PricebookEntry> priceEntries = [
                    SELECT IsActive,IsDeleted,convertCurrency(UnitPrice),Product2Id,Name,
                           UseStandardPrice,Pricebook2Id
                        FROM PricebookEntry
                        WHERE IsActive = true
                        AND IsDeleted = false
                        AND Pricebook2Id IN: priceBookIdSet
                        AND Product2Id =: prodId];
                if (priceEntries != null && priceEntries.size() > 0){
                    for (PricebookEntry entry : priceEntries){
                        pbEntryMap.put(entry.Pricebook2Id, entry);
                    }

                    for (String orcId : allPriceBookOracleIdSet){
                        String pbId = pbMap.get(orcId);
                        if (pbEntryMap.get(pbId) != null){
                            PricebookEntry prodEntry = pbEntryMap.get(pbId);
                            prodEntryList.add(prodEntry);
                            break;
                        }
                    }
                }
            }
        }
        return prodEntryList;
    }

    public static List<PricebookEntry> getDropShipPricebookEntryByProdIdWithoutConvertCurrency(String prodId, String brandName, String customerId){
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        if (String.isNotBlank(prodId) && String.isNotBlank(brandName) && String.isNotBlank(customerId)){
            Boolean isSpecial = false;
            String dropShipBillingId = '';
            List<Sales_Program__c> authBrandList = [
                    SELECT Id, Customer__c, Brands__c,Approval_Status__c,
                            IsDeleted, RecordType.DeveloperName,
                            (SELECT Id, Address_Type__c,Special_Dropship_Address__c, Customer_Line_Oracle_ID__c
                                FROM Addresses_With_Program__r
                                WHERE Address_Type__c = 'Dropship Billing Address' AND Special_Dropship_Address__c = true
                                AND Customer_Line_Oracle_ID__c != null)
                    FROM Sales_Program__c
                    WHERE Customer__c =: customerId
                    AND Approval_Status__c = 'Approved'
                    AND Brands__c =: brandName
                    AND RecordType.DeveloperName != 'Service'
                    AND IsDeleted = false];
            if (authBrandList != null && authBrandList.size() > 0){
                Sales_Program__c authBrand = authBrandList[0];
                if (authBrand.Addresses_With_Program__r != null && authBrand.Addresses_With_Program__r.size() > 0){
                    isSpecial = true;
                    dropShipBillingId = authBrand.Addresses_With_Program__r[0].Id;
                }
            }
            System.debug(LoggingLevel.INFO, '*** dropShipBillingId: ' + dropShipBillingId);
            String sqlString = 'SELECT Id, Name, Brand__c, Customer_Type__c, Price_Book__c, Type__c, '
                               +'Special_Dropship_Address__c, Billing_Address_With_Authorized_Brand__c, '
                               +'Price_Book__r.Contract_Price_Book_OracleID__c, Price_Book__r.Price_Book_OracleID__c '
                               +'FROM Customer_Brand_Pricebook_Mapping__c '
                               +'WHERE Type__c = \'Dropship\' '
                               +'AND Price_Book__c != null';
            if (isSpecial){
                sqlString += ' AND Billing_Address_With_Authorized_Brand__c = \'' + dropShipBillingId + '\' AND Special_Dropship_Address__c = true';
            }else{
                sqlString += ' AND Brand__c = \'' + brandName +'\' AND Special_Dropship_Address__c = false';
            }

            Account customer = [SELECT Id, ORG_Code__c FROM Account WHERE Id=:customerId];
            if(customer.ORG_Code__c == 'CCA'){
                sqlString += ' AND ORG_Code__c = \'CCA\' ';
            }else{
                sqlString += ' AND ORG_Code__c != \'CCA\' ';
            }

            List<Customer_Brand_Pricebook_Mapping__c> bpList = Database.query(sqlString);
            System.debug(LoggingLevel.INFO, '*** bpList: ' + JSON.serialize(bpList));
            if (bpList != null && bpList.size() > 0){
                Set<String> priceBookIdSet = new Set<String>();//收集主副价格册上的PriceId信息
                Set<String> priceBookOracleIdSet = new Set<String>(); //副价格册的Price Book Oracle Id
                Set<String> allPriceBookOracleIdSet = new Set<String>();//主副价格册的Price Book Oracle Id
                Map<String, String> pbMap = new Map<String, String>(); //Map<Price Book Oracle Id, Price Book Id>
                Map<String, PricebookEntry> pbEntryMap = new Map<String, PricebookEntry>();//Map<Price Book Id, PriceBookEntry>
                String priceBookId = bpList[0].Price_Book__c;
                priceBookIdSet.add(priceBookId);
                allPriceBookOracleIdSet.add(bpList[0].Price_Book__r.Price_Book_OracleID__c);

                //判断该本价格册上是否有特殊价格册的信息
                if (String.isNotBlank(priceBookId)){
                    List<Pricebook2> pbList = [SELECT Id, Contract_Price_Book_OracleID__c, Price_Book_OracleID__c FROM Pricebook2 WHERE Id =: priceBookId];
                    pbMap.put(pbList[0].Price_Book_OracleID__c, pbList[0].Id);
                    if (String.isNotBlank(pbList[0].Contract_Price_Book_OracleID__c)){
                        priceBookOracleIdSet.addAll(pbList[0].Contract_Price_Book_OracleID__c.split(','));
                        List<Pricebook2> priceBookList = [SELECT Id, Price_Book_OracleID__c FROM Pricebook2 WHERE Price_Book_OracleID__c IN: priceBookOracleIdSet];
                        if (priceBookList != null && priceBookList.size() > 0){
                            for (Pricebook2 pb : priceBookList){
                                pbMap.put(pb.Price_Book_OracleID__c, pb.Id);
                                allPriceBookOracleIdSet.add(pb.Price_Book_OracleID__c);
                                priceBookIdSet.add(pb.Id);
                            }
                        }
                    }
                }

                List<PricebookEntry> priceEntries = [
                    SELECT IsActive,IsDeleted,UnitPrice,Product2Id,Name,
                           UseStandardPrice,Pricebook2Id
                        FROM PricebookEntry
                        WHERE IsActive = true
                        AND IsDeleted = false
                        AND Pricebook2Id IN: priceBookIdSet
                        AND Product2Id =: prodId];
                if (priceEntries != null && priceEntries.size() > 0){
                    for (PricebookEntry entry : priceEntries){
                        pbEntryMap.put(entry.Pricebook2Id, entry);
                    }

                    for (String orcId : allPriceBookOracleIdSet){
                        String pbId = pbMap.get(orcId);
                        if (pbEntryMap.get(pbId) != null){
                            PricebookEntry prodEntry = pbEntryMap.get(pbId);
                            prodEntryList.add(prodEntry);
                            break;
                        }
                    }
                }
            }
        }
        return prodEntryList;
    }

    //根据Price Book的Oracle Id获取到Price Book的Id信息
    public static Map<String, String> getPriceBookIdByOracleId(Set<String> oracleIdSet){
        Map<String, String> pbMap = new Map<String, String>();
        if (oracleIdSet != null && oracleIdSet.size() > 0){
            List<Pricebook2> priceBookList = [SELECT Id, Price_Book_OracleID__c FROM Pricebook2 WHERE Price_Book_OracleID__c IN: oracleIdSet];
            if (priceBookList != null && priceBookList.size() > 0){
                for (Pricebook2 pb : priceBookList){
                    pbMap.put(pb.Price_Book_OracleID__c, pb.Id);
                }
            }
        }

        return pbMap;
    }

    //特殊的Dropship Billing to地址的billing address with program Id
    public static String getSpecialDropshipBillingFeightTerm(String brandName, String customerId){
        String feightTerm = '';
        List<String> brandNames = brandName.split('&');
        if (String.isNotBlank(brandName) && String.isNotBlank(customerId)){
            List<Sales_Program__c> authBrandList = [
                    SELECT Id, Customer__c, Brands__c,Approval_Status__c,
                            IsDeleted, RecordType.DeveloperName,
                            (SELECT Id, Address_Type__c,Special_Dropship_Address__c, Customer_Line_Oracle_ID__c
                                FROM Addresses_With_Program__r
                                WHERE Address_Type__c = 'Dropship Billing Address' AND Special_Dropship_Address__c = true
                                AND Customer_Line_Oracle_ID__c != null)
                    FROM Sales_Program__c
                    WHERE Customer__c =: customerId
                    AND Approval_Status__c = 'Approved'
                    AND Brands__c IN:brandNames
                    AND RecordType.DeveloperName != 'Service'
                    AND IsDeleted = false];
            if (authBrandList != null && authBrandList.size() > 0){
                Sales_Program__c authBrand = authBrandList[0];
                if (authBrand.Addresses_With_Program__r != null && authBrand.Addresses_With_Program__r.size() > 0){
                    String dropshipBillingAddrId = authBrand.Addresses_With_Program__r[0].Id;
                    List<Customer_Brand_Pricebook_Mapping__c> bpList = [
                            SELECT Id, Name, Brand__c, Customer_Type__c, Feight_Term__c, Type__c,
                                  Special_Dropship_Address__c, Billing_Address_With_Authorized_Brand__c
                            FROM Customer_Brand_Pricebook_Mapping__c
                            WHERE Feight_Term__c != null
                            AND Type__c = 'Dropship'
                            AND Billing_Address_With_Authorized_Brand__c =: dropshipBillingAddrId
                            AND Special_Dropship_Address__c = true];
                    if (bpList != null && bpList.size() > 0){
                        feightTerm = bpList[0].Feight_Term__c;
                    }
                }
            }
        }

        return feightTerm;
    }

    //特殊的Dropship Billing to地址的billing address with program Id
    public static String getSpecialDropshipBillingToId(String brandName, String customerId){
        String dropshipBillingAddrId = '';
        if (String.isNotBlank(brandName) && String.isNotBlank(customerId)){
            List<Sales_Program__c> authBrandList = [
                    SELECT Id, Customer__c, Brands__c,Approval_Status__c,
                            IsDeleted, RecordType.DeveloperName,
                            (SELECT Id, Address_Type__c,Special_Dropship_Address__c, Customer_Line_Oracle_ID__c
                                FROM Addresses_With_Program__r
                                WHERE Address_Type__c = 'Dropship Billing Address' AND Special_Dropship_Address__c = true
                                AND Customer_Line_Oracle_ID__c != null)
                    FROM Sales_Program__c
                    WHERE Customer__c =: customerId
                    AND Approval_Status__c = 'Approved'
                    AND Brands__c =: brandName
                    AND RecordType.DeveloperName != 'Service'
                    AND IsDeleted = false];
            if (authBrandList != null && authBrandList.size() > 0){
                Sales_Program__c authBrand = authBrandList[0];
                if (authBrand.Addresses_With_Program__r != null && authBrand.Addresses_With_Program__r.size() > 0){
                    dropshipBillingAddrId = authBrand.Addresses_With_Program__r[0].Id;
                }
            }
        }

        return dropshipBillingAddrId;
    }

    //Get DropShip PriceBook
    public static String getDropShipPricebookName(String brandName, String orgCode){
        String priceBookName = '';
        if (String.isNotBlank(brandName)){
            String query = 'SELECT Id, Name, Brand__c, Customer_Type__c, Price_Book__c, Price_Book__r.Name, Type__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Brand__c = :brandName AND Type__c = \'Dropship\' AND Special_Dropship_Address__c != true';
            if(CCM_Constants.ORG_CODE_CCA == orgCode) {
                query += ' AND ORG_Code__c = \'CCA\'';
            }
            else {
                query += ' AND ORG_Code__c != \'CCA\'';
            }
            List<Customer_Brand_Pricebook_Mapping__c> bpList = (List<Customer_Brand_Pricebook_Mapping__c>)Database.query(query);
            if (bpList != null && bpList.size() > 0){
                priceBookName = bpList[0].Price_Book__r.Name;
            }
        }
        return priceBookName;
    }

    //get special dropship price book
    public static Map<String, String> getSpecialDropShipPricebookName () {
        Map<String, String> priceBookMap = new Map<String, String>();
        List<Customer_Brand_Pricebook_Mapping__c> bpList = [
                    SELECT Id,
                           Name,
                           Brand__c,
                           Customer_Type__c,
                           Price_Book__c,
                           Price_Book__r.Name,
                           Billing_Address_With_Authorized_Brand__c,
                           Type__c
                    FROM Customer_Brand_Pricebook_Mapping__c
                    WHERE Special_Dropship_Address__c = true
                    AND Type__c = 'Dropship'
                    AND Billing_Address_With_Authorized_Brand__c != NULL
                    AND Price_Book__c != NULL];
        if (bpList != null && bpList.size() > 0) {
            for (Customer_Brand_Pricebook_Mapping__c cbp : bpList) {
                if (priceBookMap.get(cbp.Billing_Address_With_Authorized_Brand__c) == null) {
                    priceBookMap.put(cbp.Billing_Address_With_Authorized_Brand__c, cbp.Price_Book__r.Name);
                }
            }
        }
        return priceBookMap;
    }

    //get special dropship price book
    public static Set<String> getSpecialDropShipPricebookIds (Set<String> specialDropShipAWPIds) {
        Set<String> priceBookSet = new Set<String>();
        List<Customer_Brand_Pricebook_Mapping__c> bpList = [
                    SELECT Id,
                           Name,
                           Brand__c,
                           Customer_Type__c,
                           Price_Book__c,
                           Price_Book__r.Contract_Price_Book_OracleID__c,
                           Price_Book__r.Name,
                           Billing_Address_With_Authorized_Brand__c,
                           Type__c
                    FROM Customer_Brand_Pricebook_Mapping__c
                    WHERE Special_Dropship_Address__c = true
                    AND Type__c = 'Dropship'
                    AND Billing_Address_With_Authorized_Brand__c IN: specialDropShipAWPIds
                    AND Price_Book__c != NULL];
        if (bpList != null && bpList.size() > 0) {
            Set<String> priceBookOracleIds = new Set<String>();
            for (Customer_Brand_Pricebook_Mapping__c cbp : bpList) {
                priceBookSet.add(cbp.Price_Book__c);
                if (String.isNotEmpty(cbp.Price_Book__r.Contract_Price_Book_OracleID__c)) {
                    priceBookOracleIds.addAll(cbp.Price_Book__r.Contract_Price_Book_OracleID__c.split(','));
                }
            }
            if (!priceBookOracleIds.isEmpty()) {
                List<Pricebook2> pbList = [SELECT ID FROM Pricebook2 WHERE Price_Book_OracleID__c IN: priceBookOracleIds];
                for (Pricebook2 pb : pbList) {
                    priceBookSet.add(pb.Id);
                }
            }
        }
        return priceBookSet;
    }

    //get Auth brands
    public static List<Sales_Program__c> getAuthBrandInfo(String customerId, String brandName){
        if (String.isNotBlank(customerId) && String.isNotBlank(brandName)){
            List<String> brandNames = brandName.split('&');
            List<Sales_Program__c> authBrands = [
                    SELECT Id, Customer__c, Freight_Term__c,
                           Payment_Term__c, Brands__c,
                           tolabel(Freight_Term__c) freightTermLabel,
                           toLabel(Payment_Term__c) paymentTermLabel,
                           IsDeleted
                        FROM Sales_Program__c
                        WHERE IsDeleted = false
                        AND Customer__c =:customerId
                        AND Brands__c IN:brandNames
                        AND RecordTypeId IN :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_IDS];

            if (authBrands != null && authBrands.size() > 0){
                return authBrands;
            }
        }
        return new List<Sales_Program__c>();
    }
    // add,napoleon, 22-12-28,because of the differences between cca and cna authbrands,so get authbrands via two methods
    // get CNA service，approved brands info
    public static List<Sales_Program__c> getCnaSerivceAuthBrandInfo(String customerId){
        if (String.isNotBlank(customerId)){
            List<Sales_Program__c> authBrands = [
                SELECT
                    Id, Customer__c,IsDeleted,Brands__c,
                    Freight_Term__c,Payment_Term__c,
                    toLabel(Freight_Term__c) freightTermLabel,
                    toLabel(Payment_Term__c) paymentTermLabel
                FROM Sales_Program__c
                WHERE Id != null
                    AND IsDeleted                   = False
                    AND Customer__c                 = :customerId
                    AND Approval_Status__c          = :CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED
                    AND Customer__r.ORG_Code__c     = :CCM_Constants.ORG_CODE_CNA
                    AND RecordTypeId IN               :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_IDS
                ];
                    // AND Status__c                   = 'Active'

            if (authBrands != null && authBrands.size() > 0){
                return authBrands;
            }
        }
        return null;
    }
    // get CCA service，approved brands info
    public static List<Sales_Program__c> getCcaSerivceAuthBrandInfo(String customerId){
        if (String.isNotBlank(customerId)){
            List<Sales_Program__c> authBrands = [
                SELECT
                    Id, Customer__c,IsDeleted,Brands__c,
                    Freight_Term__c,Payment_Term__c,
                    toLabel(Freight_Term__c) freightTermLabel,
                    toLabel(Payment_Term__c) paymentTermLabel
                FROM Sales_Program__c
                WHERE Id != null
                    AND IsDeleted                   = False
                    AND Customer__c                 = :customerId
                    AND Approval_Status__c          = :CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED
                    AND Customer__r.ORG_Code__c     = :CCM_Constants.ORG_CODE_CCA
                    AND RecordTypeId IN               :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_IDS
                ];
                    // AND Status__c                   = 'Active'

            if (authBrands != null && authBrands.size() > 0){
                return authBrands;
            }
        }
        return null;
    }
    // add end


    /**
     * add,napoleon, 23-1-2
     * @description Caculate Canada taxes for Canada parts order;
     */
    public static void caculateCcaPartsOrderTotalTax(Purchase_Order__c po){
        //CA Province Tax Rate
        Map<String, CA_Tax_Rate_By_Province__mdt> rateMap = Util.getCanadaTaxRateMap();
        CA_Tax_Rate_By_Province__mdt taxRate;
        if(po.Is_Alternative_Address__c){
            taxRate = rateMap.get(po.Additional_Shipping_Province__c);
        } else {
            //Shipping Address
            List<Address_With_Program__c> lstShipAddress = [
                SELECT
                Account_Address__r.State__c
                FROM Address_With_Program__c
                WHERE Id=:po.ShipTo__c
            ];
            if(lstShipAddress.size() > 0){
                taxRate = rateMap.get(lstShipAddress[0].Account_Address__r.State__c);
            }
        }
        if(taxRate != null){
            Decimal baseAmount = 0;
            if(po.Product_Price__c != null){
                baseAmount += po.Product_Price__c;
            }
            if(po.Freight_Fee__c !=null){
                baseAmount += po.Freight_Fee__c;
            }
            if(po.Handling_Fee__c !=null){
                baseAmount += po.Handling_Fee__c;
            }
            if(po.Freight_Fee_Waived__c !=null){
                baseAmount -= Math.abs(po.Freight_Fee_Waived__c);
            }
            if(po.Extra_Freight_Fee_To_Be_Waived__c !=null){
                baseAmount -= Math.abs(po.Extra_Freight_Fee_To_Be_Waived__c);
            }
            if(po.Discount_Amount__c !=null){
                baseAmount -= Math.abs(po.Discount_Amount__c);
            }
            if(po.Surcharge_Amount__c !=null){
                baseAmount += po.Surcharge_Amount__c;
            }

            po.GST__c = (baseAmount*taxRate.GST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.HST__c = (baseAmount*taxRate.HST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.PST__c = (baseAmount*taxRate.PST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.QST__c = (baseAmount*taxRate.QST__c/100).setScale(2,System.RoundingMode.HALF_UP);
            po.Total_Tax__c = (baseAmount*(taxRate.Total_Rate__c-taxRate.PST__c)/100).setScale(2,System.RoundingMode.HALF_UP);
        } else {
            po.GST__C = null;
            po.HST__c = null;
            po.PST__c = null;
            po.QST__c = null;
            po.Total_Tax__c = null;
        }
    }
    // add end

    //get Auth brands for sales
    public static List<Sales_Program__c> getAuthBrandInfoSales(String customerId, String brandName) {
        List<Sales_Program__c> lstAuthBrand = new List<Sales_Program__c>();
        if (String.isNotBlank(customerId) && String.isNotBlank(brandName)) {
            List<String> brandNames = brandName.split('&');
            for (Sales_Program__c objAB : [
                SELECT
                    Id,
                    Customer__c,
                    Freight_Term__c,
                    Payment_Term__c,
                    Brands__c,
                    toLabel(Freight_Term__c) freightTermLabel,
                    toLabel(Payment_Term__c) paymentTermLabel,
                    IsDeleted,
                    RecordType.DeveloperName,
                    (SELECT Id FROM Addresses_With_Program__r WHERE Status__c = 'A')
                FROM Sales_Program__c
                WHERE
                    IsDeleted = FALSE
                    AND Customer__c = :customerId
                    AND Brands__c IN :brandNames
                    AND RecordType.DeveloperName != 'Service'
                    AND RecordTypeId != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_ID
                    AND RecordTypeId != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_ID
                    AND Approval_Status__c = :CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED
            ]) {
                if (!objAB.Addresses_With_Program__r.isEmpty()) {
                    lstAuthBrand.add(objAB);
                }
            }
        }
        return lstAuthBrand;
    }

    //get Auth brands for sales
    public static List<Sales_Program__c> getDropShipAuthBrandInfoSales(String customerId, String brandName) {
        List<Sales_Program__c> lstAuthBrand = new List<Sales_Program__c>();
        if (String.isNotBlank(customerId) && String.isNotBlank(brandName)) {
            List<String> brandNames = brandName.split('&');
            lstAuthBrand = [SELECT Id,
                                Customer__c,
                                Freight_Term__c,
                                Payment_Term__c,
                                Brands__c,
                                toLabel(Freight_Term__c) freightTermLabel,
                                toLabel(Payment_Term__c) paymentTermLabel,
                                RecordType.DeveloperName
                            FROM Sales_Program__c
                            WHERE Customer__c = :customerId
                                AND Brands__c IN :brandNames
                                AND RecordType.DeveloperName IN ('Dropship_Sales_Customized', 'Dropship_Sales_Standard')
                                AND Approval_Status__c = :CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED];
        }
        return lstAuthBrand;
    }

    /**
     * @description get active pricebook entry by customer and products
     * @param  customer   customer description
     * @param  productIds productIds description
     * @return            return description
     */
    public static Map<String, PricebookEntry> getActivePirceBookEntrySales(String customerId, Set<String> productCodes) {
        Set<String> contractPBId = new Set<String>();
        Map<String, List<String>> pbOracleIdMap = new Map<String, List<String>>();
        List<Sales_Program__c> lstAuthBrand = new List<Sales_Program__c>();
        if (String.isNotBlank(customerId)) {
            for (Sales_Program__c objAB : [SELECT Price_Book__c, Price_Book__r.Price_Book_OracleID__c, Price_Book__r.Contract_Price_Book_OracleID__c FROM Sales_Program__c WHERE Customer__c = :customerId AND RecordTypeId IN :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SALES_IDS AND Approval_Status__c = :CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED]) {
                if (!pbOracleIdMap.containsKey(objAB.Price_Book__r.Price_Book_OracleID__c)) {
                    pbOracleIdMap.put(objAB.Price_Book__r.Price_Book_OracleID__c, new List<String>());
                }
                System.debug('Contract_Price_Book_OracleID__c============>' + objAB.Price_Book__r.Contract_Price_Book_OracleID__c);
                if (String.isNotBlank(objAB.Price_Book__r.Contract_Price_Book_OracleID__c)) {
                    pbOracleIdMap.get(objAB.Price_Book__r.Price_Book_OracleID__c).addAll(objAB.Price_Book__r.Contract_Price_Book_OracleID__c.split(','));
                    contractPBId.addAll(objAB.Price_Book__r.Contract_Price_Book_OracleID__c.split(','));
                }
            }
        }
        Map<String, PricebookEntry> productPriceMap = new Map<String, PricebookEntry>();
        for (PricebookEntry pbe : [SELECT Id, Pricebook2.Name, UnitPrice, Product2.ProductCode FROM PricebookEntry WHERE Pricebook2.Price_Book_OracleID__c IN :pbOracleIdMap.keySet() AND Product2.ProductCode IN :productCodes AND isActive = true]) {
            productPriceMap.put(pbe.Product2.ProductCode, pbe);
        }
        if (productPriceMap.keySet().size() != productCodes.size()) {
            productCodes.removeAll(productPriceMap.keySet());
            for (PricebookEntry pbe : [SELECT Id, Pricebook2.Name, UnitPrice, Product2.ProductCode FROM PricebookEntry WHERE Pricebook2.Price_Book_OracleID__c IN :contractPBId AND Product2.ProductCode IN :productCodes AND isActive = true]) {
                productPriceMap.put(pbe.Product2.ProductCode, pbe);
            }
        }
        return productPriceMap;
    }

    //get payment term from purchase order
    public static Purchase_Order__c getPOPaymentTermInfo (String recordId){
        Purchase_Order__c po = new Purchase_Order__c();
        if (String.isNotBlank(recordId)){
            po = [SELECT Id, Payment_Term__c FROM Purchase_Order__c WHERE Id =:recordId];
        }

        return po;
    }

    //Get Custom Metadata Types: Business Volume Scoring
    public static Map<String, Map<String, Business_Volume_Scoring__mdt>> getBusinessVolumeScorings() {
        Map<String, Map<String, Business_Volume_Scoring__mdt>> theMap = new Map<String, Map<String, Business_Volume_Scoring__mdt>>();
        for (Business_Volume_Scoring__mdt bvs : [
            SELECT Id, DeveloperName, MasterLabel, Type__c, Lower_Limit__c, Upper_Limit__c, Score__c FROM Business_Volume_Scoring__mdt
        ]) {
            if (String.isNotBlank(bvs.Type__c) && theMap.containsKey(bvs.Type__c)) {
                theMap.get(bvs.Type__c).put(bvs.DeveloperName, bvs);
            } else if (String.isNotBlank(bvs.Type__c) && !theMap.containsKey(bvs.Type__c)) {
                theMap.put(bvs.Type__c, new Map<String, Business_Volume_Scoring__mdt>{bvs.DeveloperName => bvs});
            }
        }
        return theMap;
    }

    public static String findObjectNameFromRecordIdPrefix(String recordIdOrPrefix){
        String objectName = '';
        try{
            //Get prefix from record ID
            //This assumes that you have passed at least 3 characters
            String myIdPrefix = String.valueOf(recordIdOrPrefix).substring(0,3);

            //Get schema information
            Map<String, Schema.SObjectType> gd =  Schema.getGlobalDescribe();

            //Loop through all the sObject types returned by Schema
            for(Schema.SObjectType stype : gd.values()){
                Schema.DescribeSObjectResult r = stype.getDescribe();
                String prefix = r.getKeyPrefix();

                //Check if the prefix matches with requested prefix
                if(prefix!=null && prefix.equals(myIdPrefix)){
                    objectName = r.getName();
                    System.debug('Object Name! ' + objectName);
                    break;
                }
            }
        }catch(Exception e){
            System.debug(e);
        }
        return objectName;
    }

    //校验当前用户是否是当前审批人
    public static Boolean isCurrentApprover(String targetObjId){
        System.debug(LoggingLevel.INFO, '*** targetObjId: ' + targetObjId);
        Boolean isApprovalMode = false;
        if (String.isNotBlank(targetObjId)){
            Boolean isAdmin = Util.isAdmin();
            if (isAdmin){
                isApprovalMode = true;
            }else{
                // 校验是否是当前审批人
                List<ProcessInstanceWorkitem> items = [
                        SELECT Id, OriginalActorId
                        FROM ProcessInstanceWorkitem
                        WHERE ProcessInstance.Status = 'Pending'
                        AND ProcessInstance.TargetObjectId = :targetObjId LIMIT 1];
                if (items != null && items.size() > 0){
                    if (items[0].OriginalActorId.equals(UserInfo.getUserId())){
                        isApprovalMode = true;
                    }else {
                        UserRole role = [SELECT Id, DeveloperName FROM UserRole WHERE Id =: UserInfo.getUserRoleId()];
                        String queueName = '';
                        if(role.DeveloperName.contains('BEAM')){
                            queueName = 'BEAM_Manager';
                        }else if (role.DeveloperName.contains('Sales_VP')){
                            queueName = 'Sales_VP';
                        }
                        List<GroupMember> gms = [Select GroupId, Id, SystemModstamp, UserOrGroupId from GroupMember where GroupId IN (Select Id from Group where Type = 'Queue' and DeveloperName =:queueName)];
                        Set<Id> additionalManagerSet = new Set<Id>();
                        for (GroupMember gm : gms) {
                            additionalManagerSet.add(gm.UserOrGroupId);
                        }

                        if (additionalManagerSet != null && additionalManagerSet.size() > 0){
                            for (Id idStr : additionalManagerSet){
                                if (idStr.equals(UserInfo.getUserId())){
                                    isApprovalMode = true;
                                }
                            }
                        }
                    }
                }
            }
        }

        return isApprovalMode;
    }

    //根据当前Customer上的Address去获取中间地址的OracleId
    public static AddressOracleInfo getAddressOracelId(String addressId, String customerId, String brandName, Boolean isBilling, Boolean isSales, String OrderType){
        AddressOracleInfo addInfo = new AddressOracleInfo();
        List<String> approveMaplist = new List<String>();
        // Purchase Order的approval status必须是Approved
        approveMaplist.add('');
        approveMaplist.add('Approved');
        // 如果是以下类型，authorized brand的approval status不是Rejected就行
        if(OrderType == 'Warranty Return' || OrderType == 'Reverse Order' || OrderType == 'CoOp Claim'){
            approveMaplist.add('Draft');
            approveMaplist.add('Pending for Approval');
        }
        if (String.isNotBlank(addressId) && String.isNotBlank(customerId) && String.isNotBlank(brandName)){
            List<String> brandList = brandName.split('&');
            Map<Id, Sales_Program__c> authBrandMap = new Map<Id, Sales_Program__c>();
            List<Sales_Program__c> authBrands = new List<Sales_Program__c>();
            if (isBilling){
                if (isSales){
                    authBrands = [
                        SELECT Id, Brands__c, Approval_Status__c, Customer__c, RecordType.DeveloperName
                        FROM Sales_Program__c
                        WHERE Brands__c IN :brandList
                        AND Customer__c = :customerId
                        AND Approval_Status__c in :approveMaplist
                        AND RecordType.DeveloperName != 'Service'
                        AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME
                        AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME
                        // AND RecordType.DeveloperName != 'Dropship_Sales_Standard'
                        // AND RecordType.DeveloperName != 'Dropship_Sales_Customized'
                        ];
                }else{
                    authBrands = [
                        SELECT Id, Brands__c, Approval_Status__c, Customer__c, RecordType.DeveloperName
                        FROM Sales_Program__c
                        WHERE Brands__c IN :brandList
                        AND Customer__c = :customerId
                        AND Approval_Status__c in :approveMaplist
                        // AND RecordType.DeveloperName = 'Service'
                        AND (RecordType.DeveloperName = :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME OR RecordType.DeveloperName = :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME)
                        ];
                }

                // if (authBrandMap != null && authBrands.size() > 0){
                if (authBrands.size() > 0){
                    authBrandMap.putAll(authBrands);
                    List<Address_With_Program__c> addressPrograms = [
                        SELECT Id,Account_Address__c, Program__c, IsDeleted,
                               Customer_Line_Oracle_ID__c, Status__c
                        FROM Address_With_Program__c
                        WHERE IsDeleted = false
                        AND Program__c IN :authBrandMap.keySet()
                        AND Status__c = 'A'
                        AND Account_Address__c = :addressId];
                    if (addressPrograms != null && addressPrograms.size() > 0){
                        addInfo.oracelId = addressPrograms[0].Customer_Line_Oracle_ID__c;
                        addInfo.sfId = addressPrograms[0].Id;
                    }
                }
            }else{
                List<Address_With_Program__c> addressPrograms = [
                    SELECT Id,Account_Address__c, Program__c, IsDeleted,
                           Customer_Line_Oracle_ID__c, Status__c
                    FROM Address_With_Program__c
                    WHERE IsDeleted = false
                    AND Status__c = 'A'
                    AND Account_Address__c = :addressId];
                if (addressPrograms != null && addressPrograms.size() > 0){
                    addInfo.oracelId = addressPrograms[0].Customer_Line_Oracle_ID__c;
                    addInfo.sfId = addressPrograms[0].Id;
                }
            }



        }

        return addInfo;
    }

    /**
     * in parts order progress,when customer is ordering, the data of the field named bill to should be
     * found from service authorized brand associated bawb firstly.
     * scenarios:
     * 1、only one brand product in this order, try to find out this brand's authorized brand, if there is
     * no result, select one authorized brand randomly.
     * 2、the brand in this order can not be found in authorized brands' brand in db, select one
     * authorized brand randomly.
     * 3、multiple brands in this order, select one authorized brand randomly.
     * 4、if there is no service authorized in db, try to find sales authorized brand then select associated bawb.
     */
    public static AddressOracleInfo getPartsOrderAddressOracelId(String addressId, String customerId,
                                                                String brandsItems, Boolean isBilling){
        AddressOracleInfo addInfo = new AddressOracleInfo();
        List<String> approveMaplist = new List<String>();
        approveMaplist.add('');
        approveMaplist.add('Approved');
        List<String> allBrands = 'EGO&SKIL&SKILSAW&FLEX'.split('&');

        // service and sales authorized brand, 0,1 are service, 2,3 are sales
        List<String> lstSGType = new List<String>();
        lstSGType.add(CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME);
        lstSGType.add(CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME);
        lstSGType.add(CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_DEVELOPER_NAME);
        lstSGType.add(CCM_Constants.SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_DEVELOPER_NAME);

        if (String.isNotBlank(addressId) && String.isNotBlank(customerId)){
            // all authbrand in this customer
            Map<Id, Sales_Program__c> allAuthBrandInCustomer = new Map<Id, Sales_Program__c>();
            // billing address logic below
            if (isBilling){
                allAuthBrandInCustomer = new Map<Id,Sales_Program__c>([
                    SELECT Id, Brands__c, Approval_Status__c, Customer__c, RecordType.DeveloperName
                    FROM Sales_Program__c
                    WHERE Brands__c IN :allBrands
                    AND Customer__c = :customerId
                    AND Approval_Status__c in :approveMaplist
                    AND RecordType.DeveloperName in :lstSGType
                ]);

                List<String> lstBrandItems = brandsItems.split('&');

                // all address_with_program in this customer
                List<Address_With_Program__c> allAddPrograms = [
                        SELECT Id,Account_Address__c, Program__c, IsDeleted,
                               Customer_Line_Oracle_ID__c, Status__c,
                               Program__r.Brands__c,
                               Program__r.RecordType.DeveloperName
                        FROM Address_With_Program__c
                        WHERE IsDeleted = false
                        AND Program__c IN :allAuthBrandInCustomer.keySet()
                        AND Status__c = 'A'
                        AND Account_Address__c = :addressId];
                // divide these address_with_program into service and sales,
                Map<Id,Address_With_Program__c> mapServieAwp = new Map<Id,Address_With_Program__c>();
                Map<Id,Address_With_Program__c> mapSalesAwp = new Map<Id,Address_With_Program__c>();

                for (Address_With_Program__c awp : allAddPrograms) {
                    // service
                    if ((awp.Program__r.RecordType.DeveloperName == lstSGType[0]
                        || awp.Program__r.RecordType.DeveloperName == lstSGType[1])
                        ) {
                        mapServieAwp.put(awp.Id,awp);
                    // sales
                    } else if (awp.Program__r.RecordType.DeveloperName == lstSGType[2]
                        || awp.Program__r.RecordType.DeveloperName == lstSGType[3]
                        ) {
                        mapSalesAwp.put(awp.Id,awp);
                    }
                }

                List<Address_With_Program__c> addressPrograms = null;

                // if address exists in service address_with_program
                if (mapServieAwp.keySet().size() > 0) {
                    addressPrograms = mapServieAwp.values();

                // if address exists in sales address_with_program
                } else if (mapSalesAwp.keySet().size() > 0) {
                    addressPrograms = mapSalesAwp.values();

                } else {
                    // do nothing, if code into this code snippet, that means there are some errors in this ustomer's data
                }

                if (addressPrograms != null && addressPrograms.size() > 0){
                    // brands in result
                    Set<String> setResBrands = new Set<String>();
                    for (Address_With_Program__c awp : addressPrograms) {
                        // set colection is case-sensitive
                        // convert to lowcase
                        setResBrands.add(awp.Program__r.Brands__c.toLowerCase());
                    }
                    // is there a brand == product items' brand in approved authorized brand in db?
                    // true - yes, false - no
                    Boolean blMatchBrand = false;
                    // use the same brand authorized brand to store bill to which will be used in the invoice
                    // if lstBrandItems.size() == 1 is true, that means, there is only one brand of product items in this order
                    if (lstBrandItems.size() == 1
                        // set collection is case-sensitive
                        // convert to lowcase
                        && setResBrands.contains(new List<String>(lstBrandItems)[0].toLowerCase())

                    ) {
                        blMatchBrand = true;
                    }
                    // the only one brand is not equal to authorized brands' brand
                    if (blMatchBrand) {
                        for (Address_With_Program__c awp : addressPrograms) {
                            if (awp.Program__r.Brands__c == new List<String>(lstBrandItems)[0]) {
                                addInfo.oracelId = awp.Customer_Line_Oracle_ID__c;
                                addInfo.sfId = awp.Id;
                                break;
                            }
                        }
                    // multipule brands items in this order
                    // or no the same brand authorized brand
                    } else {
                        addInfo.oracelId = addressPrograms[0].Customer_Line_Oracle_ID__c;
                        addInfo.sfId = addressPrograms[0].Id;
                    }

                    // finally, if addInfo.oracleId or addInfo.sfId is still null,that means the bawb's data is incomplete
                    // the customer's data need to be repaired.
                }
            // shipping address logic below
            } else {
                List<Address_With_Program__c> addressPrograms = [
                    SELECT Id,Account_Address__c, Program__c, IsDeleted,
                            Customer_Line_Oracle_ID__c, Status__c
                    FROM Address_With_Program__c
                    WHERE IsDeleted = false
                    AND Status__c = 'A'
                    AND Account_Address__c = :addressId];
                if (addressPrograms != null && addressPrograms.size() > 0){
                    addInfo.oracelId = addressPrograms[0].Customer_Line_Oracle_ID__c;
                    addInfo.sfId = addressPrograms[0].Id;
                }
            }
        }
        return addInfo;
    }

    public class AddressOracleInfo {
        public String oracelId;
        public String sfId;
        public AddressOracleInfo(){
            this.oracelId = '';
            this.sfId = '';
        }
    }

    //根据当前用户选的地址信息
    public static Address_With_Program__c getAddressInfo(String addressId){
        Address_With_Program__c address = new Address_With_Program__c();
        if(String.isNotBlank(addressId)){
            List<Address_With_Program__c> addressInfos = [
                SELECT Id, Account_Address__c,
                       Account_Address__r.Address1__c,
                       Account_Address__r.Address2__c,
                       Account_Address__r.State__c,
                       Account_Address__r.Country__c,
                       Account_Address__r.City__c,
                       Account_Address__r.Postal_Code__c,
                       Account_Address__r.Contact__r.Name,
                       Account_Address__r.Name 
                FROM Address_With_Program__c
                WHERE Id = :addressId];
            address = addressInfos[0];
        }
        return address;
    }

    //Order上Customer的临时地址取地址中间表信息的处理
    public static AddressOracleInfo getAlternativeAddressInfo(String customerId, String brandName) {
        AddressOracleInfo addInfo = new AddressOracleInfo();
        if (String.isNotBlank(customerId) && String.isNotBlank(brandName)) {
            for (Account_Address__c objAA : [
                SELECT (SELECT Customer_Line_Oracle_ID__c FROM Addresses_With_Program__r LIMIT 1)
                FROM Account_Address__c
                WHERE
                    ((X2nd_Tier_Dealer__c = :customerId
                    AND X2nd_Tier_Dealer__r.Distributor_or_Dealer__c = '2nd Tier Dealer')
                    OR (Customer__c = :customerId
                    AND Customer__r.Distributor_or_Dealer__c != '2nd Tier Dealer'))
                    AND (RecordType_Name__c = 'Shipping_Address'
                    OR RecordType_Name__c = 'Dropship_Shipping_Address')
                    AND Approval_Status__c = 'Approved'
                    AND Active__c = TRUE
                LIMIT 1
            ]) {
                for (Address_With_Program__c objAP : objAA.Addresses_With_Program__r) {
                    addInfo.oracelId = objAP.Customer_Line_Oracle_ID__c;
                    addInfo.sfId = objAP.Id;
                }
            }
        }
        return addInfo;
    }

    //根据RecordId 获取对象的Object Name和RecordType
    public static ObjectInfo getObjectInfo(String recordId){
        ObjectInfo objInfo = new ObjectInfo();
        if (String.isNotBlank(recordId)){
            objInfo.objectName = findObjectNameFromRecordIdPrefix(recordId);
            String recordType = '';
            if (objInfo.objectName == 'Purchase_Order__c'){
                Purchase_Order__c po = [SELECT Id, RecordType.DeveloperName FROM Purchase_Order__c WHERE Id =: recordId];
                recordType = po.RecordType.DeveloperName;
            }else if (objInfo.objectName == 'Order'){
                Order order = [SELECT Id, RecordType.DeveloperName FROM Order WHERE Id =: recordId];
                recordType = order.RecordType.DeveloperName;
            }
            objInfo.isPlaceOrder = recordType == 'Place_Order' ? true : false;
        }

        return objInfo;
    }

    public class ObjectInfo {
        @AuraEnabled public String objectName {get; set;}
        @AuraEnabled public Boolean isPlaceOrder {get; set;}

        public ObjectInfo(){
            this.isPlaceOrder = true;
        }
    }

    //异常日志记录
    public static String logIntegration(String logName,String apexClassName, String method,
                                      String errMsg, String reqJsonBody,String resJsonBody){
        Log__c expLog = new Log__c();
        expLog.Name = logName;
        expLog.ApexName__c = apexClassName;
        expLog.Method__c = method;
        expLog.Error_Message__c = errMsg.left(100000);
        expLog.ReqParam__c = reqJsonBody.left(100000);
        expLog.ResParam__c = resJsonBody.left(100000);
        Database.insert(expLog, false);

        // A_API_LOG_SWITCH = Y ，表明，现在是在做测试，salesforce 要保存所有的接口数据报文。
        if( String.isNotBlank(reqJsonBody) && Label.A_API_LOG_SWITCH == 'Y'){
            createNotes('JSON Payload:'+expLog.Id,reqJsonBody,expLog.Id);
        }

        return expLog.Id;
    }
    // 异常日志附件
    public static void createNotes(String title, String notes, Id exceptionLogId) {
        CCM_CountCaseAttachmentHandler.boolToRun = false;
        notes = String.isEmpty(notes) ? '<br/>' : notes.escapeXML().replace('\r\n', '<br>').replace('\r', '<br>').replace('\n', '<br>').replace('&apos;', '&#39;');
        ContentVersion newContent = new ContentVersion(Title = title
                                                    ,VersionData = Blob.ValueOf(notes)
                                                    ,PathOnClient = title+'.snote'
                                                    ,FirstPublishLocationId = exceptionLogId);
        Insert newContent;
    }

    //是否当前用户是Inner User
    public static Boolean isInnerUser(){
        String userType = UserInfo.getUserType();
        if(userType == 'Standard'){
            return true;
        }else{
            return false;
        }
    }

    //判断Inside Sales代提的Customer是否有DropShip Address，如果没有不能代提
    public static Boolean hasDropShipAddress(String customerId){
        Boolean hasDropShipAddress = false;
        if (String.isNotBlank(customerId)){
            List<Account_Address__c> accAddressList = [
                SELECT Id, Customer__c, RecordType.DeveloperName,
                       Approval_Status__c
                FROM Account_Address__c
                WHERE Customer__c =: customerId
                AND Approval_Status__c = 'Approved'
                AND RecordType.DeveloperName = 'Dropship_Shipping_Address'];

            if (accAddressList != null && accAddressList.size() > 0){
                hasDropShipAddress = true;
            }
        }

        return hasDropShipAddress;
    }

    public static String formatDecimal (Decimal val, Integer scale) {
        val = val.setScale(scale, System.RoundingMode.HALF_UP);
        String surfix = '.';
        for (Integer i = 0; i < scale; i++ ) {
            surfix += '0';
        }
        if (Integer.valueOf(val) == val) {
            return val.format()+surfix;
        } else {
            return val.format();
        }
    }

    //如果是DropShip的Order，根据不同Brand获取对应的Freight Term
    public static Map<String, String> getDropShipFreightTermMap(Boolean isCCA){
        Map<String, String> freightTermMap = new Map<String, String>();
        List<Customer_Brand_Pricebook_Mapping__c> bpList = [
                SELECT Id,
                       Name,
                       Brand__c,
                       Customer_Type__c,
                       Authorized_Brand_Type__c,
                       Feight_Term__c,
                       Type__c,
                       ORG_Code__c
                FROM Customer_Brand_Pricebook_Mapping__c
                WHERE Type__c = 'Terms'];
        if (bpList != null && bpList.size() > 0){
            for (Customer_Brand_Pricebook_Mapping__c cbp : bpList){
                if(isCCA && cbp.ORG_Code__c !='CCA') continue;
                if(!isCCA && cbp.ORG_Code__c == 'CCA') continue;
                if (freightTermMap.get(cbp.Brand__c) == null){
                    freightTermMap.put(cbp.Brand__c, cbp.Feight_Term__c);
                }
            }
        }

        return freightTermMap;
    }

    public static Map<String, String> getDropShipFreightTermMap2(){
        Map<String, String> freightTermMap = new Map<String, String>();
        List<Customer_Brand_Pricebook_Mapping__c> bpList = [
                SELECT Id,
                       Name,
                       Brand__c,
                       Customer_Type__c,
                       Authorized_Brand_Type__c,
                       Feight_Term__c,
                       Type__c
                FROM Customer_Brand_Pricebook_Mapping__c
                WHERE Type__c = 'Terms'];
        if (bpList != null && bpList.size() > 0){
            for (Customer_Brand_Pricebook_Mapping__c cbp : bpList){
                String brand = cbp.Brand__c;
                if(String.isNotBlank(brand)){
                    brand = brand.toUpperCase();
                }
                if (freightTermMap.get(brand) == null){
                    freightTermMap.put(brand, cbp.Feight_Term__c);
                }
            }
        }

        return freightTermMap;
    }

    //get special dropship freight term
    public static Map<String, String> getSpecialDropShipFreightTermMap () {
        Map<String, String> freightTermMap = new Map<String, String>();
        List<Customer_Brand_Pricebook_Mapping__c> bpList = [
                SELECT Id,
                       Name,
                       Brand__c,
                       Customer_Type__c,
                       Authorized_Brand_Type__c,
                       Feight_Term__c,
                       Billing_Address_With_Authorized_Brand__c,
                       Type__c
                FROM Customer_Brand_Pricebook_Mapping__c
                WHERE Type__c = 'Dropship'
                AND Special_Dropship_Address__c = true
                AND Billing_Address_With_Authorized_Brand__c != NULL
                AND Feight_Term__c != NULL];
        if (bpList != null && bpList.size() > 0) {
            for (Customer_Brand_Pricebook_Mapping__c cbp : bpList) {
                if (freightTermMap.get(cbp.Billing_Address_With_Authorized_Brand__c) == null) {
                    freightTermMap.put(cbp.Billing_Address_With_Authorized_Brand__c, cbp.Feight_Term__c);
                }
            }
        }
        return freightTermMap;
    }

    //Fetch the Set<productId> from price book entry
    public static String getPriceBookEntryCondition(String brandName, String customerId, Boolean isDropShip){
        String sqlString = '';
        if (String.isNotBlank(brandName) && String.isNotBlank(customerId)){
            /*List<Account> accInfo = [SELECT Id, Distributor_or_Dealer__c FROM Account WHERE Id=: customerId];*/
            Set<String> brandNameSet = new Set<String>();
            brandNameSet.addAll(brandName.Split('&'));
            Account acc = [SELECT Id,Name,ORG_Code__c FROM Account WHERE Id = :customerId];
            Set<String> orgSet = new  Set<String>();
            if(acc.ORG_Code__c == 'CNA' || String.isEmpty(acc.ORG_Code__c)){
                orgSet.add('CNA');
                orgSet.add(null);
            }else{
                orgSet.add('CCA');
            }
            //获取客户上的AuthBrand信息
            List<Sales_Program__c> authBrandList;
            if(isDropShip){
                authBrandList = [
                    SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
                           Approval_Status__c, IsDeleted, RecordType.DeveloperName,
                           Price_Book__c, Contract_Price_Book__c,Price_Book_Mapping__c,
                           Price_Book_Mapping__r.Name, Contract_Price_Book__r.Price_Book__r.Contract_Price_Book_OracleID__c,
                           (SELECT Id, Address_Type__c, Program__r.Price_Book__c, Special_Dropship_Address__c, Customer_Line_Oracle_ID__c
                                FROM Addresses_With_Program__r
                                WHERE Address_Type__c = 'Dropship Billing Address' AND Special_Dropship_Address__c = true
                                AND Customer_Line_Oracle_ID__c != null)
                    FROM Sales_Program__c
                    WHERE Customer__c =: customerId
                    AND Approval_Status__c = 'Approved'
                    AND Brands__c IN :brandNameSet
                    AND RecordType.DeveloperName IN ('Dropship_Sales_Standard', 'Dropship_Sales_Customized')
                    AND IsDeleted = false];
            }else{
                authBrandList = [
                    SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
                           Approval_Status__c, IsDeleted, RecordType.DeveloperName,
                           Price_Book__c, Contract_Price_Book__c,Price_Book_Mapping__c,
                           Price_Book_Mapping__r.Name, Contract_Price_Book__r.Price_Book__r.Contract_Price_Book_OracleID__c,
                           (SELECT Id, Address_Type__c, Program__r.Price_Book__c, Special_Dropship_Address__c, Customer_Line_Oracle_ID__c
                                FROM Addresses_With_Program__r
                                WHERE Address_Type__c = 'Dropship Billing Address' AND Special_Dropship_Address__c = true
                                AND Customer_Line_Oracle_ID__c != null)
                    FROM Sales_Program__c
                    WHERE Customer__c =: customerId
                    AND Approval_Status__c = 'Approved'
                    AND Brands__c IN :brandNameSet
                    AND RecordType.DeveloperName != 'Service'
                    AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME
                    AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME
                    AND IsDeleted = false];
            }

            if (authBrandList != null && authBrandList.size() > 0){
                Set<String> programPriceBookIds = new Set<String>();
                Set<String> specialDropShipAWPIds = new Set<String>();
                Set<String> brandNames = new Set<String>();
                Set<String> priceBookIds = new Set<String>();
                //Add by Abby on 7/16/2020
                Set<String> contractPBOracleIdSet = new Set<String>();
                String customerType = authBrandList[0].Customer__r.Distributor_or_Dealer__c;
                //获取Auth Brand上的授权品牌的价格册
                for (Sales_Program__c authBrand : authBrandList){
                    if (!priceBookIds.contains(authBrand.Price_Book__c)){
                        priceBookIds.add(authBrand.Price_Book__c);
                    }
                    Boolean isSpecialDropshipAddress = false;
                    //update by nick 20200714: special dropship pricebook get from Price And Terms Reference which Special_Dropship_Address__c is true
                    if (isDropShip && authBrand.Addresses_With_Program__r != null && authBrand.Addresses_With_Program__r.size() > 0) {
                        if (authBrand.Addresses_With_Program__r[0].Special_Dropship_Address__c == true) {
                            isSpecialDropshipAddress = true;
                            programPriceBookIds.add(authBrand.Addresses_With_Program__r[0].Program__r.Price_Book__c);
                            specialDropShipAWPIds.add(authBrand.Addresses_With_Program__r[0].Id);
                            brandNames.add(authBrand.Brands__c);
                        }
                    }
                    //如果Auth Brand下面Price Book是Contract Price List，判断下面的价格册是否有主副价格册信息，如果有，拿到主价格册下面的副价格册的OracleId的合集
                    if (!isSpecialDropshipAddress && authBrand.Price_Book_Mapping__r != null && authBrand.Price_Book_Mapping__r.Name == 'Contract Price List'){
                        if (authBrand.Contract_Price_Book__r != null && authBrand.Contract_Price_Book__r.Price_Book__r != null){
                            if (authBrand.Contract_Price_Book__r.Price_Book__r.Contract_Price_Book_OracleID__c != null){
                                String oracleIdStr = authBrand.Contract_Price_Book__r.Price_Book__r.Contract_Price_Book_OracleID__c;
                                contractPBOracleIdSet.addAll(oracleIdStr.split(','));
                            }
                        }
                    }
                }
                //获取产品的标准价格册（非DropShip 和 DropShip）
                if (!isDropShip){
                    //非DropShip的场景
                    List<Customer_Brand_Pricebook_Mapping__c> bpList1 = [
                            SELECT Id,
                                   Name,
                                   Brand__c,
                                   Customer_Type__c,
                                   Authorized_Brand_Type__c,
                                   Price_Book__c,
                                   Type__c
                            FROM Customer_Brand_Pricebook_Mapping__c
                            WHERE Brand__c IN :brandNameSet
                            AND Type__c = 'Sales'
                            AND Customer_Type__c =:customerType
                            AND Authorized_Brand_Type__c = 'Standard'
                            AND ORG_Code__c IN :orgSet];
                    if (bpList1 != null && bpList1.size() > 0){
                        for (Customer_Brand_Pricebook_Mapping__c book1 : bpList1){
                            if (!priceBookIds.contains(book1.Price_Book__c)){
                                priceBookIds.add(book1.Price_Book__c);
                            }
                        }
                    }
                }else{
                    //DropShip的场景
                    brandNameSet.removeAll(brandNames);
                    List<Customer_Brand_Pricebook_Mapping__c> bpList2 = [
                            SELECT Id,
                                   Name,
                                   Brand__c,
                                   Customer_Type__c,
                                   Price_Book__c,
                                   Type__c
                            FROM Customer_Brand_Pricebook_Mapping__c
                            WHERE Brand__c IN :brandNameSet
                            AND Type__c = 'Dropship'
                            AND Special_Dropship_Address__c != true
                            AND ORG_Code__c IN :orgSet];
                    for (Customer_Brand_Pricebook_Mapping__c book2 : bpList2){
                        if (!priceBookIds.contains(book2.Price_Book__c)){
                            priceBookIds.add(book2.Price_Book__c);
                        }
                    }
                }
                //update by nick 20200714:remove price book that from authorized Brand
                priceBookIds.removeAll(programPriceBookIds);
                Set<String> specialDropShipPriceBookIds = getSpecialDropShipPricebookIds(specialDropShipAWPIds);
                Set<String> contractPriceListIds =  getPriceBookListByOracleId(contractPBOracleIdSet);
                priceBookIds.addAll(specialDropShipPriceBookIds);
                priceBookIds.addAll(contractPriceListIds);
                System.debug(LoggingLevel.INFO, '*** priceBookIds: ' + priceBookIds);
                if (priceBookIds != null && priceBookIds.size() > 0){
                    String priceBookCondition = '';
                    for (String pb : priceBookIds){
                        if (pb != null){
                            priceBookCondition += '\'' + pb + '\',';
                        }
                    }
                    system.debug(priceBookCondition);
                    if (String.isNotBlank(priceBookCondition)){
                        sqlString = 'SELECT Product2Id '
                                + 'FROM PricebookEntry '
                                + 'WHERE IsActive = true '
                                + 'AND IsDeleted = false '
                                + 'AND UnitPrice > 0 ';
                        sqlString += ' AND Pricebook2Id IN (' + priceBookCondition.removeEnd(',') + ')';
                    }
                }
            }
        }
        String orgCode = 'CNA';
        List<Account> accList = [SELECT ORG_Code__c FROM Account WHERE Id =: customerId];
        Map<String,Object> result = new Map<String,Object>();
        result.put('sqlString', sqlString);
        result.put('OrgCode', orgCode);
        if(accList.size() > 0 && accList[0].ORG_Code__c != null){
            result.put('OrgCode', accList[0].ORG_Code__c);
        }

        return JSON.serialize(result);

    }

    /**
     * 获取dropship对应的价格册
     * update by austin
     *  */
    public static string getDropshipPriceBookEntryCondition(String brandName, String customerId, Boolean isDropShip){
        String sqlString = '';
        if (String.isNotBlank(brandName) && String.isNotBlank(customerId)){
            /*List<Account> accInfo = [SELECT Id, Distributor_or_Dealer__c FROM Account WHERE Id=: customerId];*/
            Set<String> brandNameSet = new Set<String>();
            brandNameSet.addAll(brandName.Split('&'));

            //获取客户上的AuthBrand信息
            //update by austin
            List<Sales_Program__c> authBrandList = [
                    SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
                           Approval_Status__c, IsDeleted, RecordType.DeveloperName,
                           Price_Book__c, Contract_Price_Book__c,Price_Book_Mapping__c,
                           Price_Book_Mapping__r.Name, Contract_Price_Book__r.Price_Book__r.Contract_Price_Book_OracleID__c,
                           (SELECT Id, Address_Type__c, Program__r.Price_Book__c, Special_Dropship_Address__c, Customer_Line_Oracle_ID__c
                                FROM Addresses_With_Program__r
                                WHERE Address_Type__c = 'Dropship Billing Address' AND Special_Dropship_Address__c = true
                                AND Customer_Line_Oracle_ID__c != null)
                    FROM Sales_Program__c
                    WHERE Customer__c =: customerId
                    AND Approval_Status__c = 'Approved'
                    AND Brands__c IN :brandNameSet
                    AND RecordType.DeveloperName IN ('Dropship_Sales_Standard', 'Dropship_Sales_Customized')
                    AND IsDeleted = false];


            if (authBrandList != null && authBrandList.size() > 0){
                Set<String> programPriceBookIds = new Set<String>();
                Set<String> specialDropShipAWPIds = new Set<String>();
                Set<String> brandNames = new Set<String>();
                Set<String> priceBookIds = new Set<String>();
                //Add by Abby on 7/16/2020
                Set<String> contractPBOracleIdSet = new Set<String>();
                String customerType = authBrandList[0].Customer__r.Distributor_or_Dealer__c;
                //获取Auth Brand上的授权品牌的价格册
                for (Sales_Program__c authBrand : authBrandList){
                    if (!priceBookIds.contains(authBrand.Price_Book__c)){
                        priceBookIds.add(authBrand.Price_Book__c);
                    }
                    Boolean isSpecialDropshipAddress = false;
                    //update by nick 20200714: special dropship pricebook get from Price And Terms Reference which Special_Dropship_Address__c is true
                    if (isDropShip && authBrand.Addresses_With_Program__r != null && authBrand.Addresses_With_Program__r.size() > 0) {
                        if (authBrand.Addresses_With_Program__r[0].Special_Dropship_Address__c == true) {
                            isSpecialDropshipAddress = true;
                            programPriceBookIds.add(authBrand.Addresses_With_Program__r[0].Program__r.Price_Book__c);
                            specialDropShipAWPIds.add(authBrand.Addresses_With_Program__r[0].Id);
                            brandNames.add(authBrand.Brands__c);
                        }
                    }
                    //如果Auth Brand下面Price Book是Contract Price List，判断下面的价格册是否有主副价格册信息，如果有，拿到主价格册下面的副价格册的OracleId的合集
                    if (!isSpecialDropshipAddress && authBrand.Price_Book_Mapping__r != null && authBrand.Price_Book_Mapping__r.Name == 'Contract Price List'){
                        if (authBrand.Contract_Price_Book__r != null && authBrand.Contract_Price_Book__r.Price_Book__r != null){
                            if (authBrand.Contract_Price_Book__r.Price_Book__r.Contract_Price_Book_OracleID__c != null){
                                String oracleIdStr = authBrand.Contract_Price_Book__r.Price_Book__r.Contract_Price_Book_OracleID__c;
                                contractPBOracleIdSet.addAll(oracleIdStr.split(','));
                            }
                        }
                    }
                }

                //update by nick 20200714:remove price book that from authorized Brand
                priceBookIds.removeAll(programPriceBookIds);
                Set<String> specialDropShipPriceBookIds = getSpecialDropShipPricebookIds(specialDropShipAWPIds);
                Set<String> contractPriceListIds =  getPriceBookListByOracleId(contractPBOracleIdSet);
                priceBookIds.addAll(specialDropShipPriceBookIds);
                priceBookIds.addAll(contractPriceListIds);
                System.debug(LoggingLevel.INFO, '*** priceBookIds: ' + priceBookIds);
                if (priceBookIds != null && priceBookIds.size() > 0){
                    String priceBookCondition = '';
                    for (String pb : priceBookIds){
                        if (pb != null){
                            priceBookCondition += '\'' + pb + '\',';
                        }
                    }
                    system.debug(priceBookCondition);
                    if (String.isNotBlank(priceBookCondition)){
                        sqlString = 'SELECT Product2Id '
                                + 'FROM PricebookEntry '
                                + 'WHERE IsActive = true '
                                + 'AND IsDeleted = false '
                                + 'AND UnitPrice > 0 ';
                        sqlString += ' AND Pricebook2Id IN (' + priceBookCondition.removeEnd(',') + ')';
                    }
                }
            }
        }
        String orgCode = 'CNA';
        List<Account> accList = [SELECT ORG_Code__c FROM Account WHERE Id =: customerId];
        Map<String,Object> result = new Map<String,Object>();
        result.put('sqlString', sqlString);
        result.put('OrgCode', orgCode);
        if(accList.size() > 0 && accList[0].ORG_Code__c != null){
            result.put('OrgCode', accList[0].ORG_Code__c);
        }

        return JSON.serialize(result);
    }


    //根据Price Book OracleId获取PriceBook2的Id
    public static Set<String> getPriceBookListByOracleId(Set<String> oracleIdSet){
        Set<String> priceBookIdSet = new Set<String>();
        if (oracleIdSet != null && oracleIdSet.size() > 0){
            List<Pricebook2> priceBookList = [SELECT Id, Price_Book_OracleID__c FROM Pricebook2 WHERE Price_Book_OracleID__c IN: oracleIdSet];
            if (priceBookList != null && priceBookList.size() > 0){
                for (Pricebook2 pb : priceBookList){
                    priceBookIdSet.add(pb.Id);
                }
            }
        }
        return priceBookIdSet;
    }

    //获取Brands Options （Brand Label）
    public static SelectItem getBrandOption(List<String> brandOpts){
        SelectItem brandScopeItem = new SelectItem();
        if (brandOpts != null && brandOpts.size() > 0){
            Map<String, SelectItem> brandOptMap = getSelectOptMap(new Sales_Program__c(),'Brands__c');
            String finalStrLabel = '';
            String finalStrValue = '';
            for (String str : brandOpts){
                SelectItem item = new SelectItem();
                item = brandOptMap.get(str);
                if (item != null){
                    finalStrLabel = finalStrLabel + ' & ' + item.label;
                    finalStrValue = finalStrValue + '&' + item.value;
                }
            }

            finalStrLabel = finalStrLabel.removeStart(' & ');
            finalStrValue = finalStrValue.removeStart('&');
            brandScopeItem.label = finalStrLabel;
            brandScopeItem.value = finalStrValue;
        }

        return brandScopeItem;
    }

    //获取Freight Fee
    public static Decimal getFreightFeeAmt(String shipperCountryCode,String shipperPostalCode,String recipientCountryCode, String recipientPostalCode, List<Decimal> prodWeightList){

        Decimal actualFreightFee = CCM_SalesFedExRate.getFedExRate(shipperCountryCode, shipperPostalCode, recipientCountryCode, recipientPostalCode, prodWeightList);

        return actualFreightFee;
    }

    //获取CustomSetting Freight Fee Factor
    public static Decimal getFreightFeeFactor(){
        Decimal factor = 0.00;
        List<Freight_Fee_Factor_Setting__c> customSetting = [SELECT Freight_Factor__c FROM Freight_Fee_Factor_Setting__c];
        if (customSetting != null && customSetting.size() > 0){
            factor = customSetting[0].Freight_Factor__c;
        }

        return factor;
    }

    public static void pushExceptionEmail(String IntegrationName,Id logId,String failResult){
        String sendToLabel = Label.CCM_adminEmail;
        String[] sendToArr = sendToLabel.split(';');
        List<User> userlist = [select id from User where Email in:sendToArr];
        List<Id> sendTolist = new List<Id>();
        for(User u : userlist){
            if(!sendTolist.contains(u.Id)){
                sendTolist.add(u.Id);
            }
        }
        /*if(!sendTolist.contains(UserInfo.getUserId())){
            sendTolist.add(UserInfo.getUserId());
        } */
        String mailContent = '';
        String sfUrl = 'https://'+URL.getSalesforceBaseUrl().getHost();
        List<Log__c> loglist = [select id,ResParam__c
                                from Log__c where id =:logId];
        if(loglist.size() > 0){
            mailContent += 'CCM Integration Exception:' + IntegrationName + 'exception.<br/>';
            mailContent += 'Exception Log:' + sfUrl +'/'+ logId + '<br/>';
            mailContent += 'Failure Result:<br/>';
            mailContent += failResult;
            mailContent += 'This is an automatically generated notification by salesforce system, please do not reply to this email.<br/>';
            //mailContent += '失败数据: ' + failResult;
        }
        if (!Test.isRunningTest()){
            CCM_ServiceCallout.callsfEmail(sendTolist, new List<Id>(),'CCM Integration Exception' ,mailContent , false);
        }
    }

    public static Boolean isAgencyUser(Id profileId){
        Boolean isAgencyUser = false;
        List<Profile> profileList = new List<Profile>([
            SELECT Id, Name
            FROM Profile
            WHERE Id = :profileId
        ]);
        if(!profileList.isEmpty() && profileList[0].Name == 'Sales Agency'){
            isAgencyUser = true;
        }
        return isAgencyUser;
    }

    public static List<String> getAgencyId(Id userId){
        List<String> agencyIdList = new List<String>();
        List<Sales_Agency_Contact__c> agencyContactList = new List<Sales_Agency_Contact__c>([
            SELECT Id, Agency__c
            FROM Sales_Agency_Contact__c
            WHERE User__c = :userId
            AND Is_Active__c = TRUE
            AND Agency__r.Is_Active__c = TRUE
        ]);

        for(Sales_Agency_Contact__c agencyContact : agencyContactList){
            agencyIdList.add(agencyContact.Agency__c);
        }

        return agencyIdList;
    }

    public static Id getSalesAgencyIdByOracleId(String oracelId){
        Id agencyId = null;
        if (String.isNotBlank(oracelId)){
            List<Agency__c> agencyList = new List<Agency__c>([
                SELECT Id, Oracle_Id__c
                FROM Agency__c
                WHERE Oracle_Id__c =: oracelId]);

            if (agencyList != null && agencyList.size() > 0){
                agencyId = agencyList[0].Id;
            }
        }

        return agencyId;
    }

    //get Payment Term Option for ego
    public static List<PTSelectItem> getPaymentTermOptions(String customerType, String orgCode){
        List<PTSelectItem> ptOptions = new List<PTSelectItem>();
        if(CCM_Constants.ORG_CODE_CCA == orgCode) {
            for(Payment_Term_For_EGO__mdt ptSetting : [SELECT Description__c, Name__c, Criteria_From__c, Criteria_To__c FROM Payment_Term_For_EGO__mdt
                                                       WHERE Org_Code__c = 'CCA']) {
                PTSelectItem item = new PTSelectItem();
                item.label = ptSetting.Description__c;
                item.value = ptSetting.Name__c;
                item.criteriaFrom = ptSetting.Criteria_From__c;
                item.criteriaTo = ptSetting.Criteria_To__c;
                ptOptions.add(item);
            }
        }
        else {
            for(Payment_Term_For_EGO__mdt ptSetting : [SELECT MasterLabel, Description__c,
                                                        Name__c, Payment_Discount__c,
                                                    Payment_Leadtime__c,Criteria_From__c,
                                                    Criteria_To__c,Customer_Type__c
                                                FROM Payment_Term_For_EGO__mdt
                                                WHERE Customer_Type__c =: customerType
                                                AND Org_Code__c != 'CCA']) {
                PTSelectItem item = new PTSelectItem();
                item.label = ptSetting.Description__c;
                item.value = ptSetting.Name__c;
                item.criteriaFrom = ptSetting.Criteria_From__c;
                item.criteriaTo = ptSetting.Criteria_To__c;
                ptOptions.add(item);
            }
        }
        return ptOptions;
    }

    public class PTSelectItem {
        @AuraEnabled public String label {get; set;}
        @AuraEnabled public String value {get; set;}
        @AuraEnabled public Decimal criteriaFrom {get; set;}
        @AuraEnabled public Decimal criteriaTo {get; set;}
    }

    //CA Province Tax Rate Map
    public static Map<String, CA_Tax_Rate_By_Province__mdt> getCanadaTaxRateMap(){
        Map<String, CA_Tax_Rate_By_Province__mdt> rateMap = new Map<String, CA_Tax_Rate_By_Province__mdt>();
        for(CA_Tax_Rate_By_Province__mdt rateObj : [SELECT
                                                    Province_Code__c,
                                                    GST__c,
                                                    HST__c,
                                                    PST__c,
                                                    QST__c,
                                                    Total_Rate__c
                                                 FROM CA_Tax_Rate_By_Province__mdt]){
            rateMap.put(rateObj.Province_Code__c, rateObj);
        }
        return rateMap;
    }
    //Add by Wells at 2023-01-04. used to get the Freight fee by ORG code and and product price.
    public static Decimal getFreightFeeByProdPriceAndOrgCode(String orgCode, Decimal productPrice){
        Decimal freightFee=0;
        if(String.isBlank(orgCode)){
            orgCode='CNA';  // used for prvious code which with out any org code
        }
        if(String.isNotBlank(orgCode) && productPrice!=null)
            for(System_Configuration__c sc : [SELECT Max_Cost__c,Minimum_Cost__c,Freight_Charge__c FROM System_Configuration__c where Org_Code__c=:orgCode AND Is_Active__c = true]){
                if(sc.Minimum_Cost__c <= productPrice && productPrice <= sc.Max_Cost__c){
                    freightFee= sc.Freight_Charge__c ;
                }
            }
        return  freightFee;
    }
    //Add by Wells at 2023-01-04. used to get the Freight fee by ORG code and and product price.
    public static System_Configuration__c getFreightAndWaviedFee(String orgCode, Decimal productPrice){

        if(String.isBlank(orgCode)){
            orgCode='CNA';  // used for prvious code which with out any org code
        }
        if(String.isNotBlank(orgCode) && productPrice != null){
            for(System_Configuration__c sc : [
                                            SELECT
                                                Max_Cost__c,Minimum_Cost__c,Freight_Charge__c,Freight_Fee_Waived__c
                                            FROM System_Configuration__c
                                            WHERE Id != null
                                                AND Org_Code__c=:orgCode
                                                AND Is_Active__c = true]){
                if(sc.Minimum_Cost__c <= productPrice && productPrice <= sc.Max_Cost__c){
                    return  sc;
                }
            }
        }

        return  null;
    }

    public static Date parseDate(String dateStr, String localeKey) {
        String parseDateStr = dateStr;
        if(localeKey == 'en_CA') {
            List<String> dateItemList = dateStr.split('/');
            if(dateItemList.size() == 3) {
                parseDateStr = dateItemList[2] + '-' + dateItemList[0].leftPad(2, '0') + '-' + dateItemList[1];
            }
            return Date.valueOf(parseDateStr);
        }
        else {
            return Date.parse(parseDateStr);
        }
    }

    public static Map<String, String> getPicklistLabelValueMap(String objName, String fieldName) {
        Map<String, String> pickListValuesMap = new Map<String, String>();
        Map<String, Schema.SObjectType> schemaMap = Schema.getGlobalDescribe();
        Schema.SObjectType objSchema = schemaMap.get(objName);
        Map<String, Schema.SObjectField> fieldMap = objSchema.getDescribe().fields.getMap();
        Schema.DescribeFieldResult fieldResult = fieldMap.get(fieldName).getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        for( Schema.PicklistEntry pickListVal : ple){
            pickListValuesMap.put(pickListVal.label, pickListVal.value);
        }
        return pickListValuesMap;
    }
}