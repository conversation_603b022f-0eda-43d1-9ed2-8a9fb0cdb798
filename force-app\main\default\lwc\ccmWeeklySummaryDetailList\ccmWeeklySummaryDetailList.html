<template>
    <div class="slds-card">
        <template if:false={isSalesDirector}>
            <div>
                <lightning-button name="new" label="New Weekly Summary" variant="brand" style="float:right; padding-bottom:10px;" onclick={directToDetailPageNew}></lightning-button>
            </div>
        </template>
        <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-table_striped slds-table_fixed-layout" style="table-layout: fixed; width: 100%;">
            <thead>
                <tr class="slds-line-height_reset">
                    <th class="slds-text-title_caps slds-text-align_center" scope="col" style="width: 80px;">
                        <div class="slds-truncate" title="Action">Action</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col" style="width: 120px;">
                        <div class="slds-truncate" title="Name">Name</div>
                    </th>
                    <th class="slds-text-title_caps sortable-header" scope="col" style="width: 100px; cursor: pointer;" data-field="createdBy" onclick={handleSort}>
                        <div style="display: flex; align-items: center; justify-content: space-between;" title="Created By">
                            <span>Created By</span>
                            <span>
                                <lightning-icon icon-name="utility:arrowup" size="xx-small" class="sort-icon" data-direction="asc" style="display: none;"></lightning-icon>
                                <lightning-icon icon-name="utility:arrowdown" size="xx-small" class="sort-icon" data-direction="desc" style="display: none;"></lightning-icon>
                            </span>
                        </div>
                    </th>
                    <th class="slds-text-title_caps sortable-header" scope="col" style="width: 100px; cursor: pointer;" data-field="Status" onclick={handleSort}>
                        <div style="display: flex; align-items: center; justify-content: space-between;" title="Status">
                            <span>Status</span>
                            <span>
                                <lightning-icon icon-name="utility:arrowup" size="xx-small" class="sort-icon" data-direction="asc" style="display: none;"></lightning-icon>
                                <lightning-icon icon-name="utility:arrowdown" size="xx-small" class="sort-icon" data-direction="desc" style="display: none;"></lightning-icon>
                            </span>
                        </div>
                    </th>
                    <th class="slds-text-title_caps sortable-header" scope="col" style="width: 80px; cursor: pointer;" data-field="region" onclick={handleSort}>
                        <div style="display: flex; align-items: center; justify-content: space-between;" title="Region">
                            <span>Region</span>
                            <span>
                                <lightning-icon icon-name="utility:arrowup" size="xx-small" class="sort-icon" data-direction="asc" style="display: none;"></lightning-icon>
                                <lightning-icon icon-name="utility:arrowdown" size="xx-small" class="sort-icon" data-direction="desc" style="display: none;"></lightning-icon>
                            </span>
                        </div>
                    </th>
                    <th class="slds-text-title_caps sortable-header" scope="col" style="width: 60px; cursor: pointer;" data-field="year" onclick={handleSort}>
                        <div style="display: flex; align-items: center; justify-content: space-between;" title="Year">
                            <span>Year</span>
                            <span>
                                <lightning-icon icon-name="utility:arrowup" size="xx-small" class="sort-icon" data-direction="asc" style="display: none;"></lightning-icon>
                                <lightning-icon icon-name="utility:arrowdown" size="xx-small" class="sort-icon" data-direction="desc" style="display: none;"></lightning-icon>
                            </span>
                        </div>
                    </th>
                    <th class="slds-text-title_caps sortable-header" scope="col" style="width: 70px; cursor: pointer;" data-field="month" onclick={handleSort}>
                        <div style="display: flex; align-items: center; justify-content: space-between;" title="Month">
                            <span>Month</span>
                            <span>
                                <lightning-icon icon-name="utility:arrowup" size="xx-small" class="sort-icon" data-direction="asc" style="display: none;"></lightning-icon>
                                <lightning-icon icon-name="utility:arrowdown" size="xx-small" class="sort-icon" data-direction="desc" style="display: none;"></lightning-icon>
                            </span>
                        </div>
                    </th>
                    <th class="slds-text-title_caps sortable-header" scope="col" style="width: 60px; cursor: pointer;" data-field="week" onclick={handleSort}>
                        <div style="display: flex; align-items: center; justify-content: space-between;" title="Week">
                            <span>Week</span>
                            <span>
                                <lightning-icon icon-name="utility:arrowup" size="xx-small" class="sort-icon" data-direction="asc" style="display: none;"></lightning-icon>
                                <lightning-icon icon-name="utility:arrowdown" size="xx-small" class="sort-icon" data-direction="desc" style="display: none;"></lightning-icon>
                            </span>
                        </div>
                    </th>
                    <th class="slds-text-title_caps" scope="col" style="width: 200px;">
                        <div class="slds-truncate" title="Weekly Highlights">Weekly Highlights</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col" style="width: 150px;">
                        <div class="slds-truncate" title="Competitive Info">Competitive Info</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col" style="width: 180px;">
                        <div class="slds-truncate" title="Next Week's Focus">Next Week's Focus</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col" style="width: 120px;">
                        <div class="slds-truncate" title="Comments">Comments</div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <template if:true={data} for:each={data} for:item="row">
                    <tr key={row.id} class="slds-hint-parent">
                        <td class="slds-text-align_center" style="width: 80px; max-width: 80px;">
                            <div style="display: flex; justify-content: center; align-items: center; gap: 5px;">
                                <template if:true={row.showView}>
                                    <lightning-icon
                                        icon-name="action:preview"
                                        alternative-text="View"
                                        title="View"
                                        size="xx-small"
                                        class="iconpoint"
                                        style="cursor: pointer;"
                                        data-action="view"
                                        data-recordid={row.id}
                                        onclick={handleIconClick}>
                                    </lightning-icon>
                                </template>
                                <template if:true={row.showEdit}>
                                    <lightning-icon
                                        icon-name="action:new_note"
                                        alternative-text="Edit"
                                        title="Edit"
                                        size="xx-small"
                                        class="iconpoint"
                                        style="cursor: pointer;"
                                        data-action="edit"
                                        data-recordid={row.id}
                                        onclick={handleIconClick}>
                                    </lightning-icon>
                                </template>
                            </div>
                        </td>
                        <td style="width: 120px; max-width: 120px;">
                            <div class="slds-truncate" title={row.yearWeek}>{row.yearWeek}</div>
                        </td>
                        <td style="width: 100px; max-width: 100px;">
                            <div class="slds-truncate" title={row.createdBy}>{row.createdBy}</div>
                        </td>
                        <td style="width: 100px; max-width: 100px;">
                            <div class="slds-truncate" title={row.status}>{row.status}</div>
                        </td>
                        <td style="width: 80px; max-width: 80px;">
                            <div class="slds-truncate" title={row.region}>{row.region}</div>
                        </td>
                        <td style="width: 60px; max-width: 60px;">
                            <div class="slds-truncate" title={row.year}>{row.year}</div>
                        </td>
                        <td style="width: 70px; max-width: 70px;">
                            <div class="slds-truncate" title={row.month}>{row.month}</div>
                        </td>
                        <td style="width: 60px; max-width: 60px;">
                            <div class="slds-truncate" title={row.week}>{row.week}</div>
                        </td>
                        <td style="width: 200px; max-width: 200px;">
                            <div class="slds-truncate" title={row.weeklyHighlights}>{row.weeklyHighlights}</div>
                        </td>
                        <td style="width: 150px; max-width: 150px;">
                            <div class="slds-truncate" title={row.competitiveInfo}>{row.competitiveInfo}</div>
                        </td>
                        <td style="width: 180px; max-width: 180px;">
                            <div class="slds-truncate" title={row.nextWeekFocus}>{row.nextWeekFocus}</div>
                        </td>
                        <td style="width: 120px; max-width: 120px;">
                            <div class="slds-truncate" title={row.comments}>{row.comments}</div>
                        </td>
                    </tr>
                </template>
            </tbody>
        </table>
    </div>
</template>