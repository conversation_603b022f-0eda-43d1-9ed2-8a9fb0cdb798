<template>
    <div class="slds-card">
        <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-table_striped">
            <thead>
                <tr class="slds-line-height_reset">
                    <th class="slds-text-title_caps slds-text-align_center" scope="col">
                        <div class="slds-truncate" title="Action">Action</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Name">Name</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Created By">Created By</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Region">Region</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Year">Year</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Month">Month</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Week">Week</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Weekly Highlights">Weekly Highlights</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Competitive Info">Competitive Info</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Other">Other</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Next Week's Focus">Next Week's Focus</div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <template if:true={data} for:each={data} for:item="row">
                    <tr key={row.id} class="slds-hint-parent">
                        <td class="slds-text-align_center">
                            <div style="display: flex; justify-content: center; align-items: center; gap: 5px;">
                                <template if:true={row.showView}>
                                    <lightning-icon
                                        icon-name="action:preview"
                                        alternative-text="View"
                                        title="View"
                                        size="xx-small"
                                        class="iconpoint"
                                        style="cursor: pointer;"
                                        data-action="view"
                                        data-recordid={row.id}
                                        onclick={handleIconClick}>
                                    </lightning-icon>
                                </template>
                                <template if:true={row.showEdit}>
                                    <lightning-icon
                                        icon-name="action:new_note"
                                        alternative-text="Edit"
                                        title="Edit"
                                        size="xx-small"
                                        class="iconpoint"
                                        style="cursor: pointer;"
                                        data-action="edit"
                                        data-recordid={row.id}
                                        onclick={handleIconClick}>
                                    </lightning-icon>
                                </template>
                            </div>
                        </td>
                        <td>
                            <div class="slds-truncate" title={row.yearWeek}>{row.yearWeek}</div>
                        </td>
                        <td>
                            <div class="slds-truncate" title={row.createdBy}>{row.createdBy}</div>
                        </td>
                        <td>
                            <div class="slds-truncate" title={row.region}>{row.region}</div>
                        </td>
                        <td>
                            <div class="slds-truncate" title={row.year}>{row.year}</div>
                        </td>
                        <td>
                            <div class="slds-truncate" title={row.month}>{row.month}</div>
                        </td>
                        <td>
                            <div class="slds-truncate" title={row.week}>{row.week}</div>
                        </td>
                        <td>
                            <div class="slds-truncate" title={row.weeklyHighlights}>{row.weeklyHighlights}</div>
                        </td>
                        <td>
                            <div class="slds-truncate" title={row.competitiveInfo}>{row.competitiveInfo}</div>
                        </td>
                        <td>
                            <div class="slds-truncate" title={row.other}>{row.other}</div>
                        </td>
                        <td>
                            <div class="slds-truncate" title={row.nextWeekFocus}>{row.nextWeekFocus}</div>
                        </td>
                    </tr>
                </template>
            </tbody>
        </table>
    </div>
</template>