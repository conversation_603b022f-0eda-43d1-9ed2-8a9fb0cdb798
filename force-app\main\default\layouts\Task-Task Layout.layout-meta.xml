<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Task Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Call_Subject__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Purpose__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ActivityDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Date_of_Visit_Call__c</field>
            </layoutItems>
            <!-- <layoutItems>
                <behavior>Readonly</behavior>
                <field>Recording_URL__c</field>
            </layoutItems> -->
            <layoutItems>
                <behavior>Required</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Action_Items_Takeaways__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Call_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Country__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Place_of_Purchase__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>dnis__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Account_Address__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Visit_Survey__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>WhatId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>WhoId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Related__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Replacement__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Specific_Issue_Tracking__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Return_Label__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CallDurationInSeconds</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CallDisposition</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sent_From__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Inbound__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsReminderSet</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Priority</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Email</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Task.Demo_Log</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Task.EditDescription</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Task.Defer</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Task.UpdateStatus</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Task.UpdatePriority</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SendSurveyInvitation</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>PrintableView</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>10</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ViewSeries</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>11</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeRecordType</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>12</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FollowupEvent</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>13</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FollowupTask</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>14</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>15</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>DeleteSeries</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>16</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>NewTask</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>LogACall</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewEvent</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedActivityAttachmentList</relatedList>
    </relatedLists>
    <relatedObjects>WhoId</relatedObjects>
    <relatedObjects>WhatId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hC000000JAUpI</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
