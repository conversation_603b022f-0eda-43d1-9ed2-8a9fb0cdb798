trigger Address_Trigger on Account_Address__c(
    before insert,
    before update,
    after insert,
    after update,
    after delete
) {
    if (CCM_SharingUtil.isSharingOnly) {
        new Triggers()
            //Add by yujie on ******** for the sharing to address logic
            .bind(Triggers.Evt.beforeinsert, new CCM_AddressMergeAgencies())
            .bind(Triggers.Evt.beforeinsert, new CCM_AddressASGHandler())
            .bind(Triggers.Evt.beforeupdate, new CCM_AddressMergeAgencies())
            .bind(Triggers.Evt.beforeupdate, new CCM_AddressOwnerUpdateHandler())
            .bind(Triggers.Evt.afterupdate, new CCM_AddressAccessHandler())
            .bind(Triggers.Evt.afterinsert, new CCM_AddressAccessHandler())
            .bind(Triggers.Evt.afterdelete, new CCM_AddressAccessHandler())
            //Add by yujie on ******** for some plus logic
            .bind(Triggers.Evt.afterinsert, new CCM_AddressAccessPlusHandler())
            .bind(Triggers.Evt.afterupdate, new CCM_AddressBatchHandler())
            // share records to address owner
            .bind(Triggers.Evt.afterupdate, new CCM_DataSharingForAddressOwner())
            //validate CA Address State
            .bind(Triggers.Evt.beforeinsert, new CCM_AddressStateValidation())
            .bind(Triggers.Evt.beforeupdate, new CCM_AddressStateValidation())
            .manage();
    } else {
        new Triggers()
            //Added by Shangmin at 2020-12-30 to verify the agency.
            .bind(
                Triggers.Evt.beforeinsert,
                new CCM_VerifyAddressAgencyHandler()
            )
            .bind(
                Triggers.Evt.beforeupdate,
                new CCM_VerifyAddressAgencyHandler()
            )

            //Add by yujie on ******** for the sharing to address logic
            .bind(Triggers.Evt.beforeinsert, new CCM_AddressMergeAgencies())
            .bind(Triggers.Evt.beforeinsert, new CCM_AddressASGHandler())
            .bind(Triggers.Evt.beforeupdate, new CCM_AddressMergeAgencies())
            .bind(Triggers.Evt.afterupdate, new CCM_AddressAccessHandler())
            .bind(Triggers.Evt.afterinsert, new CCM_AddressAccessHandler())
            .bind(Triggers.Evt.afterdelete, new CCM_AddressAccessHandler())

            //.bind(Triggers.Evt.afterinsert, new CCM_AddressWithProgramInsertHandler())
            .bind(
                Triggers.Evt.afterinsert,
                new CCM_CreateContactWithAddressProgram()
            )
            .bind(Triggers.Evt.beforeinsert, new CCM_AddressUpdateFields())
            .bind(Triggers.Evt.beforeupdate, new CCM_AddressUpdateFields())
            .bind(Triggers.Evt.afterinsert, new CCM_AddressUpdateFields())
            .bind(Triggers.Evt.afterupdate, new CCM_AddressUpdateFields())
            
            .bind(Triggers.Evt.beforeinsert, new CCM_GetSubInventoryHandler())
            .bind(Triggers.Evt.beforeupdate, new CCM_GetSubInventoryHandler())
            .bind(
                Triggers.Evt.afterinsert,
                new CCM_ShippingAddressSendApproval()
            )
            .bind(
                Triggers.Evt.afterupdate,
                new CCM_ShippingAddressSendApproval()
            )
            .bind(Triggers.Evt.afterupdate, new CCM_AddressUpdateHandler())
            .bind(Triggers.Evt.afterinsert, new CCM_AgencyHistoryHandler())
            .bind(Triggers.Evt.afterupdate, new CCM_AgencyHistoryHandler())
            .bind(
                Triggers.Evt.afterupdate,
                new CCM_ChangePurchaseOrderHandler()
            )
            .bind(
                Triggers.Evt.afterdelete,
                new CCM_ChangePurchaseOrderHandler()
            )
            //Add by Abby on 06162020 for EDI Customer don't need to validate the contact infortaion
            .bind(
                Triggers.Evt.beforeinsert,
                new CCM_ValidateEDIContactHandler()
            )
            .bind(
                Triggers.Evt.beforeupdate,
                new CCM_ValidateEDIContactHandler()
            )

            //Add by yujie on ******** for some plus logic
            .bind(Triggers.Evt.afterinsert, new CCM_AddressAccessPlusHandler())
            .bind(Triggers.Evt.afterupdate, new CCM_AddressBatchHandler())
            // share records to address owner
            .bind(Triggers.Evt.afterupdate, new CCM_DataSharingForAddressOwner())
            //validate CA Address State
            .bind(Triggers.Evt.beforeinsert, new CCM_AddressStateValidation())
            .bind(Triggers.Evt.beforeupdate, new CCM_AddressStateValidation())
            .manage();
    }
}