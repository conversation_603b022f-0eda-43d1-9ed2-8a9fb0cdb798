public without sharing class CCM_Contants {
  public static final String BILLING_ADDRESS = 'Billing Address';//recordtype Name
  public static final String SHIPPING_ADDRESS = 'Shipping Address';//recordtype Name
    public static final String DROPSHIP_ADDRESS = 'Dropship Address';
    public static final String DROPSHIP_SHIPPING_ADDRESS = 'Dropship Shipping Address';
    public static final String DROPSHIP_BILLING_ADDRESS = 'Dropship Billing Address';
    public static final String BILLING_ADDRESS_RECORDTYPEID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByName().get(CCM_Contants.BILLING_ADDRESS).getRecordTypeId();
    public static final String SHIPPING_ADDRESS_RECORDTYPEID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByName().get(CCM_Contants.SHIPPING_ADDRESS).getRecordTypeId();
    //public static final String DROPSHIP_ADDRESS_RECORDTYPEID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByName().get(CCM_Contants.DROPSHIP_ADDRESS).getRecordTypeId();
    public static final String DROPSHIP_BILLING_ADDRESS_RECORDTYPEID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByName().get(CCM_Contants.DROPSHIP_BILLING_ADDRESS).getRecordTypeId();
    public static final String DROPSHIP_SHIPPING_ADDRESS_RECORDTYPEID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByName().get(CCM_Contants.DROPSHIP_SHIPPING_ADDRESS).getRecordTypeId();

    public static final String CHANNEL = 'Channel';//recordtype Name
    public static final String CHANNEL_RECORDTYPEID = Schema.SObjectType.Account.getRecordTypeInfosByName().get(CCM_Contants.CHANNEL).getRecordTypeId();
    public static final String PERSONACCOUNT = 'End User Account';//recordtype Name
    public static final String PERSONACCOUNT_RECORDTYPEID = Schema.SObjectType.Account.getRecordTypeInfosByName().get(CCM_Contants.PERSONACCOUNT).getRecordTypeId();

    public static final String KIT = 'Kit';//recordtype Name
    public static final String PRODUCT = 'Product';//recordtype Name
    public static final String PARTS = 'Parts';//recordtype Name
    public static final String KIT_RECORDTYPEID = Schema.SObjectType.Product2.getRecordTypeInfosByName().get(CCM_Contants.KIT).getRecordTypeId();
    public static final String PRODUCT_RECORDTYPEID = Schema.SObjectType.Product2.getRecordTypeInfosByName().get(CCM_Contants.PRODUCT).getRecordTypeId();
    public static final String PARTS_RECORDTYPEID = Schema.SObjectType.Product2.getRecordTypeInfosByName().get(CCM_Contants.PARTS).getRecordTypeId();

    public static final String KITS_AND_PRODUCTS = 'Kits and Products';//recordtype Name
    public static final String PRODUCTS_AND_PARTS = 'Products and Parts';//recordtype Name
    public static final String PRODUCTS_AND_DIAGRAM = 'Products and Diagram';//recordtype Name
    public static final String KITS_AND_PRODUCTS_RECORDTYPEID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByName().get(CCM_Contants.KITS_AND_PRODUCTS).getRecordTypeId();
    public static final String PRODUCTS_AND_PARTS_RECORDTYPEID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByName().get(CCM_Contants.PRODUCTS_AND_PARTS).getRecordTypeId();
    public static final String PRODUCTS_AND_DIAGRAM_RECORDTYPEID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByName().get(CCM_Contants.PRODUCTS_AND_DIAGRAM).getRecordTypeId();
    public static final String PLACE_ORDER = 'Order';//recordtype Name
    public static final String PLACE_ORDER_RECORDTYPEID = Schema.SObjectType.ORDER.getRecordTypeInfosByName().get(CCM_Contants.PLACE_ORDER).getRecordTypeId();
    public static final String PLACE_PARTS_ORDER = 'Parts Order';//recordtype Name
    public static final String PLACE_PARTS_ORDER_RECORDTYPEID = Schema.SObjectType.ORDER.getRecordTypeInfosByName().get(CCM_Contants.PLACE_PARTS_ORDER).getRecordTypeId();


    public static final String PLACE_PURCHASE_ORDER = 'Place Order';//recordtype Name
    public static final String PLACE_PURCHASE_ORDER_RECORDTYPEID = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByName().get(CCM_Contants.PLACE_PURCHASE_ORDER).getRecordTypeId();



    public static final String SERVICE_PROGRAM = 'Service';//recordtype Name
    public static final String SERVICE_PROGRAM_RECORDTYPEID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByName().get(CCM_Contants.SERVICE_PROGRAM) == null ? '' : Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByName().get(CCM_Contants.SERVICE_PROGRAM).getRecordTypeId();
    public static final String SALES_STANDARD_PROGRAM = 'Standard';//recordtype Name
    public static final String SALES_STANDARD_PROGRAM_RECORDTYPEID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByName().get(CCM_Contants.SALES_STANDARD_PROGRAM) == null ? '' : Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByName().get(CCM_Contants.SALES_STANDARD_PROGRAM).getRecordTypeId();
    public static final String SALES_CUSTOMIZED_PROGRAM = 'Customized';//recordtype Name
    public static final String SALES_CUSTOMIZED_PROGRAM_RECORDTYPEID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByName().get(CCM_Contants.SALES_CUSTOMIZED_PROGRAM) == null ? '' : Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByName().get(CCM_Contants.SALES_CUSTOMIZED_PROGRAM).getRecordTypeId();


    public static final String PROMOTION_SPECIAL_CUSTOMER = 'Specified Customer';//recordtype Name
    public static final String PROMOTION_SPECIAL_CUSTOMER_RECORDTYPEID = Schema.SObjectType.Promotion_Target_Group__c.getRecordTypeInfosByName().get(CCM_Contants.PROMOTION_SPECIAL_CUSTOMER).getRecordTypeId();
    public static final Decimal SALES_POTENTIAL1 = 250000;
    public static final Decimal SALES_POTENTIAL2 = 500000;
    public static final Decimal SALES_POTENTIAL3 = 1000000;
    public static final Decimal SALES_POTENTIAL4 = 5000000;
    public static final Decimal SALES_POTENTIALCCA1 = 5000;
    public static final Decimal SALES_POTENTIALCCA2 = 50000;
    public static final Decimal SALES_POTENTIALCCA3 = 250000;
    public static final Decimal SALES_POTENTIALCCA4 = 750000;

    public static final Decimal SALES_POTENTIAL_POINT1 = 10;
    public static final Decimal SALES_POTENTIAL_POINT2 = 25;
    public static final Decimal SALES_POTENTIAL_POINT3 = 50;
    public static final Decimal SALES_POTENTIAL_POINT4 = 75;
    public static final Decimal SALES_POTENTIAL_POINT5 = 100;

    public static final Decimal SALES_POTENTIAL_POINT_WEIGHT = 0.35;
    public static final Decimal STORE_SIZE_WEIGHT = 0.2;
    public static final Decimal NUMBER_OF_OUTSIDE_SALES_WEIGHT = 0.25;
    public static final Decimal WILLINGNESS_TO_WORK_WITH_CHERVON_WEIGHT = 0.2;

    public static final Decimal PNTENTIAL_INPUT_POINT1 = 13;
    public static final Decimal PNTENTIAL_INPUT_POINT2 = 35;
    public static final Decimal PNTENTIAL_INPUT_POINT3 = 56;
    public static final Decimal PNTENTIAL_INPUT_POINT4 = 78;

    @AuraEnabled
    public static String deleteOrderInfo(String recordId) {
        if (String.isNotBlank(recordId)){
            try{
                delete [SELECT Id FROM Purchase_Order__c WHERE Id =: recordId];
                return 'Success';
            }catch(Exception ex){
                System.debug(LoggingLevel.INFO, '*** ex.getStackTraceString(): ' + ex.getStackTraceString());
            }
        }

        return 'Fail';
    }




}