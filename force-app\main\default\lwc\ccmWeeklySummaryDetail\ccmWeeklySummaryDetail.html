<template>
    <div class="slds-card">
        <div class="slds-section slds-is-open">
            <h3 class="slds-section__title slds-theme_shade">
                <span class="slds-truncate slds-p-horizontal_small" title="Project Information">Weekly Summary</span>
            </h3>
        </div>

        <div class="slds-section slds-is-open">
            <div class="slds-section__content">
                <template if:true={isEdit}>
                    <lightning-layout multiple-rows="true">
                        <lightning-layout-item size="12" padding="around-small">
                            <lightning-textarea name="weeklyHighlights" required value={weeklyHighlights} label="Weekly Highlights" data-name="weeklyHighlights" onblur={handleInputChange}></lightning-textarea>
                            <span>Dealer Comments, Successes/Wins, Failures, Special Accomplishments, Product Issues, Sales Obstacles, etc.</span>
                        </lightning-layout-item>
                        <lightning-layout-item size="12" padding="around-small">
                            <lightning-textarea name="competitiveInfo" required value={competitiveInfo} label="Competitive Info" data-name="competitiveInfo" onblur={handleInputChange}></lightning-textarea>
                            <span>Competitive Intel, What you are hearing and seeing, etc.</span>
                        </lightning-layout-item>
                        <lightning-layout-item size="12" padding="around-small">
                            <lightning-textarea name="other" value={other} required label="Other" data-name="other" onblur={handleInputChange}></lightning-textarea>
                            <span>Important information and insights from the field</span>
                        </lightning-layout-item>
                        <lightning-layout-item size="12" padding="around-small">
                            <lightning-textarea name="nextWeekFocus" required value={nextWeekFocus} label="Next Week's Focus" data-name="nextWeekFocus" onblur={handleInputChange}></lightning-textarea>
                            <span>Your Main areas of focus for next week</span>
                        </lightning-layout-item>
                        <lightning-layout-item size="6" padding="around-small">
                            <lightning-select
                                name="week"
                                label="Week"
                                value={week}
                                options={weekOptions} disabled>
                            </lightning-select>
                        </lightning-layout-item>
                        <lightning-layout-item size="6" padding="around-small">
                            <lightning-select
                                name="month"
                                label="Month"
                                value={month}
                                options={monthOptions} disabled>
                            </lightning-select>
                        </lightning-layout-item>
                        <lightning-layout-item size="6" padding="around-small">
                            <lightning-select
                                name="year"
                                label="Year"
                                value={year}
                                options={yearOptions} disabled>
                            </lightning-select>
                        </lightning-layout-item>
                        <lightning-layout-item size="6" padding="around-small">
                            <lightning-select
                                name="region"
                                label="Region"
                                value={region}
                                options={regionOptions} disabled>
                            </lightning-select>
                        </lightning-layout-item>
                    </lightning-layout>
                </template>


                <template if:false={isEdit}>
                    <lightning-layout multiple-rows="true">
                        <lightning-layout-item size="12" padding="around-small">
                            <div class="SectionDetailHeader">Weekly Highlights</div>
                            <div>{weeklyHighlights}</div>
                        </lightning-layout-item>
                        <lightning-layout-item size="12" padding="around-small">
                            <div class="SectionDetailHeader">Competitive Info</div>
                            <div>{competitiveInfo}</div>
                        </lightning-layout-item>
                        <lightning-layout-item size="12" padding="around-small">
                            <div class="SectionDetailHeader">Other</div>
                            <div>{other}</div>
                        </lightning-layout-item>
                        <lightning-layout-item size="12" padding="around-small">
                            <div class="SectionDetailHeader">Next Week's Focus</div>
                            <div>{nextWeekFocus}</div>
                        </lightning-layout-item>
                        <lightning-layout-item size="6" padding="around-small">
                            <div class="SectionDetailHeader">Year</div>
                            <div>{year}</div>
                        </lightning-layout-item>
                        <lightning-layout-item size="6" padding="around-small">
                            <div class="SectionDetailHeader">Month</div>
                            <div>{month}</div>
                        </lightning-layout-item>
                        <lightning-layout-item size="6" padding="around-small">
                            <div class="SectionDetailHeader">Week</div>
                            <div>{week}</div>
                        </lightning-layout-item>
                        <lightning-layout-item size="6" padding="around-small">
                            <div class="SectionDetailHeader">Region</div>
                            <div>{region}</div>
                        </lightning-layout-item>
                        <lightning-layout-item size="6" padding="around-small">
                            <div class="SectionDetailHeader">Created By</div>
                            <div>{createdBy}</div>
                        </lightning-layout-item>
                        <lightning-layout-item size="6" padding="around-small">
                            <div class="SectionDetailHeader">Submit Date</div>
                            <div>{submitDate}</div>
                        </lightning-layout-item>
                        <lightning-layout-item size="6" padding="around-small">
                            <div class="SectionDetailHeader">Status</div>
                            <div>{status}</div>
                        </lightning-layout-item>
                        <lightning-layout-item size="12" padding="around-small">
                            <div class="SectionDetailHeader">Comments</div>
                            <template for:each={commentList} for:item="item" for:index="index">
                                <div key={item}>{item}</div>
                            </template>
                        </lightning-layout-item>
                    </lightning-layout>

                    <template if:true={isSalesDirector}>
                        <div class="slds-m-top_medium">
                            <lightning-textarea name="newComments" style="padding: 10px" value={newComments} label="Comments" data-name="newComments" onblur={handleInputChange}></lightning-textarea>
                        </div>
                        <div>
                            <lightning-button variant="brand" label="Add Comments" title="Add Comments" onclick={handleAddComment} class="slds-m-left_x-small"></lightning-button>
                        </div>
                    </template>
                </template>
            </div>
        </div>

        <template if:true={isEdit}>
            <div class="slds-m-top_small slds-m-bottom_medium slds-align_absolute-center">
                <lightning-button label="Save as Draft" title="Save as Draft" onclick={handleSave} class="slds-m-left_x-small"></lightning-button>
                <lightning-button variant="brand" label="Submit" title="Submit" onclick={handleSubmit} class="slds-m-left_x-small"></lightning-button>
            </div>
        </template>

        <template if:false={isEdit}>
            <template if:false={isSalesDirector}>
                <div class="slds-m-top_small slds-m-bottom_medium slds-align_absolute-center">
                    <lightning-button label="Edit As Draft" title="Edit As Draft" onclick={handleEditAsDraft} class="slds-m-left_x-small"></lightning-button>
                </div>
            </template>
        </template>
    </div>

    <template if:true={isLoading}>
        <lightning-spinner style="position: fixed;" alternative-text="Loading..."></lightning-spinner>
        <div style="position: fixed;" class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>