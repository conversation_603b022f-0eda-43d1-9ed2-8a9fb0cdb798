({
    doInit : function(component, event, helper) {
        component.set('v.isBusy', true);
        component.set('v.currencySymbol', $A.get("$Locale.currencyCode"));
        component.set('v.orderTypeVal', component.get('v.orderTypeVal'));
        var recordId = component.get("v.recordId");
        console.log('recordID ====', component.get('v.recordId'));
        console.log('用户id', component.get('v.brandScope'));
        if(!recordId){
            recordId = helper.getQueryVariable('recordId');
        }

        var action = component.get("c.getData");
        if(recordId){
            action.setParam('recordId', recordId);
        }
        action.setCallback(this, function (response) {
            console.log('子组件回调函数');
            var state = response.getState();
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    component.set('v.quotation', results.po);
                    if(results.po.ORG_ID__c == 'CCA'){
                        component.set('v.isCCA',true)
                    } else {
                        component.set("v.isShowShippingBy", true);
                    }
                    if (results.po.ShipTo_Name__c != null || results.po.Address_Line1__c !=null || results.po.Address_Line2__c != null || results.po.City__c != null || results.po.State__c != null || results.po.Country__c != null || results.po.Phone_Number__c != null || results.po.Zip_Code__c != null) {
                        component.set('v.isDisplaySection', true);
                    }
                    component.set('v.shippingBy', results.po.Shipping_By__c);
                    component.set('v.freightTermRuleFee', results.freightTermFee);
                    //获取Sales Agency 信息
                    if (results.po.Sales_Agency__c){
                        component.set('v.salesAgencyAlias', results.salesAgencyAlias);
                        component.set('v.salesAgencyId', results.salesAgencyId);
                    }
                    component.set('v.productAmount', results.po.Product_Price__c - Math.abs(results.po.Discount_Amount__c));
                    console.log('productAccount 1--->'+ component.get('v.productAmount'));
                    if (results.po.Freight_Fee__c != 0 && results.po.Freight_Fee__c != undefined){
                        component.set('v.actualShippingFee', results.po.Freight_Fee__c);
                    }
                    component.set('v.customerId', results.po.Customer__c);
                    component.set('v.isInnerUser', results.isInnerUser);
                    console.log('赋值后的用户id', component.get('v.customerId'));
                    if(results.po.Customer__c){
                        var filterConditions = [];
                        var filterCondition = {};
                        filterCondition.FieldName = 'AccountId';
                        filterCondition.Condtion = '=';
                        filterCondition.Value = results.po.Customer__c;
                        filterConditions.push(filterCondition);

                        component.set('v.quotation.Shipping_Priority__c', results.po.Customer__r.Shipment_Priority__c);
                    }

                    component.set("v.isAlternativeAddress", results.po.Is_Alternative_Address__c);
                    var orderType = results.po.Order_Type__c;
                    if (orderType == 'CNA Dropship Order' || orderType == 'CA Dropship Order'){
                        if(!component.get('v.orderTypeVal')){
                            component.set("v.orderTypeVal", 'Y');
                        }
                    }else{
                        if(!component.get('v.orderTypeVal')){
                            component.set("v.orderTypeVal", 'N');
                        }
                    }

                    if (results.po.Is_Alternative_Address__c == true || results.po.Is_Alternative_Address__c == 'true'){
                        component.set('v.isEdit', true);
                    }

                    //set the shipping Method as GROUND
                    component.set('v.quotation.Shipping_Method__c', 'Ground Freight');
                    var handlingFee = 0.00;
                    if (results.po.Product_Price__c >= results.freightTermFee){
                        handlingFee = Number(handlingFee).toFixed(2);
                    }else {
                        handlingFee = results.po.Handling_Fee__c ? Number(results.po.Handling_Fee__c).toFixed(2) : 0.00;
                    }
                    component.set('v.handlingFee', handlingFee);
                    console.log('handling Fee 1--->'+ component.get('v.handlingFee'));
                    //calculate the free shipping fee
                    var actualShippingFee = Number(component.get('v.actualShippingFee'));
                    var waivedFreightTermFee = results.po.Freight_Fee_To_Be_Waived__c ? Number(results.po.Freight_Fee_To_Be_Waived__c) : 0.00;
                    //Shipping by的值
                    var zeroNum = 0.00;
                    var extraFreightFeeToBeWaived = results.po.Extra_Freight_Fee_To_Be_Waived__c ? Number(results.po.Extra_Freight_Fee_To_Be_Waived__c) : Number(zeroNum).toFixed(2);
                    component.set('v.quotation.Extra_Freight_Fee_To_Be_Waived__c',extraFreightFeeToBeWaived);
                    if (results.po.Freight_Term__c == 'COLLECT'){
                        component.set('v.quotation.Shipping_By__c', 'Customer');
                        component.set('v.quotation.Delivery_Supplier__c', 'Others');
                        component.set('v.isDisableShippingBy', true);
                        component.set('v.freeShipping', actualShippingFee.toFixed(2));
                    }else{
                        if (waivedFreightTermFee == 0){
                            component.set('v.quotation.Shipping_By__c', 'Chervon');
                        }else{
                            var shippingBy = !results.po.Shipping_By__c ? '' : results.po.Shipping_By__c;
                            component.set('v.quotation.Shipping_By__c', shippingBy);
                            if(shippingBy == 'Customer' || shippingBy == 'Third Party Billing') {
                                component.set('v.freeShipping', actualShippingFee.toFixed(2));
                            }
                            else {
                                component.set('v.freeShipping', Number(zeroNum).toFixed(2));
                            }
                        }
                        component.set('v.waiveShippingFee', component.get('v.freeShipping'));
                        component.set('v.freeShipping', Number(results.po.Freight_Fee_Waived__c));
                    }

                    //CCA Purhcase Order
                    if(results.po.ORG_ID__c && results.po.ORG_ID__c==='CCA'){
                        component.set('v.isCCA',true);
                        if(results.po.Product_Price__c < 250){
                            var handlingFee = results.po.Product_Price__c / 10;
                            component.set('v.handlingFee', handlingFee.toFixed(2));
                        }
                    }else{
                        component.set('v.isCCA',false);
                    }
                }
            } else {
                var errors = response.getError();
            }
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    previousStep: function(component){
        var currentStep = component.get("v.currentStep");
        component.set("v.currentStep", currentStep - 1);
    },
    nextStep: function(component, event, helper){
        component.set('v.recordId', component.get('v.recordId'));
        component.set('v.isBusy', true);
         var zeroNum = 0.00;
        var quotation = component.get('v.quotation');
         var freightFeeRuleAmt = Number(component.get('v.freightTermRuleFee'));
        var productPriceAmt;
        if(quotation.Discount_Amount__c){
            productPriceAmt = Number(quotation.Product_Price__c) - Math.abs(quotation.Discount_Amount__c);
        }else{
            productPriceAmt = Number(quotation.Product_Price__c);
        }
        if (productPriceAmt == null || productPriceAmt == 0 || productPriceAmt == undefined){
            productPriceAmt = Number(quotation.Product_Price__c);
            component.set('v.freightTermRuleFee', quotation.Freight_Target_Fee__c);
        }
        var currentStep = component.get("v.currentStep");
        if (component.get('v.quotation.Shipping_Address_Name__c') == undefined && component.get('v.isAlternativeAddress') == false){
            component.set('v.actualShippingFee', Number(zeroNum).toFixed(2));
            component.set('v.freeShipping', Number(zeroNum).toFixed(2));
            helper.saveDataFun(component, (currentStep + 1), false);
        } else {
            var action = component.get("c.getFreihtFeeAmount");
            action.setParam('recordId', component.get('v.recordId'));
            action.setParam('quotation', JSON.stringify(component.get('v.quotation')));
            action.setParam('isAlternativeAddress', component.get('v.isAlternativeAddress'));
            action.setParam('freightTermRuleFee', component.get('v.freightTermRuleFee'));
            action.setParam('productAmount', productPriceAmt);
            action.setCallback(this, function (response) {
                var state = response.getState();
                console.log('state test--->'+state);
                if (state === "SUCCESS") {
                    var results = JSON.parse(response.getReturnValue());
                    if(results){
                        component.set('v.salesAgencyAlias', results.salesAgencyAlias);
                        component.set('v.salesAgencyId', results.salesAgencyId);
                        console.log('sales agency alias--->'+ results.salesAgencyAlias);
                        component.set('v.actualShippingFee', Number(results.freightFeeAmt).toFixed(2));
                        var shippingBy = component.get('v.quotation.Shipping_By__c');
                        if(shippingBy == 'Customer' || shippingBy == 'Third Party Billing') {
                            component.set('v.freeShipping', Number(results.freightFeeAmt).toFixed(2));
                        }
                        else {
                            if (productPriceAmt >= freightFeeRuleAmt){
                                component.set('v.freeShipping', Number(results.freightFeeAmt).toFixed(2));
                            }else{
                                component.set('v.freeShipping', Number(zeroNum).toFixed(2));
                            }
                        }
                        if (results.isFreeFreightFee) {
                            component.set('v.freeShipping', Number(zeroNum).toFixed(2));
                        }
                    }
                } else {
                    var errors = response.getError();
                    console.log('errors',errors);
                }
                helper.saveDataFun(component, (currentStep + 1), false);

            });
            $A.enqueueAction(action);
        }

    },
    doSave: function(component, event, helper){
        var currentStep = component.get("v.currentStep");
        component.set('v.isBusy', true);
         var zeroNum = 0.00;
        var quotation = component.get('v.quotation');
         var freightFeeRuleAmt = Number(component.get('v.freightTermRuleFee'));
        var productPriceAmt;
        if(quotation.Discount_Amount__c){
            productPriceAmt = Number(quotation.Product_Price__c) - Math.abs(quotation.Discount_Amount__c);
        }else{
            productPriceAmt = Number(quotation.Product_Price__c);
        }
        if (productPriceAmt == null || productPriceAmt == 0 || productPriceAmt == undefined){
            productPriceAmt = Number(quotation.Product_Price__c);
            component.set('v.freightTermRuleFee', quotation.Freight_Target_Fee__c);
        }
        if (component.get('v.quotation.Shipping_Address_Name__c') == undefined && component.get('v.isAlternativeAddress') == false){
            component.set('v.actualShippingFee', Number(zeroNum).toFixed(2));
            component.set('v.freeShipping', Number(zeroNum).toFixed(2));
            helper.saveDataFun(component, currentStep, true);
        } else {
            var action = component.get("c.getFreihtFeeAmount");
            action.setParam('recordId', component.get('v.recordId'));
            action.setParam('quotation', JSON.stringify(component.get('v.quotation')));
            action.setParam('isAlternativeAddress', component.get('v.isAlternativeAddress'));
            action.setParam('freightTermRuleFee', component.get('v.freightTermRuleFee'));
            action.setParam('productAmount', productPriceAmt);
            action.setCallback(this, function (response) {
                var state = response.getState();
                console.log('state test--->'+state);
                if (state === "SUCCESS") {
                    var results = JSON.parse(response.getReturnValue());
                    if(results){
                        component.set('v.salesAgencyAlias', results.salesAgencyAlias);
                        component.set('v.salesAgencyId', results.salesAgencyId);
                        console.log('sales agency alias--->'+ results.salesAgencyAlias);
                        component.set('v.actualShippingFee', Number(results.freightFeeAmt).toFixed(2));
                        var shippingBy = component.get('v.quotation.Shipping_By__c');
                        if(shippingBy == 'Customer' || shippingBy == 'Third Party Billing') {
                            component.set('v.freeShipping', Number(results.freightFeeAmt).toFixed(2));
                        }
                        else {
                            if (productPriceAmt >= freightFeeRuleAmt){
                                component.set('v.freeShipping', Number(results.freightFeeAmt).toFixed(2));
                            }else{
                                component.set('v.freeShipping', Number(zeroNum).toFixed(2));
                            }
                        }
                        if (results.isFreeFreightFee) {
                            component.set('v.freeShipping', Number(zeroNum).toFixed(2));
                        }
                    }
                } else {
                    var errors = response.getError();
                    console.log('errors',errors);
                }
                helper.saveDataFun(component, currentStep, true);

            });
            $A.enqueueAction(action);
        }
    },
    doAddAddress: function(component, event, helper){
        component.set('v.isAddAddress', true);
    },
    checkState: function (component, event, helper) {
        var stateVal = component.get('v.quotation.State__c');
        var regExpCountryformat = /[A-Z]{2}/;
        if(!$A.util.isEmpty(regExpCountryformat)){
            if(stateVal.match(regExpCountryformat) && stateVal.length == 2){
                component.set('v.showStateMessage', false);
            }else {
                component.set('v.showStateMessage', true);
            }
        }
    },
    doSaveAddress: function(component, event, helper){
        component.set('v.isBusy', true);
        var isMatch = true;
        var countryVal = component.get('v.quotation.Additional_Shipping_Country__c');
        var stateVal = component.get('v.quotation.Additional_Shipping_Province__c');
        console.log('countryVal',countryVal);
        var regExpCountryformat = /[A-Z]{2}/;
        if(!$A.util.isEmpty(regExpCountryformat)){
            if(countryVal.match(regExpCountryformat) && countryVal.length == 2){
                component.set('v.isBusy', false);
            }else{
                component.set('v.isBusy', false);
                isMatch = false;
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": $A.get("$Label.c.CCM_Portal_CountryCodeTips"),
                    "type": "Error",
                    "mode": "pester",
                    "duration": "500"
                });
                toastEvent.fire();
            }
        }

        if(!$A.util.isEmpty(regExpCountryformat)){
            if(stateVal.match(regExpCountryformat) && stateVal.length == 2){
                component.set('v.isBusy', false);
            }else{
                component.set('v.isBusy', false);
                isMatch = false;
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": $A.get("$Label.c.CCM_Portal_ProvinceStateTips"),
                    "type": "Error",
                    "mode": "pester",
                    "duration": "500"
                });
                toastEvent.fire();
            }
        }

        //check CA address
        var isCCA = component.get('v.isCCA');
        if(isCCA){
            var CAStateCodes =['AB','BC','MB','NB','NL','NT','NS','NV','ON','PE','QC','SK','YT'];
            if(countryVal != 'CA' || !CAStateCodes.includes(stateVal)){
                component.set('v.isBusy', false);
                isMatch = false;
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Error"),
                    "message": $A.get("$Label.c.CCM_Portal_PleasefillinCanadacountrycodeandstatecode"),
                    "type": "Error",
                    "mode": "pester",
                    "duration": "500"
                });
                toastEvent.fire();
            }
        }

        if (isMatch == true){
            component.set('v.isAddAddress', false);
            component.set('v.isAlternativeAddress', true);
            var quotation = component.get('v.quotation');
            quotation.Is_Alternative_Address__c = component.get('v.isAlternativeAddress');
            component.set('v.isEdit', true);
            helper.doChangeShippingAddressHandler(component, event, helper);
        }

    },
    doChangeAddress: function(component, event, helper){
        component.set('v.isAlternativeAddress', false);
        var quotation = component.get('v.quotation');
        quotation.Is_Alternative_Address__c = component.get('v.isAlternativeAddress');
        component.set('v.isEdit', false);
        component.set('v.isChangeAddress', true);
    },
    doConfirm: function(component, event, helper){
        debugger;
        var zeroNum = 0.00;
        var shippingBy = component.get('v.quotation.Shipping_By__c');
        component.set('v.isChangeAddress', false);
        component.set('v.isAlternativeAddress', false);
        component.set('v.quotation.Additional_Shipping_Street__c', null);
        component.set('v.quotation.Additional_Shipping_City__c', null);
        component.set('v.quotation.Additional_Shipping_Country__c', null);
        component.set('v.quotation.Additional_Shipping_Province__c', null);
        component.set('v.quotation.Additional_Shipping_Postal_Code__c', null);
        component.set('v.quotation.Additional_Contact_Name__c', null);
        component.set('v.quotation.Additional_Contact_Phone__c', null);
        component.set('v.quotation.Additional_Contact_Email__c', null);
        var quotation = component.get('v.quotation');
        quotation.Is_Alternative_Address__c = component.get('v.isAlternativeAddress');
        var zeroNum = 0.00;
        console.log('test 1');
        if (component.get('v.quotation.Shipping_Address_Name__c') == null || component.get('v.quotation.Shipping_Address_Name__c') == undefined){
            component.set('v.actualShippingFee', Number(zeroNum).toFixed(2));
            if (shippingBy == 'Chervon'){
                component.set('v.freeShipping', Number(component.get('v.waiveShippingFee')).toFixed(2));
            }else{
                component.set('v.freeShipping', Number(component.get('v.actualShippingFee')).toFixed(2));
            }
        }else{
            helper.doChangeShippingAddressHandler(component, event, helper);
        }
    },
    closeModal: function(component, event, helper){
        component.set('v.isAddAddress', false);
    },
    closeChangeModal: function(component, event, helper){
        component.set('v.isChangeAddress', false);
    },
    doChangeShippingBy: function(component, event, helper){
        var shippingBy = component.get('v.quotation.Shipping_By__c');
        if (shippingBy == 'Chervon'){
            component.set('v.quotation.Shipping_Method__c','Ground Freight');
            component.set('v.quotation.Delivery_Supplier__c','');
            component.set('v.quotation.Customer_Freight_Account__c', '');
            component.set('v.freeShipping', Number(component.get('v.waiveShippingFee')).toFixed(2));
        }else{
            if(component.get('v.isCCA')) {
                component.set('v.quotation.Delivery_Supplier__c', 'Others');
            }
            component.set('v.freeShipping', Number(component.get('v.actualShippingFee')).toFixed(2));
        }
    },

    doChangeBillingAddress: function(component, event, helper) {
        // 重置paymentTerm为空
        component.set('v.quotation.Payment_Term__c', component.get('v.paymentTermValue'));
        let billAddressId = component.get('v.quotation.Billing_Address__c');
        if (billAddressId) {
            helper.getCustomerFreightTerm(component);
            let action = component.get('c.getAddressInfo');
            action.setParams({'addressId': billAddressId});
            action.setCallback(this, function(response){
                let state = response.getState();
                if(state === 'SUCCESS') {
                    let npBillTo = response.getReturnValue();
                    component.set('v.quotation.NP_Bill_To__c', npBillTo);
                    if(npBillTo){
                        component.set('v.quotation.Payment_Term__c', 'NA014');
                    }
                }
            });

            $A.enqueueAction(action);
        }
        if (component.get('v.isCCA')) {
            component.set("v.isDisableShippingBy", false);
        }
    },



    doChangeShippingAddress: function(component, event, helper){

        var isCCA = component.get('v.isCCA');
        component.set('v.isBusy', true);
        var zeroNum = 0.00;
        var quotation = component.get('v.quotation');

        // if payment term promo has been selected, bypass the has order check
        helper.checkHasOrder(component);

        var freightFeeRuleAmt = Number(component.get('v.freightTermRuleFee'));
        var productPriceAmt;
        if(quotation.Discount_Amount__c){
            productPriceAmt = Number(quotation.Product_Price__c) - Math.abs(quotation.Discount_Amount__c);
        }else{
            productPriceAmt = Number(quotation.Product_Price__c);
        }
        if (productPriceAmt == null || productPriceAmt == 0 || productPriceAmt == undefined){
            productPriceAmt = Number(quotation.Product_Price__c);
            component.set('v.freightTermRuleFee', quotation.Freight_Target_Fee__c);
        }
        if (component.get('v.isAlternativeAddress') == false){
            if(!isCCA){
                component.set('v.handlingFee', Number(zeroNum).toFixed(2));
            }

        }else{
            if (productPriceAmt >= freightFeeRuleAmt){
                component.set('v.handlingFee', Number(zeroNum).toFixed(2));
            }else{
                if(!isCCA){
                    var handlingFeeFactor = $A.get("$Label.c.CCM_Handling_Fee_Charge_Percent");
                    var handlingFeeVal = productPriceAmt * Number(handlingFeeFactor) / 100;
                    component.set('v.handlingFee', handlingFeeVal.toFixed(2));
                }
            }
        }

        if (component.get('v.quotation.Shipping_Address_Name__c') == undefined){
            component.set('v.actualShippingFee', Number(zeroNum).toFixed(2));
            component.set('v.freeShipping', Number(zeroNum).toFixed(2));
            component.set('v.isBusy', false);
        } else {
            helper.selectAddressCountry(component);
            var action = component.get("c.getFreihtFeeAmount");
            action.setParam('recordId', component.get('v.recordId'));
            action.setParam('quotation', JSON.stringify(component.get('v.quotation')));
            action.setParam('isAlternativeAddress', component.get('v.isAlternativeAddress'));
            action.setParam('freightTermRuleFee', component.get('v.freightTermRuleFee'));
            action.setParam('productAmount', productPriceAmt);
            action.setCallback(this, function (response) {
                var state = response.getState();
                console.log('state test--->'+state);
                if (state === "SUCCESS") {
                    var results = JSON.parse(response.getReturnValue());
                    if(results){
                        component.set('v.salesAgencyAlias', results.salesAgencyAlias);
                        component.set('v.salesAgencyId', results.salesAgencyId);
                        console.log('sales agency alias--->'+ results.salesAgencyAlias);
                        component.set('v.actualShippingFee', Number(results.freightFeeAmt).toFixed(2));
                        var shippingBy = component.get('v.quotation.Shipping_By__c');
                        if(shippingBy == 'Customer' || shippingBy == 'Third Party Billing') {
                            component.set('v.freeShipping', Number(results.freightFeeAmt).toFixed(2));
                        }
                        else {
                            if (productPriceAmt >= freightFeeRuleAmt){
                                component.set('v.freeShipping', Number(results.freightFeeAmt).toFixed(2));
                            }else{
                                component.set('v.freeShipping', Number(zeroNum).toFixed(2));
                            }
                        }
                        if (results.isFreeFreightFee) {
                            component.set('v.freeShipping', Number(zeroNum).toFixed(2));
                        }

                    }
                } else {
                    var errors = response.getError();
                    console.log('errors',errors);
                }
                component.set('v.isBusy', false);
            });
            $A.enqueueAction(action);
        }
    }
})