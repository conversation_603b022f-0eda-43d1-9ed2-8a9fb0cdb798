<?xml version="1.0" encoding="UTF-8"?>
<LightningMessageChannel xmlns="http://soap.sforce.com/2006/04/metadata">
    <description>Message Channel for LiveX Chatbot</description>
    <isExposed>true</isExposed>
    <lightningMessageFields>
        <description>This is the function arguments to be sent to LiveX Chatbot</description>
        <fieldName>args</fieldName>
    </lightningMessageFields>
    <lightningMessageFields>
        <description>This is the function name to be called on the LiveX Chatbot</description>
        <fieldName>function</fieldName>
    </lightningMessageFields>
    <lightningMessageFields>
        <description>This is the summary sent by LiveX Chatbot</description>
        <fieldName>summary</fieldName>
    </lightningMessageFields>
    <lightningMessageFields>
        <description>Unique identifier for tracking requests and responses</description>
        <fieldName>requestId</fieldName>
    </lightningMessageFields>
    <lightningMessageFields>
        <description>Type of message (REQUEST, RESPONSE, SUMMARY_INSERT)</description>
        <fieldName>messageType</fieldName>
    </lightningMessageFields>
    <lightningMessageFields>
        <description>The result data from LiveX API responses</description>
        <fieldName>result</fieldName>
    </lightningMessageFields>
    <lightningMessageFields>
        <description>Error message if the request failed</description>
        <fieldName>error</fieldName>
    </lightningMessageFields>
    <lightningMessageFields>
        <description>Specific component that should handle this message</description>
        <fieldName>targetComponent</fieldName>
    </lightningMessageFields>
    <masterLabel>LiveXChatbot</masterLabel>
</LightningMessageChannel>
