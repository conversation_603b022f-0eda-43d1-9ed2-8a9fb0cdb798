@isTest
private class CCM_Quotation_DetailCtlTest {
    static testMethod void testMethod1() {
        User admin = [SELECT Id, Username, UserRoleId FROM User WHERE Profile.Name = 'System Administrator'  AND UserRoleId != null AND IsActive = true LIMIT 1];
        System.runAs(admin){
            Account acc = new Account();
            acc.AccountNumber = '1789933';
            acc.Sales_Group__c = 'SG01';
            acc.AccountNumber = '1001';
            acc.PaymentMethod__c = 'CHECK';
            acc.TaxID__c = '123456';
            acc.Director_Approver__c = UserInfo.getUserId();
            acc = (Account)CCM_TESTDataUtil.createSobject(acc, 'Channel');

            CCM_ShippingAddressSendApproval.isRun = false;
            Sales_Program__c program = new Sales_Program__c();
            program.Customer__c = acc.Id;
            program.Approval_Status__c = 'Approved';
            program.Director_Approver__c = UserInfo.getUserId();
            program.brands__c = 'EGO';
            program.Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.';
            program = (Sales_Program__c)CCM_TESTDataUtil.createSobject(program, 'Standard');

            Sales_Program__c program2 = new Sales_Program__c();
            program2.Customer__c = acc.Id;
            program2.Approval_Status__c = 'Approved';
            program2.Director_Approver__c = UserInfo.getUserId();
            program2.brands__c = 'EGO';
            program2.Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.';
            program2 = (Sales_Program__c)CCM_TESTDataUtil.createSobject(program2, 'Dropship_Sales_Standard');

            Account_Address__c address = new Account_Address__c();
            address.Approval_Status__c = 'Approved';
            address.Customer__c = acc.Id; 
            address = (Account_Address__c)CCM_TESTDataUtil.createSobject(address, 'Dropship_Shipping_Address');

            //Purchase Order
            Purchase_Order__c po = new Purchase_Order__c();
            po.Customer__c = acc.Id;
            po.Status__c = 'Submitted';
            po.Sync_Status__c = 'In Processing';
            po.Expected_Delivery_Date__c = Date.today();
            po.Freight_Fee__c = 10;
            po.Freight_Term__c = 'COLLECT';
            po.Payment_Term__c = 'NA001';
            po.Brand_Scope__c = 'EGO&SKIL';
            po.Shipping_Address__c = address.Id;
            po = (Purchase_Order__c)CCM_TESTDataUtil.createSobject(po, 'Place_Order');

            Purchase_Order__c po1 = new Purchase_Order__c();
            po1.Customer__c = acc.id;
            po1 = (Purchase_Order__c)CCM_TESTDataUtil.createSobject(po1,'Place_Order');

            Purchase_Order_Item__c poItem1 = new Purchase_Order_Item__c();
            poItem1.Purchase_Order__c = po1.Id;
            poItem1 = (Purchase_Order_Item__c)CCM_TESTDataUtil.createSobject(poItem1,'');

            Pricebook2 priceBook = new Pricebook2();
            priceBook = (Pricebook2)CCM_TESTDataUtil.createSobject(priceBook, '');

            Product2 prod = new Product2();
            prod.Lanch_Date__c = system.today().adddays(-30);
            prod.Name = '15" Foldable Shaft String Trimmer Kit (5.0Ah Battery, 210W Charger)';
            prod.IsActive = true;
            prod.Brand_Name__c = 'EGO';
            prod.Short_description__c = '15" Foldable Shaft String Trimmer Kit (5.0Ah Battery, 210W Charger)';
            prod.ProductCode = 'ST1504-S';
            prod.RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Product2').get('Product');
            prod.Source__c = 'PIM';
            prod.PIM_ExternalID__c = 'ST1504-S-PIM';
            insert prod;

            Product2 prod2 = new Product2();
            prod2.Name = '15" Foldable Shaft String Trimmer Kit (5.0Ah Battery, 210W Charger)';
            prod2.IsActive = true;
            prod2.Brand_Name__c = 'EGO';
            prod2.Short_description__c = '15" Foldable Shaft String Trimmer Kit (5.0Ah Battery, 210W Charger)';
            prod2.ProductCode = 'ST1504-S';
            prod2.RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Product2').get('Product');
            prod2.Source__c = 'EBS';
            prod2.PIM_ExternalID__c = 'ST1504-S-EBS';
            prod2.Lanch_Date__c = system.today().adddays(-30);
            // prod = (Product2)CCM_TESTDataUtil.createSobject(prod, 'Product');
            insert prod2;
            PricebookEntry entry = new PricebookEntry();
            entry.Description__c = 'TETS';
            entry.Product2Id = prod.Id;
            entry.Price_Book_Line_OracleID__c = '693903';
            entry.Pricebook2Id = priceBook.Id;
            entry.UnitPrice = 103;
            insert entry;

            Purchase_Order_Item__c poItem = new Purchase_Order_Item__c();
            poItem.Purchase_Order__c = po.Id;
            poItem.Product__c = prod.Id;
            poItem.Price_Book__c = priceBook.Id;
            poItem.Line_Type__c = 'CNA General Line';
            poItem.ProductCode__c = prod.ProductCode;
            poItem.Is_Over_Size_Product__c = true;
            poItem.Unit_Price__c = 100;
            poItem.List_Price__c = 100;
            poItem.Gross_Weight__c = 10;
            poItem.Quantity__c = 10;
            poItem = (Purchase_Order_Item__c)CCM_TESTDataUtil.createSobject(poItem,'');

            CCM_Quotation_DetailCtl.getData(po.Id, acc.Id);
            String poId = po.Id;
            String accId = acc.Id;
            String retString = CCM_Quotation_DetailCtl.getDataByInnerUser(poId, accId, 'Y');

            Promotion2__c promotion1 = new Promotion2__c();
            promotion1.Name = 'test1';
            insert promotion1;

            Promotion2__c promotion2 = new Promotion2__c();
            promotion2.Name = 'test2';
            insert promotion2;

            Purchase_Order__c poString = new Purchase_Order__c();
            poString.Customer__c = acc.Id;
            poString.Shipping_By__c = 'Chervon';
            poString.Shipping_priority__c = '5';
            poString.Freight_Fee__c = 20.83;
            poString.Freight_Fee_To_Be_Waived__c = 0.00;
            poString.Extra_Freight_Fee_To_Be_Waived__c = 0.00;
            poString.Delivery_Supplier__c = 'FedEx';
            poString.Customer_Freight_Account__c = '134353';
            poString.Customer_PO_Num__c = '**********';
            poString.Expected_Delivery_Date__c = Date.today();
            poString.Email__c = '<EMAIL>';
            poString.Notes__c = 'test';
            poString.Total_Quantity__c = 1;
            poString.Handling_Fee__c = 0.00;
            poString.Product_Price__c = 789;
            poString.Is_DropShip__c = false;
            poString.Is_Alternative_Address__c = true;
            poString.Additional_Shipping_Street__c = '1345 Street';
            poString.Additional_Shipping_City__c = 'Chicago';
            poString.Additional_Shipping_Country__c = 'US';
            poString.Additional_Shipping_Province__c = 'IL';
            poString.Additional_Shipping_Postal_Code__c = '22323';
            poString.Additional_Contact_Name__c = 'test user';
            poString.Additional_Contact_Phone__c = '*********';
            poString.Additional_Contact_Email__c = '<EMAIL>';
            poString.Discount_Amount__c = 0.00;
            poString.Surcharge_Amount__c = 1.00;

            List<Purchase_Order_Item__c> orderItemDataList = new List<Purchase_Order_Item__c>();
            Purchase_Order_Item__c poItemString = new Purchase_Order_Item__c();
            poItemString.ProductCode__c = prod.ProductCode;
            poItemString.Brand__c = prod.Brand_Name__c;
            poItemString.Product__c = prod.Id;
            poItemString.Ship_Date__c = Date.today();
            poItemString.Quantity__c = 1;
            poItemString.Unit_Price__c = 100;
            poItemString.List_Price__c = 108;
            poItemString.Sub_Total__c = 100;
            poItemString.Discount_Amount__c = 0.00;
            poItemString.Gross_Weight__c = 1.59;
            poItemString.Is_Over_Size_Product__c = true;
            poItemString.Promotion__c = promotion1.Id;
            poItemString.PromotionName__c = 'SBG210791';
            poItemString.Whole_Order_Promotion__c = promotion2.Id;
            poItemString.Promo_Discount_Amount__c = 1.00;
            poItemString.Whole_Order_Promo_Discount_Amount__c = 1.00;
            poItemString.Is_Initial__c = true;
            poItemString.Surcharge_Amount__c = 1.00;
            orderItemDataList.add(poItemString);

            CCM_Quotation_DetailCtl.savePurchaseOrder(JSON.serialize(poString), JSON.serialize(orderItemDataList), acc.Id);

            CCM_Quotation_DetailCtl.saveData(JSON.serialize(poString), JSON.serialize(orderItemDataList), 1);
            
            CCM_Quotation_DetailCtl.getPaymentFreightTerm(acc.Id, po.Brand_Scope__c, true, null);

            CCM_Quotation_DetailCtl.getDropShipPaymentFreightTerm(acc.Id, po.Brand_Scope__c, 'Y');

            CCM_Quotation_DetailCtl.deleteChangedBrandPO(JSON.serialize(poString));

            CCM_Quotation_DetailCtl.caculateTotalTax(poString);
            
            poString.Is_Alternative_Address__c = false;
            poString.Shipping_Address__c = address.Id;
            poString.ORG_ID__c = 'CCA';
            Test.startTest();
            CCM_Quotation_DetailCtl.saveData(JSON.serialize(poString), JSON.serialize(orderItemDataList), 1);

            Purchase_Order__c poString1 = new Purchase_Order__c();
            poString1.Customer__c = acc.Id;
            poString1.Shipping_By__c = 'Chervon';
            poString1.Shipping_priority__c = '5';
            poString1.Freight_Fee__c = 20.83;
            poString1.Freight_Fee_To_Be_Waived__c = 0.00;
            poString1.Extra_Freight_Fee_To_Be_Waived__c = 0.00;
            poString1.Delivery_Supplier__c = 'FedEx';
            poString1.Customer_Freight_Account__c = '134353';
            poString1.Customer_PO_Num__c = '**********';
            poString1.Expected_Delivery_Date__c = Date.today();
            poString1.Email__c = '<EMAIL>';
            poString1.Notes__c = 'test';
            poString1.Total_Quantity__c = 1;
            poString1.Handling_Fee__c = 0.00;
            poString1.Product_Price__c = 789;
            poString1.Is_DropShip__c = false;
            poString1.Is_Alternative_Address__c = true;
            poString1.Additional_Shipping_Street__c = '1345 Street';
            poString1.Additional_Shipping_City__c = 'Chicago';
            poString1.Additional_Shipping_Country__c = 'US';
            poString1.Additional_Shipping_Province__c = 'IL';
            poString1.Additional_Shipping_Postal_Code__c = '22323';
            poString1.Additional_Contact_Name__c = 'test user';
            poString1.Additional_Contact_Phone__c = '*********';
            poString1.Additional_Contact_Email__c = '<EMAIL>';
            poString1.Discount_Amount__c = 0.00;
            insert poString1;
		
            CCM_Quotation_DetailCtl.deleteChangedBrandPO(JSON.serialize(poString1));

            CCM_Quotation_DetailCtl.deleteQuotation(poItem1.Id);

            CCM_Quotation_DetailCtl.getFreihtFeeAmount(po.Id, JSON.serialize(po), false, 100,2000);
            CCM_Quotation_DetailCtl.getFreihtFeeAmount(po.Id, JSON.serialize(po), true, 3000,2000);
            CCM_Quotation_DetailCtl.getPromotion('SBG210791', acc.Id, false, false);
            CCM_Quotation_DetailCtl.checkPromotions(new List<String>{'SBG210791'}, acc.Id);
            CCM_Quotation_DetailCtl.getPaymentTerm(acc.Id, 'EGO', null,5000);

            CCM_Quotation_DetailCtl.deleteQuotation(new List<String>());
            CCM_Quotation_DetailCtl.getPriceBookEntryCondition('EGO', acc.Id, false);
            CCM_Quotation_DetailCtl.submitData(poString1.Id, false);
            CCM_Quotation_DetailCtl.getPriceBook(prod.Id, acc.Id, 'Y');
            CCM_Quotation_DetailCtl.getPriceBook(prod.Id, acc.Id, 'N');
        	Test.stopTest();
        }
        
       
    }
     static testMethod void testMethod2() {
        User admin = [SELECT Id, Username, UserRoleId FROM User WHERE Profile.Name = 'System Administrator'  AND UserRoleId != null AND IsActive = true LIMIT 1];
        System.runAs(admin){
            Account acc = new Account();
            acc.AccountNumber = '1789933';
            acc.Sales_Group__c = 'SG01';
            acc.AccountNumber = '1001';
            acc.Director_Approver__c = UserInfo.getUserId();
            acc.PaymentMethod__c = 'CHECK';
            acc.TaxID__c = '123456';
            acc = (Account)CCM_TESTDataUtil.createSobject(acc, 'Channel');
            CCM_ShippingAddressSendApproval.isRun = false;
            
            Sales_Program__c program = new Sales_Program__c();
            program.Customer__c = acc.Id;
            program.Approval_Status__c = 'Approved';
            program.Director_Approver__c = UserInfo.getUserId();
            program.brands__c = 'EGO';
            program.Payment_Term_Description__c = '0% discount is given if the invoice is paid within 30 days, 31th will be overdue day.';
            program = (Sales_Program__c)CCM_TESTDataUtil.createSobject(program, 'Standard');

            Account_Address__c address = new Account_Address__c();
            address.Approval_Status__c = 'Approved';
            address.Customer__c = acc.Id; 
            address = (Account_Address__c)CCM_TESTDataUtil.createSobject(address, 'Dropship_Shipping_Address');

            //Purchase Order
            Purchase_Order__c po = new Purchase_Order__c();
            po.Customer__c = acc.Id;
            po.Status__c = 'Submitted';
            po.Sync_Status__c = 'In Processing';
            po.Expected_Delivery_Date__c = Date.today();
            po.Freight_Fee__c = 10;
            po.Freight_Term__c = 'COLLECT';
            po.Payment_Term__c = 'NA001';
            po.Brand_Scope__c = 'EGO&SKIL';
            po.Shipping_Address__c = address.Id;
            po = (Purchase_Order__c)CCM_TESTDataUtil.createSobject(po, 'Place_Order');

            Purchase_Order__c po1 = new Purchase_Order__c();
            po1.Customer__c = acc.id;
            po1 = (Purchase_Order__c)CCM_TESTDataUtil.createSobject(po1,'Place_Order');

            Purchase_Order_Item__c poItem1 = new Purchase_Order_Item__c();
            poItem1.Purchase_Order__c = po1.Id;
            poItem1 = (Purchase_Order_Item__c)CCM_TESTDataUtil.createSobject(poItem1,'');

            Pricebook2 priceBook = new Pricebook2();
            priceBook = (Pricebook2)CCM_TESTDataUtil.createSobject(priceBook, '');

            Product2 prod = new Product2();
            prod.Lanch_Date__c = system.today().adddays(-30);
            prod.Name = '15" Foldable Shaft String Trimmer Kit (5.0Ah Battery, 210W Charger)';
            prod.IsActive = true;
            prod.Brand_Name__c = 'EGO';
            prod.Short_description__c = '15" Foldable Shaft String Trimmer Kit (5.0Ah Battery, 210W Charger)';
            prod.ProductCode = 'ST1504-S';
            prod.RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Product2').get('Product');
            prod.Source__c = 'PIM';
            prod.PIM_ExternalID__c = 'ST1504-S-PIM';
            insert prod;

            Product2 prod2 = new Product2();
            prod2.Name = '15" Foldable Shaft String Trimmer Kit (5.0Ah Battery, 210W Charger)';
            prod2.IsActive = true;
            prod2.Brand_Name__c = 'EGO';
            prod2.Short_description__c = '15" Foldable Shaft String Trimmer Kit (5.0Ah Battery, 210W Charger)';
            prod2.ProductCode = 'ST1504-S';
            prod2.RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Product2').get('Product');
            prod2.Source__c = 'EBS';
            prod2.PIM_ExternalID__c = 'ST1504-S-EBS';
            prod2.Lanch_Date__c = system.today().adddays(-30);
            // prod = (Product2)CCM_TESTDataUtil.createSobject(prod, 'Product');
            insert prod2;

            PricebookEntry entry = new PricebookEntry();
            entry.Description__c = 'TETS';
            entry.Product2Id = prod.Id;
            entry.Price_Book_Line_OracleID__c = '693903';
            entry.Pricebook2Id = priceBook.Id;
            entry.UnitPrice = 103;
            insert entry;

            Purchase_Order_Item__c poItem = new Purchase_Order_Item__c();
            poItem.Purchase_Order__c = po.Id;
            poItem.Product__c = prod.Id;
            poItem.Price_Book__c = priceBook.Id;
            poItem.Line_Type__c = 'CNA General Line';
            poItem.ProductCode__c = prod.ProductCode;
            poItem.Is_Over_Size_Product__c = true;
            poItem.Unit_Price__c = 100;
            poItem.List_Price__c = 100;
            poItem.Gross_Weight__c = 10;
            poItem.Quantity__c = 10;
            poItem = (Purchase_Order_Item__c)CCM_TESTDataUtil.createSobject(poItem,'');

            CCM_Quotation_DetailCtl.getData(po.Id, acc.Id);
            String poId = po.Id;
            String accId = acc.Id;
            String retString = CCM_Quotation_DetailCtl.getDataByInnerUser(poId, accId, 'N');

            Promotion2__c promotion1 = new Promotion2__c();
            promotion1.Name = 'test1';
            insert promotion1;

            Promotion2__c promotion2 = new Promotion2__c();
            promotion2.Name = 'test2';
            insert promotion2;

            Purchase_Order__c poString = new Purchase_Order__c();
            poString.Customer__c = acc.Id;
            poString.Shipping_By__c = 'Chervon';
            poString.Shipping_priority__c = '5';
            poString.Freight_Fee__c = 20.83;
            poString.Freight_Fee_To_Be_Waived__c = 0.00;
            poString.Extra_Freight_Fee_To_Be_Waived__c = 0.00;
            poString.Delivery_Supplier__c = 'FedEx';
            poString.Customer_Freight_Account__c = '134353';
            poString.Customer_PO_Num__c = '**********';
            poString.Expected_Delivery_Date__c = Date.today();
            poString.Email__c = '<EMAIL>';
            poString.Notes__c = 'test';
            poString.Total_Quantity__c = 1;
            poString.Handling_Fee__c = 0.00;
            poString.Product_Price__c = 789;
            poString.Is_DropShip__c = false;
            poString.Is_Alternative_Address__c = true;
            poString.Additional_Shipping_Street__c = '1345 Street';
            poString.Additional_Shipping_City__c = 'Chicago';
            poString.Additional_Shipping_Country__c = 'US';
            poString.Additional_Shipping_Province__c = 'IL';
            poString.Additional_Shipping_Postal_Code__c = '22323';
            poString.Additional_Contact_Name__c = 'test user';
            poString.Additional_Contact_Phone__c = '*********';
            poString.Additional_Contact_Email__c = '<EMAIL>';
            poString.Discount_Amount__c = 0.00;
            poString.Surcharge_Amount__c = 1.00;

            List<Purchase_Order_Item__c> orderItemDataList = new List<Purchase_Order_Item__c>();
            Purchase_Order_Item__c poItemString = new Purchase_Order_Item__c();
            poItemString.ProductCode__c = prod.ProductCode;
            poItemString.Brand__c = prod.Brand_Name__c;
            poItemString.Product__c = prod.Id;
            poItemString.Ship_Date__c = Date.today();
            poItemString.Quantity__c = 1;
            poItemString.Unit_Price__c = 100;
            poItemString.List_Price__c = 108;
            poItemString.Sub_Total__c = 100;
            poItemString.Discount_Amount__c = 0.00;
            poItemString.Gross_Weight__c = 1.59;
            poItemString.Is_Over_Size_Product__c = true;
            poItemString.Promotion__c = promotion1.Id;
            poItemString.PromotionName__c = 'SBG210791';
            poItemString.Whole_Order_Promotion__c = promotion2.Id;
            poItemString.Promo_Discount_Amount__c = 1.00;
            poItemString.Whole_Order_Promo_Discount_Amount__c = 1.00;
            poItemString.Is_Initial__c = true;
            poItemString.Surcharge_Amount__c = 1.00;
            orderItemDataList.add(poItemString);

            CCM_Quotation_DetailCtl.savePurchaseOrder(JSON.serialize(poString), JSON.serialize(orderItemDataList), acc.Id);

            CCM_Quotation_DetailCtl.saveData(JSON.serialize(poString), JSON.serialize(orderItemDataList), 1);
            
            CCM_Quotation_DetailCtl.getPaymentFreightTerm(acc.Id, po.Brand_Scope__c, true, null);

            CCM_Quotation_DetailCtl.getDropShipPaymentFreightTerm(acc.Id, po.Brand_Scope__c, 'N');

            CCM_Quotation_DetailCtl.deleteChangedBrandPO(JSON.serialize(poString));

            CCM_Quotation_DetailCtl.caculateTotalTax(poString);
            
            poString.Is_Alternative_Address__c = false;
            poString.Shipping_Address__c = address.Id;
            poString.ORG_ID__c = 'CCA';
            Test.startTest();
            CCM_Quotation_DetailCtl.saveData(JSON.serialize(poString), JSON.serialize(orderItemDataList), 1);

            Purchase_Order__c poString1 = new Purchase_Order__c();
            poString1.Customer__c = acc.Id;
            poString1.Shipping_By__c = 'Chervon';
            poString1.Shipping_priority__c = '5';
            poString1.Freight_Fee__c = 20.83;
            poString1.Freight_Fee_To_Be_Waived__c = 0.00;
            poString1.Extra_Freight_Fee_To_Be_Waived__c = 0.00;
            poString1.Delivery_Supplier__c = 'FedEx';
            poString1.Customer_Freight_Account__c = '134353';
            poString1.Customer_PO_Num__c = '**********';
            poString1.Expected_Delivery_Date__c = Date.today();
            poString1.Email__c = '<EMAIL>';
            poString1.Notes__c = 'test';
            poString1.Total_Quantity__c = 1;
            poString1.Handling_Fee__c = 0.00;
            poString1.Product_Price__c = 789;
            poString1.Is_DropShip__c = false;
            poString1.Is_Alternative_Address__c = false;
            poString1.Additional_Shipping_Street__c = '1345 Street';
            poString1.Additional_Shipping_City__c = 'Chicago';
            poString1.Additional_Shipping_Country__c = 'US';
            poString1.Additional_Shipping_Province__c = 'IL';
            poString1.Additional_Shipping_Postal_Code__c = '22323';
            poString1.Additional_Contact_Name__c = 'test user';
            poString1.Additional_Contact_Phone__c = '*********';
            poString1.Additional_Contact_Email__c = '<EMAIL>';
            poString1.Discount_Amount__c = 0.00;
            insert poString1;
		
            CCM_Quotation_DetailCtl.deleteChangedBrandPO(JSON.serialize(poString1));

            CCM_Quotation_DetailCtl.deleteQuotation(poItem1.Id);

            CCM_Quotation_DetailCtl.getFreihtFeeAmount(po.Id, JSON.serialize(po), false, 100,2000);
            CCM_Quotation_DetailCtl.getFreihtFeeAmount(po.Id, JSON.serialize(po), true, 3000,2000);
            CCM_Quotation_DetailCtl.getPromotion('SBG210791', acc.Id, false, false);
            CCM_Quotation_DetailCtl.checkPromotions(new List<String>{'SBG210791'}, acc.Id);
            CCM_Quotation_DetailCtl.getPaymentTerm(acc.Id, 'EGO', null,50001);

            CCM_Quotation_DetailCtl.deleteQuotation(new List<String>());
            CCM_Quotation_DetailCtl.getPriceBookEntryCondition('EGO', acc.Id, false);
            CCM_Quotation_DetailCtl.submitData(poString1.Id, false);
            CCM_Quotation_DetailCtl.getPriceBook(prod.Id, acc.Id, 'Y');
            CCM_Quotation_DetailCtl.getPriceBook(prod.Id, acc.Id, 'N');
        Test.stopTest();
        }
     }
}