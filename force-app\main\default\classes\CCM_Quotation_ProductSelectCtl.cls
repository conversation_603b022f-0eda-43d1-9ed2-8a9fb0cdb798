public without sharing class CCM_Quotation_ProductSelectCtl {

    @AuraEnabled
    public static String getData(String recordId){
        return CCM_Quotation_DetailCtl.getData(recordId,'');
    }

    @AuraEnabled
    public static String getPaymentTerm(
        String customerId,
        String brandName,
        String recordId
    ) {
        CCM_Quotation_DetailCtl.PaymentFreightTerm term = new CCM_Quotation_DetailCtl.PaymentFreightTerm();
        Map<String, Payment_Term__mdt> paymentRuleMap = Util.getPaymentTermMap();
        List<Sales_Program__c> authBrands = Util.getAuthBrandInfoSales(
                customerId,
                brandName
            );
        if (authBrands != null && authBrands.size() > 0){
            Sales_Program__c authBrand = authBrands[0];
            term.paymentTerm = authBrand.Payment_Term__c;

            Payment_Term__mdt pterm = new Payment_Term__mdt();
            if (String.isNotBlank(authBrand.Payment_Term__c)) {
                pterm = paymentRuleMap.get(authBrand.Payment_Term__c);
                term.paymentTermLabel = pterm.Description__c;
            }
        }
        return JSON.serialize(term);
    }


    @AuraEnabled
    public static List<Product2> getPromotionProductLanuchDate(List<String> productIdList){
        List<Product2> productList = new List<Product2>();
        Set<String> productCodeSet = new Set<String>();
        if(productIdList.size() > 0){
             User currentUsr = Util.getUserInfo(UserInfo.getUserId());
            String prodcutOrg = 'United States';
            if (currentUsr != null && currentUsr.Contact != null && String.isNotBlank(currentUsr.Contact.AccountId)){
                Account orderCustomer = [select Id,name,Org_Code__c From Account Where Id =: currentUsr.Contact.AccountId];
                if(orderCustomer.ORG_Code__c == 'CCA'){
                    prodcutOrg = 'Canada';
                }else{
                    prodcutOrg = 'United States';
                }
            }
            List<Product2> prodInfo = [SELECT Id, Country_of_Origin__c,Brand_Name__c,CS_Exchange_Rate__c,Item_Number__c,ProductCode,Weight__c,OverSize__c FROM Product2 WHERE Id IN :productIdList];
            if (prodInfo != null && prodInfo.size() > 0){
                for(Product2 pr : prodInfo){
                    productCodeSet.add(pr.ProductCode);
                }
                productList =[select Id,Lanch_Date__c,ProductCode FROM Product2 WHERE ProductCode IN:productCodeSet AND Source__c ='EBS' AND Lanch_Date__c != null AND Country_of_Origin__c = :prodcutOrg AND IsActive = true];
            }
        }
        return productList;

    }

    @AuraEnabled
    public static String getPriceBook(String prodId){
        InitData initD = new InitData();
        if (String.isNotBlank(prodId)){
            User currentUsr = Util.getUserInfo(UserInfo.getUserId());
            String prodcutOrg = 'United States';
            if (currentUsr != null && currentUsr.Contact != null && String.isNotBlank(currentUsr.Contact.AccountId)){
                Account orderCustomer = [select Id,name,Org_Code__c From Account Where Id =: currentUsr.Contact.AccountId];
                if(orderCustomer.ORG_Code__c == 'CCA'){
                    prodcutOrg = 'Canada';
                }else{
                    prodcutOrg = 'United States';
                }
            }
            List<Product2> prodInfo = [SELECT Id, Country_of_Origin__c,Brand_Name__c,CS_Exchange_Rate__c,Item_Number__c,ProductCode,Weight__c,OverSize__c FROM Product2 WHERE Id =:prodId];
            if (prodInfo != null && prodInfo.size() > 0){
                List<Product2> proList =[select Id,Lanch_Date__c FROM Product2 WHERE ProductCode = :prodInfo[0].ProductCode AND Source__c ='EBS' AND Lanch_Date__c != null AND Country_of_Origin__c = :prodcutOrg AND IsActive = true];
                initD.product = prodInfo[0];
                if(proList.size() > 0){
                    initD.product.Lanch_Date__c = proList[0].Lanch_Date__c;
                }

                Date Expirate = Util.parseDate(Label.Package_Order_Expire_Date, currentUsr.LocaleSidKey);
                // Date Expirate = Date.parse(Label.Package_Order_Expire_Date);
                Date Today = System.today();
                if(Today < Expirate){
                    initD.ExpirateDate = false;
                }else{
                    initD.ExpirateDate = true;
                }


                if (currentUsr != null && currentUsr.Contact != null && String.isNotBlank(currentUsr.Contact.AccountId)){
                    Account acc = [select Id,name,Org_Code__c From Account Where Id =: currentUsr.Contact.AccountId];
                    initD.OrgCode = acc.Org_Code__c;
                    // List<Sales_Program__c> authBrandList = [
                    //         SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
                    //                Approval_Status__c, IsDeleted, Price_Book_Mapping__r.Name,
                    //                Price_Book__c, Contract_Price_List_Name__c, Contract_Price_Book__c
                    //         FROM Sales_Program__c
                    //         WHERE Customer__c =: currentUsr.Contact.AccountId
                    //         AND Approval_Status__c = 'Approved'
                    //         AND Brands__c =: prodInfo[0].Brand_Name__c
                    //         AND IsDeleted = false LIMIT 1];
                    List<Sales_Program__c> authBrandList = getAuthorizedBrands(currentUsr.Contact.AccountId, prodInfo[0].Brand_Name__c, 'Sales');
                    if (authBrandList != null && authBrandList.size() > 0){
                        Sales_Program__c authBrandInfo = authBrandList[0];
                        Boolean isContract = false;
                        if (authBrandInfo.Price_Book_Mapping__r.Name == 'Contract Price List'
                            && String.isNotBlank(authBrandInfo.Contract_Price_Book__c)){
                            isContract = true;
                        }
                        PricebookEntry prodEntry = new PricebookEntry();
                        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
                        //更新特殊客户有主副价格册的逻辑 Modified By Abby On 7/10/2020
                        /*prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c);*/
                        prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c, isContract);
                        System.debug(LoggingLevel.INFO, '*** prodEntry1: ' + prodEntryList);
                        //如果当前的产品不再当前的价格册里，就去查找当前brand & Customer Type下的标准价格册（取Customize 和 Standard的合集产品）
                        if (prodEntryList == null || prodEntryList.size() == 0){
                            prodEntryList = Util.getStandardPricebookEntryByProdId(prodId, authBrandInfo.Brands__c,
                                authBrandInfo.Customer__r.Distributor_or_Dealer__c,'Sales');
                            System.debug(LoggingLevel.INFO, '*** prodEntry2: ' + prodEntryList);
                        }

                        if (prodEntryList != null && prodEntryList.size() > 0){
                            prodEntry = prodEntryList[0];
                        }
                        initD.priceBookEntry = prodEntry;

                        //get the promotions by product id and customer id
                        if(Test.isRunningTest()){
                            initD.promotionList = null;
                        }else{
                            initD.promotionList = getPromotions(prodId, currentUsr.Contact.AccountId, true, false);
                        }

                    }
                }
            }
        }

        return JSON.serialize(initD);
    }

    //update by austin
    @AuraEnabled
    public static String getFirstTierPriceBook(String prodId, String customerId){
        InitData initD = new InitData();
        if (String.isNotBlank(prodId)){
            List<Product2> prodInfo = [SELECT Id, Brand_Name__c,CS_Exchange_Rate__c,Item_Number__c,ProductCode,Weight__c,OverSize__c FROM Product2 WHERE Id =:prodId];
            if (prodInfo != null && prodInfo.size() > 0){
                initD.product = prodInfo[0];

                //User currentUsr = Util.getUserInfo(UserInfo.getUserId());
                if (String.isNotBlank(customerId)){
                    List<Sales_Program__c> authBrandList = [
                            SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
                                   Approval_Status__c, IsDeleted, Price_Book_Mapping__r.Name,
                                   Price_Book__c, Contract_Price_List_Name__c, Contract_Price_Book__c
                            FROM Sales_Program__c
                            WHERE Customer__c =: customerId
                            AND Approval_Status__c = 'Approved'
                            AND Brands__c =: prodInfo[0].Brand_Name__c
                            AND RecordType.DeveloperName IN ('Dropship_Sales_Standard', 'Dropship_Sales_Customized')
                            AND IsDeleted = false LIMIT 1];
                    if (authBrandList != null && authBrandList.size() > 0){
                        Sales_Program__c authBrandInfo = authBrandList[0];
                        Boolean isContract = false;
                        if (authBrandInfo.Price_Book_Mapping__r.Name == 'Contract Price List'
                            && String.isNotBlank(authBrandInfo.Contract_Price_Book__c)){
                            isContract = true;
                        }
                        PricebookEntry prodEntry = new PricebookEntry();
                        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
                        //更新特殊客户有主副价格册的逻辑 Modified By Abby On 7/10/2020
                        /*prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c);*/
                        prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c, isContract);
                        System.debug(LoggingLevel.INFO, '*** prodEntry1: ' + prodEntryList);
                        //如果当前的产品不再当前的价格册里，就去查找当前brand & Customer Type下的标准价格册（取Customize 和 Standard的合集产品）
                        if (prodEntryList == null || prodEntryList.size() == 0){
                            prodEntryList = Util.getStandardPricebookEntryByProdId(prodId, authBrandInfo.Brands__c,
                                authBrandInfo.Customer__r.Distributor_or_Dealer__c,'Sales');
                            System.debug(LoggingLevel.INFO, '*** prodEntry2: ' + prodEntryList);
                        }

                        if (prodEntryList != null && prodEntryList.size() > 0){
                            prodEntry = prodEntryList[0];
                        }
                        initD.priceBookEntry = prodEntry;

                        //get the promotions by product id and customer id
                        if(Test.isRunningTest()){
                            initD.promotionList = null;
                        }else{
                            initD.promotionList = getPromotions(prodId, customerId, true, false);
                        }

                    }
                }
            }
        }

        return JSON.serialize(initD);
    }

    //add by austin，CNA价格册取值
    @AuraEnabled
    public static String getDropShipInternalPriceBookCNA(String prodId, String customerId){
        InitData initD = new InitData();

        initD.OrgCode = 'CNA';
        Date Expirate = Date.parse(Label.Package_Order_Expire_Date);
        Date Today = System.today();
        if(Today < Expirate){
            initD.ExpirateDate = false;
        }else{
            initD.ExpirateDate = true;
        }
        Account orderCustomer = [SELECT Id,ORG_Code__c FROM Account WHERE Id =:customerId];
        String prodcutOrg = 'United States';
        if(orderCustomer.ORG_Code__c == 'CCA'){
            prodcutOrg = 'Canada';
        }else{
            prodcutOrg = 'United States';
        }
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        if (String.isNotBlank(prodId)){
            List<Product2> prodInfo = [SELECT Id ,Country_of_Origin__c,Brand_Name__c,Item_Number__c,CS_Exchange_Rate__c,ProductCode,Weight__c,OverSize__c FROM Product2 WHERE Id =:prodId];
            if (prodInfo != null && prodInfo.size() > 0){
                List<Product2> proList =[select Id,Lanch_Date__c FROM Product2 WHERE ProductCode = :prodInfo[0].ProductCode AND Source__c ='EBS' AND Lanch_Date__c != null AND Country_of_Origin__c = :prodcutOrg AND IsActive = true];
                initD.product = prodInfo[0];
                if(proList.size() > 0){
                    initD.product.Lanch_Date__c = proList[0].Lanch_Date__c;
                }
                String brandName = prodInfo[0].Brand_Name__c;
                if (String.isNotBlank(customerId)){
                    List<Sales_Program__c> authBrandList = [
                            SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
                                   Approval_Status__c, IsDeleted, Price_Book_Mapping__r.Name,
                                   Price_Book__c, Contract_Price_List_Name__c, Contract_Price_Book__c
                            FROM Sales_Program__c
                            WHERE Customer__c =: customerId
                            AND Approval_Status__c = 'Approved'
                            AND Brands__c =: prodInfo[0].Brand_Name__c
                            AND RecordType.DeveloperName IN ('Dropship_Sales_Standard', 'Dropship_Sales_Customized')
                            AND IsDeleted = false LIMIT 1];
                    if (authBrandList != null && authBrandList.size() > 0){
                        Sales_Program__c authBrandInfo = authBrandList[0];
                        Boolean isContract = false;
                        if (authBrandInfo.Price_Book_Mapping__r.Name == 'Contract Price List'
                            && String.isNotBlank(authBrandInfo.Contract_Price_Book__c)){
                            isContract = true;
                        }
                        //更新特殊客户有主副价格册的逻辑 Modified By Abby On 7/10/2020
                        prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c, isContract);
                        System.debug(LoggingLevel.INFO, '*** prodEntryList TEST: ' + prodEntryList);
                        //如果当前的产品不再当前的价格册里，就去查找price book entry下对应产品价格最低的的标准价格册
                        if (prodEntryList == null || prodEntryList.size() == 0){
                            prodEntryList = Util.getStandardPricebookEntry(prodId,brandName,customerId);
                            System.debug(LoggingLevel.INFO, '*** prodEntry2: ' + prodEntryList);
                        }

                        if (prodEntryList != null && prodEntryList.size() > 0){
                            initD.priceBookEntry  = prodEntryList[0];
                        }
                    }

                    //get available promotions by product id and customer id
                    if(Test.isRunningTest()){
                        initD.promotionList = null;
                    }else{
                        initD.promotionList = getPromotions(prodId, customerId, false, true);
                    }

                }
            }
        }
        return JSON.serialize(initD);
    }

    @AuraEnabled
    public static String getPriceBookFromModify(String prodId, String customerId, String type, Boolean isDropship, String DropshipCustomer) {
        if (!isDropship) {
            return CCM_Quotation_ProductSelectCtl.getInternalPriceBook(prodId, customerId, type);
        } else {
            return CCM_Quotation_ProductSelectCtl.getInternalPriceBookDropship(prodId, customerId, type, DropshipCustomer);
        }
    }

    @AuraEnabled
    public static String getInternalPriceBook(String prodId, String customerId, String type){
        InitData initD = new InitData();

        Account customer = [SELECT Id, ORG_Code__c FROM Account WHERE Id =: customerid];
        initD.OrgCode = customer.ORG_Code__c;
        Date Expirate = Date.parse(Label.Package_Order_Expire_Date);
        Date Today = System.today();
        if(Today < Expirate){
            initD.ExpirateDate = false;
        }else{
            initD.ExpirateDate = true;
        }
        Account orderCustomer = [SELECT Id,ORG_Code__c FROM Account WHERE Id =:customerId];
        String prodcutOrg = 'United States';
        if(orderCustomer.ORG_Code__c == 'CCA'){
            prodcutOrg = 'Canada';
        }else{
            prodcutOrg = 'United States';
        }
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        if (String.isNotBlank(prodId)){
            List<Product2> prodInfo = [
                                        SELECT Id,Country_of_Origin__c, Brand_Name__c,CS_Exchange_Rate__c
                                        ,ProductCode,Weight__c,OverSize__c
                                        ,Item_Number__c
                                        FROM Product2 WHERE Id != NULL
                                        AND Id =:prodId
                                        AND Is_History_Product__C = :CCM_Constants.blHistoryProduct
                                    ];
            if (prodInfo != null && prodInfo.size() > 0){
                List<Product2> proList =[select Id,Lanch_Date__c FROM Product2 WHERE ProductCode = :prodInfo[0].ProductCode AND Source__c ='EBS' AND Lanch_Date__c != null AND Country_of_Origin__c = :prodcutOrg AND IsActive = true];
                initD.product = prodInfo[0];
                if(proList.size() > 0){
                    initD.product.Lanch_Date__c = proList[0].Lanch_Date__c;
                }
                if (String.isNotBlank(customerId)){
                    List<Sales_Program__c> authBrandList = getAuthorizedBrands(customerId, prodInfo[0].Brand_Name__c, type);
                    if (authBrandList != null && authBrandList.size() > 0){
                        Sales_Program__c authBrandInfo = authBrandList[0];
                        Boolean isContract = false;
                        if (authBrandInfo.Price_Book_Mapping__r.Name == 'Contract Price List'
                            && String.isNotBlank(authBrandInfo.Contract_Price_Book__c)){
                            isContract = true;
                        }
                        //更新特殊客户有主副价格册的逻辑 Modified By Abby On 7/10/2020
                        prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c, isContract);
                        System.debug(LoggingLevel.INFO, '*** prodEntryList TEST: ' + prodEntryList);
                        //剔除PricebookEntry:{}这种情况
                        for(Integer i = 0;i<prodEntryList.size(); i++){
                            if(prodEntryList[i].Id == null){
                                prodEntryList.remove(i);
                            }
                        }
                        //如果当前的产品不再当前的价格册里，就去查找当前brand & Customer Type下的标准价格册（取Customize 和 Standard的合集产品）
                        if (prodEntryList == null || prodEntryList.size() == 0){
                            prodEntryList = Util.getStandardPricebookEntryByProdId(prodId, authBrandInfo.Brands__c,
                                authBrandInfo.Customer__r.Distributor_or_Dealer__c,'Sales');
                            System.debug(LoggingLevel.INFO, '*** prodEntry2: ' + prodEntryList);
                        }

                        if (prodEntryList != null && prodEntryList.size() > 0){
                            initD.priceBookEntry  = prodEntryList[0];
                        }
                    }

                    //get available promotions by product id and customer id
                    if(Test.isRunningTest()){
                        initD.promotionList = null;
                    }else{
                        initD.promotionList = getPromotions(prodId, customerId, false, false);
                    }

                }
            }
        }

        return JSON.serialize(initD);
    }


    @AuraEnabled
    public static String getInternalPriceBookDropship(String prodId, String customerId, String type, String DropshipCustomer){
        InitData initD = new InitData();

        Account customer = [SELECT Id, ORG_Code__c FROM Account WHERE Id =: customerid];
        initD.OrgCode = customer.ORG_Code__c;
        Date Expirate = Date.parse(Label.Package_Order_Expire_Date);
        Date Today = System.today();
        if(Today < Expirate){
            initD.ExpirateDate = false;
        }else{
            initD.ExpirateDate = true;
        }
        Account orderCustomer = [SELECT Id,ORG_Code__c FROM Account WHERE Id =:customerId];
        String prodcutOrg = 'United States';
        if(orderCustomer.ORG_Code__c == 'CCA'){
            prodcutOrg = 'Canada';
        }else{
            prodcutOrg = 'United States';
        }
        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        if (String.isNotBlank(prodId)){
            List<Product2> prodInfo = [
                                        SELECT Id, Country_of_Origin__c,Brand_Name__c,Item_Number__c,CS_Exchange_Rate__c
                                        ,ProductCode,Weight__c,OverSize__c
                                        FROM Product2 WHERE Id != NULL
                                        AND Id =:prodId
                                        AND Is_History_Product__C = :CCM_Constants.blHistoryProduct
                                    ];
            if (prodInfo != null && prodInfo.size() > 0){
                List<Product2> proList =[select Id,Lanch_Date__c FROM Product2 WHERE ProductCode = :prodInfo[0].ProductCode AND Source__c ='EBS' AND Lanch_Date__c != null AND Country_of_Origin__c = :prodcutOrg AND IsActive = true];
                initD.product = prodInfo[0];
                if(proList.size() > 0){
                    initD.product.Lanch_Date__c = proList[0].Lanch_Date__c;
                }
                if (String.isNotBlank(customerId)){
                    List<Sales_Program__c> authBrandList = getAuthorizedBrands(customerId, prodInfo[0].Brand_Name__c, type);
                    if (authBrandList != null && authBrandList.size() > 0){
                        Sales_Program__c authBrandInfo = authBrandList[0];
                        Boolean isContract = false;
                        if (authBrandInfo.Price_Book_Mapping__r.Name == 'Contract Price List'
                            && String.isNotBlank(authBrandInfo.Contract_Price_Book__c)){
                            isContract = true;
                        }
                        //更新特殊客户有主副价格册的逻辑 Modified By Abby On 7/10/2020
                        /*prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c);*/
                        prodEntryList = Util.getPriceBookEntryByProdId(prodId, authBrandInfo.Price_Book__c, isContract);
                        System.debug(LoggingLevel.INFO, '*** prodEntryList TEST: ' + prodEntryList);
                        //如果当前的产品不再当前的价格册里，就去查找当前brand & Customer Type下的标准价格册（取Customize 和 Standard的合集产品）
                        if (prodEntryList == null || prodEntryList.size() == 0){
                            prodEntryList = Util.getStandardPricebookEntryByProdId(prodId, authBrandInfo.Brands__c,
                                authBrandInfo.Customer__r.Distributor_or_Dealer__c,'Sales');
                            System.debug(LoggingLevel.INFO, '*** prodEntry2: ' + prodEntryList);
                        }

                        if (prodEntryList != null && prodEntryList.size() > 0){
                            initD.priceBookEntry  = prodEntryList[0];
                        }
                    }

                    //get available promotions by product id and customer id
                    if(Test.isRunningTest()){
                        initD.promotionList = null;
                    }else{
                        initD.promotionList = getPromotionsDropship(prodId, customerId, false, true, DropshipCustomer);
                    }

                }
            }
        }

        return JSON.serialize(initD);
    }

    /**
     * @description Get promotion data by current customer and the selected product
     */
    public static List<PromotionData> getPromotionsDropship(String prodId, String customerId, Boolean isPortal, Boolean isDropShip, String DropshipCustomer){
        List<PromotionData> promotionList = new List<PromotionData>();
        List<Promotion_Target_Customer__c> promo2CustomerList = new List<Promotion_Target_Customer__c>();
        //update by austin 2023.2.1
        //get all promotions of current customer
        // String str = 'SELECT Id, Name, Promotion__c FROM Promotion_Target_Customer__c WHERE  Promotion__r.RecordType.DeveloperName = \'Sell_In_Promotion\' AND Promotion__r.Promotion_Status__c = \'Open\' AND Promotion__r.Is_DropShip_Promotion__c =:isDropShip';
        // str+= isCCA? ' AND Customer__c =:customerId': ' AND Top_Customer__c =:customerId';
        // System.debug('str=====promotion'+str);
        // promo2CustomerList = Database.query(str);
        Account customer = [SELECT Id, ORG_Code__c FROM Account WHERE Id =: customerId];
        Boolean isCCA = customer.ORG_Code__c == 'CCA'? true : false;
        String str = 'SELECT Id, Name, Promotion__c FROM Promotion_Target_Customer__c WHERE  Promotion__r.RecordType.DeveloperName = \'Sell_In_Promotion\' AND Promotion__r.Promotion_Status__c = \'Open\' ';
        if (!isCCA) {
            str += isDropShip? 'AND Promotion__r.Is_DropShip_Promotion__c = true': 'AND Promotion__r.Non_DropShip_Promotion__c = true';
            // str += ' AND (Customer__c = :customerId OR Top_Customer__c = :customerId) ';
            str += ' AND Customer__c = :DropshipCustomer AND Top_Customer__c = :customerId ';
            promo2CustomerList = Database.query(str);
        }else {
            promo2CustomerList = [
                SELECT
                    Id,
                    Name,
                    Promotion__c,
                    Promotion__r.Promotion_Type__c
                FROM Promotion_Target_Customer__c
                WHERE Customer__c =:customerId
                AND Promotion__r.RecordType.DeveloperName = 'Sell_In_Promotion'
                AND Promotion__r.Promotion_Status__c = 'Open'
                AND Promotion__r.Is_DropShip_Promotion__c =: isDropShip
            ];
        }
        //end update by austin 2023.2.1

        if(promo2CustomerList.size() ==0){
            //there is no promotion for the customer
            return promotionList;
        }

        Set<String> promoIds = new Set<String>();
        for(Promotion_Target_Customer__c p2cItem : promo2CustomerList){
            promoIds.add(p2cItem.Promotion__c);
        }

        Date today = System.today();
        List<String> windowStatus = new List<String>();
        windowStatus.add('In Progress');
        if(!isPortal){
            windowStatus.add('Closed For External');
        }
        List<Promotion_Window__c> promotionWindowList = [
            SELECT
                Id,
                Promotion__c,
                Availability_in_Dealer_Portal__c
            FROM Promotion_Window__c
            WHERE Promotion__c IN:promoIds
            //AND Start_Date__c <= :today
            //AND End_Date__c >= :today
            AND Promotion_Window_Status2__c IN :windowStatus
        ];

        if(promotionWindowList.size() ==0){
            //there is no promotion for the customer
            return promotionList;
        }

        //the promotions with available windows
        promoIds = new Set<String>();
        Map<Id, Id> promoId2WindowId = new Map<Id, Id>();
        for(Promotion_Window__c pWindow : promotionWindowList){
            if(isPortal){
                if(pWindow.Availability_in_Dealer_Portal__c == 'Available in Portal'
                    || pWindow.Availability_in_Dealer_Portal__c == 'Invisible in Portal'){
                    promoIds.add(pWindow.Promotion__c);
                    if(!promoId2WindowId.containsKey(pWindow.Promotion__c)){
                        promoId2WindowId.put(pWindow.Promotion__c, pWindow.Id);
                    }
                }
            }else{
                promoIds.add(pWindow.Promotion__c);
                if(!promoId2WindowId.containsKey(pWindow.Promotion__c)){
                    promoId2WindowId.put(pWindow.Promotion__c, pWindow.Id);
                }
            }
        }

        List<Promotion2__c> promotion2List = [
            SELECT
                Id,
                Brands__c,
                Name,
                // add haibo: promotion (french) 翻译
                Promotion_Name_French__c,
                Promotion_Type__c,
                Promo_Code__c,
                Promotion_Code_For_External__c
            FROM Promotion2__c
            WHERE Id IN :promoIds
        ];
        Map<Id, Promotion2__c> id2PromoMap = new Map<Id, Promotion2__c>();
        for(Promotion2__c pItem : promotion2List){
            id2PromoMap.put(pItem.Id, pItem);
        }
        List<Promotion_Rule__c> promoRuleList = [
            SELECT
                Id,
                Name,
                Promotion__c,
                (SELECT
                    Id,
                    Name,
                    Gift_Total_Quantity__c,
                    Payment_Term__c,
                    RecordTypeId,
                    RecordType.Name,
                    RecordType.DeveloperName,
                    Promotion_Rule__c,
                    Discount_Off__c,
                    Amount_Off__c
                FROM Promotion_Offerings__r),
                (SELECT
                    Id,
                    Name,
                    Bogo_Remark__c,
                    RecordTypeId,
                    RecordType.Name,
                    RecordType.DeveloperName,
                    Minimum_Total_Amount__c,
                    Minimum_Total_Quantity__c,
                    Multiple_Control__c,
                    Buy_Qty__c,
                    Minimum_Different_Tool_Models__c,
                    Max_Different_Tool_Models__c,
                    Minimum_Whole_Order_Amount__c,
                    Maximum_Whole_Order_Amount__c,
                    Promotion_Rule__c
                FROM Thresholds__r
                ORDER BY Name)
            FROM Promotion_Rule__c
            WHERE Promotion__c IN :promoIds
        ];
        Map<Id, List<Promotion_Rule__c>> promo2RuleMap = new Map<Id, List<Promotion_Rule__c>>();
        Map<Id, Promotion_Rule__c> id2RuleMap = new Map<Id, Promotion_Rule__c>();
        Set<Id> thresholdIds = new Set<Id>();
        Set<Id> offeringIds = new Set<Id>();
        for(Promotion_Rule__c ruleItem : promoRuleList){
            if(!promo2RuleMap.containsKey(ruleItem.Promotion__c)){
                promo2RuleMap.put(ruleItem.Promotion__c, new List<Promotion_Rule__c>());
            }
            promo2RuleMap.get(ruleItem.Promotion__c).add(ruleItem);
            id2RuleMap.put(ruleItem.Id, ruleItem);
            for(Promotion_Threshold__c ptItem : ruleItem.Thresholds__r){
                thresholdIds.add(ptItem.Id);
            }
            for(Promotion_Offering__c poItem : ruleItem.Promotion_Offerings__r){
                offeringIds.add(poItem.Id);
            }
        }
        List<Promotion_Product__c> allPromotionProductList = [
            SELECT
                Id,
                Minimum_Amount__c,
                Multiple_Control__c,
                Maximum_Amount__c,
                Minimum_Quantity__c,
                Maximum_Quantity__c,
                Gift_Quantity__c,
                Number__c,
                Chervon_Funding__c,
                Increment_For_Free_Goods__c,
                PMAPP__c,
                Promotion2_Threshold__c,
                Promotion2_Offering__c,
                Additional_Discount__c,
                Additional_Amount_Off__c,
                Product__c,
                Product__r.Name,
                // add haibo: product french
                Product__r.Product_Name_French__c,
                Product__r.Brand_Name__c,
                Product__r.CS_Exchange_Rate__c,
                Product__r.ProductCode,
                Product__r.SF_Description__c,
                // add haibo: product french
                Product__r.SF_Description_French__c,
                Product__r.Full_Pallet_Quantity__c,
                Product__r.RecordType.DeveloperName
            FROM Promotion_Product__c
            WHERE Promotion2_Threshold__c IN :thresholdIds OR Promotion2_Offering__c IN :offeringIds
        ];
        Map<Id, List<Promotion_Product__c>> threshold2ProductMap = new Map<Id, List<Promotion_Product__c>>();
        Map<Id, List<Promotion_Product__c>> offering2ProductMap = new Map<Id, List<Promotion_Product__c>>();
        for(Promotion_Product__c ppItem : allPromotionProductList){
            if(String.isNotEmpty(ppItem.Promotion2_Threshold__c)){
                if(!threshold2ProductMap.containsKey(ppItem.Promotion2_Threshold__c)){
                    threshold2ProductMap.put(ppItem.Promotion2_Threshold__c, new List<Promotion_Product__c>());
                }
                threshold2ProductMap.get(ppItem.Promotion2_Threshold__c).add(ppItem);
            }
            if(String.isNotEmpty(ppItem.Promotion2_Offering__c)){
                if(!offering2ProductMap.containsKey(ppItem.Promotion2_Offering__c)){
                    offering2ProductMap.put(ppItem.Promotion2_Offering__c, new List<Promotion_Product__c>());
                }
                offering2ProductMap.get(ppItem.Promotion2_Offering__c).add(ppItem);
            }
        }

        List<Promotion_Product__c> thresholdProductList = [
            SELECT
                Id,
                Multiple_Control__c,
                Minimum_Amount__c,
                Maximum_Amount__c,
                Minimum_Quantity__c,
                Maximum_Quantity__c,
                Gift_Quantity__c,
                Number__c,
                Increment_For_Free_Goods__c,
                Chervon_Funding__c,
                PMAPP__c,
                Promotion2_Threshold__c,
                Promotion2_Offering__c,
                product__r.CS_Exchange_Rate__c,
                Product__c
            FROM Promotion_Product__c
            WHERE Promotion2_Threshold__c IN :thresholdIds AND Product__c =:prodId
        ];
        System.debug('***** thresholdIds:' + thresholdIds);
        System.debug('***** thresholdProductList:' + thresholdProductList);
        if(thresholdProductList.size() == 0){
            //there is no promotion for the product
            return promotionList;
        }

        //available threshold records for the selected product
        Set<Id> availableThresholdIds = new Set<Id>();
        for(Promotion_Product__c ppItem : thresholdProductList){
            availableThresholdIds.add(ppItem.Promotion2_Threshold__c);
        }
        List<Promotion_Threshold__c> availableThresholdList = [
            SELECT
                Id,
                Promotion_Rule__c,
                Multiple_Control__c,
                Buy_Qty__c,
                Promotion_Rule__r.Promotion__c
            FROM Promotion_Threshold__c
            WHERE Id IN :availableThresholdIds
        ];
        System.debug('***** availableThresholdList:' + availableThresholdList);
        Set<Id> availablePromoIds = new Set<Id>();
        Set<Id> availableRuleIds = new Set<Id>();
        for(Promotion_Threshold__c ptItem : availableThresholdList){
            availablePromoIds.add(ptItem.Promotion_Rule__r.Promotion__c);
            availableRuleIds.add(ptItem.Promotion_Rule__c);
        }
        System.debug('***** availablePromoIds:' + availablePromoIds);

        Set<String> brandSet = new Set<String>();
        Set<Id> productIdSet = new Set<Id>();
        Set<Id> merchandisingProductIdSet = new Set<Id>();
        for(Id pId : availablePromoIds){
            for(Promotion_Rule__c ruleItem : promo2RuleMap.get(pId)){
                if(!availableRuleIds.contains(ruleItem.Id)){
                    continue;
                }
                for(Promotion_Threshold__c ptItem : ruleItem.Thresholds__r){
                    for(Promotion_Product__c ppItem : threshold2ProductMap.get(ptItem.Id)){
                        brandSet.add(ppItem.Product__r.Brand_Name__c);
                        productIdSet.add(ppItem.Product__c);
                        if(ppItem.Product__r.RecordType.DeveloperName == 'Amware_Merchandising'
                            || ppItem.Product__r.RecordType.DeveloperName == 'Oracle_Merchandising'){
                            merchandisingProductIdSet.add(ppItem.Product__c);
                        }
                    }
                }
                for(Promotion_Offering__c poItem : ruleItem.Promotion_Offerings__r){
                    if(offering2ProductMap.get(poItem.Id)==null) continue;
                    for(Promotion_Product__c ppItem : offering2ProductMap.get(poItem.Id)){
                        brandSet.add(ppItem.Product__r.Brand_Name__c);
                        productIdSet.add(ppItem.Product__c);
                        if(ppItem.Product__r.RecordType.DeveloperName == 'Amware_Merchandising'
                            || ppItem.Product__r.RecordType.DeveloperName == 'Oracle_Merchandising'){
                            merchandisingProductIdSet.add(ppItem.Product__c);
                        }
                    }
                }
            }
        }

        Map<Id,PricebookEntry> productId2PriceBookEntryMap = getProduc2tPriceBookEntryMap(productIdSet, brandSet, customerId, isDropShip);
        Map<Id,PricebookEntry> merchProductId2PriceBookEntryMap = getMerchProduc2tPriceBookEntryMap(merchandisingProductIdSet);

        for(Id pId : availablePromoIds){
            PromotionData pData = new PromotionData();
            pData.promotion = id2PromoMap.get(pId);
            pData.windowId = promoId2WindowId.get(pId);
            for(Promotion_Rule__c ruleItem : promo2RuleMap.get(pId)){
                if(!availableRuleIds.contains(ruleItem.Id)){
                    continue;
                }
                PromoRuleData rData = new PromoRuleData();
                rData.ruleId = ruleItem.Id;
                rData.ruleName = ruleItem.Name;
                for(Promotion_Threshold__c ptItem : ruleItem.Thresholds__r){
                    PromoThresholdData ptData = new PromoThresholdData();
                    ptData.threshold = ptItem;
                    ptData.products = threshold2ProductMap.get(ptItem.Id);
                    if(ptData.products != null){
                        for(Promotion_Product__c ppItem : ptData.products){
                            PricebookEntry pbItem = productId2PriceBookEntryMap.get(ppItem.Product__c);
                            if(merchandisingProductIdSet.contains(ppItem.Product__c)){
                                pbItem = merchProductId2PriceBookEntryMap.get(ppItem.Product__c);
                            }
                            if(pbItem!=null){
                                ptData.priceBookEntrys.add(pbItem);
                            }else{
                                ptData.priceBookEntrys.add(new PricebookEntry());
                            }
                        }
                    }
                    rData.thresholdList.add(ptData);
                }
                for(Promotion_Offering__c poItem : ruleItem.Promotion_Offerings__r){
                    PromoOfferingData poData = new PromoOfferingData();
                    poData.offering = poItem;
                    poData.products = offering2ProductMap.get(poItem.Id);
                    if(poData.products != null){
                        for(Promotion_Product__c ppItem : poData.products){
                            PricebookEntry pbItem = productId2PriceBookEntryMap.get(ppItem.Product__c);
                            if(merchandisingProductIdSet.contains(ppItem.Product__c)){
                                pbItem = merchProductId2PriceBookEntryMap.get(ppItem.Product__c);
                            }
                            if(pbItem!=null){
                                poData.priceBookEntrys.add(pbItem);
                            }else{
                                poData.priceBookEntrys.add(new PricebookEntry());
                            }
                        }
                    }
                    rData.offeringList.add(poData);
                }
                pData.ruleList.add(rData);
            }
            promotionList.add(pData);
        }
		System.debug('promotionList======'+promotionList);
        return promotionList;
    }

    private static List<Sales_Program__c> getAuthorizedBrands(String customerId, String brand, String type) {
        List<Sales_Program__c> authBrandMatchList = new List<Sales_Program__c>();
        List<Sales_Program__c> authBrandList = [
                    SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
                            Approval_Status__c, IsDeleted, Price_Book_Mapping__r.Name,
                            Price_Book__c, Contract_Price_List_Name__c, Contract_Price_Book__c,
                            RecordType.DeveloperName
                    FROM Sales_Program__c
                    WHERE Customer__c =: customerId
                    AND Approval_Status__c = 'Approved'
                    AND Brands__c =: brand];

        for(Sales_Program__c authBrand : authBrandList) {
            if(type == 'Sales') {
                if(authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_STANDARD_DEVELOPER_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_DEVELOPER_NAME) {
                    authBrandMatchList.add(authBrand);
                }
            }
            if(type == 'Service') {
                if(authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_DEVELOPER_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME) {
                    authBrandMatchList.add(authBrand);
                }
            }
            if(type == 'Dropship Sales') {
                if(authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_STANDARD_DEVELOPER_NAME || authBrand.RecordType.DeveloperName == CCM_Constants.SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_CUSTOMIZED_DEVELOPER_NAME) {
                    authBrandMatchList.add(authBrand);
                }
            }
        }
        if(!authBrandMatchList.isEmpty()) {
            return authBrandMatchList;
        }
        return authBrandList;
    }

    /**
     * @description Get promotion data by current customer and the selected product
     */
    public static List<PromotionData> getPromotions(String prodId, String customerId, Boolean isPortal, Boolean isDropShip){
        List<PromotionData> promotionList = new List<PromotionData>();
        List<Promotion_Target_Customer__c> promo2CustomerList = new List<Promotion_Target_Customer__c>();
        //update by austin 2023.2.1
        //get all promotions of current customer

        String query = 'SELECT Promotion2_Threshold__r.Promotion_Rule__r.Promotion__c FROM Promotion_Product__c WHERE Product__c = :prodId AND Promotion2_Threshold__r.Promotion_Rule__r.Promotion__r.Promotion_Status__c = \'Open\'';
        Set<String> promotionFromProducts = new Set<String>();
        for(Promotion_Product__c promoProd : (List<Promotion_Product__c>)Database.query(query)) {
            if(String.isNotBlank(promoProd.Promotion2_Threshold__r.Promotion_Rule__r.Promotion__c)) {
                promotionFromProducts.add(promoProd.Promotion2_Threshold__r.Promotion_Rule__r.Promotion__c);
            }
        }
        Account customer = [SELECT Id, ORG_Code__c FROM Account WHERE Id =: customerId];
        Boolean isCCA = customer.ORG_Code__c == 'CCA'? true : false;
        String str = 'SELECT Promotion__c FROM Promotion_Target_Customer__c WHERE  Promotion__r.RecordType.DeveloperName = \'Sell_In_Promotion\' AND Promotion__r.Promotion_Status__c = \'Open\' ';
        if (!isCCA) {
            str += isDropShip? 'AND Promotion__r.Is_DropShip_Promotion__c = true': 'AND Promotion__r.Non_DropShip_Promotion__c = true';
            str += ' AND (Customer__c = :customerId OR Top_Customer__c = :customerId) ';
            str += ' AND Promotion__c = :promotionFromProducts';
            promo2CustomerList = Database.query(str);
        }else {
            promo2CustomerList = [
                SELECT Promotion__c
                FROM Promotion_Target_Customer__c
                WHERE Customer__c =:customerId
                AND Promotion__r.RecordType.DeveloperName = 'Sell_In_Promotion'
                AND Promotion__r.Promotion_Status__c = 'Open'
                AND Promotion__r.Is_DropShip_Promotion__c =: isDropShip
            ];
        }
        //end update by austin 2023.2.1

        if(promo2CustomerList.size() ==0){
            //there is no promotion for the customer
            return promotionList;
        }

        Set<String> promoIds = new Set<String>();
        for(Promotion_Target_Customer__c result : promo2CustomerList){
            promoIds.add(result.Promotion__c);
        }

        Date today = System.today();
        List<String> windowStatus = new List<String>();
        windowStatus.add('In Progress');
        if(!isPortal){
            windowStatus.add('Closed For External');
        }
        List<Promotion_Window__c> promotionWindowList = [
            SELECT
                Id,
                Promotion__c,
                Availability_in_Dealer_Portal__c
            FROM Promotion_Window__c
            WHERE Promotion__c IN:promoIds
            //AND Start_Date__c <= :today
            //AND End_Date__c >= :today
            AND Promotion_Window_Status2__c IN :windowStatus
        ];

        if(promotionWindowList.size() ==0){
            //there is no promotion for the customer
            return promotionList;
        }

        //the promotions with available windows
        promoIds = new Set<String>();
        Map<Id, Id> promoId2WindowId = new Map<Id, Id>();
        for(Promotion_Window__c pWindow : promotionWindowList){
            if(isPortal){
                if(pWindow.Availability_in_Dealer_Portal__c == 'Available in Portal'
                    || pWindow.Availability_in_Dealer_Portal__c == 'Invisible in Portal'){
                    promoIds.add(pWindow.Promotion__c);
                    if(!promoId2WindowId.containsKey(pWindow.Promotion__c)){
                        promoId2WindowId.put(pWindow.Promotion__c, pWindow.Id);
                    }
                }
            }else{
                promoIds.add(pWindow.Promotion__c);
                if(!promoId2WindowId.containsKey(pWindow.Promotion__c)){
                    promoId2WindowId.put(pWindow.Promotion__c, pWindow.Id);
                }
            }
        }

        List<Promotion2__c> promotion2List = [
            SELECT
                Id,
                Brands__c,
                Name,
                // add haibo: promotion (french) 翻译
                Promotion_Name_French__c,
                Promotion_Type__c,
                Promo_Code__c,
                Promotion_Code_For_External__c
            FROM Promotion2__c
            WHERE Id IN :promoIds
        ];
        Map<Id, Promotion2__c> id2PromoMap = new Map<Id, Promotion2__c>();
        for(Promotion2__c pItem : promotion2List){
            id2PromoMap.put(pItem.Id, pItem);
        }
        List<Promotion_Rule__c> promoRuleList = [
            SELECT
                Id,
                Name,
                Promotion__c,
                (SELECT
                    Id,
                    Name,
                    Gift_Total_Quantity__c,
                    Payment_Term__c,
                    RecordTypeId,
                    RecordType.Name,
                    RecordType.DeveloperName,
                    Promotion_Rule__c,
                    Discount_Off__c,
                    Amount_Off__c
                FROM Promotion_Offerings__r),
                (SELECT
                    Id,
                    Name,
                    Bogo_Remark__c,
                    RecordTypeId,
                    RecordType.Name,
                    RecordType.DeveloperName,
                    Minimum_Total_Amount__c,
                    Minimum_Total_Quantity__c,
                    Multiple_Control__c,
                    Buy_Qty__c,
                    Minimum_Different_Tool_Models__c,
                    Max_Different_Tool_Models__c,
                    Minimum_Whole_Order_Amount__c,
                    Maximum_Whole_Order_Amount__c,
                    Promotion_Rule__c
                FROM Thresholds__r
                ORDER BY Name)
            FROM Promotion_Rule__c
            WHERE Promotion__c IN :promoIds
        ];
        Map<Id, List<Promotion_Rule__c>> promo2RuleMap = new Map<Id, List<Promotion_Rule__c>>();
        Map<Id, Promotion_Rule__c> id2RuleMap = new Map<Id, Promotion_Rule__c>();
        Set<Id> thresholdIds = new Set<Id>();
        Set<Id> offeringIds = new Set<Id>();
        for(Promotion_Rule__c ruleItem : promoRuleList){
            if(!promo2RuleMap.containsKey(ruleItem.Promotion__c)){
                promo2RuleMap.put(ruleItem.Promotion__c, new List<Promotion_Rule__c>());
            }
            promo2RuleMap.get(ruleItem.Promotion__c).add(ruleItem);
            id2RuleMap.put(ruleItem.Id, ruleItem);
            for(Promotion_Threshold__c ptItem : ruleItem.Thresholds__r){
                thresholdIds.add(ptItem.Id);
            }
            for(Promotion_Offering__c poItem : ruleItem.Promotion_Offerings__r){
                offeringIds.add(poItem.Id);
            }
        }
        List<Promotion_Product__c> allPromotionProductList = [
            SELECT
                Id,
                Minimum_Amount__c,
                Multiple_Control__c,
                Maximum_Amount__c,
                Minimum_Quantity__c,
                Maximum_Quantity__c,
                Gift_Quantity__c,
                Number__c,
                Chervon_Funding__c,
                Increment_For_Free_Goods__c,
                PMAPP__c,
                Promotion2_Threshold__c,
                Promotion2_Offering__c,
                Additional_Discount__c,
                Additional_Amount_Off__c,
                Product__c,
                Product__r.Name,
                // add haibo: product french
                Product__r.Product_Name_French__c,
                Product__r.Brand_Name__c,
                Product__r.CS_Exchange_Rate__c,
                Product__r.ProductCode,
                Product__r.SF_Description__c,
                // add haibo: product french
                Product__r.SF_Description_French__c,
                Product__r.Full_Pallet_Quantity__c,
                Product__r.RecordType.DeveloperName
            FROM Promotion_Product__c
            WHERE Promotion2_Threshold__c IN :thresholdIds OR Promotion2_Offering__c IN :offeringIds
        ];
        Map<Id, List<Promotion_Product__c>> threshold2ProductMap = new Map<Id, List<Promotion_Product__c>>();
        Map<Id, List<Promotion_Product__c>> offering2ProductMap = new Map<Id, List<Promotion_Product__c>>();
        for(Promotion_Product__c ppItem : allPromotionProductList){
            if(String.isNotEmpty(ppItem.Promotion2_Threshold__c)){
                if(!threshold2ProductMap.containsKey(ppItem.Promotion2_Threshold__c)){
                    threshold2ProductMap.put(ppItem.Promotion2_Threshold__c, new List<Promotion_Product__c>());
                }
                threshold2ProductMap.get(ppItem.Promotion2_Threshold__c).add(ppItem);
            }
            if(String.isNotEmpty(ppItem.Promotion2_Offering__c)){
                if(!offering2ProductMap.containsKey(ppItem.Promotion2_Offering__c)){
                    offering2ProductMap.put(ppItem.Promotion2_Offering__c, new List<Promotion_Product__c>());
                }
                offering2ProductMap.get(ppItem.Promotion2_Offering__c).add(ppItem);
            }
        }

        List<Promotion_Product__c> thresholdProductList = [
            SELECT
                Id,
                Multiple_Control__c,
                Minimum_Amount__c,
                Maximum_Amount__c,
                Minimum_Quantity__c,
                Maximum_Quantity__c,
                Gift_Quantity__c,
                Number__c,
                Increment_For_Free_Goods__c,
                Chervon_Funding__c,
                PMAPP__c,
                Promotion2_Threshold__c,
                Promotion2_Offering__c,
                product__r.CS_Exchange_Rate__c,
                Product__c
            FROM Promotion_Product__c
            WHERE Promotion2_Threshold__c IN :thresholdIds AND Product__c =:prodId
        ];
        System.debug('***** thresholdIds:' + thresholdIds);
        System.debug('***** thresholdProductList:' + thresholdProductList);
        if(thresholdProductList.size() == 0){
            //there is no promotion for the product
            return promotionList;
        }

        //available threshold records for the selected product
        Set<Id> availableThresholdIds = new Set<Id>();
        for(Promotion_Product__c ppItem : thresholdProductList){
            availableThresholdIds.add(ppItem.Promotion2_Threshold__c);
        }
        List<Promotion_Threshold__c> availableThresholdList = [
            SELECT
                Id,
                Promotion_Rule__c,
                Multiple_Control__c,
                Buy_Qty__c,
                Promotion_Rule__r.Promotion__c
            FROM Promotion_Threshold__c
            WHERE Id IN :availableThresholdIds
        ];
        System.debug('***** availableThresholdList:' + availableThresholdList);
        Set<Id> availablePromoIds = new Set<Id>();
        Set<Id> availableRuleIds = new Set<Id>();
        for(Promotion_Threshold__c ptItem : availableThresholdList){
            availablePromoIds.add(ptItem.Promotion_Rule__r.Promotion__c);
            availableRuleIds.add(ptItem.Promotion_Rule__c);
        }
        System.debug('***** availablePromoIds:' + availablePromoIds);

        Set<String> brandSet = new Set<String>();
        Set<Id> productIdSet = new Set<Id>();
        Set<Id> merchandisingProductIdSet = new Set<Id>();
        for(Id pId : availablePromoIds){
            for(Promotion_Rule__c ruleItem : promo2RuleMap.get(pId)){
                if(!availableRuleIds.contains(ruleItem.Id)){
                    continue;
                }
                for(Promotion_Threshold__c ptItem : ruleItem.Thresholds__r){
                    for(Promotion_Product__c ppItem : threshold2ProductMap.get(ptItem.Id)){
                        brandSet.add(ppItem.Product__r.Brand_Name__c);
                        productIdSet.add(ppItem.Product__c);
                        if(ppItem.Product__r.RecordType.DeveloperName == 'Amware_Merchandising'
                            || ppItem.Product__r.RecordType.DeveloperName == 'Oracle_Merchandising'){
                            merchandisingProductIdSet.add(ppItem.Product__c);
                        }
                    }
                }
                for(Promotion_Offering__c poItem : ruleItem.Promotion_Offerings__r){
                    if(offering2ProductMap.get(poItem.Id)==null) continue;
                    for(Promotion_Product__c ppItem : offering2ProductMap.get(poItem.Id)){
                        brandSet.add(ppItem.Product__r.Brand_Name__c);
                        productIdSet.add(ppItem.Product__c);
                        if(ppItem.Product__r.RecordType.DeveloperName == 'Amware_Merchandising'
                            || ppItem.Product__r.RecordType.DeveloperName == 'Oracle_Merchandising'){
                            merchandisingProductIdSet.add(ppItem.Product__c);
                        }
                    }
                }
            }
        }

        Map<Id,PricebookEntry> productId2PriceBookEntryMap = getProduc2tPriceBookEntryMap(productIdSet, brandSet, customerId, isDropShip);
        Map<Id,PricebookEntry> merchProductId2PriceBookEntryMap = getMerchProduc2tPriceBookEntryMap(merchandisingProductIdSet);

        for(Id pId : availablePromoIds){
            PromotionData pData = new PromotionData();
            pData.promotion = id2PromoMap.get(pId);
            pData.windowId = promoId2WindowId.get(pId);
            for(Promotion_Rule__c ruleItem : promo2RuleMap.get(pId)){
                if(!availableRuleIds.contains(ruleItem.Id)){
                    continue;
                }
                PromoRuleData rData = new PromoRuleData();
                rData.ruleId = ruleItem.Id;
                rData.ruleName = ruleItem.Name;
                for(Promotion_Threshold__c ptItem : ruleItem.Thresholds__r){
                    PromoThresholdData ptData = new PromoThresholdData();
                    ptData.threshold = ptItem;
                    ptData.products = threshold2ProductMap.get(ptItem.Id);
                    if(ptData.products != null){
                        for(Promotion_Product__c ppItem : ptData.products){
                            PricebookEntry pbItem = productId2PriceBookEntryMap.get(ppItem.Product__c);
                            if(merchandisingProductIdSet.contains(ppItem.Product__c)){
                                pbItem = merchProductId2PriceBookEntryMap.get(ppItem.Product__c);
                            }
                            if(pbItem!=null){
                                ptData.priceBookEntrys.add(pbItem);
                            }else{
                                ptData.priceBookEntrys.add(new PricebookEntry());
                            }
                        }
                    }
                    rData.thresholdList.add(ptData);
                }
                for(Promotion_Offering__c poItem : ruleItem.Promotion_Offerings__r){
                    PromoOfferingData poData = new PromoOfferingData();
                    poData.offering = poItem;
                    poData.products = offering2ProductMap.get(poItem.Id);
                    if(poData.products != null){
                        for(Promotion_Product__c ppItem : poData.products){
                            PricebookEntry pbItem = productId2PriceBookEntryMap.get(ppItem.Product__c);
                            if(merchandisingProductIdSet.contains(ppItem.Product__c)){
                                pbItem = merchProductId2PriceBookEntryMap.get(ppItem.Product__c);
                            }
                            if(pbItem!=null){
                                poData.priceBookEntrys.add(pbItem);
                            }else{
                                poData.priceBookEntrys.add(new PricebookEntry());
                            }
                        }
                    }
                    rData.offeringList.add(poData);
                }
                pData.ruleList.add(rData);
            }
            promotionList.add(pData);
        }
		System.debug('promotionList======'+promotionList);
        return promotionList;
    }

    /**
     * @description Get the map from Product to PriceBookEntry
     */
    public static Map<Id,PricebookEntry> getProduc2tPriceBookEntryMap(Set<Id> productIds, Set<String> brandSet, String customerId, Boolean isDropShip){
        Map<Id,PricebookEntry> product2PriceBookEntryMap = new Map<Id,PricebookEntry>();
        if(productIds.size() == 0 || brandSet.size() == 0){
            return product2PriceBookEntryMap;
        }
        //Boolean isDropShip = true;
        //获取客户上的AuthBrand信息
        String orgCode = '';
        List<Sales_Program__c> authBrandList = [
                SELECT Id, Customer__c, Brands__c, Customer__r.Distributor_or_Dealer__c,
                        Approval_Status__c, IsDeleted, RecordType.DeveloperName,
                        Price_Book__c, Contract_Price_Book__c,Price_Book_Mapping__c,
                        Price_Book_Mapping__r.Name, Contract_Price_Book__r.Price_Book__r.Contract_Price_Book_OracleID__c,
                        Customer__r.ORG_Code__c,
                        (SELECT Id, Address_Type__c, Program__r.Price_Book__c, Special_Dropship_Address__c, Customer_Line_Oracle_ID__c
                            FROM Addresses_With_Program__r
                            WHERE Address_Type__c = 'Dropship Billing Address' AND Special_Dropship_Address__c = true
                            AND Customer_Line_Oracle_ID__c != null)
                FROM Sales_Program__c
                WHERE Customer__c =: customerId
                AND Approval_Status__c = 'Approved'
                AND Brands__c IN :brandSet
                AND RecordType.DeveloperName != 'Service'
                AND IsDeleted = false];
        if (authBrandList != null && authBrandList.size() > 0){
            Set<String> programPriceBookIds = new Set<String>();
            Set<String> specialDropShipAWPIds = new Set<String>();
            Set<String> brandNames = new Set<String>();
            Set<String> priceBookIds = new Set<String>();
            //Add by Abby on 7/16/2020
            Set<String> contractPBOracleIdSet = new Set<String>();
            String customerType = authBrandList[0].Customer__r.Distributor_or_Dealer__c;
            //获取Auth Brand上的授权品牌的价格册
            for (Sales_Program__c authBrand : authBrandList){
                if (!priceBookIds.contains(authBrand.Price_Book__c) && !isDropShip){
                    priceBookIds.add(authBrand.Price_Book__c);
                }
                Boolean isSpecialDropshipAddress = false;
                //update by nick 20200714: special dropship pricebook get from Price And Terms Reference which Special_Dropship_Address__c is true
                if (isDropShip && authBrand.Addresses_With_Program__r != null && authBrand.Addresses_With_Program__r.size() > 0) {
                    if (authBrand.Addresses_With_Program__r[0].Special_Dropship_Address__c == true) {
                        isSpecialDropshipAddress = true;
                        programPriceBookIds.add(authBrand.Addresses_With_Program__r[0].Program__r.Price_Book__c);
                        specialDropShipAWPIds.add(authBrand.Addresses_With_Program__r[0].Id);
                        brandNames.add(authBrand.Brands__c);
                    }
                }
                //如果Auth Brand下面Price Book是Contract Price List，判断下面的价格册是否有主副价格册信息，如果有，拿到主价格册下面的副价格册的OracleId的合集
                if (!isSpecialDropshipAddress && authBrand.Price_Book_Mapping__r != null && authBrand.Price_Book_Mapping__r.Name == 'Contract Price List'){
                    if (authBrand.Contract_Price_Book__r != null && authBrand.Contract_Price_Book__r.Price_Book__r != null){
                        if (authBrand.Contract_Price_Book__r.Price_Book__r.Contract_Price_Book_OracleID__c != null){
                            String oracleIdStr = authBrand.Contract_Price_Book__r.Price_Book__r.Contract_Price_Book_OracleID__c;
                            contractPBOracleIdSet.addAll(oracleIdStr.split(','));
                        }
                    }
                }
                orgCode = authBrand.Customer__r.ORG_Code__c;
            }
            //获取产品的标准价格册（非DropShip 和 DropShip）
            if (!isDropShip){
                //非DropShip的场景
                List<Customer_Brand_Pricebook_Mapping__c> bpList1 = [
                        SELECT Id,
                                Name,
                                Brand__c,
                                Customer_Type__c,
                                Authorized_Brand_Type__c,
                                Price_Book__c,
                                Type__c,
                                Org_Code__c
                        FROM Customer_Brand_Pricebook_Mapping__c
                        WHERE Brand__c IN :brandSet
                        AND Type__c = 'Sales'
                        AND Customer_Type__c =:customerType
                        AND Authorized_Brand_Type__c = 'Standard'];
                getPriceBookByOrgCode(bpList1, orgCode, priceBookIds);
            }else{
                //DropShip的场景
                brandSet.removeAll(brandNames);
                List<Customer_Brand_Pricebook_Mapping__c> bpList2 = [
                        SELECT Id,
                                Name,
                                Brand__c,
                                Customer_Type__c,
                                Price_Book__c,
                                Type__c,
                                Org_Code__c
                        FROM Customer_Brand_Pricebook_Mapping__c
                        WHERE Brand__c IN :brandSet
                        AND Type__c = 'Dropship'
                        AND Special_Dropship_Address__c != true];
                getPriceBookByOrgCode(bpList2, orgCode, priceBookIds);
            }
            //update by nick 20200714:remove price book that from authorized Brand
            priceBookIds.removeAll(programPriceBookIds);
            Set<String> specialDropShipPriceBookIds = Util.getSpecialDropShipPricebookIds(specialDropShipAWPIds);
            Set<String> contractPriceListIds = Util.getPriceBookListByOracleId(contractPBOracleIdSet);
            priceBookIds.addAll(specialDropShipPriceBookIds);
            priceBookIds.addAll(contractPriceListIds);
            System.debug(LoggingLevel.INFO, '*** priceBookIds: ' + priceBookIds);
            if (priceBookIds != null && priceBookIds.size() > 0){
                List<PricebookEntry> priceBookEntryList = [
                    SELECT
                        IsActive,
                        IsDeleted,
                        convertCurrency(UnitPrice),
                        Product2Id,
                        Name,
                        UseStandardPrice,
                        Pricebook2Id
                    FROM PricebookEntry
                    WHERE IsActive = true
                    AND IsDeleted = false
                    AND Pricebook2Id IN :priceBookIds
                    AND Product2Id IN :productIds
                ];
                System.debug('***** priceBookEntryList:' + priceBookEntryList);
                List<String> priceBookIdsList = new List<String>(priceBookIds);
                for(PricebookEntry pbItem: priceBookEntryList){
                    if(!product2PriceBookEntryMap.containsKey(pbItem.Product2Id)){
                        product2PriceBookEntryMap.put(pbItem.Product2Id, pbItem);
                    }else{
                        PricebookEntry pbItemOld = product2PriceBookEntryMap.get(pbItem.Product2Id);
                        String priceBookOld = pbItemOld.Pricebook2Id;
                        String priceBookNew = pbItem.PriceBook2Id;
                        if(priceBookIdsList.indexOf(priceBookOld) > priceBookIdsList.indexOf(priceBookNew)){
                            product2PriceBookEntryMap.put(pbItem.Product2Id, pbItem);
                        }
                    }
                }
            }
        }

        return product2PriceBookEntryMap;
    }

    private static void getPriceBookByOrgCode(List<Customer_Brand_Pricebook_Mapping__c> bpList, String orgCode, Set<String> priceBookIds) {
        for(Customer_Brand_Pricebook_Mapping__c priceBookMap : bpList) {
            if(CCM_Constants.ORG_CODE_CCA == orgCode && CCM_Constants.ORG_CODE_CCA == priceBookMap.Org_Code__c) {
                priceBookIds.add(priceBookMap.Price_Book__c);
            }
            else if((String.isBlank(orgCode) || CCM_Constants.ORG_CODE_CNA == orgCode)
                        && (String.isBlank(priceBookMap.Org_Code__c) || CCM_Constants.ORG_CODE_CNA == priceBookMap.Org_Code__c)) {
                priceBookIds.add(priceBookMap.Price_Book__c);
            }
        }
    }

    /**
     * @description get price book entrys of merchandising products
     */
    public static Map<Id,PricebookEntry> getMerchProduc2tPriceBookEntryMap(Set<Id> productIds){
        Map<Id,PricebookEntry> product2PriceBookEntryMap = new Map<Id,PricebookEntry>();
        String strOracleIds = Label.CCM_Merchandising_Price_Book;
        List<String> merchPriceBookOracleIds = strOracleIds.split(',');
        Set<String> priceBookIds = new Set<String>();
        List<Pricebook2> merchPriceBooks = [
            SELECT Id,
                    Name,
                    Price_Book_OracleID__c,
                    IsActive,
                    Status__c
            FROM Pricebook2
            WHERE Price_Book_OracleID__c IN:merchPriceBookOracleIds
            AND IsActive = true
        ];
        for(Pricebook2 book2 : merchPriceBooks){
            priceBookIds.add(book2.Id);
        }
        if(merchPriceBooks.size() > 0){
            List<PricebookEntry> priceBookEntryList = [
                SELECT
                    IsActive,
                    IsDeleted,
                    convertCurrency(UnitPrice),
                    Product2Id,
                    Name,
                    UseStandardPrice,
                    Pricebook2Id
                FROM PricebookEntry
                WHERE IsActive = true
                AND IsDeleted = false
                AND Pricebook2Id IN :priceBookIds
                AND Product2Id IN :productIds
            ];

            for(PricebookEntry pbItem: priceBookEntryList){
                if(!product2PriceBookEntryMap.containsKey(pbItem.Product2Id)){
                    product2PriceBookEntryMap.put(pbItem.Product2Id, pbItem);
                }
            }
        }
        return product2PriceBookEntryMap;
    }

    @AuraEnabled
    public static String getDropShipInternalPriceBook(String prodId, String customerId){
        InitData initD = new InitData();

        initD.OrgCode = 'CCA';
        Date Expirate = Date.parse(Label.Package_Order_Expire_Date);
        Date Today = System.today();
        if(Today < Expirate){
            initD.ExpirateDate = false;
        }else{
            initD.ExpirateDate = true;
        }
        Account orderCustomer = [SELECT Id,ORG_Code__c FROM Account WHERE Id =:customerId];
        String prodcutOrg = 'United States';
        if(orderCustomer.ORG_Code__c == 'CCA'){
            prodcutOrg = 'Canada';
        }else{
            prodcutOrg = 'United States';
        }

        List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
        if (String.isNotBlank(prodId)){
            List<Product2> prodInfo = [SELECT Id, Country_of_Origin__c,Brand_Name__c,Item_Number__c,ProductCode,CS_Exchange_Rate__c,Weight__c,OverSize__c FROM Product2 WHERE Id =:prodId];
            if (prodInfo != null && prodInfo.size() > 0){
                List<Product2> proList =[select Id,Lanch_Date__c FROM Product2 WHERE ProductCode = :prodInfo[0].ProductCode AND Source__c ='EBS' AND Lanch_Date__c != null AND Country_of_Origin__c = :prodcutOrg AND IsActive = true];
                initD.product = prodInfo[0];
                if(proList.size() > 0){
                    initD.product.Lanch_Date__c = proList[0].Lanch_Date__c;
                }
                String brandName = prodInfo[0].Brand_Name__c;
                prodEntryList = Util.getDropShipPricebookEntryByProdId(prodId, brandName, customerId);
                System.debug(LoggingLevel.INFO, '*** prodEntryList: ' + JSON.serialize(prodEntryList));
                if (prodEntryList != null && prodEntryList.size() > 0){
                    initD.priceBookEntry  = prodEntryList[0];
                }

                //get available promotions by product id and customer id
                if(Test.isRunningTest()){
                    initD.promotionList = null;
                }else{
                    initD.promotionList = getPromotions(prodId, customerId, false, true);
                }
            }
        }

        return JSON.serialize(initD);
    }

    @AuraEnabled
    public static String saveQuotation(String poInfo, String poItemInfos, String customerId, Integer currentStep){
        InitData initD = new InitData();
        if (String.isNotBlank(poInfo)){
            Map<String, String> rtMap = Util.getSobjectRecordTypeNameMap('Purchase_Order__c');
            User currentUsr = Util.getUserInfo(UserInfo.getUserId());
            Purchase_Order__c po = (Purchase_Order__c)JSON.deserialize(poInfo, Purchase_Order__c.class);
            po.Step__c = 'A';
            po.Customer__c = customerId;
            Po.Buyer_Contact__c = currentUsr.Contact.Id;
            Po.RecordTypeId = rtMap.get('Place_Order');

            //handling_fee
            /*Decimal productPrice = po.Product_Price__c;
            if(po.Brand_Scope__c == 'EGO'){
                po.Handling_Fee__c = 0;
            }else{
                if(productPrice < 250){
                    po.Handling_Fee__c = productPrice * 0.1;
                }else{
                    po.Handling_Fee__c = 0;
                }
            }*/

            Account acc = [SELECT Id, ORG_Code__c,Surcharge__c FROM Account WHERE Id=:customerId];
            //Org Code of purchase order
            po.ORG_ID__c = acc.ORG_Code__c;

            if (po.Is_DropShip__c == true || po.Is_Alternative_Address__c == true) {
                if(po.ORG_ID__c == 'CCA'){
                    po.Order_Type__c = 'CA Dropship Order';
                }else{
                    po.Order_Type__c = 'CNA Dropship Order';
                }
            } else {
                if(po.ORG_ID__c == 'CCA'){
                    po.Order_Type__c = 'CA Sales Order - CAD';
                }else{
                    po.Order_Type__c = 'CNA Sales Order - USD';
                }
            }

            //handling fee for CCA
            if(acc.ORG_Code__c == 'CCA'){
                Decimal productPrice = po.Product_Price__c;
                if(productPrice < 250){
                    po.Handling_Fee__c = productPrice * 0.1;
                }else{
                    po.Handling_Fee__c = 0;
                }
            }

            List<Purchase_Order_Item__c> poItemsExsit = [SELECT Id FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :po.Id];
            Decimal totalSurchargeAmout = 0;
            if (String.isNotBlank(poItemInfos)){
                List<Object> deserialized = (List<Object>)JSON.deserializeUntyped(poItemInfos);
                System.debug(LoggingLevel.INFO, '*** deserialized: ' + deserialized);
                List<String > finalJsonString = new List<String>();
                List<Purchase_Order_Item__c> poItems = new List<Purchase_Order_Item__c>();
                for (Object instance : deserialized){
                    Map<String, Object> obj = (Map<String, Object>)instance;
                    Purchase_Order_Item__c item = new Purchase_Order_Item__c();
                    item.Product__c = String.valueOf(obj.get('Product__c'));
                    item.Quantity__c = Integer.valueOf(obj.get('Quantity__c'));
                    item.Brand__c = String.valueOf(obj.get('Brand__c'));
                    if (obj.get('Ship_Date__c') != null && obj.get('Ship_Date__c') != ''){
                        item.Ship_Date__c = Date.valueOf(String.valueOf(obj.get('Ship_Date__c')));
                        po.Expected_Delivery_Date__c = Date.valueOf(String.valueOf(obj.get('Ship_Date__c')));
                    }
                    if (obj.get('Lanch_Date__c') != null && obj.get('Lanch_Date__c') != '') {
                        item.Lanch_Date__c = Date.valueOf(String.valueOf(obj.get('Lanch_Date__c')));
                    }
                    item.ProductCode__c = String.valueOf(obj.get('ProductCode__c'));
                    /*item.Purchase_Order__c = po.Id;*/
                    item.Price_Book__c = String.valueOf(obj.get('Price_Book__c'));
                    item.List_Price__c = Decimal.valueOf(String.valueOf(obj.get('List_Price__c'))).setScale(2);
                    item.Unit_Price__c = Decimal.valueOf(String.valueOf(obj.get('Unit_Price__c'))).setScale(2);
                    // if(acc.ORG_Code__c == 'CCA') {
                    //     item.Ship_Date__c = System.today();
                    // }
                    // else {
                    //     item.Ship_Date__c = getMinSelectDate(po.Submit_Date__c);
                    // }
                    if (obj.get('Discount_Amount__c') != null){
                        item.Discount_Amount__c = Decimal.valueOf(String.valueOf(obj.get('Discount_Amount__c'))).setScale(2);
                    }

                    item.Sub_Total__c = String.valueOf(obj.get('Sub_Total__c')) == null? 0.00: Decimal.valueOf(String.valueOf(obj.get('Sub_Total__c'))).setScale(2);
                    if (obj.get('OverSize__c') != null){
                        item.Is_Over_Size_Product__c = Boolean.valueOf(String.valueOf(obj.get('OverSize__c')));
                    }
                    if (obj.get('Gross_Weight__c') != null){
                        item.Gross_Weight__c = Decimal.valueOf(String.valueOf(obj.get('Gross_Weight__c')));
                    }

                    //Promotion Info
                    if(obj.get('Promotion__c') != null){
                        item.Promotion__c = String.valueOf(obj.get('Promotion__c'));
                    }
                    if(obj.get('PromotionName__c') != null){
                        item.PromotionName__c = String.valueOf(obj.get('PromotionName__c'));
                    }
                    if(obj.get('Promotion_Rule_Name__c') != null){
                        item.Promotion_Rule_Name__c = String.valueOf(obj.get('Promotion_Rule_Name__c'));
                    }
                    if(obj.get('Whole_Order_Promotion__c') != null){
                        item.Whole_Order_Promotion__c = String.valueOf(obj.get('Whole_Order_Promotion__c'));
                    }
                    if (obj.get('Promo_Discount_Amount__c') != null) {
                        item.Promo_Discount_Amount__c = Decimal.valueOf(
                                String.valueOf(obj.get('Promo_Discount_Amount__c'))
                            )
                            .setScale(2);
                    }
                    if (obj.get('Whole_Order_Promo_Discount_Amount__c') != null) {
                        item.Whole_Order_Promo_Discount_Amount__c = Decimal.valueOf(
                                String.valueOf(obj.get('Whole_Order_Promo_Discount_Amount__c'))
                            )
                            .setScale(2);
                    }
                    if(obj.get('Is_Initial__c') != null){
                        item.Is_Initial__c = Boolean.valueOf(obj.get('Is_Initial__c'));
                    }
                    if(obj.get('Regular_Promotion_Window__c') != null){
                        item.Regular_Promotion_Window__c = String.valueOf(obj.get('Regular_Promotion_Window__c'));
                    }

                    //Org Code of purchase order item
                    item.ORG_Code__c = acc.ORG_Code__c;

                    if(item.ORG_Code__c == 'CCA'){
                        item.Line_Type__c = 'CA General Line';
                    }

                    //Surcharge Amount
                    if(acc!= null && acc.Surcharge__c != null && acc.Surcharge__c > 0){
                        item.Surcharge_Amount__c = (item.Unit_Price__c * item.Quantity__c * acc.Surcharge__c/100).setScale(2,System.RoundingMode.HALF_UP);
                        totalSurchargeAmout+=item.Surcharge_Amount__c;
                    }

                    poItems.add(item);
                }


                if (po != null){
                    if(acc!= null && acc.Surcharge__c != null && acc.Surcharge__c > 0){
                        po.Surcharge_Amount__c = totalSurchargeAmout;
                    }
                    upsert po;
                    initD.recordId = po.Id;
                    initD.po = po;

                    if (poItems != null && poItems.size() > 0){
                        for (Purchase_Order_Item__c poItem : poItems){
                            poItem.Purchase_Order__c = po.Id;
                        }
                        upsert poItems;
                    }
                }

                if(poItemsExsit.size() > 0){
                    delete poItemsExsit;
                }

                Decimal ridingMowerNum = 0;
                String ridingMower = Label.CCM_Riding_Mower;
                List<String> ridingMowerSet = ridingMower.split(';');
                Purchase_Order__c currentPo = [SELECT Id,Is_Add_Handling_Fee__c,Order_Type__c,Handling_Fee__c,Freight_Fee__c,Customer__r.AccountNumber,Customer__c FROM Purchase_Order__c WHERE Id = :po.Id];
                String specialAccount = Label.CCM_FreightFee600;
                List<String> specialAccountNumberList = specialAccount.split(';');
                if(!specialAccountNumberList.contains(currentPo.Customer__c)){

                    for(Purchase_Order_Item__c pi : poItems){
                        if(ridingMowerSet.contains(pi.ProductCode__c)){
                            ridingMowerNum += pi.Quantity__c;
                        }
                    }
                    if(currentPo.Order_Type__c == 'CA Dropship Order' || currentPo.Order_Type__c == 'CA Sales Order - CAD'){
                        if(ridingMowerNum > 0 && ridingMowerNum < 3){
                            if(!currentPo.Is_Add_Handling_Fee__c){
                                currentPo.Is_Add_Handling_Fee__c = true;
                                if(currentPo.Freight_Fee__c == 0 || currentPo.Freight_Fee__c == null){
                                    currentPo.Freight_Fee__c = 600;
                                }else{
                                    currentPo.Freight_Fee__c = currentPo.Freight_Fee__c + 600;
                                }
                            }
                        }else{
                            if(currentPo.Is_Add_Handling_Fee__c){
                                currentPo.Is_Add_Handling_Fee__c = false;
                                currentPo.Freight_Fee__c = currentPo.Freight_Fee__c - 600;
                            }
                        }
                    }
                    update currentPo;
                }
                poItems = [SELECT Id,
                                Name,
                                Brand__c,
                                Price_Book__c,
                                ProductCode__c,
                                Product__c,
                                Product__r.Name,
                                // add haibo: product french
                                Product__r.Product_Name_French__c,
                                Product__r.Description,
                                Product__r.Item_Number__c,
                                Product__r.Brand_Name__c,
                                Product__r.CS_Exchange_Rate__c,
                                Product__r.Short_description__c,
                                Product__r.SF_Description__c,
                                // add haibo: product french
                                Product__r.SF_Description_French__c,
                                Quantity__c,
                                Ship_Date__c,
                                convertCurrency(List_Price__c),
                                convertCurrency(Unit_Price__c),
                                MSRP__c,
                                convertCurrency(Sub_Total__c)
                                FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :po.Id];
                initD.poItems = poItems;
                initD.isSuccess = true;
            }
        }

        return JSON.serialize(initD);
    }

    private static Date getMinSelectDate(Datetime submitDate) {
        Set<String> weekends = new Set<String> {
            'Saturday',
            'Sunday'
        };
        Datetime orderDate = Datetime.now();
        if(submitDate != null) {
            orderDate = submitDate;
        }

        Integer selectDays = 5;
        while (selectDays > 0) {
            orderDate = orderDate.addDays(1);
            String dayOfWeek = orderDate.format('EEEE');
            if(!weekends.contains(dayOfWeek)) {
                selectDays = selectDays - 1;
            }
        }

        return Date.newInstance(orderDate.year(), orderDate.month(), orderDate.day());
    }

    @AuraEnabled
    public static String deleteQuotation(String recordId){
        if (String.isNotBlank(recordId)){
            Purchase_Order_Item__c poItem = [SELECT Id FROM Purchase_Order_Item__c WHERE Id =: recordId];
            delete poItem;

            return 'Success';
        }
        return 'Fail';
    }

    @AuraEnabled
    public static String deleteQuotation(List<String> recordIds){
        if (recordIds!=null && recordIds.size()>0){
            List<Purchase_Order_Item__c> poItems = [SELECT Id FROM Purchase_Order_Item__c WHERE Id IN: recordIds];
            delete poItems;

            return 'Success';
        }
        return 'Fail';
    }

    @AuraEnabled
    public static String getPriceBookEntryCondition(String brandName, String customerId, Boolean isDropShip){
        return Util.getPriceBookEntryCondition(brandName, customerId, isDropShip);
    }

    @AuraEnabled
    public static Decimal calcFreightFee(String orderInfo, String orderItemsInfo){
        return CCM_Community_OrderApplicationDetailCtl.calculateFreightFee(orderInfo, orderItemsInfo);
    }

    //Yanko Find FreeGoods CS_Exchange_Rate
    @AuraEnabled
    public static Decimal findCSbyprodID(String prodid){
        list<product2> product = [SELECT id,CS_Exchange_Rate__c FROM Product2 WHERE id = :prodid];
        Decimal cs = product[0].CS_Exchange_Rate__c;
        return cs;
    }

    /**
     * @description Get promotion details by customerId and promotion code.
     */
    @AuraEnabled
    public static String getPromotion(String promotionCode, String customerId, Boolean isDropShip, Boolean needCheckOrder){
        Boolean isPortal = false;
        if(needCheckOrder == null) {
            needCheckOrder = true;
        }
        if(String.isEmpty(customerId)){
            User currentUser = Util.getUserInfo(UserInfo.getUserId());
            if (currentUser.ContactId != null) {
                customerId = currentUser.Contact.AccountId;
            }
            isPortal = true;
        }
        //update by austin, get target customer
        List<Promotion_Target_Customer__c> promo2CustomerList = new List<Promotion_Target_Customer__c>();
        Account customer = [SELECT Id, ORG_Code__c FROM Account WHERE Id =: customerId];
        Boolean isCCA = customer.ORG_Code__c == 'CCA'? true : false;
        if (!isCCA) {
            promo2CustomerList = [
            SELECT
                Id,
                Name,
                Promotion__c,
                Promotion__r.Promotion_Type__c
            FROM Promotion_Target_Customer__c
            WHERE Customer__c =:customerId
            AND Promotion__r.Promotion_Code_For_External__c =:promotionCode
            AND Promotion__r.RecordType.DeveloperName = 'Sell_In_Promotion'
            AND Promotion__r.Promotion_Status__c = 'Open'
            AND Promotion__r.Non_DropShip_Promotion__c = true
        ];
        }else {
            promo2CustomerList = [
                SELECT
                    Id,
                    Name,
                    Promotion__c,
                    Promotion__r.Promotion_Type__c
                FROM Promotion_Target_Customer__c
                WHERE Customer__c =:customerId
                AND Promotion__r.Promotion_Code_For_External__c =:promotionCode
                AND Promotion__r.RecordType.DeveloperName = 'Sell_In_Promotion'
                AND Promotion__r.Promotion_Status__c = 'Open'
                AND Promotion__r.Is_DropShip_Promotion__c =: isDropShip
            ];
        }

        if(promo2CustomerList.size() == 0){
            return '';
        }
        Promotion_Target_Customer__c p2Customer = promo2CustomerList[0];
        List<String> windowStatus = new List<String>();
        windowStatus.add('In Progress');
        if(!isPortal){
            windowStatus.add('Closed For External');
        }
        List<Promotion_Window__c> promotionWindowList = [
            SELECT
                Id,
                Promotion__c,
                Availability_in_Dealer_Portal__c
            FROM Promotion_Window__c
            WHERE Promotion__c =:p2Customer.Promotion__c
            AND Promotion_Window_Status2__c IN: windowStatus
        ];

        if(promotionWindowList.size() ==0){
            //there is no promotion for the customer
            return '';
        }

        List<Promotion2__c> promotion2List = [
            SELECT
                Id,
                Brands__c,
                Name,
                // add haibo: promotion (french) 翻译
                Promotion_Name_French__c,
                Promotion_Type__c,
                Promo_Code__c,
                Promotion_Code_For_External__c,
                First_Time_Purchase_Promotion__c
            FROM Promotion2__c
            WHERE Id =:p2Customer.Promotion__c
        ];

        List<Promotion_Rule__c> promoRuleList = [
            SELECT
                Id,
                Promotion__c,
                (SELECT
                    Id,
                    Name,
                    Gift_Total_Quantity__c,
                    Payment_Term__c,
                    Payment_Term_Label__c,
                    RecordTypeId,
                    RecordType.Name,
                    RecordType.DeveloperName,
                    Promotion_Rule__c,
                    Discount_Off__c,
                    Amount_Off__c
                FROM Promotion_Offerings__r),
                (SELECT
                    Id,
                    Name,
                    Bogo_Remark__c,
                    RecordTypeId,
                    RecordType.Name,
                    RecordType.DeveloperName,
                    Minimum_Total_Amount__c,
                    Minimum_Total_Quantity__c,
                    Multiple_Control__c,
                    Buy_Qty__c,
                    Minimum_Different_Tool_Models__c,
                    Max_Different_Tool_Models__c,
                    Minimum_Whole_Order_Amount__c,
                    Maximum_Whole_Order_Amount__c,
                    Minimum_Whole_Order_Quantity__c,
                    Min_Different_Categories__c,
                    Promotion_Rule__c
                FROM Thresholds__r)
            FROM Promotion_Rule__c
            WHERE Promotion__c = :p2Customer.Promotion__c
        ];

        Set<Id> thresholdIds = new Set<Id>();
        Set<Id> offeringIds = new Set<Id>();
        for(Promotion_Rule__c ruleItem : promoRuleList){
            for(Promotion_Threshold__c ptItem : ruleItem.Thresholds__r){
                thresholdIds.add(ptItem.Id);
            }
            for(Promotion_Offering__c poItem : ruleItem.Promotion_Offerings__r){
                offeringIds.add(poItem.Id);
            }
        }
        List<Promotion_Product__c> allPromotionProductList = [
            SELECT
                Id,
                Multiple_Control__c,
                Minimum_Amount__c,
                Maximum_Amount__c,
                Minimum_Quantity__c,
                Maximum_Quantity__c,
                Gift_Quantity__c,
                Number__c,
                Increment_For_Free_Goods__c,
                Chervon_Funding__c,
                PMAPP__c,
                Promotion2_Threshold__c,
                Promotion2_Offering__c,
                Product__c,
                Product__r.Name,
                // add haibo: product french
                Product__r.Product_Name_French__c,
                Product__r.CS_Exchange_Rate__c,
                Product__r.Brand_Name__c,
                Product__r.ProductCode,
                Product__r.SF_Description__c,
                // add haibo: product french
                Product__r.SF_Description_French__c,
                Product__r.Full_Pallet_Quantity__c,
                Product__r.RecordType.DeveloperName
            FROM Promotion_Product__c
            WHERE Promotion2_Threshold__c IN :thresholdIds OR Promotion2_Offering__c IN :offeringIds
        ];
        Map<Id, List<Promotion_Product__c>> threshold2ProductMap = new Map<Id, List<Promotion_Product__c>>();
        Map<Id, List<Promotion_Product__c>> offering2ProductMap = new Map<Id, List<Promotion_Product__c>>();
        Set<String> brandSet = new Set<String>();
        Set<Id> productIdSet = new Set<Id>();
        Set<Id> merchandisingProductIdSet = new Set<Id>();
        for(Promotion_Product__c ppItem : allPromotionProductList){
            if(String.isNotEmpty(ppItem.Promotion2_Threshold__c)){
                if(!threshold2ProductMap.containsKey(ppItem.Promotion2_Threshold__c)){
                    threshold2ProductMap.put(ppItem.Promotion2_Threshold__c, new List<Promotion_Product__c>());
                }
                threshold2ProductMap.get(ppItem.Promotion2_Threshold__c).add(ppItem);
            }
            if(String.isNotEmpty(ppItem.Promotion2_Offering__c)){
                if(!offering2ProductMap.containsKey(ppItem.Promotion2_Offering__c)){
                    offering2ProductMap.put(ppItem.Promotion2_Offering__c, new List<Promotion_Product__c>());
                }
                offering2ProductMap.get(ppItem.Promotion2_Offering__c).add(ppItem);
            }
            brandSet.add(ppItem.Product__r.Brand_Name__c);
            productIdSet.add(ppItem.Product__c);
            if(ppItem.Product__r.RecordType.DeveloperName == 'Amware_Merchandising'
                || ppItem.Product__r.RecordType.DeveloperName == 'Oracle_Merchandising'){
                merchandisingProductIdSet.add(ppItem.Product__c);
            }
        }

        Map<Id,PricebookEntry> productId2PriceBookEntryMap = getProduc2tPriceBookEntryMap(productIdSet, brandSet, customerId, isDropShip);
        Map<Id,PricebookEntry> merchProductId2PriceBookEntryMap = getMerchProduc2tPriceBookEntryMap(merchandisingProductIdSet);

        PromotionData promoData = new PromotionData();
        promoData.promotion = promotion2List[0];
        if(promoData.promotion.Promotion_Type__c == 'Payment Term Promo' && promoData.promotion.First_Time_Purchase_Promotion__c && needCheckOrder) {
            Boolean hasOrder = checkHasOrder(customerId, null);
            if(hasOrder) {
                return '';
            }
        }

        promoData.windowId = promotionWindowList[0].Id;
        for(Promotion_Rule__c ruleItem : promoRuleList){
            PromoRuleData rData = new PromoRuleData();
            for(Promotion_Threshold__c ptItem : ruleItem.Thresholds__r){
                PromoThresholdData ptData = new PromoThresholdData();
                ptData.threshold = ptItem;
                ptData.products = threshold2ProductMap.get(ptItem.Id);
                if(ptData.products != null){
                    for(Promotion_Product__c ppItem : ptData.products){
                        PricebookEntry pbItem = productId2PriceBookEntryMap.get(ppItem.Product__c);
                        if(pbItem!=null){
                            ptData.priceBookEntrys.add(pbItem);
                        }else{
                            ptData.priceBookEntrys.add(new PricebookEntry());
                        }
                    }
                }
                rData.thresholdList.add(ptData);
            }
            for(Promotion_Offering__c poItem : ruleItem.Promotion_Offerings__r){
                PromoOfferingData poData = new PromoOfferingData();
                poData.offering = poItem;
                poData.products = offering2ProductMap.get(poItem.Id);
                if(poData.products != null){
                    for(Promotion_Product__c ppItem : poData.products){
                        PricebookEntry pbItem = productId2PriceBookEntryMap.get(ppItem.Product__c);
                        if(merchandisingProductIdSet.contains(ppItem.Product__c)){
                            pbItem = merchProductId2PriceBookEntryMap.get(ppItem.Product__c);
                        }
                        if(pbItem!=null){
                            poData.priceBookEntrys.add(pbItem);
                        }else{
                            poData.priceBookEntrys.add(new PricebookEntry());
                        }
                    }
                }
                rData.offeringList.add(poData);
            }
            promoData.ruleList.add(rData);
        }
        return JSON.serialize(promoData);
    }


    @AuraEnabled
    public static String checkAvailablePaymentTermPromo(String addressId, String customerId, Boolean isDropShip, String paymentTermPromoCode) {
        Boolean isFirstTimePuchasePromo = false;
        if(String.isNotBlank(paymentTermPromoCode)) {
            List<Promotion2__c> promotions = [SELECT Promotion_Code_For_External__c, First_Time_Purchase_Promotion__c FROM Promotion2__c WHERE Promotion_Code_For_External__c = :paymentTermPromoCode];
            for(Promotion2__c promotion : promotions) {
                if(promotion.First_Time_Purchase_Promotion__c) {
                    isFirstTimePuchasePromo = true;
                }
            }
        }

        if(isFirstTimePuchasePromo) {
            return '';
        }

        Boolean hasOrder = checkHasOrder(null, addressId);
        if(hasOrder) {
            return '';
        }

        Boolean isPortal = false;
        if(String.isEmpty(customerId)){
            User currentUser = Util.getUserInfo(UserInfo.getUserId());
            if (currentUser.ContactId != null) {
                customerId = currentUser.Contact.AccountId;
            }
            isPortal = true;
        }

        Account customer = [SELECT Id, ORG_Code__c FROM Account WHERE Id =: customerId];
        Boolean isCCA = customer.ORG_Code__c == 'CCA'? true : false;
        List<Promotion_Target_Customer__c> promo2CustomerList = new List<Promotion_Target_Customer__c>();
        if (!isCCA) {
            promo2CustomerList = [
            SELECT
                Id,
                Name,
                Promotion__c,
                Promotion__r.Promotion_Type__c
            FROM Promotion_Target_Customer__c
            WHERE Customer__c =:customerId
            AND Promotion__r.RecordType.DeveloperName = 'Sell_In_Promotion'
            AND Promotion__r.Promotion_Status__c = 'Open'
            AND Promotion__r.Non_DropShip_Promotion__c = true
            AND Promotion__r.Promotion_Type__c = 'Payment Term Promo'
            AND Promotion__r.First_Time_Purchase_Promotion__c = true
        ];
        }else {
            promo2CustomerList = [
                SELECT
                    Id,
                    Name,
                    Promotion__c,
                    Promotion__r.Promotion_Type__c
                FROM Promotion_Target_Customer__c
                WHERE Customer__c =:customerId
                AND Promotion__r.RecordType.DeveloperName = 'Sell_In_Promotion'
                AND Promotion__r.Promotion_Status__c = 'Open'
                AND Promotion__r.Is_DropShip_Promotion__c =: isDropShip
                AND Promotion__r.Promotion_Type__c = 'Payment Term Promo'
                AND Promotion__r.First_Time_Purchase_Promotion__c = true
            ];
        }

        if(promo2CustomerList.size() == 0){
            return '';
        }

        Set<String> promotionIds = new Set<String>();
        for(Promotion_Target_Customer__c targetCustomer : promo2CustomerList) {
            promotionIds.add(targetCustomer.Promotion__c);
        }

        List<String> windowStatus = new List<String>();
        windowStatus.add('In Progress');
        if(!isPortal){
            windowStatus.add('Closed For External');
        }
        List<Promotion_Window__c> promotionWindowList = [
            SELECT
                Id,
                Promotion__c,
                Availability_in_Dealer_Portal__c
            FROM Promotion_Window__c
            WHERE Promotion__c IN :promotionIds
            AND Promotion_Window_Status2__c IN: windowStatus
        ];

        if(promotionWindowList.size() ==0){
            return '';
        }

        Set<String> availablePromotionIds = new Set<String>();
        for(Promotion_Window__c pw : promotionWindowList) {
            availablePromotionIds.add(pw.Promotion__c);
        }

        List<Promotion2__c> promotion2List = [
            SELECT
                Id,
                Brands__c,
                Name,
                // add haibo: promotion (french) 翻译
                Promotion_Name_French__c,
                Promotion_Type__c,
                Promo_Code__c,
                Promotion_Code_For_External__c
            FROM Promotion2__c
            WHERE Id IN :availablePromotionIds
        ];

        if(!promotion2List.isEmpty()) {
            List<String> availablePromotionCodes = new List<String>();
            for(Promotion2__c promotion : promotion2List) {
                availablePromotionCodes.add(promotion.Promotion_Code_For_External__c);
            }
            return String.join(availablePromotionCodes, ', ');
        }
        return '';
    }


    private static Boolean checkHasOrder(String customerId, String addressId) {
        Boolean hasOrder = false;
        List<Order> ods = new List<Order>();
        if(String.isNotBlank(addressId)) {
            Set<String> storeLocationIds = new Set<String>();
            for(Account_Address__c address : [SELECT Store_Location__c FROM Account_Address__c WHERE Id = :addressId]) {
                storeLocationIds.add(address.Store_Location__c);
            }
            if(storeLocationIds.isEmpty()) {
                hasOrder = true;
            }
            else {
                ods = [SELECT Id FROM Order WHERE Store_Location__c IN :storeLocationIds LIMIT 1];
                if(!ods.isEmpty()) {
                    hasOrder = true;
                }
            }
        }
        else if(String.isNotBlank(customerId)) {
            ods = [SELECT Id FROM Order WHERE AccountId = :customerId AND Order_Status__c != 'Cancelled' LIMIT 1];
        }
        if(!ods.isEmpty()) {
            hasOrder = true;
        }
        return hasOrder;
    }

    /**
     * @description Check applied promotion codes by customer id.
     */
    @AuraEnabled
    public static String checkPromotions(List<String> promotionCodes, String customerId){
        Boolean isPortal = false;
        if(String.isEmpty(customerId)){
            User currentUser = Util.getUserInfo(UserInfo.getUserId());
            if (currentUser.ContactId != null) {
                customerId = currentUser.Contact.AccountId;
            }
            isPortal = true;
        }
        List<Promotion_Target_Customer__c> promo2CustomerList = [
            SELECT
                Id,
                Name,
                Promotion__c,
                Promotion__r.Promotion_Type__c
            FROM Promotion_Target_Customer__c
            WHERE (Customer__c =:customerId OR Top_Customer__c =: customerId)
            AND Promotion__r.Promotion_Code_For_External__c IN:promotionCodes
            AND Promotion__r.RecordType.DeveloperName = 'Sell_In_Promotion'
            AND Promotion__r.Promotion_Status__c = 'Open'
        ];
        if(promo2CustomerList.size() == 0){
            InitData checkResult = new InitData();
            checkResult.isSuccess = false;
            checkResult.errorMsg = 'There are invalid promotion codes ('+ JSON.serialize(promotionCodes).remove('[').remove(']').remove('"') +') in the quotation list.';
            return JSON.serialize(checkResult);
        }

        Set<Id> promoIds = new Set<Id>();
        for(Promotion_Target_Customer__c tItem : promo2CustomerList){
            promoIds.add(tItem.Promotion__c);
        }

        Date today = System.today();
        List<String> windowStatus = new List<String>();
        windowStatus.add('In Progress');
        if(!isPortal){
            windowStatus.add('Closed For External');
        }
        List<Promotion_Window__c> promotionWindowList = [
            SELECT
                Id,
                Promotion__c,
                Promotion__r.Promotion_Code_For_External__c,
                Availability_in_Dealer_Portal__c
            FROM Promotion_Window__c
            WHERE Promotion__c IN:promoIds
            //AND Start_Date__c <= :today
            //AND End_Date__c >= :today
            AND Promotion_Window_Status2__c IN :windowStatus
        ];

        if(promotionWindowList.size() ==0){
            //there is no promotion for the customer
            InitData checkResult = new InitData();
            checkResult.isSuccess = false;
            checkResult.errorMsg = 'There are invalid promotion codes ('+ JSON.serialize(promotionCodes).remove('[').remove(']').remove('"') +') in the quotation list.';
            return JSON.serialize(checkResult);
        }

        Set<String> validCodes = new Set<String>();
        for(Promotion_Window__c wItem : promotionWindowList){
            validCodes.add(wItem.Promotion__r.Promotion_Code_For_External__c);
        }

        Set<String> invalidCodes = new Set<String>();
        for(String codeItem : promotionCodes){
            if(!validCodes.contains(codeItem)){
                invalidCodes.add(codeItem);
            }
        }

        InitData checkResult = new InitData();
        if(invalidCodes.size() == 0){
            checkResult.isSuccess = true;
        }else{
            checkResult.isSuccess = false;
            checkResult.errorMsg = 'There are invalid promotion codes ('+ JSON.serialize(invalidCodes).remove('[').remove(']').remove('"') +') in the quotation list.';
        }

        return JSON.serialize(checkResult);
    }

    @AuraEnabled
    public static String getCurrentLaunchDate(String recordId) {
        Map<String,Date> launchDateMap = new Map<String,Date>();
        Set<String> productCodeSet = new Set<String>();
        String customerId = '';
        for(Purchase_Order_Item__c poi : [SELECT Id,Product__c,Product__r.ProductCode,Purchase_Order__c,Purchase_Order__r.Customer__c,Purchase_Order__r.Is_DropShip__c FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :recordId]){
            productCodeSet.add(poi.Product__r.ProductCode);
            customerId = poi.Purchase_Order__r.Customer__c;
        }
        Account orderCustomer = [SELECT Id,ORG_Code__c FROM Account WHERE Id =:customerId];
        String prodcutOrg = 'United States';
        if(orderCustomer.ORG_Code__c == 'CCA'){
            prodcutOrg = 'Canada';
        }else{
            prodcutOrg = 'United States';
        }
        for(Product2 pr : [select Id,Lanch_Date__c,ProductCode FROM Product2 WHERE ProductCode IN :productCodeSet AND Source__c ='EBS' AND Lanch_Date__c != null AND Country_of_Origin__c = :prodcutOrg AND IsActive = true]){
            if(!launchDateMap.containsKey(pr.ProductCode)){
                launchDateMap.put(pr.ProductCode, pr.Lanch_Date__c);
            }
        }
        return JSON.serialize(launchDateMap);
    }

    @AuraEnabled
    public static String getCurrentPriceList(String recordId) {
        Map<String,Decimal> priceListMap = new Map<String,Decimal>();
        for(Purchase_Order_Item__c poi : [SELECT Id,Product__c,Product__r.ProductCode,List_Price__c FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :recordId]){
            priceListMap.put(poi.Product__r.ProductCode, poi.List_Price__c);
        }
        return JSON.serialize(priceListMap);
    }

    @AuraEnabled
    public static String getProductSellable(String recordId) {
        String result = '';
        for(Purchase_Order_Item__c poi : [SELECT Id,Product__c,Product__r.ProductCode,Product__r.Sellable__c,Purchase_Order__c,Purchase_Order__r.Customer__c,Purchase_Order__r.Is_DropShip__c,Purchase_Order__r.Customer__r.ORG_Code__c,Product__r.Sellable_CCA__c FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :recordId]){
            if(poi.Purchase_Order__r.Customer__r.ORG_Code__c == 'CCA'){
                if(!poi.Product__r.Sellable_CCA__c){
                    result += '[' + poi.Product__r.ProductCode + ']';
                }
            }else{
                if(!poi.Product__r.Sellable__c){
                    result += '[' + poi.Product__r.ProductCode + ']';
                }
            }
        }
        if(result != ''){
            result += ' is Unsellable!';
        }
        return result;
    }

    public class InitData{
        @AuraEnabled public Sales_Program__c authBrandInfo {get; set;}
        @AuraEnabled public PricebookEntry priceBookEntry {get; set;}
        @AuraEnabled public Product2 product {get; set;}
        @AuraEnabled public Integer currentStep {get; set;}
        @AuraEnabled public String recordId {get; set;}
        @AuraEnabled public Purchase_Order__c po {get; set;}
        @AuraEnabled public List<Purchase_Order_Item__c> poItems {get; set;}
        @AuraEnabled public Boolean isSuccess {get; set;}
        //Yanko
        @AuraEnabled public Boolean ExpirateDate {get; set;}
        @AuraEnabled public String OrgCode {get; set;}
        @AuraEnabled public String errorMsg {get; set;}

        /* promotion list*/
        @AuraEnabled public List<PromotionData> promotionList {get;set;}

        public InitData(){
            this.authBrandInfo = new Sales_Program__c();
            this.priceBookEntry = new PricebookEntry();
            this.product = new Product2();
            this.po = new Purchase_Order__c();
            this.poItems = new List<Purchase_Order_Item__c>();
            this.isSuccess = false;
            this.ExpirateDate = false;
            this.OrgCode = '';
            this.errorMsg = '';
            this.promotionList = new List<PromotionData>();
        }
    }

    public class PromotionData{
        /* promotion rule list*/
        @AuraEnabled public List<PromoRuleData> ruleList { get;set;}
        @AuraEnabled public Promotion2__c promotion {get; set;}
        @AuraEnabled Public String windowId {get;set;}
        public PromotionData(){
            this.ruleList = new List<PromoRuleData>();
        }
    }

    public class PromoRuleData{
        /* promotion threshold list*/
        @AuraEnabled public List<PromoThresholdData> thresholdList { get;set;}
        /* promotion offering list*/
        @AuraEnabled public List<PromoOfferingData> offeringList { get;set;}
        @AuraEnabled public String ruleName {get;set;}
        @AuraEnabled public String ruleId {get;set;}
        public PromoRuleData(){
            this.thresholdList = new List<PromoThresholdData>();
            this.offeringList = new List<PromoOfferingData>();
        }
    }

    public class PromoThresholdData{
        @AuraEnabled public Promotion_Threshold__c threshold {get;set;}
        @AuraEnabled public List<Promotion_Product__c> products { get; set;}
        @AuraEnabled public List<PricebookEntry> priceBookEntrys {get;set;}
        public PromoThresholdData(){
            this.products = new List<Promotion_Product__c>();
            this.priceBookEntrys = new List<PricebookEntry>();
        }
    }

    public class PromoOfferingData{
        @AuraEnabled public Promotion_Offering__c offering {get;set;}
        @AuraEnabled public List<Promotion_Product__c> products { get; set;}
        @AuraEnabled public List<PricebookEntry> priceBookEntrys {get;set;}
        public PromoOfferingData(){
            this.products = new List<Promotion_Product__c>();
            this.priceBookEntrys = new List<PricebookEntry>();
        }
    }
}