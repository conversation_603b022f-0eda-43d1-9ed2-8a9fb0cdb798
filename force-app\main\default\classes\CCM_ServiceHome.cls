/**
 * @description       :
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 03-13-2024
 * @last modified by  : <EMAIL>
**/
public without sharing class CCM_ServiceHome {

    @AuraEnabled
    public static String Init(String userId, String type, Integer pageNumber, Integer singlePageSize){
        String accId = null;

        if(!Test.isRunningTest()){
            accId = CCM_PortalPageUtil.getCustomerByUser(UserInfo.getUserId());
        }else{
            accId = userId;
        }

        String orgCode = '';
        if(String.isNotBlank(accId)) {
            for(Account acc : [SELECT ORG_Code__c FROM Account WHERE Id = :accId]) {
                orgCode = acc.ORG_Code__c;
            }
        }

        Map<String,Object> result = new Map<String,Object>();
        result.put('orgCode',orgCode);

        List<Id> caseIdList = new List<Id>();
        if(type == 'Order'){
            List<Order> orderList = [SELECT Id,OrderNumber,PO_No__c,Submit_Date__c,Status,TotalAmount
                                FROM Order
                                WHERE AccountId =: accId
                                ORDER BY CreatedDate DESC
                                ];

            Integer totalPage = orderList.size();

            List<Order> orderListInOnePage = new List<Order>();
            for(Integer i=(pageNumber-1)*singlePageSize; i<=(pageNumber-1)*singlePageSize+singlePageSize-1; i++ ){
                if(orderList.size()>i){
                    orderListInOnePage.add(orderList[i]);
                }
            }
            result.put('Order',orderListInOnePage);
            result.put('TotalOrderPageNumber',totalPage);
        }else if(type == 'Claim'){
            User user = Util.getUserInfo(UserInfo.getUserId());
            Account customer = [SELECT Name, ORG_Code__c, Distributor_or_Dealer__c, AccountNumber FROM Account WHERE Id =: accId LIMIT 1];
            String accountName = customer.Name;
            List<Warranty_Claim__c> claimList = [SELECT Id,Name,Status__c,Payment_Status__c,Labor_Cost_Summary__c,Parts_Cost__c,Total__c,Invoice_Item__r.Invoice__r.Name, Inventory__c, Brand_Name__c, Service_Option__c, Repair_Type__c, Case__r.ProductId, Case__r.Product.ProductCode,Service_Partner__r.Name,Case__c
                                FROM Warranty_Claim__c
                                WHERE Service_Partner__c =: accId
                                ORDER BY CreatedDate DESC
                                ];

            if(accountName == 'Hayward Distributing Co'){
                List<Warranty_Claim__c> claimDistributorList = [SELECT Id,Name,Status__c,Payment_Status__c,Labor_Cost_Summary__c,Parts_Cost__c,Total__c,Invoice_Item__r.Invoice__r.Name, Inventory__c, Brand_Name__c, Service_Option__c, Repair_Type__c, Case__r.ProductId, Case__r.Product.ProductCode,Service_Partner__r.ParentId,Service_Partner__r.Name,Case__c
                                FROM Warranty_Claim__c
                                WHERE Service_Partner__r.ParentId =: accId
                                ORDER BY CreatedDate DESC
                                ];
                claimList.addAll(claimDistributorList);
            }

            Map<Id, String> claimRejectCommentsMap = getClaimRejectCommentsMap(claimList);

            Integer pendingClaim = 0;
            Integer approvedClaim = 0;
            Integer rejectedClaim = 0;
            Set<String> draftClaimIds = new Set<String>();

            Map<String, Decimal> warrantyMaterialCostMap = new Map<String, Decimal>();
            Set<String> productIds = new Set<String>();
            Set<String> dealerInventoryProductCodes = new Set<String>();
            Set<String> dealerInventoryBrands = new Set<String>();
            for(Warranty_Claim__c wc : claimList){
                caseIdList.add(wc.Case__c);
                if (wc.Status__c == 'Draft') {
                    draftClaimIds.add(wc.Id);
                    warrantyMaterialCostMap.put(wc.Id, wc.Parts_Cost__c);
                    if (wc.Service_Option__c != 'Repair' && wc.Inventory__c == 'Dealer Inventory') {
                        dealerInventoryProductCodes.add(wc.Case__r.Product.ProductCode);
                        dealerInventoryBrands.add(wc.Brand_Name__c);
                    }
                } else if(wc.Status__c == 'Submitted'){
                    pendingClaim += 1;
                }else if(wc.Status__c == 'Rejected'){
                    rejectedClaim += 1;
                }else{
                    approvedClaim += 1;
                }
            }


            System.debug('*** warrantyMaterialCostMap: ' + warrantyMaterialCostMap);
            if (draftClaimIds.size() > 0) {

                List<Claim_Item__c> itemList = [SELECT Id, Warranty_Claim__c, Product__c, Price__c, Quantity__c FROM Claim_Item__c WHERE Warranty_Claim__c = :draftClaimIds];
                for (Claim_Item__c item : itemList) {
                    productIds.add(item.Product__c);
                }
                String priceBookId = null;
                if(CCM_Constants.ORG_CODE_CCA == orgCOde) {
                    if(customer.Distributor_or_Dealer__c.contains('Distributor')) {
                        priceBookId = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Name = 'CA-Distributor Price for Parts' AND Type__c = 'Service' AND Is_Active__c = true LIMIT 1].Price_Book__c;
                    }
                    else {
                        priceBookId = [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Name = 'CA-Direct Dealer Price for Parts' AND Type__c = 'Service' AND Is_Active__c = true LIMIT 1].Price_Book__c;
                    }
                }
                else {
                    if(customer.Distributor_or_Dealer__c.contains('Dealer')){
                        priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Direct Dealer Price for Parts' LIMIT 1].Price_Book__c;
                    }else if((customer.Distributor_or_Dealer__c.contains('Distributor')) || user.Profile.Name.contains(CCM_Constants.PROFILE_NAME_SERVICE_LIVE)){
                        priceBookId =  [SELECT Price_Book__c FROM Customer_Brand_Pricebook_Mapping__c WHERE Type__c = 'Service' AND Is_Active__c = true AND Name = 'CNA-Distributor Price for Parts' LIMIT 1].Price_Book__c;
                    }
                }
                if(customer.AccountNumber == 'B10127'){
                    priceBookId =  [SELECT Id FROM Pricebook2 WHERE Name = 'CNA-MSRP for Parts' LIMIT 1].Id;
                }
                List<PricebookEntry> prodEntryList = new List<PricebookEntry>();
                List<Pricebook2> priceBook = [SELECT IsStandard, Name, IsActive, Id FROM Pricebook2 WHERE IsStandard = false AND Id =: priceBookId AND IsActive = true LIMIT 1];
                Map<String, Decimal> productPriceMap = new Map<String, Decimal>();
                if (priceBook != null && priceBook.size() > 0){
                    for (PricebookEntry entry : [SELECT IsActive, IsDeleted, convertCurrency(UnitPrice), Product2Id, Name, UseStandardPrice, Pricebook2Id
                                                    FROM PricebookEntry
                                                    WHERE IsActive = true
                                                    AND IsDeleted = false
                                                    AND Pricebook2Id =: priceBook[0].Id
                                                    AND Product2Id IN :productIds]) {
                        productPriceMap.put(entry.Product2Id, entry.UnitPrice);
                    }
                }


                for (Claim_Item__c item : itemList) {
                    if (productPriceMap.containsKey(item.Product__c) && item.Price__c != productPriceMap.get(item.Product__c)) {
                        System.debug('*** Material cost: ' + warrantyMaterialCostMap.get(item.Warranty_Claim__c));
                        System.debug('*** warranty claim: ' + item.Warranty_Claim__c + ' - ' + item.Price__c);
                        // warrantyMaterialCostMap.put(item.Warranty_Claim__c, warrantyMaterialCostMap.get(item.Warranty_Claim__c) + (item.Price__c -  productPriceMap.get(item.Product__c)) * item.Quantity__c);
                        warrantyMaterialCostMap.put(item.Warranty_Claim__c, productPriceMap.get(item.Product__c) * item.Quantity__c);
                        System.debug('*** Material cost: ' + warrantyMaterialCostMap.get(item.Warranty_Claim__c));
                    }
                }

                for(Warranty_Claim__c wc : claimList){
                    if (wc.Status__c == 'Draft' && wc.Service_Option__c == 'Repair' && wc.Repair_Type__c != 'Labor Time Only') {
                        wc.Total__c -= wc.Parts_Cost__c;
                        wc.Parts_Cost__c = warrantyMaterialCostMap.get(wc.Id).abs();
                        wc.Total__c += wc.Parts_Cost__c;
                    }
                }

                Set<String> pricebookIds = new Set<String>();
                for (Sales_Program__c sp : [SELECT Price_Book_Mapping__r.Price_Book__c FROM Sales_Program__c
                                                WHERE Customer__c = :accId
                                                    AND Brands__c IN :dealerInventoryBrands
                                                    AND Approval_Status__c = :CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED
                                                    AND RecordTypeId != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_ID
                                                LIMIT 1]) {
                    pricebookIds.add(sp.Price_Book_Mapping__r.Price_Book__c);
                }

                Map<String, Decimal> prodEntryMap = new Map<String, Decimal>();
                List<PricebookEntry> priceEntries = [SELECT IsActive, IsDeleted, convertCurrency(UnitPrice), Product2Id, Product2.ProductCode, Name, UseStandardPrice, Pricebook2Id
                                                        FROM PricebookEntry
                                                        WHERE IsDeleted = false
                                                        AND Pricebook2Id IN: pricebookIds
                                                        AND Product2.ProductCode IN: dealerInventoryProductCodes
                                                        AND Product2.Source__c = :CCM_Constants.PRODUCT_SOURCE_PIM
                                                        AND IsActive = TRUE
                                                        AND Product2.RecordTypeId = :CCM_Constants.PRODUCT_PRODUCT_RECORD_TYPE_ID];
                for (PriceBookEntry entry : priceEntries) {
                    prodEntryMap.put(entry.Product2.ProductCode, entry.UnitPrice);
                }
                for(Warranty_Claim__c wc : claimList){
                    if (wc.Status__c == 'Draft' && wc.Service_Option__c != 'Repair' && wc.Inventory__c == 'Dealer Inventory') {
                        if (String.isNotEmpty(wc.Case__c) && String.isNotEmpty(wc.Case__r.ProductId) && prodEntryMap.get(wc.Case__r.Product.ProductCode) != null) {
                            wc.Total__c -= wc.Parts_Cost__c;
                            wc.Parts_Cost__c = prodEntryMap.get(wc.Case__r.Product.ProductCode).abs();
                            wc.Total__c += wc.Parts_Cost__c;
                        }
                    }
                }
            }

            Integer totalPage = claimList.size();

            List<AllClaimClass> claimListInOnePage = new List<AllClaimClass>();
            Map<Id,String> caseidMaptoTracking = new Map<Id,String>();
            for(Order ord : [SELECT id,Tracking_Number__c,Case__c FROM Order WHERE Case__c IN: caseIdList]){
                caseidMaptoTracking.put(ord.Case__c, ord.Tracking_Number__c);
            }

            for(Integer i=(pageNumber-1)*singlePageSize; i<=(pageNumber-1)*singlePageSize+singlePageSize-1; i++ ){
                if(claimList.size()>i){
                    AllClaimClass acc = new AllClaimClass();
                    acc.claim = claimList[i];
                    if(acc.claim.Service_Option__c == 'Replacement' && caseidMaptoTracking.containsKey(acc.claim.Case__c)){
                        acc.trackingNumber =  caseidMaptoTracking.get(acc.claim.Case__c);
                    }
                    if(claimRejectCommentsMap.containsKey(claimList[i].Id)) {
                        acc.rejectComments = claimRejectCommentsMap.get(claimList[i].Id);
                    }
                    acc.viewStyleCss = 'showbtn';
                    if (claimList[i].Status__c == 'Draft') {
                        acc.viewStyleCss = 'hidebtn';
                        acc.editStyleCss = 'showbtn';
                        acc.rejectStypleCss = 'hidebtn';
                        acc.deleteStyleCss = 'showbtn';
                    }
                    else if(claimList[i].Status__c == 'Rejected'){
                        acc.rejectStypleCss = 'showbtn';
                        acc.editStyleCss = 'showbtn';
                    } else {
                        acc.rejectStypleCss = 'hidebtn';
                        acc.editStyleCss = 'hidebtn';
                    }
                    claimListInOnePage.add(acc);
                }
            }
            result.put('Claim',claimListInOnePage);
            result.put('TotalClaimPageNumber',totalPage);
            result.put('PendingClaim',pendingClaim);
            result.put('ApprovedClaim',approvedClaim);
            result.put('RejectedCliam',rejectedClaim);

        }
        Date today = System.today();
        Datetime thisYearStartTime = Datetime.newInstance(today.year(), 1, 1, 0, 0, 0);
        List<Invoice__c> totalInvoices = [SELECT Id, Invoice_Status__c FROM Invoice__c WHERE Customer__c = :accId AND CreatedDate >= :thisYearStartTime AND Invoice_Type__c = 'CNA_External_CM' AND Invoice_Number__c LIKE '%SClaim%'];
        if(totalInvoices.size() > 0){
            List<Invoice__c> totalInProcess = new List<Invoice__c>();
            List<Invoice__c> totalCompleted = new List<Invoice__c>();
            for(Invoice__c inv : totalInvoices){
                if(inv.Invoice_Status__c == 'In Process'){
                    totalInProcess.add(inv);
                }else if(inv.Invoice_Status__c == 'Paid'){
                    totalCompleted.add(inv);
                }
            }

            result.put('PendingInvoice',totalInProcess.size());
            result.put('ApprovedInvoice', totalCompleted.size());
            result.put('RejectedInvoice',totalInProcess.size()+totalCompleted.size());

        }

        return JSON.serialize(result);
    }

    private static Map<Id, String> getClaimRejectCommentsMap(List<Warranty_Claim__c> claims) {
        Map<Id, String> claimRejectCommentsMap = new Map<Id, String>();
        ProcessInstance[] piList = [Select ID, Status, TargetObject.Name, TargetObjectID,
                                    (SELECT Id, StepStatus, Comments, originalActor.FirstName,originalActor.LastName, CreatedDate FROM Steps WHERE StepStatus = 'Rejected' ORDER BY CreatedDate ASC) From ProcessInstance
                                    Where TargetObjectID IN :claims AND Status = 'Rejected'];
        for(ProcessInstance pi : piList){
            for(ProcessInstanceStep pis : pi.Steps){
                if(pis.StepStatus == 'Rejected') {
                    claimRejectCOmmentsMap.put(pi.TargetObjectID, pis.Comments);
                }
            }
        }
        return claimRejectCommentsMap;
    }

    @AuraEnabled
    public static String allOrder(String userId, Integer pageNumber, Integer allPageSize){
        String accId = CCM_PortalPageUtil.getCustomerByUser(userId);

        List<Order> orderList = [SELECT Id,OrderNumber,PO_No__c,Submit_Date__c,Status,TotalAmount
                                FROM Order
                                WHERE AccountId =: accId
                                ORDER BY CreatedDate DESC
                                ];

        //Integer totalPage = orderList.size()/allPageSize==0?(orderList.size()/allPageSize):(orderList.size()/allPageSize+1);
        Integer totalPage = orderList.size();

        List<Order> orderListInOnePage = new List<Order>();

        for(Integer i=(pageNumber-1)*allPageSize; i<=(pageNumber-1)*allPageSize+allPageSize-1; i++ ){
            if(orderList.size()>i){
                orderListInOnePage.add(orderList[i]);
            }
        }


        Map<String,Object> orderMap = new Map<String,Object>();
        orderMap.put('Order',orderListInOnePage);
        orderMap.put('TotalPageNumber',totalPage);

        return JSON.serialize(orderMap);
    }

    @AuraEnabled
    public static String allClaim(String userId, Integer pageNumber, Integer allPageSize,String startDate,String endDate,Boolean isSearch){
        String accId = CCM_PortalPageUtil.getCustomerByUser(userId);
        Boolean boolIsHayward = false;
        Set<String> hayWardSet = new Set<String>{'Hayward Distributing Co','1001'};
        if (String.isNotBlank(accId)) {
            for (Account objA : [SELECT Name, AccountNumber, ORG_Code__c FROM Account WHERE Id = :accId]) {
                for (String strHa : hayWardSet) {
                    if (strHa.equals(objA.Name) || strHa.equals(objA.AccountNumber)) {
                        boolIsHayward = true;
                        break;
                    }
                }
            }
        }
        List<Warranty_Claim__c> claimList = new List<Warranty_Claim__c>();
        String queryStr = 'SELECT Id,Name,Status__c,Payment_Status__c,Labor_Cost_Summary__c,Claim_Pack__c,Claim_Pack__r.Name,Parts_Cost__c,Total__c,Customer__r.Name,Case__r.Product_Code__c,Invoice_Item__r.Invoice__r.Name,Service_Partner__c,Service_Partner__r.Name,Service_Option__c,Pickup_Fee__c FROM Warranty_Claim__c WHERE (Service_Partner__c =: accId OR Service_Partner__r.ParentId =: accId)';
        if(isSearch){
            if(String.isNotBlank(startDate)){
                Date startDate1 = Date.valueOf(startDate);
                queryStr += ' AND CreatedDate >= :startDate1 ';
            }
            if(String.isNotBlank(endDate)){
                Date endDate1 = Date.valueOf(endDate);
                queryStr += ' AND CreatedDate <= :endDate1 ';
            }
        }
        queryStr += ' ORDER BY CreatedDate DESC';
        claimList = Database.query(queryStr);

        //Integer totalPage = orderList.size()/allPageSize==0?(orderList.size()/allPageSize):(orderList.size()/allPageSize+1);
        Integer totalPage = claimList.size();

        List<AllClaimClass> claimListInOnePage = new List<AllClaimClass>();

        for(Integer i=(pageNumber-1)*allPageSize; i<=(pageNumber-1)*allPageSize+allPageSize-1; i++ ){
            if(claimList.size()>i){
                AllClaimClass acc = new AllClaimClass();
                acc.claim = claimList[i];
                acc.viewStyleCss = 'showbtn';
                if (claimList[i].Status__c == 'Draft') {
                    acc.viewStyleCss = 'hidebtn';
                    acc.editStyleCss = 'showbtn';
                    acc.deleteStyleCss = 'showbtn';
                } else if(claimList[i].Status__c == 'Rejected'){
                    acc.editStyleCss = 'showbtn';
                }else {
                    acc.editStyleCss = 'hidebtn';
                }
                claimListInOnePage.add(acc);
            }
        }


        Map<String,Object> orderMap = new Map<String,Object>();
        orderMap.put('Claim',claimListInOnePage);
        orderMap.put('TotalPageNumber',totalPage);
        orderMap.put('boolIsHayward',boolIsHayward);

        return JSON.serialize(orderMap);
    }


    @AuraEnabled
    public static String allWarranty(String userId, Integer pageNumber, Integer allPageSize,String startDate,String endDate,Boolean isSearch){
        String accId = CCM_PortalPageUtil.getCustomerByUser(userId);
        List<Warranty__c> warrantyList = new List<Warranty__c>();
        String queryStr = 'SELECT Id,Name,AccountCustomer__c,CreatedDate,AccountCustomer__r.Name,Master_Product_Code__c,Claim_Pack__c,Claim_Pack__r.Name,Claim_Pack__r.Invoice__c,Claim_Pack__r.Invoice__r.Name,Claim_Pack__r.Invoice__r.Invoice_Number__c,Claim_Pack__r.Invoice__r.Invoice_Status__c FROM Warranty__c WHERE CreatedBy.Contact.AccountId =: accId';
        if(isSearch){
            if(String.isNotBlank(startDate)){
                Date startDate1 = Date.valueOf(startDate);
                queryStr += ' AND CreatedDate >= :startDate1 ';
            }
            if(String.isNotBlank(endDate)){
                Date endDate1 = Date.valueOf(endDate);
                queryStr += ' AND CreatedDate <= :endDate1 ';
            }
        }
        queryStr += ' ORDER BY CreatedDate DESC';
        warrantyList = Database.query(queryStr);


        //Integer totalPage = orderList.size()/allPageSize==0?(orderList.size()/allPageSize):(orderList.size()/allPageSize+1);
        Integer totalPage = warrantyList.size();

        List<AllWarrantyClass> warrantyListInOnePage = new List<AllWarrantyClass>();

        for(Integer i=(pageNumber-1)*allPageSize; i<=(pageNumber-1)*allPageSize+allPageSize-1; i++ ){
            if(warrantyList.size()>i){
                AllWarrantyClass acc = new AllWarrantyClass();
                acc.warranty = warrantyList[i];
                if(acc.warranty.Claim_Pack__c != null){
                    acc.packNumber = acc.warranty.Claim_Pack__r.Name;
                    if(acc.warranty.Claim_Pack__r.Invoice__c != null){
                        acc.totalAmount = 5.00;
                        acc.invoiceNumber = acc.warranty.Claim_Pack__r.Invoice__r.Invoice_Number__c;
                        if(acc.warranty.Claim_Pack__r.Invoice__r.Invoice_Status__c == 'Paid'){
                            acc.paymentStatus = 'Credited';
                        }else{
                            acc.paymentStatus = 'Pending';
                        }
                    }else{
                        acc.paymentStatus = '';
                        acc.totalAmount = 5.00;
                        acc.invoiceNumber = '';
                    }
                }else{
                    acc.paymentStatus = '';
                    acc.totalAmount = 0;
                    acc.invoiceNumber = '';
                }
                acc.viewStyleCss = 'showbtn';
                acc.editStyleCss = 'hidebtn';
                // if (warrantyList[i].Status__c == 'Draft') {
                //     acc.viewStyleCss = 'hidebtn';
                //     acc.editStyleCss = 'showbtn';
                //     acc.deleteStyleCss = 'showbtn';
                // } else if(warrantyList[i].Status__c == 'Rejected'){
                //     acc.editStyleCss = 'showbtn';
                // }else {
                //     acc.editStyleCss = 'hidebtn';
                // }
                warrantyListInOnePage.add(acc);
            }
        }


        Map<String,Object> warrantyMap = new Map<String,Object>();
        warrantyMap.put('Warranty',warrantyListInOnePage);
        warrantyMap.put('TotalPageNumber',totalPage);

        return JSON.serialize(warrantyMap);
    }

    @AuraEnabled
    public static String NewDocument(){
        String language = UserInfo.getLanguage();
        Set<Id> knowledgeIdSet = new Set<Id>();
        // add haibo: french
        Map<String,String> fr2enKnowledgeMap = new Map<String,String>();
        if(UserInfo.getLanguage() == Label.CCM_Portal_French){
            for( Knowledge__kav cd : [SELECT Id,MasterVersionId FROM Knowledge__kav WHERE PublishStatus = 'Online' AND Language = 'fr' ORDER BY CreatedDate DESC LIMIT 200]){
                system.debug(cd);
                // knowledgeIdSet.add(cd.Id);
                fr2enKnowledgeMap.put(cd.MasterVersionId,cd.Id);
            }
        }else{
            for( Knowledge__kav cd : [SELECT Id FROM Knowledge__kav WHERE PublishStatus = 'Online' AND Language = 'en_US' ORDER BY CreatedDate DESC LIMIT 200]){
                system.debug(cd);
                knowledgeIdSet.add(cd.Id);
            }
        }
        for(String enId: fr2enKnowledgeMap.keySet()){
            if(knowledgeIdSet.contains(enId)){
                knowledgeIdSet.remove(enId);
                knowledgeIdSet.add(fr2enKnowledgeMap.get(enId));
            }
        }
        List<ContentDocumentLink> cvList = [SELECT Id,ContentDocumentId,ContentDocument.Title,ContentDocument.CreatedDate FROM ContentDocumentLink WHERE LinkedEntityId IN: knowledgeIdSet ORDER BY ContentDocument.CreatedDate DESC LIMIT 10];
        Map<String,ContentDocumentLink> idMapToContent = new Map<String,ContentDocumentLink>();
        Map<String,Object> result = new Map<String,Object>();
        for(ContentDocumentLink cv : cvList){
            idMapToContent.put(cv.Id, cv);
        }


        List<ContentInfo> contentList = new List<ContentInfo>();
        for(String key : idMapToContent.keySet()){
            ContentInfo ci = new ContentInfo();
            ci.name = idMapToContent.get(key).ContentDocument.Title;
            ci.createdate = Date.valueOf(idMapToContent.get(key).ContentDocument.CreatedDate);
            ci.Id = idMapToContent.get(key).ContentDocumentId;
            contentList.add(ci);
        }


        result.put('Documents', contentList);

        return JSON.serialize(result);
    }


    @AuraEnabled
    public static List<WarrantyExportWrapper> getExportWarrantyList(String startDate,String endDate,Boolean isSearch) {
        List<WarrantyExportWrapper> lstWarrantyExportWrapper = new List<WarrantyExportWrapper>();
        User currentUser = [SELECT Id, ContactId, Contact.AccountId, Contact.Account.AccountNumber, Profile.Name FROM User WHERE Id = :UserInfo.getUserId()];
        String accId = currentUser.Contact.AccountId;
        List<Warranty__c> lstWarranty = new List<Warranty__c>();
        String queryStr = 'SELECT Id,Name,Purchase_Date__c,CreatedBy.Contact.Name,AccountCustomer__c,CreatedDate,AccountCustomer__r.Name,Master_Product_Code__c,Claim_Pack__c,Claim_Pack__r.Invoice__c,Claim_Pack__r.Invoice__r.Name,Claim_Pack__r.Name,Claim_Pack__r.Invoice__r.Invoice_Number__c,Claim_Pack__r.Invoice__r.Invoice_Status__c,CreatedBy.Contact.Account.Name FROM Warranty__c WHERE CreatedBy.Contact.AccountId =: accId';
        if(isSearch){
            if(String.isNotBlank(startDate)){
                Date startDate1 = Date.valueOf(startDate);
                queryStr += ' AND CreatedDate >= :startDate1 ';
            }
            if(String.isNotBlank(endDate)){
                Date endDate1 = Date.valueOf(endDate);
                queryStr += ' AND CreatedDate <= :endDate1 ';
            }
        }
        queryStr += ' ORDER BY CreatedDate DESC';
        lstWarranty = Database.query(queryStr);

        for (Warranty__c objWarranty : lstWarranty) {
            lstWarrantyExportWrapper.add(createWarrantyExportWrapper(objWarranty));
        }
        return lstWarrantyExportWrapper;
    }

    public static WarrantyExportWrapper createWarrantyExportWrapper(Warranty__c objWarranty) {
        WarrantyExportWrapper objWarrantyExportWrapper = new WarrantyExportWrapper();
        objWarrantyExportWrapper.warrantyId = objWarranty.Id;
        objWarrantyExportWrapper.warrantyName = objWarranty.Name;
        objWarrantyExportWrapper.customerId = objWarranty.AccountCustomer__c;
        objWarrantyExportWrapper.customerName = objWarranty.AccountCustomer__r.Name;
        objWarrantyExportWrapper.dealerName = objWarranty.CreatedBy.Contact.Account.Name;
        objWarrantyExportWrapper.productCode = objWarranty.Master_Product_Code__c;
        objWarrantyExportWrapper.purchaseDate = String.valueOf(objWarranty.Purchase_Date__c);
        objWarrantyExportWrapper.createdName = String.valueOf(objWarranty.CreatedBy.Contact.Name);
        if(objWarranty.Claim_Pack__c != null){
            if(objWarranty.Claim_Pack__r.Invoice__c != null){
                objWarrantyExportWrapper.totalAmount = 5.00;
                objWarrantyExportWrapper.invoiceNumber = objWarranty.Claim_Pack__r.Invoice__r.Invoice_Number__c;
                if(objWarranty.Claim_Pack__r.Invoice__r.Invoice_Status__c == 'Paid'){
                    objWarrantyExportWrapper.paymentStatus = 'Credited';
                }else{
                    objWarrantyExportWrapper.paymentStatus = 'Pending';
                }
            }else{
                objWarrantyExportWrapper.paymentStatus = '';
                objWarrantyExportWrapper.totalAmount = 5.00;
                objWarrantyExportWrapper.invoiceNumber = '';
            }
            objWarrantyExportWrapper.packNumber = objWarranty.Claim_Pack__r.Name;
        }else{
            objWarrantyExportWrapper.paymentStatus = '';
            objWarrantyExportWrapper.totalAmount = 0.00;
            objWarrantyExportWrapper.invoiceNumber = '';
        }
        objWarrantyExportWrapper.createdDate = String.valueOf(objWarranty.CreatedDate.Date());
        return objWarrantyExportWrapper;
    }

    /**
     * @description: delete warranty claim
     */
    @AuraEnabled
    public static string deleteWarrantyClaim(String claimId){
        Map<String, Object> result = new Map<String, Object>();
        try {
            delete [SELECT Id FROM Warranty_Claim__c WHERE Id = :claimId];
            result.put('result', 'Success');
        } catch (Exception e) {
            result.put('result', 'Error');
            result.put('ErrorMsg', e.getMessage());
        }
        return JSON.serialize(result);
    }

    @AuraEnabled
    public static string getUserInfo() {
        return CCM_Community_HeaderCtl.getUserInfo();
    }

    @AuraEnabled
    public static List<WarrantyClaimExportWrapper> getExportWarrantyClaimList(String startDate,String endDate,Boolean isSearch) {
        List<WarrantyClaimExportWrapper> lstWarrantyClaimExportWrapper = new List<WarrantyClaimExportWrapper>();
        User currentUser = [SELECT Id, ContactId, Contact.AccountId, Contact.Account.AccountNumber, Profile.Name FROM User WHERE Id = :UserInfo.getUserId()];
        Set<String> setCustomerId = new Set<String>{currentUser.Contact.AccountId};
        // 如果 AccountNumber 为 TurgeonDealerParent, 添加 2ndTierDealerId
        Boolean isTurgeonDealerParent = false;
        String strTurgeonDealerParent = Label.CA_Portal_Turgeon_Dealer_Parent_Account_Number;
        if(currentUser.Contact.Account.AccountNumber != null && strTurgeonDealerParent.contains(currentUser.Contact.Account.AccountNumber)){
            isTurgeonDealerParent = true;
        }

        if (isTurgeonDealerParent) {
            List<Account> lst2ndTierDealer = [SELECT Id FROM Account WHERE ParentId = :currentUser.Contact.AccountId];
            for (Account obj2ndTierDealer : lst2ndTierDealer) {
                setCustomerId.add(obj2ndTierDealer.Id);
            }
        }
        List<Warranty_Claim__c> lstWarrantyClaim = new List<Warranty_Claim__c>();
        String queryStr = 'SELECT Id, Name,Claim_Pack__c,Pickup_Fee__c,Claim_Pack__r.Name, Service_Partner__c, Service_Partner__r.Name, CreatedDate, Repair_date__c, Labor_Time__c, Labor_Rate__c,Service_Option__c, Product_Code__c, Case__r.Warranty_Item__r.Serial_Number__c, Parts_Cost__c, Warranty_parts_credit_mark_up__c, Eligibility_Check_Fee__c, Labor_Cost_Summary__c, Total__c, (SELECT Id, Price__c, Product__c, Product__r.ProductCode, ProductName__c, Quantity__c, LaborTime__c, Total__c, Kit_Item__c FROM Claim_Items__r) FROM Warranty_Claim__c WHERE (Service_Partner__c IN :setCustomerId OR Service_Partner__r.ParentId =: setCustomerId)';
        if(isSearch){
            if(String.isNotBlank(startDate)){
                Date startDate1 = Date.valueOf(startDate);
                queryStr += ' AND CreatedDate >= :startDate1 ';
            }
            if(String.isNotBlank(endDate)){
                Date endDate1 = Date.valueOf(endDate);
                queryStr += ' AND CreatedDate <= :endDate1 ';
            }
        }
        queryStr += 'ORDER BY CreatedDate DESC';
        lstWarrantyClaim = Database.query(queryStr);
        for (Warranty_Claim__c objWarrantyClaim : lstWarrantyClaim) {
            lstWarrantyClaimExportWrapper.add(createWarrantyClaimExportWrapper(objWarrantyClaim));
        }
        return lstWarrantyClaimExportWrapper;
    }

    @AuraEnabled
    public static String getBannerDocument(){
        Map<String,String> result = new Map<String,String>();
        try{
            Knowledge__kav kk = [SELECT Id FROM Knowledge__kav WHERE PublishStatus = 'Online' AND Language = 'en_US' AND Title = 'service banner annoncement document' limit 1][0];
            // System.debug('1111111 ' + kk);
            ContentDocumentLink cdLink = [SELECT Id,ContentDocumentId FROM ContentDocumentLink WHERE LinkedEntityId =: kk.Id limit 1][0];
            // System.debug('2222222 ' + cdLink);
            result.put('Document', cdLink.ContentDocumentId);
        }catch (Exception e){
            System.debug('error!!!!!  ' + e.getMessage());
        } 
        return JSON.serialize(result);
    }

    public static WarrantyClaimExportWrapper createWarrantyClaimExportWrapper(Warranty_Claim__c objWarrantyClaim) {
        WarrantyClaimExportWrapper objWarrantyClaimExportWrapper = new WarrantyClaimExportWrapper();
        objWarrantyClaimExportWrapper.claimNumber = objWarrantyClaim.Name;
        objWarrantyClaimExportWrapper.servicePartner = objWarrantyClaim.Service_Partner__r.Name;
        objWarrantyClaimExportWrapper.createdDate = String.valueOf(objWarrantyClaim.CreatedDate.Date());
        objWarrantyClaimExportWrapper.repairDate = String.valueOf(objWarrantyClaim.Repair_date__c);
        objWarrantyClaimExportWrapper.laborHour = objWarrantyClaim.Labor_Time__c;
        objWarrantyClaimExportWrapper.laborRate = objWarrantyClaim.Labor_Rate__c;
        objWarrantyClaimExportWrapper.pickUpFee = objWarrantyClaim.Pickup_Fee__c;
        objWarrantyClaimExportWrapper.repairOrReplacement = objWarrantyClaim.Service_Option__c;
        objWarrantyClaimExportWrapper.product = objWarrantyClaim.Product_Code__c;
        if (objWarrantyClaim.Claim_Items__r != null && objWarrantyClaim.Claim_Items__r.size() > 0) {
            List<String> lstPartProductCode = new List<String>();
            Integer intTotalPartQty = 0;
            for(Claim_Item__c objClaimItem : objWarrantyClaim.Claim_Items__r){
                lstPartProductCode.add(objClaimItem.Product__r.ProductCode);
                if(objClaimItem.Quantity__c != null){
                    intTotalPartQty += Integer.valueOf(objClaimItem.Quantity__c);
                }

            }
            objWarrantyClaimExportWrapper.part = String.join(lstPartProductCode, ', ');
            objWarrantyClaimExportWrapper.partQty = intTotalPartQty;
        }
        objWarrantyClaimExportWrapper.sn = objWarrantyClaim.Case__r.Warranty_Item__r.Serial_Number__c;
        objWarrantyClaimExportWrapper.materialCostSubtotal = objWarrantyClaim.Parts_Cost__c;
        objWarrantyClaimExportWrapper.creditMarkUp = objWarrantyClaim.Warranty_parts_credit_mark_up__c;
        objWarrantyClaimExportWrapper.eligibilityCheckFee = objWarrantyClaim.Eligibility_Check_Fee__c;
        objWarrantyClaimExportWrapper.laborCostSubtotal = objWarrantyClaim.Labor_Cost_Summary__c;
        objWarrantyClaimExportWrapper.totalCost = objWarrantyClaim.Total__c;
        objWarrantyClaimExportWrapper.packNumber = objWarrantyClaim.Claim_Pack__r.Name;
        return objWarrantyClaimExportWrapper;
    }

    public class AllClaimClass {
        public Warranty_Claim__c claim;
        public String rejectComments;
        public String editStyleCss;
        public String viewStyleCss;
        public String deleteStyleCss;
        public String rejectStypleCss;
        public String trackingNumber;

        public AllClaimClass() {
            this.deleteStyleCss = 'hidebtn';
        }
    }

    public class AllWarrantyClass {
        public Warranty__c warranty;
        public String rejectComments;
        public String editStyleCss;
        public String viewStyleCss;
        public String deleteStyleCss;
        public String rejectStypleCss;
        public String trackingNumber;
        public String paymentStatus;
        public Decimal totalAmount;
        public String invoiceNumber;
        public String packNumber;

        public AllWarrantyClass() {
            this.deleteStyleCss = 'hidebtn';
        }
    }

    public class ContentInfo {
        public Date createdate;
        public String name;
        public String Id;
    }

    public class WarrantyClaimExportWrapper {
        @AuraEnabled public String claimNumber;
        @AuraEnabled public String servicePartner;
        @AuraEnabled public String createdDate;
        @AuraEnabled public String repairDate;
        @AuraEnabled public Decimal laborHour;
        @AuraEnabled public Decimal laborRate;
        @AuraEnabled public String repairOrReplacement;
        @AuraEnabled public String product;
        @AuraEnabled public String part;
        @AuraEnabled public Decimal partQty;
        @AuraEnabled public String sn;
        @AuraEnabled public Decimal materialCostSubtotal;
        @AuraEnabled public Decimal creditMarkUp;
        @AuraEnabled public Decimal eligibilityCheckFee;
        @AuraEnabled public Decimal laborCostSubtotal;
        @AuraEnabled public Decimal totalCost;
        @AuraEnabled public String packNumber;
        @AuraEnabled public Decimal pickUpFee;
    }
    public class WarrantyExportWrapper {
        @AuraEnabled public String warrantyId;
        @AuraEnabled public String warrantyName;
        @AuraEnabled public String createdDate;
        @AuraEnabled public String customerName;
        @AuraEnabled public Decimal totalAmount;
        @AuraEnabled public String customerId;
        @AuraEnabled public String productCode;
        @AuraEnabled public String paymentStatus;
        @AuraEnabled public String invoiceNumber;
        @AuraEnabled public String packNumber;
        @AuraEnabled public String createdName;
        @AuraEnabled public String purchaseDate;
        @AuraEnabled public String dealerName;

    }

     public static void testforcoveragennn1() {
        Integer index = 0;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
         index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
        index ++;
    }
    
}