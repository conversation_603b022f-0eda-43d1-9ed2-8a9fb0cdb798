import { api, LightningElement, track, wire } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { CurrentPageReference } from 'lightning/navigation';
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import saveWarrantyReturn from '@salesforce/apex/CCM_WarrantyReturnInfoCtl.saveWarrantyReturn';
import getProducts from '@salesforce/apex/CCM_WarrantyReturnInfoCtl.getProducts';
import getCustomer from '@salesforce/apex/CCM_WarrantyReturnInfoCtl.getCustomer';
import getAddress from '@salesforce/apex/CCM_WarrantyReturnInfoCtl.getAddress';
import getProfile from '@salesforce/apex/CCM_WarrantyReturnInfoCtl.getProfile';
import getAvailabelInvoice from '@salesforce/apex/CCM_WarrantyReturnInfoCtl.getAvailabelInvoice';
import getInvoicePrice from '@salesforce/apex/CCM_WarrantyReturnInfoCtl.getInvoicePrice';
import deleteContentDocument from '@salesforce/apex/CCM_WarrantyReturnInfoCtl.deleteContentDocument';
import getWarrantyReturnClaimInfo from '@salesforce/apex/CCM_WarrantyReturnInfoCtl.getWarrantyReturnClaimInfo';
import getDIFRTV from '@salesforce/apex/CCM_WarrantyReturnInfoCtl.getDIFRTV';
import getCustomerPONumber from '@salesforce/apex/CCM_WarrantyReturnInfoCtl.getCustomerPONumber';
import momentJs from '@salesforce/resourceUrl/momentJs';
import { loadScript } from 'lightning/platformResourceLoader';

// 导入 Custom Label
import CCM_Portal_CustomerInformation from "@salesforce/label/c.CCM_Portal_CustomerInformation";
import CCM_Portal_CustomerName from "@salesforce/label/c.CCM_Portal_CustomerName";
import CCM_Portal_CustomerNumber from "@salesforce/label/c.CCM_Portal_CustomerNumber";
import CCM_Portal_StoreWarehouseLocation from "@salesforce/label/c.CCM_Portal_StoreWarehouseLocation";
import CCM_Portal_ChervonorWarehousePurchase from "@salesforce/label/c.CCM_Portal_ChervonorWarehousePurchase";
import CCM_Portal_ContactName from "@salesforce/label/c.CCM_Portal_ContactName";
import CCM_Portal_ContactPhoneNumber from "@salesforce/label/c.CCM_Portal_ContactPhoneNumber";
import CCM_Portal_CustomerReferenceNumber from "@salesforce/label/c.CCM_Portal_CustomerReferenceNumber";
import CCM_Portal_WarehouseLocation from "@salesforce/label/c.CCM_Portal_WarehouseLocation";
import CCM_Portal_ContactEmailAddress from "@salesforce/label/c.CCM_Portal_ContactEmailAddress";
import CCM_Portal_AddToList from "@salesforce/label/c.CCM_Portal_AddToList";
import CCM_Portal_Notes from "@salesforce/label/c.CCM_Portal_Notes";
import CCM_Portal_ReturnProductInformation from "@salesforce/label/c.CCM_Portal_ReturnProductInformation";
import CCM_Portal_WarrantyReturnProducts from "@salesforce/label/c.CCM_Portal_WarrantyReturnProducts";
import CCM_Portal_Line from "@salesforce/label/c.CCM_Portal_Line";
import CCM_Portal_Model from "@salesforce/label/c.CCM_Portal_Model";
import CCM_Portal_Description from "@salesforce/label/c.CCM_Portal_Description";
import CCM_Portal_Brand from "@salesforce/label/c.CCM_Portal_Brand";
import CCM_Portal_CustomerPONumber from "@salesforce/label/c.CCM_Portal_CustomerPONumber";
import CCM_Portal_Unit from "@salesforce/label/c.CCM_Portal_Unit";
import CCM_Portal_InvoicePrice from "@salesforce/label/c.CCM_Portal_InvoicePrice";
import CCM_Portal_Quantity from "@salesforce/label/c.CCM_Portal_Quantity";
import CCM_Portal_Subtotal from "@salesforce/label/c.CCM_Portal_Subtotal";
import CCM_Portal_ReturnReason from "@salesforce/label/c.CCM_Portal_ReturnReason";
import CCM_Portal_EndConsumerPurchaseDate from "@salesforce/label/c.CCM_Portal_EndConsumerPurchaseDate";
import CCM_Portal_EndConsumerReturnDate from "@salesforce/label/c.CCM_Portal_EndConsumerReturnDate";
import CCM_Portal_DIFRTV from "@salesforce/label/c.CCM_Portal_DIFRTV";
import CCM_Portal_SerialNumber from "@salesforce/label/c.CCM_Portal_SerialNumber";
import CCM_Portal_PleaseusetoseparateSerialNumbers from "@salesforce/label/c.CCM_Portal_PleaseusetoseparateSerialNumbers";
import CCM_Portal_Comments from "@salesforce/label/c.CCM_Portal_Comments";
import CCM_Portal_creditonfile from "@salesforce/label/c.CCM_Portal_creditonfile";
import CCM_Portal_TotalQuantity from "@salesforce/label/c.CCM_Portal_TotalQuantity";
import CCM_Portal_ProductAmount from "@salesforce/label/c.CCM_Portal_ProductAmount";
import CCM_Portal_ClaimedCreditMemoAmount from "@salesforce/label/c.CCM_Portal_ClaimedCreditMemoAmount";
import CCM_Portal_Freight from "@salesforce/label/c.CCM_Portal_Freight";
import CCM_Portal_GST from "@salesforce/label/c.CCM_Portal_GST";
import CCM_Portal_HST from "@salesforce/label/c.CCM_Portal_HST";
import CCM_Portal_QST from "@salesforce/label/c.CCM_Portal_QST";
import CCM_Portal_PaymentInformation from "@salesforce/label/c.CCM_Portal_PaymentInformation";
import CCM_Portal_BillingAddress from "@salesforce/label/c.CCM_Portal_BillingAddress";
import CCM_Portal_ShippingAddress from "@salesforce/label/c.CCM_Portal_ShippingAddress";
import CCM_Portal_addalternativeshippingaddress from "@salesforce/label/c.CCM_Portal_addalternativeshippingaddress";
import CCM_Portal_StoreAddress from "@salesforce/label/c.CCM_Portal_StoreAddress";
import CCM_Portal_addalternativestoreaddress from "@salesforce/label/c.CCM_Portal_addalternativestoreaddress";
import CCM_Portal_PaymentMethod from "@salesforce/label/c.CCM_Portal_PaymentMethod";
import CCM_Portal_DebitMemoNumber from "@salesforce/label/c.CCM_Portal_DebitMemoNumber";
import CCM_Portal_DebitMemoAmount from "@salesforce/label/c.CCM_Portal_DebitMemoAmount";
import CCM_Portal_Save from "@salesforce/label/c.CCM_Portal_Save";
import CCM_Portal_Submit from "@salesforce/label/c.CCM_Portal_Submit";
import CCM_Portal_CustomerNumberRequired from "@salesforce/label/c.CCM_Portal_CustomerNumberRequired";
import CCM_Portal_Pleaseselectacustomer from "@salesforce/label/c.CCM_Portal_Pleaseselectacustomer";
import CCM_Portal_ReturnProductRequired from "@salesforce/label/c.CCM_Portal_ReturnProductRequired";
import CCM_Portal_PleaseAddreturnproduct from "@salesforce/label/c.CCM_Portal_PleaseAddreturnproduct";
import CCM_Portal_BillingAddressRequired from "@salesforce/label/c.CCM_Portal_BillingAddressRequired";
import CCM_Portal_Pleaseselectabillingaddress from "@salesforce/label/c.CCM_Portal_Pleaseselectabillingaddress";
import CCM_Portal_ShippingAddressRequired from "@salesforce/label/c.CCM_Portal_ShippingAddressRequired";
import CCM_Portal_Pleaseselectashippingaddress from "@salesforce/label/c.CCM_Portal_Pleaseselectashippingaddress";
import CCM_Portal_StoreAddressRequired from "@salesforce/label/c.CCM_Portal_StoreAddressRequired";
import CCM_Portal_Pleaseselectastoreaddress from "@salesforce/label/c.CCM_Portal_Pleaseselectastoreaddress";
import CCM_Portal_Warrning from "@salesforce/label/c.CCM_Portal_Warrning";
import CCM_Portal_AddEmailToList from "@salesforce/label/c.CCM_Portal_AddEmailToList";
import CCM_Portal_Pleaseaddtheinputedemailtolist from "@salesforce/label/c.CCM_Portal_Pleaseaddtheinputedemailtolist";
import CCM_Portal_Pleaseaddatleastoneemailtolist from "@salesforce/label/c.CCM_Portal_Pleaseaddatleastoneemailtolist";
import CCM_Portal_WarrantyReturnhasbeensaved from "@salesforce/label/c.CCM_Portal_WarrantyReturnhasbeensaved";
import CCM_Portal_WarrantyReturnhasbeensubmitted from "@salesforce/label/c.CCM_Portal_WarrantyReturnhasbeensubmitted";
import CCM_Portal_DuplicateProduct from "@salesforce/label/c.CCM_Portal_DuplicateProduct";
import CCM_Portal_ThisProducthasbeenadded from "@salesforce/label/c.CCM_Portal_ThisProducthasbeenadded";
import CCM_Portal_ThereisnocustomerPOfoundforthismodel from "@salesforce/label/c.CCM_Portal_ThereisnocustomerPOfoundforthismodel";
import CCM_Portal_Loading from "@salesforce/label/c.CCM_Portal_Loading";
import CCM_Portal_Remove from "@salesforce/label/c.CCM_Portal_Remove";
import CCM_Portal_Upload from "@salesforce/label/c.CCM_Portal_Upload";
import CCM_Portal_Delete from "@salesforce/label/c.CCM_Portal_Delete";
import CCM_Portal_WarrantyReturnInstructions from "@salesforce/label/c.CCM_Portal_WarrantyReturnInstructions";
import CCM_Portal_AlternativeShippingAddressInformation from "@salesforce/label/c.CCM_Portal_AlternativeShippingAddressInformation";
import CCM_Portal_AlternativeShippingAddress from "@salesforce/label/c.CCM_Portal_AlternativeShippingAddress";
import CCM_Portal_Street from "@salesforce/label/c.CCM_Portal_Street";
import CCM_Portal_City from "@salesforce/label/c.CCM_Portal_City";
import CCM_Portal_Country from "@salesforce/label/c.CCM_Portal_Country";
import CCM_Portal_ProvinceState from "@salesforce/label/c.CCM_Portal_ProvinceState";
import CCM_Portal_PostalCode from "@salesforce/label/c.CCM_Portal_PostalCode";
import CCM_Portal_ContactPhone from "@salesforce/label/c.CCM_Portal_ContactPhone";
import CCM_Portal_ContactEmail from "@salesforce/label/c.CCM_Portal_ContactEmail";
import CCM_Portal_Cancel from "@salesforce/label/c.CCM_Portal_Cancel";
import CCM_Portal_File from "@salesforce/label/c.CCM_Portal_File";
import CCM_Portal_Close from "@salesforce/label/c.CCM_Portal_Close";
import CCM_Portal_Error from "@salesforce/label/c.CCM_Portal_Error";
import CCM_Portal_ChervonorWarehousePurchaseRequired from "@salesforce/label/c.CCM_Portal_ChervonorWarehousePurchaseRequired";
import CCM_Portal_Pleaseselectanoption from "@salesforce/label/c.CCM_Portal_Pleaseselectanoption";
import CCM_Portal_ContactNameRequired from "@salesforce/label/c.CCM_Portal_ContactNameRequired";
import CCM_Portal_Pleaseinputcontactname from "@salesforce/label/c.CCM_Portal_Pleaseinputcontactname";
import CCM_Portal_MODELRequired from "@salesforce/label/c.CCM_Portal_MODELRequired";
import CCM_Portal_Pleaseenteramodel from "@salesforce/label/c.CCM_Portal_Pleaseenteramodel";
import CCM_Portal_CUSTOMERPONumberRequired from "@salesforce/label/c.CCM_Portal_CUSTOMERPONumberRequired";
import CCM_Portal_Pleaseenteracustomerponumber from "@salesforce/label/c.CCM_Portal_Pleaseenteracustomerponumber";
import CCM_Portal_QUANTITYRequired from "@salesforce/label/c.CCM_Portal_QUANTITYRequired";
import CCM_Portal_Pleaseinputaquantity from "@salesforce/label/c.CCM_Portal_Pleaseinputaquantity";
import CCM_Portal_RETURNREASONRequired from "@salesforce/label/c.CCM_Portal_RETURNREASONRequired";
import CCM_Portal_Pleaseinputareturnreason from "@salesforce/label/c.CCM_Portal_Pleaseinputareturnreason";
import CCM_Portal_PaymentMethodRequired from "@salesforce/label/c.CCM_Portal_PaymentMethodRequired";
import CCM_Portal_DebitmemoTips from "@salesforce/label/c.CCM_Portal_DebitmemoTips";
import CCM_Portal_Success from "@salesforce/label/c.CCM_Portal_Success";
import CCM_Portal_StoreWarehouseTips from "@salesforce/label/c.CCM_Portal_StoreWarehouseTips";
import CCM_Portal_returnperiodTips from "@salesforce/label/c.CCM_Portal_returnperiodTips";
import CCM_Portal_Instructions1 from "@salesforce/label/c.CCM_Portal_Instructions1";
import CCM_Portal_Instructions2 from "@salesforce/label/c.CCM_Portal_Instructions2";
import CCM_Portal_Instructions3 from "@salesforce/label/c.CCM_Portal_Instructions3";
import CCM_Portal_Instructions4 from "@salesforce/label/c.CCM_Portal_Instructions4";
import CCM_Portal_Instructions5 from "@salesforce/label/c.CCM_Portal_Instructions5";



export default class CcmWarrantyReturnClaimRequest extends NavigationMixin(LightningElement) {

    // 定义 label
    @track label = {
        CCM_Portal_returnperiodTips,
        CCM_Portal_StoreWarehouseTips,
        CCM_Portal_Success,
        CCM_Portal_DebitmemoTips,
        CCM_Portal_Pleaseinputareturnreason,
        CCM_Portal_RETURNREASONRequired,
        CCM_Portal_Pleaseinputaquantity,
        CCM_Portal_QUANTITYRequired,
        CCM_Portal_Pleaseenteracustomerponumber,
        CCM_Portal_CUSTOMERPONumberRequired,
        CCM_Portal_Pleaseenteramodel,
        CCM_Portal_MODELRequired,
        CCM_Portal_PaymentMethodRequired,
        CCM_Portal_Pleaseinputcontactname,
        CCM_Portal_ContactNameRequired,
        CCM_Portal_Pleaseselectanoption,
        CCM_Portal_ChervonorWarehousePurchaseRequired,
        CCM_Portal_Error,
        CCM_Portal_Close,
        CCM_Portal_File,
        CCM_Portal_Cancel,
        CCM_Portal_ContactEmail,
        CCM_Portal_ContactPhone,
        CCM_Portal_PostalCode,
        CCM_Portal_ProvinceState,
        CCM_Portal_Country,
        CCM_Portal_City,
        CCM_Portal_Street,
        CCM_Portal_AlternativeShippingAddress,
        CCM_Portal_AlternativeShippingAddressInformation,
        CCM_Portal_WarrantyReturnInstructions,
        CCM_Portal_Delete,
        CCM_Portal_Upload,
        CCM_Portal_Remove,
        CCM_Portal_Loading,
        CCM_Portal_CustomerInformation,
        CCM_Portal_CustomerName,
        CCM_Portal_CustomerNumber,
        CCM_Portal_StoreWarehouseLocation,
        CCM_Portal_ChervonorWarehousePurchase,
        CCM_Portal_ContactName,
        CCM_Portal_ContactPhoneNumber,
        CCM_Portal_CustomerReferenceNumber,
        CCM_Portal_WarehouseLocation,
        CCM_Portal_ContactEmailAddress,
        CCM_Portal_AddToList,
        CCM_Portal_Notes,
        CCM_Portal_ReturnProductInformation,
        CCM_Portal_WarrantyReturnProducts,
        CCM_Portal_Line,
        CCM_Portal_Model,
        CCM_Portal_Description,
        CCM_Portal_Brand,
        CCM_Portal_CustomerPONumber,
        CCM_Portal_Unit,
        CCM_Portal_InvoicePrice,
        CCM_Portal_Quantity,
        CCM_Portal_Subtotal,
        CCM_Portal_ReturnReason,
        CCM_Portal_EndConsumerPurchaseDate,
        CCM_Portal_EndConsumerReturnDate,
        CCM_Portal_DIFRTV,
        CCM_Portal_SerialNumber,
        CCM_Portal_PleaseusetoseparateSerialNumbers,
        CCM_Portal_Comments,
        CCM_Portal_creditonfile,
        CCM_Portal_TotalQuantity,
        CCM_Portal_ProductAmount,
        CCM_Portal_ClaimedCreditMemoAmount,
        CCM_Portal_Freight,
        CCM_Portal_GST,
        CCM_Portal_HST,
        CCM_Portal_QST,
        CCM_Portal_PaymentInformation,
        CCM_Portal_BillingAddress,
        CCM_Portal_ShippingAddress,
        CCM_Portal_addalternativeshippingaddress,
        CCM_Portal_StoreAddress,
        CCM_Portal_addalternativestoreaddress,
        CCM_Portal_PaymentMethod,
        CCM_Portal_DebitMemoNumber,
        CCM_Portal_DebitMemoAmount,
        CCM_Portal_Save,
        CCM_Portal_Submit,
        CCM_Portal_CustomerNumberRequired,
        CCM_Portal_Pleaseselectacustomer,
        CCM_Portal_ReturnProductRequired,
        CCM_Portal_PleaseAddreturnproduct,
        CCM_Portal_BillingAddressRequired,
        CCM_Portal_Pleaseselectabillingaddress,
        CCM_Portal_ShippingAddressRequired,
        CCM_Portal_Pleaseselectashippingaddress,
        CCM_Portal_StoreAddressRequired,
        CCM_Portal_Pleaseselectastoreaddress,
        CCM_Portal_Warrning,
        CCM_Portal_AddEmailToList,
        CCM_Portal_Pleaseaddtheinputedemailtolist,
        CCM_Portal_Pleaseaddatleastoneemailtolist,
        CCM_Portal_WarrantyReturnhasbeensaved,
        CCM_Portal_WarrantyReturnhasbeensubmitted,
        CCM_Portal_DuplicateProduct,
        CCM_Portal_ThisProducthasbeenadded,
        CCM_Portal_ThereisnocustomerPOfoundforthismodel,
        CCM_Portal_Instructions1,
        CCM_Portal_Instructions2,
        CCM_Portal_Instructions3,
        CCM_Portal_Instructions4,
        CCM_Portal_Instructions5
    };
    @api
    warrantyReturnId = "";
    isInternal = true;
    @track
    userProfile = '';

    unit = 'EA';

    customerName = '';
    @api
    customerId = '';
    recordId = '';
    @track customerSource = [];
    @track autoAccountNumber = [];
    @track autoCustomerName = [];
    @track autoCustomerId = [];
    customerChanged = false;
    accountNumber = '';
    policyId = '';
    isAlternativeAddress = false;

    storeLocation = '';
    warehouseLocation = '';
    showSpinner = false;

    DIFRTVOptions = [{ label: 'DIF', value: 'DIF' }, { label: 'RTV', value: 'RTV' }];
    returnReasonOptions = [{ label: 'Defective', value: 'Defective' }, { label: 'Customer Satisfaction', value: 'Customer Satisfaction' }];

    @track
    chervonOrWarehousePurchases = [
        { label: 'Chervon', value: 'Chervon' },
        { label: 'Warehouse', value: 'Warehouse' }
    ];
    chervonOrWarehousePurchase = '';

    contactName = '';
    contactEmailAddresses = [];
    contactEmailAddress = '';
    contactPhoneNumber = '';
    customerReferenceNumber = '';
    notes = '';
    isDirectDealer = false;

    @track
    items = [
        { lineNo: 1, index: 0, returnReason: 'Defective' }
    ];

    uploadType = '';

    @track
    productSource = [];
    productCode = '';

    @track
    deleteItemIds = [];

    @track
    selectedBillingAddress = {};
    @track
    billingAddressSource = [];

    @track
    selectedDropshipShippingAddress = {};
    @track
    dropshipShippingAddressSource = [];

    @track
    selectedDropshipBillingAddress = {};
    @track
    dropshipBillingAddressSource = [];

    @track
    selectedShippingAddress = {};
    @track
    shippingAddressSource = [];
    @track
    alternativeShipping = [];

    @track
    selectedStoreAddress = {};
    @track
    storeAddressSource = [];
    @track
    alternativeStore = [];

    invoicePrice = '';

    paymentMethod = '';
    @track
    paymentMethods = [
        { label: 'Credit Memo', value: 'Credit Memo' },
        { label: 'Deduction', value: 'Deduction' },
        { label: 'Credit Upfront Based On Warranty Return Policy', value: 'Credit Upfront Based On Warranty Return Policy' }
    ];

    @track
    DebitMemos = [];
    DebitMemo = '';
    debitMemoAmount = '';
    DebitMemoId = '';

    showAlternativeShipping = false;
    showAlternativeStore = false;
    showUploadModal = false;

    showSubmit = true;

    SKILSAWReturnPeriod = 180;
    SKILReturnPeriod = 'N/A';
    EGOReturnPeriod = 'N/A';
    FLEXReturnPeriod = 'N/A';

    @track
    selectedItemUploadedFiles = [];

    // 如果是warehouse，则显示出store address
    isWarehouse = false;

    // 如果是Deduction，则显示出debit memo
    isDeduction = false;

    itemFileUploadLineNo = 1;

    @api isShippingAddressRequired = false;

    /**
     * display "Submit" when create
    */
    get saveButtonLabel() {
        return "Submit";
    }
    // get batchButtonLabel() {
    //     return "Batch Operation";
    // }

    /**
     *
     * get url param by the page type
     */
    @wire(CurrentPageReference)
    setCurrentPageReference(currentPageReference) {
        // record page
        if (currentPageReference.type === "standard__recordPage" && currentPageReference.attributes) {
            const _attributes = currentPageReference.attributes;
            if (_attributes.recordId) {
                this.recordId = _attributes.recordId;
            }
            if (currentPageReference.state && currentPageReference.state.c__mode && currentPageReference.state.c__mode === "view") {
                this.mode = "view";
            }
        }
        // community page
        if (currentPageReference.type === "comm__namedPage" && currentPageReference.state) {
            const _state = currentPageReference.state;
            if (_state.c__warrantyreturnid) {
                this.warrantyReturnId = _state.c__warrantyreturnid;
            }
            if (_state.c__mode === "view") {
                this.mode = "view";
            }
        }
        // APP page in sf
        if (currentPageReference.type === "standard__navItemPage" && currentPageReference.state) {
            const _state = currentPageReference.state;
            if (_state.c__warrantyreturnid) {
                this.warrantyReturnId = _state.c__warrantyreturnid;
            }
            if (_state.c__mode === "view") {
                this.mode = "view";
            }
        }
        // direct from detail edit
        if (currentPageReference.type === "standard__directCmpReference") {
            const attributes = currentPageReference.attributes.attributes;
            this.warrantyReturnId = attributes["warrantyReturnId"];
            // this.showSubmit = false;
        }
    }

    get showContactEmails() {
        return this.contactEmailAddresses.length > 0;
    }

    /**
     * add email list
     */
    handleClickEmailAddToList() {
        if (this.template.querySelector('.contactEmailAddress').value && this.template.querySelector('.contactEmailAddress').value.trim() !== '' && this.template.querySelector('.contactEmailAddress').reportValidity()) {
            this.contactEmailAddresses.push(this.contactEmailAddress);
            this.contactEmailAddress = '';
        }
    }

    /**
     * delete email list
     */
    deleteEmailByIndex(event) {
        let _emails = JSON.parse(JSON.stringify(this.contactEmailAddresses));
        let _index = event.currentTarget.dataset.index;
        _emails.splice(_index, 1);
        this.contactEmailAddresses = _emails;
    }

    // handleClickBatchButton() {
    //     var url = window.location.origin + '/lightning/n/Warranty_Return_Batch_Operation';
    //     window.open(url,'_self');
    // }

    // 页面初始化
    connectedCallback() {
        getProfile().then(result => {
            this.userProfile = result.userProfile;
            if (this.userProfile == 'Portal User') {
                this.isInternal = false;
                this.isDirectDealer = result.customerInfo.customerType === 'Direct Dealer' ? false : true;
                this.autoAccountNumber = result.accountNumber;
                this.autoCustomerName = result.name;
                this.autoCustomerId = result.id;
                this.customerId = this.autoCustomerId;
                this.policyId = result.customerInfo.policyId;
                this.paymentMethods = result.customerInfo.policyPaymentMethod;
                this.policyRecordType = result.customerInfo.policyRecordType;
                this.policyDIFRTVModel = result.customerInfo.policyDIFRTVModel;
                this.storeReturnPeriod = result.customerInfo.returnPeriod;
                this.models = result.customerInfo.models;

                // if portal user, paymentMethods can only select "credit method"
                this.paymentMethods = [
                    { label: 'Credit Memo', value: 'Credit Memo' },
                ];
                this.paymentMethod = this.paymentMethods[0].value;

                getProducts({ customerId: this.customerId, keyWord: '' }).then((result) => {
                    if (result != null && result != undefined && result != '') {

                        if (result.isSuccess) {
                            let _product = [];
                            _product = result.returnData.map(item => {
                                return {
                                    productCode: item.productCode,
                                    productDescription: item.description,
                                    brand: item.brand,
                                    productId: item.productId,
                                };
                            });
                            this.productSource = _product;
                        }
                    }
                }, error => {
                    this.showSpinner = false;
                    this.showMessage(this.label.CCM_Portal_Error, error.body.message, 'error');
                })


                //带出地址信息
                getAddress({ customerId: this.customerId }).then(result => {
                    if (result) {
                        if (result.isSuccess) {
                            if (result.returnData && result.returnData.billing) {

                                this.billingAddressSource = result.returnData.billing;
                                this.billingAddressSource.forEach(item => {
                                    let _str = '';
                                    _str += item.country ? item.country + '  ' : '';
                                    _str += item.state ? item.state + '  ' : '';
                                    _str += item.country ? item.city + '  ' : '';
                                    _str += item.address1 ? item.address1 + '  ' : '';
                                    _str += item.address2 ? item.address2 : '';
                                    item.addressStr = _str.trim();
                                    item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                                })
                            }
                            if (result.returnData && result.returnData.shipping) {
                                this.shippingAddressSource = result.returnData.shipping;
                                this.shippingAddressSource.forEach(item => {
                                    let _str = '';
                                    _str += item.country ? item.country + '  ' : '';
                                    _str += item.state ? item.state + '  ' : '';
                                    _str += item.country ? item.city + '  ' : '';
                                    _str += item.address1 ? item.address1 + '  ' : '';
                                    _str += item.address2 ? item.address2 : '';
                                    item.addressStr = _str.trim();
                                    item.street = _str.trim();
                                    item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                                })
                            }
                            if (result.returnData && result.returnData.dropshipShipping) {
                                // this.isDirectDealer = result.returnData.dropshipShipping.length == 0 ? false : true;
                                this.dropshipShippingAddressSource = result.returnData.dropshipShipping;
                                this.dropshipShippingAddressSource.forEach(item => {
                                    let _str = '';
                                    _str += item.country ? item.country + '  ' : '';
                                    _str += item.state ? item.state + '  ' : '';
                                    _str += item.country ? item.city + '  ' : '';
                                    _str += item.address1 ? item.address1 + '  ' : '';
                                    _str += item.address2 ? item.address2 : '';
                                    item.addressStr = _str.trim();
                                    item.street = _str.trim();
                                    item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                                })
                            }
                            if (result.returnData && result.returnData.dropshipBilling) {
                                this.dropshipBillingAddressSource = result.returnData.dropshipBilling;
                                this.dropshipBillingAddressSource.forEach(item => {
                                    let _str = '';
                                    _str += item.country ? item.country + '  ' : '';
                                    _str += item.state ? item.state + '  ' : '';
                                    _str += item.country ? item.city + '  ' : '';
                                    _str += item.address1 ? item.address1 + '  ' : '';
                                    _str += item.address2 ? item.address2 : '';
                                    item.addressStr = _str.trim();
                                    item.street = _str.trim();
                                    item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                                })
                            }

                            if (result.returnData && result.returnData.dropship) {
                                this.storeAddressSource = result.returnData.dropship;
                                this.storeAddressSource.forEach(item => {
                                    let _str = '';
                                    _str += item.country ? item.country + '  ' : '';
                                    _str += item.state ? item.state + '  ' : '';
                                    _str += item.country ? item.city + '  ' : '';
                                    _str += item.address1 ? item.address1 + '  ' : '';
                                    _str += item.address2 ? item.address2 : '';
                                    item.addressStr = _str.trim();
                                    item.street = _str.trim();
                                    item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                                })
                            }
                        } else {
                            this.showMessage(this.label.CCM_Portal_Error, result.errorMsg, 'error');
                        }
                    }
                });

                //带出发票信息
                getAvailabelInvoice({ customerId: this.customerId }).then((result) => {
                    if (result != null && result != undefined && result != '') {
                        if (result) {
                            let _invoice = [];
                            _invoice = result.map(item => {
                                return {
                                    // invoiceId: item.Id,
                                    DebitMemoId: item.invoiceId,
                                    DebitMemo: item.invoiceName,
                                    debitMemoAmount: item.debitMemoAmount,
                                }
                            });
                            this.DebitMemos = _invoice;
                        }
                    }
                }, error => {
                    this.showSpinner = false;
                    this.showMessage(this.label.CCM_Portal_Error, error.body.message, 'error');
                })

            } else if (this.userProfile == 'Inside Sales') {
                this.isInternal = true;
            }
        })

        if (!this.connected) {
            loadScript(this, momentJs + '/moment.min.js').then(() => {
            })
            this.connected = true;
            this.showSpinner = true;

            // recordId 从List页面传到后台，再从后台拿到这里调用getWarrantyReturnClaimInfo。
            if (!this.recordId && this.warrantyReturnId) {

                //通过list page返回的warranty return claim id查数据
                getWarrantyReturnClaimInfo({ warrantyReturnClaimId: this.warrantyReturnId }).then(result => {
                    if (result.isSuccess) {
                        // handle data
                        this.handleWarrantyReturnClaimInfo(result);
                    } else {
                        this.showMessage(this.label.CCM_Portal_Error, result.errorMsg, 'error');
                    }
                })
                this.showSpinner = false;
            }
        }

        this.showSpinner = false;
    }

    renderedCallback() {
    }

    // 处理返回数据
    handleWarrantyReturnClaimInfo(result) {
        this.recordId = this.warrantyReturnId;
        this.customerId = result.returnData.customerId;
        this.customerName = result.returnData.customerName;
        this.accountNumber = result.returnData.accountNumber;

        if (result.returnData.storeLocation != null) {
            this.selectedDropshipShippingAddress = result.returnData.storeLocation;
        }
        if (result.returnData.warehouseLocation != null) {
            this.selectedDropshipBillingAddress = result.returnData.warehouseLocation;
        }
        this.contactName = result.returnData.contactName;
        // this.contactEmailAddress = result.returnData.contactEmailAddress;
        if (result.returnData.contactEmailAddress && result.returnData.contactEmailAddress.length > 0) {
            this.contactEmailAddresses = result.returnData.contactEmailAddress.split(';');
        }
        this.contactPhoneNumber = result.returnData.contactPhoneNumber;
        this.customerReferenceNumber = result.returnData.customerReferenceNumber;
        this.notes = result.returnData.notes;
        this.selectedStoreAddress.addressId = result.returnData.storeAddressId;
        this.chervonOrWarehousePurchase = result.returnData.chervonOrWarehousePurchase;
        this.paymentMethod = result.returnData.paymentMethod;


        // debit memo info
        if (result.returnData.debitMemos != null && result.returnData.paymentMethod === 'Deduction') {
            this.isDeduction = true;
            this.DebitMemos = result.returnData.debitMemos;
            this.DebitMemoId = result.returnData.debitMemos.invoiceId;
            this.DebitMemo = result.returnData.debitMemos.invoiceName;
            this.debitMemoAmount = result.returnData.debitMemos.debitMemoAmount;
        }
        // this.DebitMemoId = '';

        // shipping address
        this.isAlternativeAddress = result.returnData.isAlternativeAddress;
        if (this.isAlternativeAddress) {
            this.isShippingAddressRequired = false;
        } else {
            this.isShippingAddressRequired = true;
        }

        if (this.isAlternativeAddress) {
            this.alternativeShipping.street = result.returnData.shippingAddress.address1;
            this.alternativeShipping.city = result.returnData.shippingAddress.city;
            this.alternativeShipping.state = result.returnData.shippingAddress.province;
            this.alternativeShipping.country = result.returnData.shippingAddress.country;
            this.alternativeShipping.postalCode = result.returnData.shippingAddress.postalCode;
            this.alternativeShipping.contactEmail = result.returnData.shippingAddress.contactEmail;
            this.alternativeShipping.contactName = result.returnData.shippingAddress.contactName;
            this.alternativeShipping.contactPhone = result.returnData.shippingAddress.contactPhone;
        }

        // store address
        this.isAlternativeStoreAddress = result.returnData.isAlternativeStoreAddress;

        if (this.isAlternativeStoreAddress) {
            this.alternativeStore.street = result.returnData.storeAddress.address1;
            this.alternativeStore.city = result.returnData.storeAddress.city;
            this.alternativeStore.state = result.returnData.storeAddress.province;
            this.alternativeStore.country = result.returnData.storeAddress.country;
            this.alternativeStore.postalCode = result.returnData.storeAddress.postalCode;
            this.alternativeStore.contactEmail = result.returnData.storeAddress.contactEmail;
            this.alternativeStore.contactName = result.returnData.storeAddress.contactName;
            this.alternativeStore.contactPhone = result.returnData.storeAddress.contactPhone;
        }

        if (result.returnData.items && result.returnData.items.length > 0) {
            this.items = result.returnData.items;
            this.items.forEach((item, index) => {
                item.lineNo = index + 1;
                if (item.returnReason === undefined || item.returnReason === '') {
                    item.returnReason = 'Defective';
                }
            })
        }

        if (result.returnData.billingAddress != null) {
            this.selectedBillingAddress = result.returnData.billingAddress;
            this.selectedBillingAddress.street = result.returnData.billingAddress.address1 ? result.returnData.billingAddress.address1 + ' ' : '' + result.returnData.billingAddress.address2 ? result.returnData.billingAddress.address2 : '';
        }

        // shipping address
        if (result.returnData.shippingAddress != null) {
            this.selectedShippingAddress = result.returnData.shippingAddress;
            this.selectedShippingAddress.street = result.returnData.shippingAddress.address1 ? result.returnData.shippingAddress.address1 + ' ' : '' + result.returnData.shippingAddress.address2 ? result.returnData.shippingAddress.address2 : '';
            this.selectedShippingAddress.state = this.selectedShippingAddress.state ? this.selectedShippingAddress.state : this.selectedShippingAddress.province;
        }

        // store address
        if (result.returnData.storeAddress != null && result.returnData.chervonOrWarehousePurchase === 'Warehouse') {
            this.isWarehouse = true;
            this.selectedStoreAddress = result.returnData.storeAddress;
            this.selectedStoreAddress.street = result.returnData.storeAddress.address1 ? result.returnData.storeAddress.address1 + ' ' : '' + result.returnData.storeAddress.address2 ? result.returnData.storeAddress.address2 : '';
            this.selectedStoreAddress.state = this.selectedStoreAddress.state ? this.selectedStoreAddress.state : this.selectedStoreAddress.province;
        }

        // 带出产品信息
        getProducts({ customerId: this.customerId, keyWord: '' }).then((result) => {
            if (result != null && result != undefined && result != '') {

                if (result.isSuccess) {
                    let _product = [];
                    _product = result.returnData.map(item => {
                        return {
                            productCode: item.productCode,
                            productDescription: item.description,
                            brand: item.brand,
                            productId: item.productId,
                        };
                    });
                    this.productSource = _product;
                }
            }
        }, error => {
            this.showSpinner = false;
            this.showMessage(this.label.CCM_Portal_Error, error.body.message, 'error');
        })

        //带出地址信息
        getAddress({ customerId: this.customerId }).then(result => {
            if (result) {
                if (result.isSuccess) {
                    if (result.returnData && result.returnData.billing) {

                        this.billingAddressSource = result.returnData.billing;
                        this.billingAddressSource.forEach(item => {
                            let _str = '';
                            _str += item.country ? item.country + '  ' : '';
                            _str += item.state ? item.state + '  ' : '';
                            _str += item.country ? item.city + '  ' : '';
                            _str += item.address1 ? item.address1 + '  ' : '';
                            _str += item.address2 ? item.address2 : '';
                            item.addressStr = _str.trim();
                            item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                        })
                    }
                    if (result.returnData && result.returnData.shipping) {
                        this.shippingAddressSource = result.returnData.shipping;
                        this.shippingAddressSource.forEach(item => {
                            let _str = '';
                            _str += item.country ? item.country + '  ' : '';
                            _str += item.state ? item.state + '  ' : '';
                            _str += item.country ? item.city + '  ' : '';
                            _str += item.address1 ? item.address1 + '  ' : '';
                            _str += item.address2 ? item.address2 : '';
                            item.addressStr = _str.trim();
                            item.street = _str.trim();
                            item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                        })
                    }
                    if (result.returnData && result.returnData.dropshipShipping) {
                        this.dropshipShippingAddressSource = result.returnData.dropshipShipping;
                        this.dropshipShippingAddressSource.forEach(item => {
                            let _str = '';
                            _str += item.country ? item.country + '  ' : '';
                            _str += item.state ? item.state + '  ' : '';
                            _str += item.country ? item.city + '  ' : '';
                            _str += item.address1 ? item.address1 + '  ' : '';
                            _str += item.address2 ? item.address2 : '';
                            item.addressStr = _str.trim();
                            item.street = _str.trim();
                            item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                        })
                    }
                    if (result.returnData && result.returnData.dropshipBilling) {
                        this.dropshipBillingAddressSource = result.returnData.dropshipBilling;
                        this.dropshipBillingAddressSource.forEach(item => {
                            let _str = '';
                            _str += item.country ? item.country + '  ' : '';
                            _str += item.state ? item.state + '  ' : '';
                            _str += item.country ? item.city + '  ' : '';
                            _str += item.address1 ? item.address1 + '  ' : '';
                            _str += item.address2 ? item.address2 : '';
                            item.addressStr = _str.trim();
                            item.street = _str.trim();
                            item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                        })
                    }

                    if (result.returnData && result.returnData.dropship) {
                        this.storeAddressSource = result.returnData.dropship;
                        this.storeAddressSource.forEach(item => {
                            let _str = '';
                            _str += item.country ? item.country + '  ' : '';
                            _str += item.state ? item.state + '  ' : '';
                            _str += item.country ? item.city + '  ' : '';
                            _str += item.address1 ? item.address1 + '  ' : '';
                            _str += item.address2 ? item.address2 : '';
                            item.addressStr = _str.trim();
                            item.street = _str.trim();
                            item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                        })
                    }
                } else {
                    this.showMessage(this.label.CCM_Portal_Error, result.errorMsg, 'error');
                }
            }
        });

        //带出发票信息
        getAvailabelInvoice({ customerId: this.customerId }).then((result) => {

            if (result) {
                let _invoice = [];
                _invoice = result.map(item => {
                    return {
                        // invoiceId: item.Id,
                        DebitMemoId: item.invoiceId,
                        DebitMemo: item.invoiceName,
                        debitMemoAmount: item.debitMemoAmount,
                    };

                });
                this.DebitMemos = _invoice;
                // this.template.querySelector('.autoComplete').setSource(this.DebitMemos);
            }
        }, error => {
            this.showMessage(this.label.CCM_Portal_Error, error.body.message, 'error');
        })

    }

    /**
    * validate required, number min max
    */
    validateBeforeSave() {
        let _validate = true;
        this.template.querySelectorAll('lightning-combobox').forEach((item) => {
            if (!item.reportValidity()) {
                _validate = false;
            }
        })
        this.template.querySelectorAll('lightning-input').forEach((item) => {
            if (!item.reportValidity()) {
                _validate = false;
            }
        })
        this.template.querySelectorAll('c-ccm-autocomplete').forEach((item) => {
            if (!item.reportValidity()) {
                _validate = false;
            }
        })
        if (this.isDirectDealer && (this.chervonOrWarehousePurchase == null || this.chervonOrWarehousePurchase == '')) {
            this.showMessage(this.label.CCM_Portal_ChervonorWarehousePurchaseRequired, this.label.CCM_Portal_Pleaseselectanoption, 'warning');
            _validate = false;
        }

        if (!this.contactName) {
            this.showMessage(this.label.CCM_Portal_ContactNameRequired, this.label.CCM_Portal_Pleaseinputcontactname, 'warning');
            _validate = false;
        }

        if (this.AccountNumber) {
            this.showMessage(this.label.CCM_Portal_CustomerNumberRequired, this.label.CCM_Portal_Pleaseselectacustomer, 'warning');
            _validate = false;
        }
        if (this.items.length === 0) {
            this.showMessage(this.label.CCM_Portal_ReturnProductRequired, this.label.CCM_Portal_PleaseAddreturnproduct, 'warning');
            _validate = false;
        }
        if ((!this.selectedBillingAddress || JSON.stringify(this.selectedBillingAddress) === "{}")) {
            this.showMessage(this.label.CCM_Portal_BillingAddressRequired, this.label.CCM_Portal_Pleaseselectabillingaddress, 'warning');
            _validate = false;
        }
        if ((!this.selectedShippingAddress || JSON.stringify(this.selectedShippingAddress) === "{}")) {
            this.showMessage(this.label.CCM_Portal_ShippingAddressRequired, this.label.CCM_Portal_Pleaseselectashippingaddress, 'warning');
            _validate = false;
        }
        if (this.isWarehouse === true && (!this.selectedStoreAddress || JSON.stringify(this.selectedStoreAddress) === "{}")) {
            this.showMessage(this.label.CCM_Portal_StoreAddressRequired, this.label.CCM_Portal_Pleaseselectastoreaddress, 'warning');
            _validate = false;
        }
        if (!this.paymentMethod) {
            this.showMessage(this.label.CCM_Portal_PaymentMethodRequired, this.label.CCM_Portal_Pleaseselectanoption, 'warning');
            _validate = false;
        }
        // if (_validate) {
        this.items.forEach((item) => {
            //Yanko
            // if (!item.endConsumerPurchaseDate || !item.endConsumerReturnDate) {
            //     let _confirm = confirm("If End Consumer Purchase Date and End Consumer Return Date are not filled in, there will be an additional approval process. Are you sure to countinue?");
            //     if (!_confirm) {
            //         _validate = false;
            //     }
            // }
            // if (item.DIFRTV == 'DIF' && !item.serialNumber) {
            // this.showMessage('Serial Number Required', "Please enter the correct Serial Number!", 'warning');
            // _validate = false;
            // }

            if(!item.DIFRTV) {
                this.showMessage('DIF/RTV Required', 'Please check DIF/RTV is inputed', 'warning');
            }

            if (!item.productCode) {
                this.showMessage(this.label.CCM_Portal_MODELRequired, this.label.CCM_Portal_Pleaseenteramodel, 'warning');
                _validate = false;
            }
            if (!item.customerPONumber) {
                this.showMessage(this.label.CCM_Portal_CUSTOMERPONumberRequired, this.label.CCM_Portal_Pleaseenteracustomerponumber, 'warning');
                _validate = false;
            }
            if (!item.quantity) {
                this.showMessage(this.label.CCM_Portal_QUANTITYRequired, this.label.CCM_Portal_Pleaseinputaquantity, 'warning');
                _validate = false;
            }
            if (!item.returnReason) {
                this.showMessage(this.label.CCM_Portal_RETURNREASONRequired, this.label.CCM_Portal_Pleaseselectanoption, 'warning');
                _validate = false;
            }
            if (!item.returnReasonRemark) {
                this.showMessage(this.label.CCM_Portal_RETURNREASONRequired, this.label.CCM_Portal_Pleaseinputareturnreason, 'warning');
                _validate = false;
            }
        })
        // }

        if (_validate && this.isDeduction && this.debitMemoAmount != this.claimedCreditMemoAmount) {
            this.showMessage(this.label.CCM_Portal_Warrning, this.label.CCM_Portal_DebitmemoTips, 'warrning');
            _validate = false;
        }

        if (this.contactEmailAddress !== '') {
            this.showMessage(this.label.CCM_Portal_AddEmailToList, this.label.CCM_Portal_Pleaseaddtheinputedemailtolist, 'warning');
            _validate = false;
        }
        if (this.contactEmailAddress === '' && this.contactEmailAddresses.length === 0) {
            this.showMessage(this.label.CCM_Portal_AddEmailToList, this.label.CCM_Portal_Pleaseaddatleastoneemailtolist, 'warning');
            _validate = false;
        }

        return _validate;
    }

    /**
    * validate inputs, save warranty return
    */
    handleClickSave(event) {
        let _validate = this.validateBeforeSave();
        if (_validate) {

            if (!this.isWarehouse) {
                this.selectedStoreAddress = {};
                this.selectedDropshipBillingAddress = {};
            }

            let _json = {
                "customerName": this.customerName,
                "customerId": this.customerId,
                "accountNumber": this.accountNumber,
                "warehouseLocationId": this.selectedDropshipBillingAddress.addressId,
                "storeLocationId": this.selectedDropshipShippingAddress.addressId,

                "chervonOrWarehousePurchase": this.chervonOrWarehousePurchase,
                "contactName": this.contactName,
                // "contactEmailAddress": this.contactEmailAddress,
                "contactEmailAddress": this.contactEmailAddresses.join(';'),
                "contactPhoneNumber": this.contactPhoneNumber,
                "customerReferenceNumber": this.customerReferenceNumber,
                "notes": this.notes,

                "billingAddressId": this.selectedBillingAddress.addressId,
                "isAlternativeAddress": this.isAlternativeAddress,
                "isAlternativeStoreAddress": this.isAlternativeStoreAddress,
                "paymentMethod": this.paymentMethod,
                // "debitMemoId": this.DebitMemoId
            }

            if (this.paymentMethod === 'Deduction') {
                _json.debitMemoId = this.DebitMemoId;
            }

            if (this.isAlternativeAddress) {
                _json.AdditionalShippingStreet = this.alternativeShipping.street;
                _json.AdditionalShippingCity = this.alternativeShipping.city;
                _json.AdditionalShippingProvince = this.alternativeShipping.state;
                _json.AdditionalShippingCountry = this.alternativeShipping.country;
                _json.AdditionalShippingPostalCode = this.alternativeShipping.postalCode;
                _json.AdditionalContactEmail = this.alternativeShipping.contactEmail;
                _json.AdditionalContactName = this.alternativeShipping.contactName;
                _json.AdditionalContactPhone = this.alternativeShipping.contactPhone;
            } else {
                _json.shippingAddressId = this.selectedShippingAddress.addressId;
            }

            if (this.isAlternativeStoreAddress) {
                _json.AdditionalStoreStreet = this.alternativeStore.street;
                _json.AdditionalStoreCity = this.alternativeStore.city;
                _json.AdditionalStoreProvince = this.alternativeStore.state;
                _json.AdditionalStoreCountry = this.alternativeStore.country;
                _json.AdditionalStorePostalCode = this.alternativeStore.postalCode;
                _json.AdditionalStoreContactEmail = this.alternativeStore.contactEmail;
                _json.AdditionalStoreContactName = this.alternativeStore.contactName;
                _json.AdditionalStoreContactPhone = this.alternativeStore.contactPhone;
            } else {
                _json.storeAddressId = this.selectedStoreAddress.addressId;
            }

            let _items = this.items.map((item) => {
                let _jsonTake = {
                    "index": item.index,
                    "recordId": item.recordId,
                    "productId": item.productId,
                    "productCode": item.productCode,
                    "customerPONumber": item.orderItemId,
                    "quantity": item.quantity,
                    "endConsumerPurchaseDate": item.endConsumerPurchaseDate,
                    "endConsumerReturnDate": item.endConsumerReturnDate,
                    "DIFRTV": item.DIFRTV,
                    "returnReason": item.returnReason,
                    "returnReasonRemark": item.returnReasonRemark,
                    "serialNumber": item.serialNumber,
                    "brand": item.brand,
                    "invoicePrice": item.invoicePrice,
                    "subTotal": item.subTotal,
                    'purchaseDateFiles': item.purchaseDateFiles,
                    'returnDateFiles': item.returnDateFiles,
                    'returnReasonFiles': item.returnReasonFiles
                };
                return _jsonTake;
            })
            _json.items = _items;

            if (this.recordId) {
                _json.recordId = this.recordId;
                _json.deleteItemIds = this.deleteItemIds;
            }

            this.showSpinner = true;
            let _element = event.currentTarget;
            _element.disabled = true;

            saveWarrantyReturn({ warrantyReturnStr: JSON.stringify(_json), isSave: true }).then(result => {
                _element.disabled = false;
                if (result.isSuccess) {
                    this.showSpinner = false;
                    let data = result.returnData;
                    if (data.Id) {
                        this.showMessage(this.label.CCM_Portal_Success, this.label.CCM_Portal_WarrantyReturnhasbeensaved, 'success');
                        //跳转到详情页面，这里创建一个lightning page，跳转到这个page上边去。
                        this.handleView(data.Id);
                        this.accountNumber = '';
                        this.customerName = ''
                        this.selectedDropshipShippingAddress = '';
                        this.selectedDropshipBillingAddress = '';
                        this.chervonOrWarehousePurchase = '';
                        this.customerReferenceNumber = '';
                        this.notes = '';
                        this.contactName = '';
                        this.contactEmailAddress = '';
                        this.contactEmailAddresses = [];
                        this.contactPhoneNumber = '';

                        // this.items.length = 0;
                        this.items = [
                            { lineNo: 1, index: 0, returnReason: 'Defective' }
                        ];

                        this.selectedBillingAddress = '';
                        this.selectedShippingAddress = '';
                        this.selectedStoreAddress = '';

                        this.paymentMethod = '';
                        this.DebitMemo = '';
                        this.debitMemoAmount = '';
                    }
                } else {
                    this.showSpinner = false;
                    this.showMessage(this.label.CCM_Portal_Error, result.errorMsg, 'error');
                }
            })
        }
    }

    /**
    * validate inputs, save warranty return, submit the warranty return
    */
    handleClickSubmit(event) {
        // assign dif/rtv in portal to fix dif/rtv not populated issue
        if(!this.isInternal) {
            this.items.forEach((item, index)=>{
                if(!item.DIFRTV) {
                    this.assignDIFRTV(index, item.invoicePrice, item.quantity, item.orderType, item.state);
                }
            });
        }

        let _validate = this.validateBeforeSave();

        if (!this.isWarehouse) {
            this.selectedStoreAddress = {};
            this.selectedDropshipBillingAddress = {};
        }

        if (_validate) {
            let _json = {
                "customerName": this.customerName,
                "customerId": this.customerId,
                "accountNumber": this.accountNumber,
                "warehouseLocationId": this.selectedDropshipBillingAddress.addressId,
                "storeLocationId": this.selectedDropshipShippingAddress.addressId,

                "chervonOrWarehousePurchase": this.chervonOrWarehousePurchase,
                "contactName": this.contactName,
                // "contactEmailAddress": this.contactEmailAddress,
                "contactEmailAddress": this.contactEmailAddresses.join(';'),
                "contactPhoneNumber": this.contactPhoneNumber,
                "customerReferenceNumber": this.customerReferenceNumber,
                "notes": this.notes,

                "billingAddressId": this.selectedBillingAddress.addressId,
                "isAlternativeAddress": this.isAlternativeAddress,
                "isAlternativeStoreAddress": this.isAlternativeStoreAddress,
                "paymentMethod": this.paymentMethod,
                // "debitMemoId": this.DebitMemoId
            }

            if (this.paymentMethod === 'Deduction') {
                _json.debitMemoId = this.DebitMemoId;
            }

            if (this.isAlternativeAddress) {
                _json.AdditionalShippingStreet = this.alternativeShipping.street;
                _json.AdditionalShippingCity = this.alternativeShipping.city;
                _json.AdditionalShippingProvince = this.alternativeShipping.state;
                _json.AdditionalShippingCountry = this.alternativeShipping.country;
                _json.AdditionalShippingPostalCode = this.alternativeShipping.postalCode;
                _json.AdditionalContactEmail = this.alternativeShipping.contactEmail;
                _json.AdditionalContactName = this.alternativeShipping.contactName;
                _json.AdditionalContactPhone = this.alternativeShipping.contactPhone;
            } else {
                _json.shippingAddressId = this.selectedShippingAddress.addressId;
            }

            if (this.isAlternativeStoreAddress) {
                _json.AdditionalStoreStreet = this.alternativeStore.street;
                _json.AdditionalStoreCity = this.alternativeStore.city;
                _json.AdditionalStoreProvince = this.alternativeStore.state;
                _json.AdditionalStoreCountry = this.alternativeStore.country;
                _json.AdditionalStorePostalCode = this.alternativeStore.postalCode;
                _json.AdditionalStoreContactEmail = this.alternativeStore.contactEmail;
                _json.AdditionalStoreContactName = this.alternativeStore.contactName;
                _json.AdditionalStoreContactPhone = this.alternativeStore.contactPhone;
            } else {
                _json.storeAddressId = this.selectedStoreAddress.addressId;
            }

            let _items = this.items.map((item) => {
                let _jsonTake = {
                    "index": item.index,
                    "recordId": item.recordId,
                    "productId": item.productId,
                    "productCode": item.productCode,
                    "customerPONumber": item.orderItemId,
                    "quantity": item.quantity,
                    "endConsumerPurchaseDate": item.endConsumerPurchaseDate,
                    "endConsumerReturnDate": item.endConsumerReturnDate,
                    "DIFRTV": item.DIFRTV,
                    "returnReason": item.returnReason,
                    "returnReasonRemark": item.returnReasonRemark,
                    "serialNumber": item.serialNumber,
                    "brand": item.brand,
                    "invoicePrice": item.invoicePrice,
                    "subTotal": item.subTotal,
                    'purchaseDateFiles': item.purchaseDateFiles,
                    'returnDateFiles': item.returnDateFiles,
                    'returnReasonFiles': item.returnReasonFiles
                };
                return _jsonTake;
            })
            _json.items = _items;

            if (this.recordId) {
                _json.recordId = this.recordId;
                _json.deleteItemIds = this.deleteItemIds;
            }

            this.showSpinner = true;
            let _element = event.currentTarget;
            _element.disabled = true;

            saveWarrantyReturn({ warrantyReturnStr: JSON.stringify(_json), isSave: false }).then(result => {
                _element.disabled = false;
                if (result.isSuccess) {
                    this.showSpinner = false;
                    let data = result.returnData;
                    if (data.Id) {
                        this.showMessage(this.label.CCM_Portal_Success, this.label.CCM_Portal_WarrantyReturnhasbeensubmitted, 'success');
                        //跳转到详情页面，这里创建一个lightning page，跳转到这个page上边去。
                        this.handleView(data.Id);
                        this.accountNumber = '';
                        this.customerName = ''
                        this.selectedDropshipShippingAddress = '';
                        this.selectedDropshipBillingAddress = '';
                        this.chervonOrWarehousePurchase = '';
                        this.customerReferenceNumber = '';
                        this.notes = '';
                        this.contactName = '';
                        this.contactEmailAddresses = [];
                        this.contactEmailAddress = '';
                        this.contactPhoneNumber = '';

                        // this.items.length = 0;
                        this.items = [
                            { lineNo: 1, index: 0, returnReason: 'Defective' }
                        ];

                        this.selectedBillingAddress = '';
                        this.selectedShippingAddress = '';
                        this.selectedStoreAddress = '';

                        this.paymentMethod = '';
                        this.DebitMemo = '';
                        this.debitMemoAmount = '';
                    }
                } else {
                    this.showSpinner = false;
                    this.showMessage(this.label.CCM_Portal_Error, result.errorMsg, 'error');
                }
            })
        }
    }

    handleProductChange(e) {
        console.log('handle product change: ' + this.isDirectDealer);

        if (this.isDirectDealer === true && (this.chervonOrWarehousePurchase === '' || this.selectedDropshipShippingAddress === {} || this.selectedDropshipShippingAddress === '')) {
            this.showMessage(this.label.CCM_Portal_Warrning, this.label.CCM_Portal_StoreWarehouseTips, 'warning');
        }

        let _index = parseInt(e.currentTarget.dataset.index, 10);
        if (e.detail.value !== this.items[_index].productCode) {
            // this.items[_index] = [];
            this.items[_index].orderSource = [];
            this.items[_index].customerPONumber = '';
            this.items[_index].invoicePrice = '';
            this.items[_index].orderItemId = '';
            this.items[_index].pricebookId = '';
            this.items[_index].productDescription = '';
            this.items[_index].brand = '';
            this.items[_index].quantity = '';
            this.items[_index].subTotal = '';
            this.items[_index].endConsumerPurchaseDate = '';
            this.items[_index].purchaseDateFiles = [];
            this.items[_index].endConsumerReturnDate = '';
            this.items[_index].returnDateFiles = [];
            this.items[_index].DIFRTV = '';
            this.items[_index].serialNumber = '';
            this.items[_index].returnReason = '';
            this.items[_index].returnReasonFiles = [];
            this.items[_index].returnReasonRemark = '';
            // this.items[_index].lineNo = _index + 1;
        }
    }

    /**
     * handle product selected
     */
    handleProductSelected(e) {
        console.log('handle product change: ' + this.isDirectDealer);
        if (this.isDirectDealer === true && (this.chervonOrWarehousePurchase === '' || this.selectedDropshipShippingAddress === {} || this.selectedDropshipShippingAddress === '')) {
            this.showMessage(this.label.CCM_Portal_Warrning, this.label.CCM_Portal_StoreWarehouseTips, 'warning');
        } else {
            let _product = JSON.parse(e.detail);
            let _index = parseInt(e.currentTarget.dataset.index, 10);
            let _existProduct = this.items.find((item, index) => item.productCode === _product.productCode && index !== _index);

            // remove duplicate validate
            if (!_existProduct) {
                let _item = {
                    productId: _product.productId,
                    productDescription: _product.productDescription,
                    productCode: _product.productCode,
                    brand: _product.brand,
                    returnReason: 'Defective'
                    // itemNumbers: _product.itemNumbers
                };
                Object.assign(this.items[_index], _item);
            } else {
                this.showMessage(this.label.CCM_Portal_DuplicateProduct, this.label.CCM_Portal_ThisProducthasbeenadded, 'warning');
                e.currentTarget.displayText = '';
            }
            //带出dif/rtv
            getDIFRTV({ productId: _product.productId, policyId: this.policyId }).then((result) => {
                if (result && result != null && result != '') {
                    if (result.recordTypeName === 'Customized') {
                        this.items[_index].DIFRTVThreshold = result.difRTVThreshold;
                    } else {
                        this.items[_index].DIFRTV = result.DIFRTV;
                        this.items[_index].DIFRTVThreshold = result.difRTVThreshold;
                    }
                    // Object.assign(this.items[_index], _DifRtv);
                }
            }, error => {
                this.showMessage(this.label.CCM_Portal_Error, error.body.message, 'error');
            })
            getCustomerPONumber({ customerId: this.customerId, productId: _product.productId, chervonOrWarehousePurchase: this.chervonOrWarehousePurchase, storeLocationId: this.selectedDropshipShippingAddress.addressId }).then((result) => {

                if (result && result.length > 0) {
                    this.items[_index].orderSource = result;
                    // this.invoicePrices=_invoicePrice;
                    // Object.assign(this.items[_index], _invoicePrice);
                } else {
                    this.items[_index].orderSource = [];
                    this.items[_index].customerPONumber = '';
                    this.items[_index].invoicePrice = '';
                    this.items[_index].orderItemId = '';
                    this.items[_index].pricebookId = '';
                    this.showMessage(this.label.CCM_Portal_Warrning, this.label.CCM_Portal_ThereisnocustomerPOfoundforthismodel, 'error');
                }
            }, error => {
                this.showMessage(this.label.CCM_Portal_Error, error.body.message, 'error');
            })
        }
    }

    /**
     * handle order selected
     */
    handleOrderSelected(e) {
        let _order = JSON.parse(e.detail);
        let _index = parseInt(e.currentTarget.dataset.index, 10);

        let _invoicePrice = _order.price;
        let _qty = this.items[_index].quantity
        let _orderItemId = _order.orderItemId;
        let _pricebookId = _order.pricebookId;
        let _customerPONumber = _order.orderNumber;
        let _orderType = _order.orderType;
        let _state = _order.state;
        let _item = {
            quantity: _qty,
            invoicePrice: _invoicePrice,
            orderItemId: _orderItemId,
            pricebookId: _pricebookId,
            customerPONumber: _customerPONumber,
            subTotal: parseFloat(this.decimalPlaces(_invoicePrice * _qty)),
        };

        this.assignDIFRTV(_index, _invoicePrice, _qty, _orderType, _state);
        // this.items[_index].DIFRTV = '';
        // if(_orderType === 'CNA Export Order' || ['HI', 'AK'].includes(_state)) {
        //     this.items[_index].DIFRTV = 'DIF';
        //     e.target.disabled = true;
        // }
        // else {
        //     e.target.disabled = false;
        //     if (this.policyRecordType === 'Standard') {
        //         this.items[_index].DIFRTV = parseFloat(this.decimalPlaces(_invoicePrice * _qty)) < this.items[_index].DIFRTVThreshold ? 'DIF' : 'RTV';
        //     } else {
        //         if (this.policyDIFRTVModel == 'DIF/RTV Threshold') {
        //             this.items[_index].DIFRTV = parseFloat(this.decimalPlaces(_invoicePrice * _qty)) < this.items[_index].DIFRTVThreshold ? 'DIF' : 'RTV';
        //         } else {
        //             if (this.models != null && this.models.length > 0 && this.models.findIndex(item => item === this.items[_index].productCode) > -1) {
        //                 this.items[_index].DIFRTV = 'RTV';
        //             } else {
        //                 this.items[_index].DIFRTV = 'DIF for All' ? 'DIF' : 'RTV';
        //             }


        //         }
        //     }
        // }


        Object.assign(this.items[_index], _item);
    }



    handleQuantityChange(event) {
        let _qty = parseInt(event.detail.value, 10);
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _invoicePrice = 0;
        if (this.items[_index].invoicePrice) {
            _invoicePrice = this.items[_index].invoicePrice;
        }
        let _item = {
            quantity: _qty,
            subTotal: parseFloat(this.decimalPlaces(_invoicePrice * _qty)),
        };

        let _orderType = '';
        let _state = '';


        if (this.items[_index].orderSource) {
            this.items[_index].orderSource.forEach(item => {
                if (item.orderItemId === this.items[_index].orderItemId) {

                    _orderType = item.orderType;
                    _state = item.state;
                }
            });
        }
        else {

            if (this.items[_index].orderType) {
                this._orderType = this.items[_index].orderType;
            }
            if (this.items[_index].state) {
                this._state = this.items[_index].state;
            }
        }


        this.assignDIFRTV(_index, _invoicePrice, _qty, _orderType, _state);
        Object.assign(this.items[_index], _item);
    }

    handleInvoicePriceChange(event) {
        let _invoicePrice = parseFloat(event.detail.value);
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _qty = 0;
        if (this.items[_index].quantity) {
            _qty = this.items[_index].quantity;
        }
        let _item = {
            invoicePrice: _invoicePrice,
            subTotal: parseFloat(this.decimalPlaces(_invoicePrice * _qty)),
        };
        let _orderType = '';
        let _state = '';
        if (this.items[_index].orderSource) {
            this.items[_index].orderSource.forEach(item => {
                if (item.orderItemId === this.items[_index].orderItemId) {
                    _orderType = item.orderType;
                    _state = item.state;
                }
            });
        }
        else {
            if (this.items[_index].orderType) {
                this._orderType = this.items[_index].orderType;
            }
            if (this.items[_index].state) {
                this._state = this.items[_index].state;
            }
        }
        this.assignDIFRTV(_index, _invoicePrice, _qty, _orderType, _state);
        Object.assign(this.items[_index], _item);
    }

    assignDIFRTV(_index, _invoicePrice, _qty, _orderType, _state) {
        let element = this.template.querySelector('[data-id="difrtv"]');
        this.items[_index].DIFRTV = '';
        if (_orderType === 'CNA Export Order' || ['HI', 'AK'].includes(_state)) {

            this.items[_index].DIFRTV = 'DIF';
            // element.disabled = true;
        }
        else {
            // element.disabled = false;
            if (this.policyRecordType === 'Standard') {
                this.items[_index].DIFRTV = parseFloat(this.decimalPlaces(_invoicePrice * _qty)) < this.items[_index].DIFRTVThreshold ? 'DIF' : 'RTV';
            } else {
                if (this.policyDIFRTVModel == 'DIF/RTV Threshold') {
                    this.items[_index].DIFRTV = parseFloat(this.decimalPlaces(_invoicePrice * _qty)) < this.items[_index].DIFRTVThreshold ? 'DIF' : 'RTV';
                } else {
                    if (this.models != null && this.models.length > 0 && this.models.findIndex(item => item === this.items[_index].productCode) > -1) {
                        this.items[_index].DIFRTV = 'RTV';
                    } else {
                        this.items[_index].DIFRTV = 'DIF for All' ? 'DIF' : 'RTV';
                    }
                }
            }
        }
    }

    /**
     * total qty
     */
    get totalQty() {
        let _qtys = this.items.map(item => {
            return item.quantity ? item.quantity : 0;
        });
        let _total = _qtys.reduce((prev, cur) => {
            return prev + cur;
        }, 0);
        return _total;
    }

    /**
     * total qty
     */
    get productAmount() {
        let _subTotal = this.items.map(item => {
            return item.subTotal ? item.subTotal : 0;
        });
        let _total = _subTotal.reduce((prev, cur) => {
            return prev + cur;
        }, 0);
        return _total;
    }

    /*
     * creditMemoAmount
     */
    get claimedCreditMemoAmount() {
        let _subTotal = this.items.map(item => {
            return item.subTotal ? item.subTotal : 0;
        });
        let _total = _subTotal.reduce((prev, cur) => {
            return prev + cur;
        }, 0);
        return _total;
    }

    get isShowPrice() {
        if (this.isInternal) {
            return true;
        } else {
            return this.chervonOrWarehousePurchase === 'Warehouse' ? false : true;
        }
    }

    /**
     * 查询到客户
     */
    handleCustomerSelected(e) {

        let _selected = JSON.parse(e.detail);
        // connectedcallback中取得的customerId与重新选择的customerId比较，如果不同，则清空其他信息。
        if (_selected.value != this.customerId) {
            this.selectedDropshipShippingAddress = '';
            this.selectedDropshipBillingAddress = '';
            this.chervonOrWarehousePurchase = '';
            this.customerReferenceNumber = '';
            this.notes = '';
            this.contactName = '';
            this.contactEmailAddress = '';
            this.contactEmailAddresses = [];
            this.contactPhoneNumber = '';

            this.selectedBillingAddress = '';
            this.selectedShippingAddress = '';
            this.selectedStoreAddress = {};

            this.paymentMethod = '';
            this.DebitMemo = '';
            this.debitMemoAmount = '';
        }

        this.customerId = _selected.value;
        this.customerName = _selected.label;
        this.isDirectDealer = _selected.customerType === 'Direct Dealer' ? false : true;
        this.accountNumber = _selected.AccountNumber;
        this.policyId = _selected.policyId;
        this.paymentMethods = _selected.policyPaymentMethod;
        if (this.paymentMethods.length === 1) {
            this.paymentMethod = this.paymentMethods[0].value;
        }
        this.policyRecordType = _selected.policyRecordType;
        this.policyDIFRTVModel = _selected.policyDIFRTVModel;
        this.storeReturnPeriod = _selected.storeReturnPeriod;
        this.models = _selected.models;
        if (_selected.value != this.customerId) {
            this.items = [
                { lineNo: 1, index: 0, DIFRTV: this.policyDIFRTVModel, returnReason: 'Defective' }
            ];
        }

        // 带出产品信息
        getProducts({ customerId: this.customerId, keyWord: '' }).then((result) => {
            if (result != null && result != undefined && result != '') {

                if (result.isSuccess) {
                    let _product = [];
                    _product = result.returnData.map(item => {
                        return {
                            productCode: item.productCode,
                            productDescription: item.description,
                            brand: item.brand,
                            productId: item.productId,
                        };
                    });
                    this.productSource = _product;
                }
            }
        }, error => {
            this.showSpinner = false;
            this.showMessage(this.label.CCM_Portal_Error, error.body.message, 'error');
        })

        //带出地址信息
        getAddress({ customerId: this.customerId }).then(result => {
            if (result) {
                if (result.isSuccess) {
                    if (result.returnData && result.returnData.billing) {

                        this.billingAddressSource = result.returnData.billing;
                        this.billingAddressSource.forEach(item => {
                            let _str = '';
                            _str += item.country ? item.country + '  ' : '';
                            _str += item.state ? item.state + '  ' : '';
                            _str += item.country ? item.city + '  ' : '';
                            _str += item.address1 ? item.address1 + '  ' : '';
                            _str += item.address2 ? item.address2 : '';
                            item.addressStr = _str.trim();
                            item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                        })
                    }
                    if (result.returnData && result.returnData.shipping) {
                        this.shippingAddressSource = result.returnData.shipping;
                        this.shippingAddressSource.forEach(item => {
                            let _str = '';
                            _str += item.country ? item.country + '  ' : '';
                            _str += item.state ? item.state + '  ' : '';
                            _str += item.country ? item.city + '  ' : '';
                            _str += item.address1 ? item.address1 + '  ' : '';
                            _str += item.address2 ? item.address2 : '';
                            item.addressStr = _str.trim();
                            item.street = _str.trim();
                            item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                        })
                    }
                    if (result.returnData && result.returnData.dropshipShipping) {
                        // this.isDirectDealer = result.returnData.dropshipShipping.length == 0 ? false : true;
                        this.dropshipShippingAddressSource = result.returnData.dropshipShipping;
                        this.dropshipShippingAddressSource.forEach(item => {
                            let _str = '';
                            _str += item.country ? item.country + '  ' : '';
                            _str += item.state ? item.state + '  ' : '';
                            _str += item.country ? item.city + '  ' : '';
                            _str += item.address1 ? item.address1 + '  ' : '';
                            _str += item.address2 ? item.address2 : '';
                            item.addressStr = _str.trim();
                            item.street = _str.trim();
                            item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                        })
                    }
                    if (result.returnData && result.returnData.dropshipBilling) {
                        this.dropshipBillingAddressSource = result.returnData.dropshipBilling;
                        this.dropshipBillingAddressSource.forEach(item => {
                            let _str = '';
                            _str += item.country ? item.country + '  ' : '';
                            _str += item.state ? item.state + '  ' : '';
                            _str += item.country ? item.city + '  ' : '';
                            _str += item.address1 ? item.address1 + '  ' : '';
                            _str += item.address2 ? item.address2 : '';
                            item.addressStr = _str.trim();
                            item.street = _str.trim();
                            item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                        })
                    }

                    if (result.returnData && result.returnData.dropship) {
                        this.storeAddressSource = result.returnData.dropship;
                        this.storeAddressSource.forEach(item => {
                            let _str = '';
                            _str += item.country ? item.country + '  ' : '';
                            _str += item.state ? item.state + '  ' : '';
                            _str += item.country ? item.city + '  ' : '';
                            _str += item.address1 ? item.address1 + '  ' : '';
                            _str += item.address2 ? item.address2 : '';
                            item.addressStr = _str.trim();
                            item.street = _str.trim();
                            item.street = item.address1 ? item.address1 + ' ' : '' + item.address2 ? item.address2 : '';
                        })
                    }
                } else {
                    this.showMessage(this.label.CCM_Portal_Error, result.errorMsg, 'error');
                }
            }
        });

        //带出发票信息
        getAvailabelInvoice({ customerId: this.customerId }).then((result) => {
            if (result != null && result != undefined && result != '') {
                if (result) {
                    let _invoice = [];
                    _invoice = result.map(item => {
                        return {
                            // invoiceId: item.Id,
                            DebitMemoId: item.invoiceId,
                            DebitMemo: item.invoiceName,
                            debitMemoAmount: item.debitMemoAmount,
                        }
                    });
                    this.DebitMemos = _invoice;
                    // this.template.querySelector('.autoComplete').setSource(this.DebitMemos);
                }
            }
        }, error => {
            this.showSpinner = false;
            this.showMessage(this.label.CCM_Portal_Error, error.body.message, 'error');
        })
    }

    handleProductCodeChange(event) {
        if (event.detail.trim() === '') {

            let _index = parseInt(event.currentTarget.dataset.index, 10);
            this.items[_index].orderSource = [];
            this.items[_index].customerPONumber = '';
            this.items[_index].invoicePrice = '';
            this.items[_index].orderItemId = '';
            this.items[_index].pricebookId = '';
            this.items[_index].productDescription = '';
            this.items[_index].brand = '';
            this.items[_index].quantity = '';
            this.items[_index].subTotal = '';
            this.items[_index].endConsumerPurchaseDate = '';
            this.items[_index].purchaseDateFiles = [];
            this.items[_index].endConsumerReturnDate = '';
            this.items[_index].returnDateFiles = [];
            this.items[_index].DIFRTV = '';
            this.items[_index].serialNumber = '';
            this.items[_index].returnReason = '';
            this.items[_index].returnReasonFiles = [];
            this.items[_index].returnReasonRemark = '';
        }
        else {
            getProducts({ customerId: this.customerId, keyWord: event.detail.trim() }).then((result) => {
                if (result != null && result != undefined && result != '') {

                    if (result.isSuccess) {
                        let _product = [];
                        _product = result.returnData.map(item => {
                            return {
                                productCode: item.productCode,
                                productDescription: item.description,
                                brand: item.brand,
                                productId: item.productId,
                            };
                        });
                        this.productSource = _product;
                        this.template.querySelector('.autoComplete').setSource(this.productSource);
                        this.template.querySelector('.autoComplete').filterDataByKeyword(event.detail.trim());
                    }
                }
            }, error => {
                this.showSpinner = false;
                // this.showMessage('Error', error.body.message, 'error');
            })
        }
    }

    handleCustomerSearchInputChange(event) {
        if (event.detail.trim() === '') {
            this.customerName = '';
            this.accountNumber = '';
            this.selectedBillingAddress = {};
            this.selectedShippingAddress = {};
            this.productSource = [];

            this.items = [
                { lineNo: 1, index: 0, returnReason: 'Defective' }
            ];

            this.selectedDropshipShippingAddress = {};
            this.selectedDropshipBillingAddress = {};
            this.chervonOrWarehousePurchase = '';
            this.customerReferenceNumber = '';
            this.notes = '';
            this.contactName = '';
            this.contactEmailAddress = '';
            this.contactEmailAddresses = [];
            this.contactPhoneNumber = '';
            this.selectedStoreAddress = {};

            this.paymentMethod = '';
            this.DebitMemo = '';
            this.debitMemoAmount = '';

            this.isDirectDealer = false;
            this.isWarehouse = false;

        } else {
            getCustomer({ keyWord: event.detail }).then((result) => {
                if (result.isSuccess) {
                    let _customers = [];
                    _customers = result.returnData.map(item => {
                        return {
                            value: item.id,
                            label: item.name,
                            customerType: item.customerType,
                            AccountNumber: item.accountNumber,
                            policyId: item.policyId,
                            policyPaymentMethod: item.policyPaymentMethod,
                            policyRecordType: item.policyRecordType,
                            policyDIFRTVModel: item.policyDIFRTVModel,
                            storeReturnPeriod: item.returnPeriod,
                            models: item.models
                        }
                    });
                    this.customerSource = _customers;
                    this.template.querySelector('.autoComplete').setSource(this.customerSource);
                    this.template.querySelector('.autoComplete').filterDataByKeyword(event.detail);
                } else {
                    this.showMessage(this.label.CCM_Portal_Error, result.errorMsg, 'error');
                }
            }, error => {
                this.showMessage(this.label.CCM_Portal_Error, error.body.message, 'error');
            })
        }
    }

    handleAddressSelected(event) {
        let _type = event.currentTarget.dataset.type;
        if (!this.isWarehouse) {
            if (_type === "billing") {
                this.selectedBillingAddress = JSON.parse(event.detail);
            }
            if (_type === "shipping") {
                this.selectedShippingAddress = JSON.parse(event.detail);
            }
            if (_type === "dropshipShipping") {
                this.selectedDropshipShippingAddress = JSON.parse(event.detail);
                this.selectedShippingAddress = JSON.parse(event.detail);
                this.selectedStoreAddress = JSON.parse(event.detail);
            }
            // 这种情况不需要填以下两个地址
            // if (_type === "dropshipBilling") {
            //     this.selectedDropshipBillingAddress = JSON.parse(event.detail);
            // }
            // if (_type === "store") {
            //     this.selectedStoreAddress = JSON.parse(event.detail);
            // }
        } else {
            if (_type === "billing") {
                this.selectedBillingAddress = JSON.parse(event.detail);
            }
            if (_type === "dropshipShipping") {
                this.selectedDropshipShippingAddress = JSON.parse(event.detail);
            }
            if (_type === "dropshipBilling") {
                this.selectedDropshipBillingAddress = JSON.parse(event.detail);
                this.selectedShippingAddress = JSON.parse(event.detail);
            }
            if (_type === "shipping") {
                this.selectedShippingAddress = JSON.parse(event.detail);
                this.isAlternativeAddress = false;
                this.isShippingAddressRequired = true;
            }
            if (_type === "store") {
                this.selectedStoreAddress = JSON.parse(event.detail);
                this.isAlternativeStoreAddress = false;
            }
        }
    }

    /**
     * clear address
     */
    handleBillingAddressSearchInputChange(event) {
        if (event.detail.trim() === '') {
            this.selectedBillingAddress = {};
        }
    }

    /**
     * clear address
     */
    handleShippingAddressSearchInputChange(event) {
        if (event.detail.trim() === '') {
            this.selectedShippingAddress = {};
        }
    }

    /**
     * clear address
     */
    handleStoreAddressSearchInputChange(event) {
        if (event.detail.trim() === '') {
            this.selectedStoreAddress = {};
        }
    }

    /**
     * clear debit memo info
     */
    handleDebitMemoSearchInputChange(event) {
        if (event.detail.trim() === '') {
            // this.DebitMemoId = undefined;
            this.DebitMemo = '';
            this.debitMemoAmount = '';
        }
    }

    /**
     * clear address
     */
    handleDropshipShippingAddressSearchInputChange(event) {
        // if (event.detail.trim() === '') {
        this.selectedDropshipShippingAddress = '';
        // }
        this.items = [
            { lineNo: 1, index: 0 }
        ];
    }

    /**
     * clear address
     */
    handleDropBillingAddressSearchInputChange(event) {
        if (event.detail.trim() === '') {
            this.selectedDropshipBillingAddress = {};
        }
    }

    handleDebitMemoSelected(e) {
        let _select = JSON.parse(e.detail);
        this.DebitMemoId = _select.DebitMemoId;
        this.DebitMemo = _select.DebitMemo;
        this.debitMemoAmount = _select.debitMemoAmount;
    }

    /**
     * additional address update
     */
    handleAlternativeShippingChange(event) {
        this.alternativeShipping.street = event.detail.street;
        this.alternativeShipping.city = event.detail.city;
        this.alternativeShipping.state = event.detail.province;
        this.alternativeShipping.country = event.detail.country;
        this.alternativeShipping.postalCode = event.detail.postalCode;
    }

    /**
     * additional address update
     */
    handleAlternativeStoreChange(event) {
        this.alternativeStore.street = event.detail.street;
        this.alternativeStore.city = event.detail.city;
        this.alternativeStore.state = event.detail.province;
        this.alternativeStore.country = event.detail.country;
        this.alternativeStore.postalCode = event.detail.postalCode;
    }

    //Preview upload files
    navigateToFiles(event) {
        let _docId = event.currentTarget.dataset.docid;
        this[NavigationMixin.Navigate]({
            type: 'standard__namedPage',
            attributes: {
                pageName: 'filePreview'
            },
            state: {
                recordIds: _docId,
                selectedRecordId: _docId
            }
        })
    }

    //close upload file form
    //     handleClickClosePictruesModel(){
    //     this.showPicturesModal = false;
    // }


    /**
     * handle file uploaded event on the table item
     */
    @track
    isPurchaseDate = false;
    @track
    isReturnDate = false;
    @track
    isReturnReason = false;
    @track
    selectedPurchaseDateUploadedFiles = [];
    @track
    selectedReturnDateUploadedFiles = [];
    @track
    selectedReturnReasonUploadedFiles = [];
    handleItemUploadImage(event) {

        this.itemFileUploadLineNo = event.currentTarget.dataset.index;
        this.isPurchaseDate = true;
        this.showUploadModal = true;


        let files = [];
        if (event.currentTarget.dataset.type === "purchaseDate") {
            this.uploadType = event.currentTarget.dataset.type;
            let _purchaseItems = this.items[this.itemFileUploadLineNo];
            if (_purchaseItems && _purchaseItems.purchaseDateFiles) {
                files = files.concat(_purchaseItems.purchaseDateFiles);
            }
            files.forEach((item, index) => {
                // item.lineNo = this.itemFileUploadLineNo;
                item.attachlineNo = index + 1;
            })
        }
        this.selectedPurchaseDateUploadedFiles = files;

    }

    handleReturnDateItemUploadImage(event) {
        this.itemFileUploadLineNo = event.currentTarget.dataset.index;
        this.isReturnDate = true;
        this.showUploadModal = true;

        let files = [];
        if (event.currentTarget.dataset.type === "returnDate") {
            this.uploadType = event.currentTarget.dataset.type;
            let _returnItems = this.items[this.itemFileUploadLineNo];
            if (_returnItems && _returnItems.returnDateFiles) {
                files = files.concat(_returnItems.returnDateFiles);
            }
            files.forEach((item, index) => {
                // item.lineNo = this.itemFileUploadLineNo;
                item.attachlineNo = index + 1;
            })
        }

        this.selectedReturnDateUploadedFiles = files;

    }

    handleReturnReasonItemUploadImage(event) {
        this.itemFileUploadLineNo = event.currentTarget.dataset.index;
        this.isReturnReason = true;
        this.showUploadModal = true;


        let files = [];
        if (event.currentTarget.dataset.type === "returnReason") {
            this.uploadType = event.currentTarget.dataset.type;
            let _returnItems = this.items[this.itemFileUploadLineNo];
            if (_returnItems && _returnItems.returnReasonFiles) {
                files = files.concat(_returnItems.returnReasonFiles);
            }
            files.forEach((item, index) => {
                // item.lineNo = this.itemFileUploadLineNo;
                item.attachlineNo = index + 1;
            })
        }
        this.selectedReturnReasonUploadedFiles = files;
    }

    /**
     * handle file uploaded event on the table item
     */
    handleUploadFinished(event) {
        let _uploadedFiles = event.detail.files;
        let files = [];

        if (this.uploadType === "purchaseDate") {

            let _purchaseItems = this.items[this.itemFileUploadLineNo];
            _purchaseItems.uploadType = this.uploadType;
            if (_purchaseItems && _purchaseItems.purchaseDateFiles) {
                files = _purchaseItems.purchaseDateFiles.concat(_uploadedFiles);
            } else {
                files = _uploadedFiles;
            }
            files.forEach((item, index) => {
                item.lineNo = this.itemFileUploadLineNo;
                item.attachlineNo = index + 1;
                item.uploadType = "purchaseDate";
            })
            _purchaseItems.purchaseDateFiles = files;
            this.items[this.itemFileUploadLineNo].purchaseDateFiles = _purchaseItems.purchaseDateFiles;

            this.items.forEach((item, index) => {
                item.index = index;
            });

            this.selectedPurchaseDateUploadedFiles = _purchaseItems.purchaseDateFiles;
        }
        if (this.uploadType === "returnDate") {
            let _returnItems = this.items[this.itemFileUploadLineNo];
            _returnItems.uploadType = this.uploadType;
            if (_returnItems && _returnItems.returnDateFiles) {
                files = _returnItems.returnDateFiles.concat(_uploadedFiles);
            } else {
                files = _uploadedFiles;
            }
            files.forEach((item, index) => {
                item.lineNo = this.itemFileUploadLineNo;
                item.attachlineNo = index + 1;
                item.uploadType = "returnDate";
            })
            _returnItems.returnDateFiles = files;
            this.items[this.itemFileUploadLineNo].returnDateFiles = _returnItems.returnDateFiles;

            this.items.forEach((item, index) => {
                item.index = index;
            });

            this.selectedReturnDateUploadedFiles = _returnItems.returnDateFiles;
        }
        if (this.uploadType === "returnReason") {
            let _returnReasonItems = this.items[this.itemFileUploadLineNo];
            // let _returnItems = [];
            _returnReasonItems.uploadType = this.uploadType;
            if (_returnReasonItems && _returnReasonItems.returnReasonFiles) {
                files = _returnReasonItems.returnReasonFiles.concat(_uploadedFiles);
            } else {
                files = _uploadedFiles;
            }
            files.forEach((item, index) => {
                item.lineNo = this.itemFileUploadLineNo;
                item.attachlineNo = index + 1;
                item.uploadType = "returnReason";
            })
            _returnReasonItems.returnReasonFiles = files;
            this.items[this.itemFileUploadLineNo].returnReasonFiles = _returnReasonItems.returnReasonFiles;

            this.items.forEach((item, index) => {
                item.index = index;
            });

            this.selectedReturnReasonUploadedFiles = _returnReasonItems.returnReasonFiles;
        }

        // this.selectedItemUploadedFiles = files;
    }

    /** delete file */
    handleDeleteFile(event) {
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        this.selectedPurchaseDateUploadedFiles.splice(_index, 1);
        this.selectedReturnDateUploadedFiles.splice(_index, 1);
        this.selectedReturnReasonUploadedFiles.splice(_index, 1);
        let _docid = event.currentTarget.dataset.docid;

        // let _uploadType = event.currentTarget.dataset.uploadType;
        deleteContentDocument({ cdId: _docid }).then((data) => {
            if (this.uploadType === 'purchaseDate') {
                this.selectedPurchaseDateUploadedFiles.forEach((item, index) => {
                    item.attachlineNo = index + 1;
                })

                let _purchaseItems = this.items[this.itemFileUploadLineNo];
                _purchaseItems.purchaseDateFiles = JSON.parse(JSON.stringify(this.selectedPurchaseDateUploadedFiles));
            }
            if (this.uploadType === 'returnDate') {
                this.selectedReturnDateUploadedFiles.forEach((item, index) => {
                    item.attachlineNo = index + 1;
                })

                let _returnItems = this.items[this.itemFileUploadLineNo];
                _returnItems.returnDateFiles = JSON.parse(JSON.stringify(this.selectedReturnDateUploadedFiles));
            }
            if (this.uploadType === 'returnReason') {
                this.selectedReturnReasonUploadedFiles.forEach((item, index) => {
                    item.attachlineNo = index + 1;
                })

                let _returnReasonItems = this.items[this.itemFileUploadLineNo];
                _returnReasonItems.returnReasonFiles = JSON.parse(JSON.stringify(this.selectedReturnReasonUploadedFiles));
            }
        }, error => {
            this.showMessage(this.label.CCM_Portal_Error, error.body.message, 'error');
        })
    }

    handleClickClose() {
        this.showUploadModal = false;
        this.isPurchaseDate = false;
        this.isReturnDate = false;
        this.isReturnReason = false;
    }

    handleContactNameChange(event) {
        this.alternativeShipping.contactName = event.detail.value;
    }

    handleContactPhoneChange(event) {
        this.alternativeShipping.contactPhone = event.detail.value;
    }

    handleContactEmailChange(event) {
        this.alternativeShipping.contactEmail = event.detail.value;
    }

    handleStoreContactEmailChange(event) {
        this.alternativeStore.contactEmail = event.detail.value;
    }

    handleStoreContactNameChange(event) {
        this.alternativeStore.contactName = event.detail.value;
    }

    handleStoreContactPhoneChange(event) {
        this.alternativeStore.contactPhone = event.detail.value;
    }

    handleClickCancelAlternative() {
        this.showAlternativeShipping = false;
    }

    handleAddAlternativeShipping() {
        this.showAlternativeShipping = true;
    }

    handleClickCancelAlternativeStore() {
        this.showAlternativeStore = false;
    }

    handleAddAlternativeStore() {
        this.showAlternativeStore = true;
    }

    /**
     * save additional address
     */
    handleClickSaveAlternative() {
        let _validate = true;
        if (!this.template.querySelector('lightning-input-address').reportValidity()) {
            _validate = false;
        }
        if (!this.template.querySelector('.contactName').reportValidity() ||
            !this.template.querySelector('.contactPhone').reportValidity() ||
            !this.template.querySelector('.contactEmail').reportValidity()) {
            _validate = false;
        }

        if (_validate) {
            this.isAlternativeAddress = true;
            this.isShippingAddressRequired = false;
            this.selectedShippingAddress = this.alternativeShipping;
            this.showAlternativeShipping = false;
        }
    }

    /**
     * save additional address
     */
    handleClickSaveAlternativeStore() {
        let _validate = true;
        if (!this.template.querySelector('lightning-input-address').reportValidity()) {
            _validate = false;
        }
        if (!this.template.querySelector('.contactName').reportValidity() ||
            !this.template.querySelector('.contactPhone').reportValidity() ||
            !this.template.querySelector('.contactEmail').reportValidity()) {
            _validate = false;
        }

        if (_validate) {
            this.isAlternativeStoreAddress = true;
            this.selectedStoreAddress = this.alternativeStore;
            this.showAlternativeStore = false;
        }
    }

    handleClickAddItem(event) {
        this.items.push({ lineNo: this.items.length + 1, returnReason: 'Defective' });
    }

    // handleStoreLocationChange(event) {
    //     this.storeLocation = event.detail.value;
    // }

    handleChangeChervonOrWarehousePurchase(event) {
        this.chervonOrWarehousePurchase = event.detail.value;
        this.items = [
            { lineNo: 1, index: 0 }
        ];
        if (this.chervonOrWarehousePurchase === "Warehouse") {
            this.isWarehouse = true;
        } else {
            this.isWarehouse = false;
        }
    }

    handleWareHouseChange(event) {
        this.warehouseLocation = event.detail.value;
    }

    handleSerialNumberChange(event) {
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _serialNumber = event.detail.value;
        let _item = {
            serialNumber: _serialNumber
        }
        Object.assign(this.items[_index], _item);
    }

    handleDIFRTVChange(event) {
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _difRTV = event.detail.value;
        let _item = {
            DIFRTV: _difRTV
        }
        Object.assign(this.items[_index], _item);
    }

    handleReturnReasonChange(event) {
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _returnReason = event.detail.value;
        let _item = {
            returnReason: _returnReason
        }
        Object.assign(this.items[_index], _item);
    }

    handleReturnReasonRemarkChange(event) {
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _returnReasonRemark = event.detail.value;
        let _item = {
            returnReasonRemark: _returnReasonRemark
        }
        Object.assign(this.items[_index], _item);
    }

    handleEndConsumerReturnDateChange(event) {
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _endConsumerReturnDate = event.detail.value;
        let _item = {
            endConsumerReturnDate: _endConsumerReturnDate
        }

        var _brand = this.items[_index].brand;
        var _maxReturnPeriod = this.storeReturnPeriod;
        if (_brand.toUpperCase() == 'SKILSAW') {
            _maxReturnPeriod = this.storeReturnPeriod > this.SKILSAWReturnPeriod ? this.storeReturnPeriod : this.SKILSAWReturnPeriod;
        }
        let date1 = new Date(this.items[_index].endConsumerPurchaseDate);
        let date2 = new Date(_endConsumerReturnDate);
        var diffTime = Math.abs(date1.getTime() - date2.getTime());
        var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        console.log(diffDays);
        if (this.isInternal === true) {

            if (diffDays > _maxReturnPeriod) {
                this.showMessage(this.label.CCM_Portal_Warrning, this.label.CCM_Portal_returnperiodTips, 'warning');
                return;
            }
        }

        Object.assign(this.items[_index], _item);
    }

    handleEndConsumerPurchaseDateChange(event) {
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let _endConsumerPurchaseDate = event.detail.value; // format: 2023-02-07

        // let __endMaxConsumerPurchaseDate = moment(_endConsumerPurchaseDate).add(2, 'days').format("YYYY-MM-DD");

        let _item = {
            endConsumerPurchaseDate: _endConsumerPurchaseDate,
        }
        Object.assign(this.items[_index], _item);
    }

    handleContactNameChange(event) {
        this.contactName = event.detail.value;
    }

    handleContactEmailAddress(event) {
        this.contactEmailAddress = event.detail.value;
    }
    handleContactPhoneNumber(event) {
        this.contactPhoneNumber = event.detail.value;
    }

    handleCustomerReferenceNumber(event) {
        this.customerReferenceNumber = event.detail.value;
    }

    handleNotesChange(event) {
        this.notes = event.detail.value;
    }

    handlePaymentMethodChange(event) {
        this.paymentMethod = event.detail.value;

        if (this.paymentMethod === 'Deduction') {
            this.isDeduction = true;
        } else {
            this.isDeduction = false;
            // this.DebitMemoId = undefined;
            this.DebitMemo = '';
            this.debitMemoAmount = '';
        }
    }

    /**
     * delete item from take table
     */
    handleDeleteProductItem(event) {
        let _index = parseInt(event.currentTarget.dataset.index, 10);
        let deleteItems = this.items.splice(_index, 1);
        this.items.forEach((item, index) => {
            item.lineNo = index + 1;
        })
        if (this.recordId && deleteItems[0].recordId) {
            this.deleteItemIds.push(deleteItems[0].recordId);
        }
    }

    decimalPlaces(num) {
        num = num.toString()
        let index = num.indexOf('.')
        if (index !== -1) {
            num = num.substring(0, 2 + index + 1)
        } else {
            num = num.substring(0)
        }
        return parseFloat(num).toFixed(2);
    }

    /**
     * collapse, expand the each section.
     */
    handleSwitchCollapse(event) {
        if (!event.currentTarget.classList.contains('collapse_icon')) {
            event.currentTarget.classList.add('collapse_icon');
            event.currentTarget.parentElement.nextSibling.classList.add('collapse');
        } else {
            event.currentTarget.classList.remove('collapse_icon');
            event.currentTarget.parentElement.nextSibling.classList.remove('collapse');
        }
    }

    showMessage(tit, msg, type) {
        this.dispatchEvent(
            new ShowToastEvent({
                title: tit,
                message: msg,
                variant: type
            })
        );
    }

    handleView(id) {
        if (!this.isInternal) {
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    name: "Warranty_Return_Claim_Detail__c"
                },
                state: {
                    c__warrantyreturnclaimid: id,
                    c__mode: "view"
                }
            });
        } else {
            this[NavigationMixin.Navigate]({
                type: 'standard__recordPage',
                attributes: {
                    recordId: id,
                    objectApiName: 'Warranty_Return_Claim__c',
                    actionName: 'view'
                }
            });
        }
    }
}