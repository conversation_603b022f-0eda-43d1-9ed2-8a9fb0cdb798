({
	doInit: function (component, event, helper) {
		component.set("v.isBusy", true);
		console.log("defaultPaymentTerm0--->" + component.get("v.defaultPaymentTerm"));
		component.set("v.currencySymbol", $A.get("$Locale.currencyCode"));
		component.set("v.orderTypeVal", component.get("v.orderTypeVal"));
		if (!component.get("v.quotationItemList")) {
			component.set("v.quotation.Total_Quantity__c", 0);
			component.set("v.quotation.Product_Price__c", 0.0);
			component.set("v.showFreeShippingMsg", false);
			component.set("v.isWaivedFreight", false);
		} else {
			var waivedFee = component.get("v.quotation.Freight_Fee_To_Be_Waived__c");
			waivedFee = isNaN(waivedFee) ? 0 : waivedFee;
			var freightTargetFee = component.get("v.quotation.Freight_Target_Fee__c");
			freightTargetFee = isNaN(freightTargetFee) ? 0 : freightTargetFee;
			if (freightTargetFee == 0) {
				component.set("v.showFreeShippingMsg", false);
			} else {
				if (waivedFee > 0) {
					component.set("v.showFreeShippingMsg", true);
					component.set("v.isWaivedFreight", false);
				} else {
					component.set("v.showFreeShippingMsg", true);
					component.set("v.isWaivedFreight", true);
				}
			}
			var quotationItemList = component.get("v.quotationItemList");
			quotationItemList.forEach(function (pItem) {
				if (pItem.Lanch_Date__c > component.get("v.quotation.Expected_Delivery_Date__c")) {
					pItem.shipDateGreaterLanuchDate = true;
					pItem.minSelectDate = pItem.Lanch_Date__c;
				}
			});
			helper.calculateTotal(component);

			var recordId = helper.getUrlParameter("recordId");
			if (recordId) {
				helper.initialAvailablePromotion(component);
				helper.initialWholeOrderPromotion(component);
                helper.initialPaymentTermPromotion(component);
                helper.getProductSellable(component, event, helper);
                helper.getCurrentLaunchDate(component);
                helper.checkPromotionValid(component, event, helper);
                helper.getCurrentPriceList(component, event, helper);


			}
		}

		var brandList = component.get("v.brandScopeOpt");
		if (brandList && brandList.length == 1) {
			component.set("v.brandScope", brandList[0].value);
		}

		if (component.get("v.brandScope")) {
			component.set("v.isShowTerm", true);
			component.set("v.disableBtn", false);
			component.set("v.disableDate", false);
			component.set("v.uploadDisabed", false);
		}
		var orderTypeVal = component.get("v.orderTypeVal");
		if (orderTypeVal == "Y") {
			component.set("v.isDropShip", true);
		} else {
			component.set("v.isDropShip", false);
		}

		helper.generateCondtion(component, component.get("v.brandScope"), component.get("v.isDropShip"));

		helper.rebindPaymentHandler(component, event, helper, false);
		if (component.get("v.paymentTermValue")) {
			let paymentTermValue = component.get("v.paymentTermValue");
			component.set("v.paymentTermValue", null);
			component.set("v.paymentTermValue", paymentTermValue);
		} else if (!component.get("v.multipleOptions")) {
			if (component.get("v.brandScope")) {
				helper.getPaymentFreightRule(component, event, helper);
			}
			if (component.get("v.quotation.Payment_Term__c")) {
				component.set("v.paymentTermValue", component.get("v.quotation.Payment_Term__c"));
			}
		}
		component.set("v.isBusy", false);
	},
	rowFocus: function (component, event, helper) {
		var indexRow = event.currentTarget.id;
		component.set("v.operationRow", indexRow);
	},
	addItem: function (component, event, helper) {
		var today = $A.localizationService.formatDate(new Date(), "YYYY-MM-DD");
		var quotationItemList = component.get("v.quotationItemList");
		var newOrderItem = {
			"Product__c": "",
			"Brand__c": "",
			"Ship_Date__c": component.get("v.quotation.Expected_Delivery_Date__c"),
			"Quantity__c": "1",
			"List_Price__c": "0.00",
			"Discount_Amount__c": "0.00",
			"minSelectDate": component.get("v.minSelectDate"),
			"Unit_Price__c": "0.00",
			"Sub_Total__c": "0.00",
			"promotionFilterCondition": '[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Error"}]',
			"shipDateGreaterLanuchDate": false
		};
		quotationItemList.push(newOrderItem);
		helper.generateCondtion(component, component.get("v.brandScope"), component.get("v.isDropShip"));
		component.set("v.quotationItemList", []);
		component.set("v.quotationItemList", quotationItemList);
	},
	onSelectProd: function (component, event, helper) {
		// helper.getPaymentTermRule(component);
		var productId = event.getSource().get("v.value");
		if (productId) {
			component.set("v.isBusy", true);
			var action = component.get("c.getPriceBook");
			action.setParams({
				"prodId": productId,
				"customerId": component.get("v.customerId"),
				"orderType": component.get("v.orderTypeVal")
			});
			action.setCallback(this, function (response) {
				var state = response.getState();
				if (state === "SUCCESS") {
					var results = response.getReturnValue();
					if (results) {
						var data = JSON.parse(results);
						if (data) {
							var quotationItemList = component.get("v.quotationItemList");
							var index = component.get("v.operationRow");

							component.set("v.ExpirateDate", data.ExpirateDate);

							if (data.OrgCode) {
								component.set("v.OrgCode", data.OrgCode);
							}
							var orderType = component.get("v.orderTypeVal");
							var orgcode = component.get("v.OrgCode");
							var ExpirateDate = component.get("v.ExpirateDate");
							if (data.product) {
								//yanko Start
								if (data.product.CS_Exchange_Rate__c && orgcode != "CCA") {
									quotationItemList[index].CS_Exchange_Rate__c = data.product.CS_Exchange_Rate__c;
									quotationItemList[index].Quantity__c = quotationItemList[index].CS_Exchange_Rate__c;
								} else {
									quotationItemList[index].CS_Exchange_Rate__c = 1;
									quotationItemList[index].Quantity__c = 1;
								}
								quotationItemList[index].ExpirateDate = data.ExpirateDate;
								quotationItemList[index].Lanch_Date__c = data.product.Lanch_Date__c;
								if (quotationItemList[index].Lanch_Date__c > quotationItemList[index].Ship_Date__c) {
									quotationItemList[index].Ship_Date__c = quotationItemList[index].Lanch_Date__c;
									quotationItemList[index].shipDateGreaterLanuchDate = true;
									quotationItemList[index].minSelectDate = quotationItemList[index].Lanch_Date__c;
								}
								//Yanko End
								quotationItemList[index].Brand__c = data.product.Brand_Name__c;
								quotationItemList[index].ProductCode__c = data.product.ProductCode;
								quotationItemList[index].Gross_Weight__c = data.product.Weight__c == undefined ? 0.0 : data.product.Weight__c;
								quotationItemList[index].Is_Over_Size_Product__c = data.product.OverSize__c;
								//calvin start
								quotationItemList[index].counterror = false;
								console.log("index", index, component.get("v.model"));
								if (component.get("v.model") == Number(index) + 1) {
									component.set("v.showcaseqtyalert", false);
								}
								// end
								console.log("Gross Weight--->" + quotationItemList[index].Gross_Weight__c);
							}
							var unitPrice = 0.0;
							if (data.priceBookEntry) {
								unitPrice = data.priceBookEntry.UnitPrice;
								quotationItemList[index].Price_Book__c = data.priceBookEntry.Pricebook2Id;
							}

							if (unitPrice) {
								quotationItemList[index].List_Price__c = unitPrice.toFixed(2);
								quotationItemList[index].Unit_Price__c = unitPrice.toFixed(2);

								var quantity = quotationItemList[index].Quantity__c;
								var subtotal = unitPrice * quantity;
								quotationItemList[index].Sub_Total__c = subtotal.toFixed(2);
							} else {
								quotationItemList[index].List_Price__c = 0.0;
								quotationItemList[index].Unit_Price__c = 0.0;
								quotationItemList[index].Sub_Total__c = 0.0;
							}
							quotationItemList[index].Discount_Amount__c = 0.0;
							quotationItemList[index].Promo_Discount_Amount__c = 0.0;
							quotationItemList[index].Whole_Order_Promo_Discount_Amount__c = 0.0;

							if (quotationItemList[index].PromotionName__c) {
								helper.removePromotionQuotationItem(quotationItemList, index, component);
								helper.clearPromotionStatus(quotationItemList, index);
							}

							quotationItemList[index].promotionList = data.promotionList;
							if (data.promotionList && data.promotionList.length > 0) {
								quotationItemList[index].HasPromo = true;
								var promotionCodesStr = "";
								data.promotionList.forEach(function (pItem) {
									promotionCodesStr += "'" + pItem.promotion.Promo_Code__c + "',";
								});
								var strCodes = promotionCodesStr.slice(0, -1);
								quotationItemList[index].promotionFilterCondition =
									'[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Open"},{"FieldName":"Promo_Code__c", "Condtion":"IN", "Value":"(' +
									strCodes +
									')"}]';
							} else {
								quotationItemList[index].HasPromo = false;
								quotationItemList[index].promotionFilterCondition =
									'[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Error"}]';
							}

							quotationItemList[index].Is_Initial__c = true;
							//component.set('v.quotationItemList', quotationItemList);

							helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
							helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

							component.set("v.quotationItemList", []);
							component.set("v.quotationItemList", quotationItemList);

							helper.calculateTotal(component);
							component.set("v.isBusy", false);
						}
					}
				} else {
					component.set("v.isBusy", false);
					var errors = response.getError();
					if (errors) {
						if (errors[0] && errors[0].message) {
							console.log("ERROR: " + errors[0].message);
						}
					} else {
						console.log("ERROR: Unknown error");
					}
				}
			});
			$A.enqueueAction(action);
		}
	},
	// handleCheckDate:function(component,event,helper){
	//     var quotationItemList = component.get('v.quotationItemList');
	//     var index = event.getSource().get('v.name');
	//     if(quotationItemList[index].Ship_Date__c < quotationItemList[index].Lanch_Date__c) {
	//         quotationItemList[index].shipDateGreaterLanuchDate = true;
	//     }else{
	//         quotationItemList[index].shipDateGreaterLanuchDate = false;
	//     }
	//     component.set('v.quotationItemList', quotationItemList);
	// },
	doSave: function (component, event, helper) {
		if (!helper.checkOrderQty(component)) {
			return;
		}
		helper.doSaveAction(component, event, true);
	},
	nextStep: function (component, event, helper) {
		if (!helper.checkOrderQty(component)) {
			return;
		}
		helper.doSaveAction(component, event, false);
	},
	previousStep: function (component) {
		var currentStep = component.get("v.currentStep");
		component.set("v.currentStep", currentStep - 1);
	},
	calculateCurrencyFormat: function (component, event, helper) {
		var quotationItemList = component.get("v.quotationItemList");
		var index = component.get("v.operationRow");
		var unitPrice = quotationItemList[index].Unit_Price__c;
		if (!isNaN(Number(unitPrice))) {
			quotationItemList[index].Unit_Price__c = helper.currencyFormat(unitPrice);
			component.set("v.quotationItemList", quotationItemList);
		}
	},
	calculateSubTotal: function (component, event, helper) {
		console.log('update qty===================');
		var hasPopup = false;
		var quotationItemList = component.get("v.quotationItemList");
		if (quotationItemList && quotationItemList.length > 0) {
			// var index = component.get('v.operationRow');
			var index = event.getSource().get("v.name");
			var qItem = quotationItemList[index];
			var listPrice = quotationItemList[index].List_Price__c;
			var unitPrice = quotationItemList[index].Unit_Price__c;
			var quantity = quotationItemList[index].Quantity__c;
			// var ExpirateDate = quotationItemList[index].ExpirateDate;
			var ExpirateDate = component.get("v.ExpirateDate");

			if (Number(quotationItemList[index].Quantity__c) % 1 != 0) {
				var toastEvent = $A.get("e.force:showToast");
				toastEvent.setParams({
					"title": "Warning!",
					"message": "quantity must be an integer ",
					"type": "Warning"
				});
				toastEvent.fire();
				quotationItemList[index].Quantity__c = quotationItemList[index].CS_Exchange_Rate__c;
			}

			//for buyqty
			var buyqty = 1;
			var productBuyQty = 1;

			if (quotationItemList[index].Promotion) {
				if (quotationItemList[index].Promotion.ruleList) {
					for (var i = 0; i < quotationItemList[index].Promotion.ruleList.length; i++) {
						if (quotationItemList[index].Promotion.ruleList[i] && quotationItemList[index].Promotion.ruleList[i].thresholdList) {
							for (var j = 0; j < quotationItemList[index].Promotion.ruleList[i].thresholdList.length; j++) {
								// add haibo: 新增 Therthod 为By Quantity of Specific Product && product buyQty, 按 product buyQty成倍修改数量（应用promotion时）
                                for(var k=0; k<quotationItemList[index].Promotion.ruleList[i].thresholdList[j].products.length; k++){
                                    //BUY QTY promotion如果是price Discount 或者 Price Break，查找BuyQty
                                    if(quotationItemList[index].Promotion.promotion.Promotion_Type__c == "Price Discount" || quotationItemList[index].Promotion.promotion.Promotion_Type__c == "Price Break"){
                                        if(quotationItemList[index].Promotion.ruleList[i].thresholdList[j]
                                            && ['By_Quantity_of_Specific_Product', 'By_Amount_of_Specific_Product'].includes(quotationItemList[index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName)
                                            && quotationItemList[index].Promotion.ruleList[i].thresholdList[j].products[0].Product__c === quotationItemList[index].Product__c
                                        ){
                                            buyqty = quotationItemList[index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
                                        }
                                    }
                                    if(quotationItemList[index].Promotion.ruleList[i].thresholdList[j]
                                        && (quotationItemList[index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName == 'By_Quantity_of_Specific_Product'
                                        && quotationItemList[index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c)){
                                            if (quotationItemList[index].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == qItem.Product__c) {
                                                // 根据promotion type获取不同的buy qty
                                                if (quotationItemList[index].Promotion.promotion.Promotion_Type__c == 'Price Discount') {
                                                    productBuyQty = quotationItemList[index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
                                                } else {
                                                    productBuyQty = quotationItemList[index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c;
                                                }
                                            }
                                    }
                                }
							}
						}
					}
				}
			}
			if(buyqty && buyqty != 0 && buyqty != 1 && Number(productBuyQty) == 1){
                //for buyqty
                if(quotationItemList[index].Quantity__c % Number(buyqty) != 0){
                    for(var a = 0; a < buyqty; a++){
                        if((Number(quotationItemList[index].Quantity__c) + a) % Number(buyqty) == 0){
                            quotationItemList[index].Quantity__c = Number(quotationItemList[index].Quantity__c) + a;
                        }
                    }
                }
            } else if (productBuyQty && productBuyQty != 0 && productBuyQty != 1) {
                if(Number(quotationItemList[index].Quantity__c) % Number(productBuyQty) != 0){
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Warning!",
                        "message": `This product must be ordered by buy qty (${productBuyQty}) if you apply this promotion.`,
                        "type": "Warning"
                    });
                    toastEvent.fire();
                    // productBuyQty向上取整
                    quotationItemList[index].Quantity__c = Math.ceil(Number(quotationItemList[index].Quantity__c) / Number(productBuyQty)) * Number(productBuyQty);
                }
            }
			var woqty = quotationItemList[index].Quantity__c;
			for (i = 0; i < quotationItemList.length; i++) {
				if (quotationItemList[i].ProductCode__c == quotationItemList[index].ProductCode__c) {
					if (i != index) {
						var woqty = Number(woqty) + Number(quotationItemList[i].Quantity__c);
						var needcheck = 1;
					}
				}
			}
			if (needcheck == 1) {
				var promremainder = Number(woqty) % Number(quotationItemList[index].CS_Exchange_Rate__c);
			} else {
				var promremainder = Number(quotationItemList[index].Quantity__c) % Number(quotationItemList[index].CS_Exchange_Rate__c);
			}
			if (promremainder != 0 && ExpirateDate == true) {
				var needcheckqty = false;
				var notonlyfreegoods = false;
				for (var x = 0; x < quotationItemList.length; x++) {
					if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[index].Product__c) {
						var notonlyfreegoods = true;
						if (quotationItemList[x].Promotion) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
														// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						} else {
							var needcheckqty = true;
						}
					}
				}

				if (notonlyfreegoods == false) {
					var needcheckfreegoodsqty = false;
					for (var x = 0; x < quotationItemList.length; x++) {
						if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[index].Product__c) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
														// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckfreegoodsqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					if (needcheckfreegoodsqty == true) {
						for (var i = 0; i < quotationItemList.length; i++) {
							if (quotationItemList[i].ProductCode__c == quotationItemList[index].ProductCode__c) {
								quotationItemList[i].counterror = true;
							}
						}
					}
				} else {
					if (needcheckqty == true) {
						for (var i = 0; i < quotationItemList.length; i++) {
							if (quotationItemList[i].ProductCode__c == quotationItemList[index].ProductCode__c) {
								quotationItemList[i].counterror = true;
							}
						}
					}
				}
			} else {
				quotationItemList[index].counterror = false;
				for (var i = 0; i < quotationItemList.length; i++) {
					if (quotationItemList[i].ProductCode__c == quotationItemList[index].ProductCode__c) {
						quotationItemList[i].counterror = false;
					}
				}
			}

			var subtotal = Number(quotationItemList[index].List_Price__c) * Number(quotationItemList[index].Quantity__c);
			quotationItemList[index].Sub_Total__c = subtotal.toFixed(2);
			// // change0227 没有promotion情况下 加减会错误更新subtotal
			// helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
			// helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);
			helper.calculateTotal(component);
			component.set("v.quotationItemList", quotationItemList);

			if (quotationItemList[index].Promotion__c) {
				var subtotal = unitPrice * quantity;
				var promotion = quotationItemList[index].Promotion;
				var hasRemoveItem = false;

				//have a activated promotion
				var ruleName = quotationItemList[index].Promotion_Rule_Name__c;
				if (promotion && (quotationItemList[index].HasPromo || quotationItemList[index].isThreshold)) {
					var isMeetThreshold = helper.isMeetThreshold(quotationItemList, index, promotion);
					if (promotion.promotion.Promotion_Type__c == "BOGO" || promotion.promotion.Promotion_Type__c == "Full Pallet Promo") {
						if (isMeetThreshold) {
							helper.updateThresholdItem(quotationItemList, index, promotion);
							helper.updateOfferingItems(quotationItemList, index, promotion);
						} else {
							var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
							helper.removePromotionQuotationItem(quotationItemList, index, component);
							helper.clearPromotionStatus(quotationItemList, initialIndex);
							hasRemoveItem = true;
						}
					} else if (promotion.promotion.Promotion_Type__c == "Price Break") {
						if (isMeetThreshold) {
							var oldRuleName = quotationItemList[index].Promotion_Rule_Name__c;
							var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, promotion);
							var newRuleName = avilableRuleItem.ruleName;
							if (oldRuleName == newRuleName) {
								helper.updateThresholdItem(quotationItemList, index, promotion);
								helper.updateOfferingItems(quotationItemList, index, promotion);
							} else {
								if (helper.isPoolFreeGoods(avilableRuleItem)) {
									helper.clearSelectedProducts(promotion);
									var offeringOpts = [];
									avilableRuleItem.offeringList.forEach(function (oItem) {
										if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
											offeringOpts.push(oItem);
										}
									});

									quotationItemList[index].hasPool = true;
									component.set("v.offeringOpts", offeringOpts);
									component.set("v.selectedPromotion", promotion);
									component.set("v.showOfferingProduct", true);
									hasPopup = true;
									component.set("v.editRow", index);
									return;
								} else {
									helper.removePromotionQuotationItem(quotationItemList, index, component);
									var indexObj = {
										"startindex": 0
									};
									quotationItemList[index].hasPool = false;
									helper.addThresholdItems(quotationItemList, index, indexObj, component);
									helper.updateThresholdItem(quotationItemList, index, promotion);
									helper.addOfferingItems(quotationItemList, index, promotion, indexObj, component);
									hasRemoveItem = true;
								}
							}
						} else {
							helper.removePromotionQuotationItem(quotationItemList, index, component);
							helper.clearPromotionStatus(quotationItemList, index);
							hasRemoveItem = true;
						}
					} else if (promotion.promotion.Promotion_Type__c == "Price Discount") {
						if (isMeetThreshold) {
							helper.updateThresholdItem(quotationItemList, index, promotion);
						} else {
							var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
							helper.removePromotionQuotationItem(quotationItemList, index, component);
							helper.clearPromotionStatus(quotationItemList, initialIndex);
							hasRemoveItem = true;
						}
					} else if (promotion.promotion.Promotion_Type__c == "Mix & Match") {
						if (isMeetThreshold) {
							// helper.updateThresholdItem(quotationItemList, _index, promotion);
							helper.updateThresholdItemMixMatch(quotationItemList, index, promotion);
							helper.updateOfferingItems(quotationItemList, index, promotion);
						} else {
							quotationItemList.forEach(function (qItem) {
								if (qItem.Promotion_Rule_Name__c == ruleName && qItem.isMix) {
									qItem.isMeet = false;
									qItem.Promo_Discount_Amount__c = 0.0;
									if (qItem.Quantity__c && qItem.Quantity__c > 0) {
										qItem.Sub_Total__c = (
											Number(qItem.List_Price__c * qItem.Quantity__c) +
											Number(qItem.Discount_Amount__c) +
											Number(qItem.Promo_Discount_Amount__c) +
											Number(qItem.Whole_Order_Promo_Discount_Amount__c)
										).toFixed(2);
										qItem.Unit_Price__c = Number(qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2);
									} else {
										qItem.Sub_Total__c = 0.0;
										qItem.Unit_Price__c = qItem.List_Price__c;
									}
								}
							});
							helper.removeOfferingItemsOutOfPool(quotationItemList, index, promotion);
						}
					} else if (promotion.promotion.Promotion_Type__c == "Others") {
						if (isMeetThreshold) {
							// check threshold has mix & match, if has, use mix & match method
							if (helper.hasMixMatch(promotion)) {
								helper.updateThresholdItemMixMatch(quotationItemList, index, promotion);
							} else {
								helper.updateThresholdItem(quotationItemList, index, promotion);
							}
							helper.updateOfferingItems(quotationItemList, index, promotion);
						} else {
							if (helper.hasMixMatch(promotion)) {
								quotationItemList.forEach(function (qItem) {
									if (qItem.Promotion_Rule_Name__c == ruleName && (qItem.Is_Initial__c || qItem.isThreshold)) {
										qItem.Promo_Discount_Amount__c = 0.0;
										if (qItem.Quantity__c && qItem.Quantity__c > 0) {
											qItem.Sub_Total__c = (
												Number(qItem.List_Price__c * qItem.Quantity__c) +
												Number(qItem.Discount_Amount__c) +
												Number(qItem.Promo_Discount_Amount__c) +
												Number(qItem.Whole_Order_Promo_Discount_Amount__c)
											).toFixed(2);
											qItem.Unit_Price__c = Number(qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2);
										} else {
											qItem.Sub_Total__c = 0.0;
											qItem.Unit_Price__c = qItem.List_Price__c;
										}
										if (qItem.isMix) {
											qItem.isMeet = false;
										}
									}
								});
								helper.removeOfferingItemsOutOfPool(quotationItemList, index, promotion);
							} else {
								var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
								helper.removePromotionQuotationItem(quotationItemList, index, component);
								helper.clearPromotionStatus(quotationItemList, initialIndex);
								hasRemoveItem = true;
							}
						}
					}
				} else if (promotion && quotationItemList[index].isOffering && quotationItemList[index].isPool) {
					if (quotationItemList[index].Promotion__c == quotationItemList[index].Whole_Order_Promotion__c) {
						quotationItemList[index].Whole_Order_Promo_Discount_Amount__c = Number(quotationItemList[index].List_Price__c * quotationItemList[index].Quantity__c) * -1;
					} else {
						quotationItemList[index].Promo_Discount_Amount__c = Number(quotationItemList[index].List_Price__c * quotationItemList[index].Quantity__c) * -1;
					}

					quotationItemList[index].Sub_Total__c = 0.0;
				}

				if (hasPopup) {
					return;
				}

				// if(promotion && quotationItemList[index].isOffering && quotationItemList[index].isPool){
				//     quotationItemList[index].Promo_Discount_Amount__c = Number(quotationItemList[index].List_Price__c * quotationItemList[index].Quantity__c) * -1;
				//     quotationItemList[index].Sub_Total__c = 0.00;
				// }
				if (!hasRemoveItem) {
					if (promotion && quotationItemList[index].Promotion) {
						var ruleItem = promotion.ruleList[0];
						if (promotion.promotion.Promotion_Type__c == "Price Break") {
							var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, promotion);
							if (avilableRuleItem) {
								ruleItem = avilableRuleItem;
							}
						}
						if (quotationItemList[index].Promotion__c != quotationItemList[index].Whole_Order_Promotion__c) {
							if (helper.isPoolFreeGoods(ruleItem)) {
								helper.checkMeetOfferingPoolLimit(quotationItemList, index, promotion);
							}
						} else {
							if (helper.isPoolFreeGoods(ruleItem)) {
								helper.checkMeetWholeOrderPromoOfferingPoolLimit(quotationItemList, component);
								component.set("v.quotationItemList", quotationItemList);
								helper.calculateTotal(component);
								return;
							}
						}
					}
				}

				helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
				helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

				if (hasRemoveItem) {
					component.set("v.quotationItemList", []);
				}
			}
			//Yanko End Input Quantity

			if (quotationItemList[index].counterror == false) {
				for (var s = 0; s < quotationItemList.length; s++) {
					var woqty = quotationItemList[s].Quantity__c;
					for (j = 0; j < quotationItemList.length; j++) {
						if (quotationItemList[j].ProductCode__c == quotationItemList[s].ProductCode__c) {
							if (j != s) {
								var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
								var needcheck = 1;
							}
						}
					}
					if (needcheck == 1) {
						var validateqty = Number(woqty) % Number(quotationItemList[s].CS_Exchange_Rate__c);
					} else {
						var validateqty = Number(quotationItemList[s].Quantity__c) % Number(quotationItemList[s].CS_Exchange_Rate__c);
					}

					if (validateqty != 0 && ExpirateDate == true) {
						var needcheckqty = false;
						var notonlyfreegoods = false;
						for (var x = 0; x < quotationItemList.length; x++) {
							if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
								var notonlyfreegoods = true;
								if (quotationItemList[x].Promotion) {
									if (quotationItemList[x].Promotion.ruleList) {
										for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
											if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
												for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
													if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
														for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
															if (
																quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
																quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
																quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
															) {
																var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
																// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
																if (multiplecontrol == "Inner Box Multiple") {
																	var needcheckqty = true;
																}
															}
														}
													}
												}
											}
										}
									}
								} else {
									var needcheckqty = true;
								}
							}
						}

						if (notonlyfreegoods == false) {
							var needcheckfreegoodsqty = false;
							for (var x = 0; x < quotationItemList.length; x++) {
								if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
									if (quotationItemList[x].Promotion.ruleList) {
										for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
											if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
												for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
													if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
														for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
															if (
																quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
																quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
																quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
															) {
																var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
																// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
																if (multiplecontrol == "Inner Box Multiple") {
																	var needcheckfreegoodsqty = true;
																}
															}
														}
													}
												}
											}
										}
									}
								}
							}
							if (needcheckfreegoodsqty == true) {
								quotationItemList[s].counterror = true;
							} else {
								quotationItemList[s].counterror = false;
							}
						} else {
							if (needcheckqty == true) {
								quotationItemList[s].counterror = true;
							} else {
								quotationItemList[s].counterror = false;
							}
						}
					} else {
						quotationItemList[s].counterror = false;
					}
				}
			}
		}
		component.set("v.quotationItemList", quotationItemList);
		helper.calculateTotal(component);
		helper.getPaymentTermRule(component);
	},
	handleDelete: function (component, event, helper) {
		var quotationItemList = component.get("v.quotationItemList");
		var deleteIndex = component.get("v.operationRow");
		if (deleteIndex != undefined) {
			var recordId = quotationItemList[deleteIndex].Id;
			var promotionCode = quotationItemList[deleteIndex].PromotionName__c;
			var ruleName = quotationItemList[deleteIndex].Promotion_Rule_Name__c;
			var recordIds = [];
			if (recordId) {
				if (promotionCode) {
					quotationItemList.forEach(function (qItem) {
						if (qItem.Promotion_Rule_Name__c == ruleName && qItem.Id) {
							recordIds.push(qItem.Id);
						}
					});
				} else {
					recordIds.push(recordId);
				}
				var action = component.get("c.deleteQuotation");
				action.setParams({
					"recordIds": recordIds
				});
				action.setCallback(this, function (response) {
					var state = response.getState();
					if (state === "SUCCESS") {
						var results = response.getReturnValue();
						if (results) {
							//quotationItemList.splice(deleteIndex, recordIds.length);
							var index = quotationItemList.length - 1;
							if (ruleName) {
								for (; index >= 0; index--) {
									var qItem = quotationItemList[index];
									if (qItem.Promotion_Rule_Name__c == ruleName) {
										quotationItemList.splice(index, 1);
									}
								}
							} else {
								quotationItemList.splice(deleteIndex, 1);
							}

							helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
							helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

							component.set("v.quotationItemList", []);
							component.set("v.quotationItemList", quotationItemList);
						}
					} else {
						var errors = response.getError();
						if (errors) {
							if (errors[0] && errors[0].message) {
								alert("ERROR: " + errors[0].message);
							}
						} else {
							alert("ERROR: Unknown error");
						}
					}

					helper.calculateTotal(component);
				});
				$A.enqueueAction(action);
			} else {
				//delete related threshold and offering items
				if (promotionCode) {
					var index = quotationItemList.length - 1;
					for (; index >= 0; index--) {
						var qItem = quotationItemList[index];
						if (qItem.Promotion_Rule_Name__c == ruleName) {
							quotationItemList.splice(index, 1);
							// calvin start
							if (component.get("v.model") == Number(index) + 1) {
								component.set("v.showcaseqtyalert", false);
							}
							// end
						}
					}
				} else {
					quotationItemList.splice(deleteIndex, 1);
					// calvin start
					if (component.get("v.model") == Number(deleteIndex) + 1) {
						component.set("v.showcaseqtyalert", false);
					}
					// end
				}

				helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
				helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);
				component.set("v.quotationItemList", []);
				component.set("v.quotationItemList", quotationItemList);
			}
		}
		helper.calculateTotal(component);

		//删除item后做一次全局校验：
		var ExpirateDate = component.get("v.ExpirateDate");
		var quotationItemList = component.get("v.quotationItemList");
		for (var s = 0; s < quotationItemList.length; s++) {
			var woqty = quotationItemList[s].Quantity__c;
			for (j = 0; j < quotationItemList.length; j++) {
				if (quotationItemList[j].ProductCode__c == quotationItemList[s].ProductCode__c) {
					if (j != s) {
						var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
						var needcheck = 1;
					}
				}
			}
			if (needcheck == 1) {
				var validateqty = Number(woqty) % Number(quotationItemList[s].CS_Exchange_Rate__c);
			} else {
				var validateqty = Number(quotationItemList[s].Quantity__c) % Number(quotationItemList[s].CS_Exchange_Rate__c);
			}

			if (validateqty != 0 && ExpirateDate == true) {
				var needcheckqty = false;
				var notonlyfreegoods = false;
				for (var x = 0; x < quotationItemList.length; x++) {
					if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
						var notonlyfreegoods = true;
						if (quotationItemList[x].Promotion) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
														// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						} else {
							var needcheckqty = true;
						}
					}
				}

				if (notonlyfreegoods == false) {
					var needcheckfreegoodsqty = false;
					for (var x = 0; x < quotationItemList.length; x++) {
						if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
														// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckfreegoodsqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					if (needcheckfreegoodsqty == true) {
						quotationItemList[s].counterror = true;
					} else {
						quotationItemList[s].counterror = false;
					}
				} else {
					if (needcheckqty == true) {
						quotationItemList[s].counterror = true;
					} else {
						quotationItemList[s].counterror = false;
					}
				}
			} else {
				quotationItemList[s].counterror = false;
			}
		}
		component.set("v.quotationItemList", quotationItemList);
	},

	brandChange: function (component, event, helper) {
		component.set("v.uploadDisabed", false);
		component.set("v.disableDate", false);
		var expectDate = component.get("v.quotation.Expected_Delivery_Date__c");
		if (expectDate != null && expectDate != undefined) {
			component.set("v.disableBtn", false);
		}
		var oldValue = event.getParam("oldValue");
		component.set("v.oldBrandName", oldValue);
		var val = event.getParam("value");
		var purchaseList = component.get("v.quotationItemList");
		var purchaseOrder = component.get("v.quotation");
		var vaildOrder = false;
		component.set("v.isShowTerm", true);
		if (oldValue != val) {
			helper.getPaymentFreightRule(component, event, helper);
		}
		purchaseList.forEach(function (ele) {
			if (ele.Product__c != "" && ele.Product__c != undefined && ele.Quantity__c != "" && ele.Quantity__c != undefined) {
				vaildOrder = true;
			}
		});
		if (oldValue != null && oldValue != "" && val != oldValue && vaildOrder) {
			component.set("v.showEditModal", true);
			var content =
				"The freight term or payment term or warehouse of the authorized brand " +
				oldValue +
				" is different with the authorized brand " +
				val +
				", if you choose the new brand the order you are editing would be saved as a draft " +
				"and a new order would be created for the new authorized brand";
			component.set("v.modalContent", content);
		}
		helper.generateCondtion(component, component.get("v.brandScope"), component.get("v.isDropShip"));
	},

	ecpectedDateChange: function (component, event, helper) {
        component.set("v.disableBtn", false);
        component.set("v.showMessage", false);
		var expectDate = component.get("v.quotation.Expected_Delivery_Date__c");
		if (expectDate == null || expectDate == "" || expectDate == undefined) {
			component.set("v.disableBtn", true);
		}
    },

    displayMessage: function (component, event, helper) {
		component.set("v.showMessage", false);
	},
	displayItemMessage: function (component, event, helper) {
		var pIndex = event.getSource().get("v.name");
		var quotationItemList = component.get("v.quotationItemList");
		quotationItemList[pIndex].showMessage = false;
		component.set("v.quotationItemList", quotationItemList);
	},


	closeModal: function (component, event, helper) {
		component.set("v.brandScope", component.get("v.oldBrandName"));
		component.set("v.showEditModal", false);
	},

	confirmChange: function (component, event, helper) {
		var action = component.get("c.deleteChangedBrandPO");
		action.setParams({
			"poInfo": JSON.stringify(component.get("v.quotation"))
		});
		action.setCallback(this, function (response) {
			var state = response.getState();
			if (state === "SUCCESS") {
				var results = response.getReturnValue();
				results = JSON.parse(results);
				if (!results.isSuccess) {
					var toastEvent = $A.get("e.force:showToast");
					toastEvent.setParams({
						"title": "Error!",
						"message": results.errorMsg,
						"type": "Error"
					});
					toastEvent.fire();
				} else {
					component.set("v.showEditModal", false);
					component.set("v.quotationItemList", []);
					component.set("v.quotation", results.result);

					helper.clearWholeOrderPromotion(component);
					helper.getPaymentTermRule(component);
				}
			} else {
				var toastEvent = $A.get("e.force:showToast");
				toastEvent.setParams({
					"title": "Error!",
					"message": response.getError(),
					"type": "Error"
				});
				toastEvent.fire();
			}
		});
		$A.enqueueAction(action);
	},
	rebindPayment: function (component, event, helper) {
		helper.rebindPaymentHandler(component, event, helper, false);
	},

	onDisplayPromo: function (component, event, helper) {
		var index = component.get("v.operationRow");
		var targetId = index + "Tip";
		var cmpTarget = document.getElementById(targetId);
		$A.util.removeClass(cmpTarget, "hideTip");
		$A.util.addClass(cmpTarget, "promotionTip");
	},
	onHidePromo: function (component, event, helper) {
		var index = component.get("v.operationRow");
		var targetId = index + "Tip";
		var cmpTarget = document.getElementById(targetId);
		$A.util.removeClass(cmpTarget, "promotionTip");
		$A.util.addClass(cmpTarget, "hideTip");
	},
	onApplyPromo: function (component, event, helper) {
		var index = component.get("v.operationRow");
		var targetId = index + "Tip";
		var cmpTarget = document.getElementById(targetId);
		$A.util.removeClass(cmpTarget, "promotionTip");
		$A.util.addClass(cmpTarget, "hideTip");
		var pIndex = event.getSource().get("v.name");
		var quotationItemList = component.get("v.quotationItemList");
		var promotionList = quotationItemList[index].promotionList;
		var selectedPromotion = promotionList[pIndex];
		var qItem = quotationItemList[index];
        console.log(JSON.stringify(qItem), 'qItem==============');
        // add haibo
        console.log(JSON.stringify(quotationItemList), 'quotationItemList================');
        helper.counterrorCheck(quotationItemList);
		//the promotion has already been used.
		var isDuplicatePromo = helper.isDuplicatePromo(quotationItemList, selectedPromotion);
		if (isDuplicatePromo) {
			//the promotion has already been used.
			var toastEvent = $A.get("e.force:showToast");
			toastEvent.setParams({
				"title": "Warning!",
				"message": "ERROR: The promotion " + selectedPromotion.promotion.Promotion_Code_For_External__c + " has already been used!",
				"type": "Warning"
			});
			toastEvent.fire();
			return;
		}

		helper.clearSelectedProducts(selectedPromotion);
		component.set("v.selectedPromotion", selectedPromotion);
		quotationItemList[index].hasPool = helper.isPoolFreeGoods(selectedPromotion.ruleList[0]);
		if (quotationItemList[index].hasPool) {
			component.set("v.isPoolFreeGoods", true);
		} else {
			component.set("v.isPoolFreeGoods", false);
		}
		component.set("v.quotationItemList", quotationItemList);

		if (selectedPromotion.promotion.Promotion_Type__c == "BOGO") {
			quotationItemList[index].hasMix = false;
			quotationItemList[index].isMix = false;
			if (quotationItemList[index].hasPool) {
				var offeringOpts = [];
				selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
					if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
						offeringOpts.push(oItem);
					}
				});
				component.set("v.offeringOpts", offeringOpts);
				component.set("v.showOfferingProduct", true);
				component.set("v.quotationItemList", quotationItemList);
			} else {
				if (quotationItemList[index].PromotionName__c) {
					helper.removePromotionQuotationItem(quotationItemList, index, component);
					helper.clearPromotionStatus(quotationItemList, index);
				}
				quotationItemList[index].Promotion = selectedPromotion;
				quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
				quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
				quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
				quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
				var indexObj = {
					"startindex": 0
				};
				helper.addThresholdItems(quotationItemList, index, indexObj, component);
				helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);

				helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
				helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

				component.set("v.quotationItemList", []);
				component.set("v.quotationItemList", quotationItemList);
				helper.calculateTotal(component);
			}
		} else if (selectedPromotion.promotion.Promotion_Type__c == "Mix & Match") {
			var thresholdOpts = selectedPromotion.ruleList[0].thresholdList;
			if (thresholdOpts.length > 1) {
				component.set("v.isMultiThresholdProduct", true);
			} else {
				component.set("v.isMultiThresholdProduct", false);
			}
			thresholdOpts.forEach(function (tItem) {
				var products = tItem.products;
				products.forEach(function (pItem) {
					if (quotationItemList[index].ProductCode__c == pItem.Product__r.ProductCode) {
						pItem.selected = true;
					}
				});
			});
			component.set("v.thresholdOpts", thresholdOpts);

			if (quotationItemList[index].hasPool) {
				var offeringOpts = [];
				selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
					if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
						offeringOpts.push(oItem);
					}
				});
				component.set("v.offeringOpts", offeringOpts);
			}

			component.set("v.showThresholdProduct", true);
			component.set("v.quotationItemList", []);
			component.set("v.quotationItemList", quotationItemList);
		} else if (selectedPromotion.promotion.Promotion_Type__c == "Price Discount") {
			quotationItemList[index].hasMix = false;
			quotationItemList[index].isMix = false;
			if (quotationItemList[index].PromotionName__c) {
				helper.removePromotionQuotationItem(quotationItemList, index, component);
				helper.clearPromotionStatus(quotationItemList, index);
			}
			quotationItemList[index].Promotion = selectedPromotion;
			quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
			quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
			quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
			quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
			var indexObj = {
				"startindex": 0
			};
			helper.addThresholdItems(quotationItemList, index, indexObj, component);
			helper.updateThresholdItem(quotationItemList, index, selectedPromotion);

			helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
			helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

			component.set("v.quotationItemList", []);
			component.set("v.quotationItemList", quotationItemList);
			helper.calculateTotal(component);
		} else if (selectedPromotion.promotion.Promotion_Type__c == "Price Break") {
			var ruleItem = helper.getMiniRuleInPriceBreak(selectedPromotion);
			var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, selectedPromotion);
			if (avilableRuleItem) {
				ruleItem = avilableRuleItem;
			}
			var offeringOpts = [];
			ruleItem.offeringList.forEach(function (oItem) {
				if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
					offeringOpts.push(oItem);
				}
			});

			if (offeringOpts.length > 0) {
				quotationItemList[index].hasPool = true;
				quotationItemList[index].hasMix = false;
				quotationItemList[index].isMix = false;
				component.set("v.offeringOpts", offeringOpts);
				component.set("v.showOfferingProduct", true);
				component.set("v.quotationItemList", quotationItemList);
			} else {
				quotationItemList[index].hasMix = false;
				quotationItemList[index].isMix = false;
				if (quotationItemList[index].PromotionName__c) {
					helper.removePromotionQuotationItem(quotationItemList, index, component);
					helper.clearPromotionStatus(quotationItemList, index);
				}
				quotationItemList[index].Promotion = selectedPromotion;
				quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
				quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
				quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
				quotationItemList[index].Promotion_Rule_Name__c = ruleItem.ruleName;
				var indexObj = {
					"startindex": 0
				};
				helper.addThresholdItems(quotationItemList, index, indexObj, component);
				helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
				helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);

				helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
				helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

				component.set("v.quotationItemList", []);
				component.set("v.quotationItemList", quotationItemList);
				helper.calculateTotal(component);
			}
		} else if (selectedPromotion.promotion.Promotion_Type__c == "Full Pallet Promo") {
			if (quotationItemList[index].hasPool) {
				var offeringOpts = [];
				selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
					if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
						offeringOpts.push(oItem);
					}
				});
				component.set("v.offeringOpts", offeringOpts);
				component.set("v.showOfferingProduct", true);
				component.set("v.quotationItemList", quotationItemList);
			} else {
				quotationItemList[index].hasMix = false;
				quotationItemList[index].isMix = false;
				if (quotationItemList[index].PromotionName__c) {
					helper.removePromotionQuotationItem(quotationItemList, index, component);
					helper.clearPromotionStatus(quotationItemList, index);
				}
				quotationItemList[index].Promotion = selectedPromotion;
				quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
				quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
				quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
				quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
				var indexObj = {
					"startindex": 0
				};
				helper.addThresholdItems(quotationItemList, index, indexObj, component);
				helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
				helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);

				helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
				helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

				component.set("v.quotationItemList", []);
				component.set("v.quotationItemList", quotationItemList);
				helper.calculateTotal(component);
			}
		} else if (selectedPromotion.promotion.Promotion_Type__c == "Others") {
			var thresholdList = selectedPromotion.ruleList[0].thresholdList;
			var thresholdOpts = [];
			thresholdList.forEach(function (tItem) {
				if (tItem.threshold.RecordType.DeveloperName == "By_Mix_Match") {
					thresholdOpts.push(tItem);
					var products = tItem.products;
					products.forEach(function (pItem) {
						if (quotationItemList[index].ProductCode__c == pItem.Product__r.ProductCode) {
							pItem.selected = true;
						}
					});
				}
			});
			component.set("v.thresholdOpts", thresholdOpts);

			var offeringOpts = [];
			if (quotationItemList[index].hasPool) {
				selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
					if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
						offeringOpts.push(oItem);
					}
				});
			}
			component.set("v.offeringOpts", offeringOpts);

			if (thresholdOpts.length > 0) {
				component.set("v.showThresholdProduct", true);
			} else if (offeringOpts.length > 0) {
				component.set("v.showOfferingProduct", true);
			} else {
				quotationItemList[index].hasMix = false;
				quotationItemList[index].isMix = false;
				if (quotationItemList[index].PromotionName__c) {
					helper.removePromotionQuotationItem(quotationItemList, index, component);
				}
				quotationItemList[index].Promotion = selectedPromotion;
				quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
				quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
				quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
				quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
				var indexObj = {
					"startindex": 0
				};
				helper.addThresholdItems(quotationItemList, index, indexObj, component);
				helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
				helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);

				helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
				helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

				component.set("v.quotationItemList", []);
				component.set("v.quotationItemList", quotationItemList);
				helper.calculateTotal(component);
			}
		}
	},
	onSelectPromo: function (component, event, helper) {
		var index = component.get("v.operationRow");
		var promotionId = event.getSource().get("v.value");
		var quotationItemList = component.get("v.quotationItemList");
		if (!promotionId) {
			if (quotationItemList[index].Promotion__c) {
				helper.removePromotionQuotationItem(quotationItemList, index, component);
				var quotationItem = quotationItemList[index];
				quotationItem.hasPool = false;
				quotationItem.isPool = false;
				quotationItem.hasMix = false;
				quotationItem.isMix = false;
				quotationItem.isThreshold = false;
				quotationItem.isOffering = false;
				quotationItem.Promotion__c = undefined;
				quotationItem.Promotion = undefined;
				quotationItem.Promotion_Rule_Name__c = undefined;
				helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
				helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

				component.set("v.quotationItemList", []);
				component.set("v.quotationItemList", quotationItemList);
				helper.calculateTotal(component);
			}
			return;
		}

		if (promotionId && quotationItemList[index].Promotion__c == promotionId) {
			return;
		}

		var promotionList = quotationItemList[index].promotionList;
		var selectedPromotion;
		var isAvailable = false;
		if (promotionList && promotionList.length > 0) {
			promotionList.forEach(function (pItem) {
				if (pItem.promotion.Id == promotionId) {
					isAvailable = true;
					selectedPromotion = pItem;
				}
			});
		} else {
			isAvailable = false;
		}
		if (isAvailable) {
			var isDuplicatePromo = helper.isDuplicatePromo(quotationItemList, selectedPromotion);
			if (isDuplicatePromo) {
				//the promotion has already been used.
				var toastEvent = $A.get("e.force:showToast");
				toastEvent.setParams({
					"title": "Warning!",
					"message": "ERROR: The promotion " + selectedPromotion.promotion.Promotion_Code_For_External__c + " has already been used!",
					"type": "Warning"
				});
				toastEvent.fire();

				if (quotationItemList[index].Promotion__c) {
					promotionList.forEach(function (pItem) {
						if (pItem.promotion.Id == quotationItemList[index].Promotion__c) {
							quotationItemList[index].PromotionName__c = pItem.promotion.Promotion_Code_For_External__c;
						}
					});
				} else {
					quotationItemList[index].PromotionName__c = undefined;
				}
				component.set("v.quotationItemList", quotationItemList);
				return;
			}

			if (quotationItemList[index].Promotion__c && quotationItemList[index].Promotion__c != selectedPromotion.promotion.Id) {
				helper.removePromotionQuotationItem(quotationItemList, index, component);
				var quotationItem = quotationItemList[index];
				quotationItem.hasPool = false;
				quotationItem.isPool = false;
				quotationItem.hasMix = false;
				quotationItem.isMix = false;
				quotationItem.isThreshold = false;
				quotationItem.isOffering = false;
				quotationItem.Promotion__c = undefined;
				quotationItem.Promotion = undefined;
				quotationItem.Promotion_Rule_Name__c = undefined;
			}
			quotationItemList[index].Promotion = selectedPromotion;
			//quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
			quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
			quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
			quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;

			helper.clearSelectedProducts(selectedPromotion);
			component.set("v.selectedPromotion", selectedPromotion);
			quotationItemList[index].hasPool = helper.isPoolFreeGoods(selectedPromotion.ruleList[0]);
			if (quotationItemList[index].hasPool) {
				component.set("v.isPoolFreeGoods", true);
			} else {
				component.set("v.isPoolFreeGoods", false);
			}
			component.set("v.quotationItemList", quotationItemList);

			if (selectedPromotion.promotion.Promotion_Type__c == "BOGO") {
				quotationItemList[index].hasMix = false;
				quotationItemList[index].isMix = false;
				if (quotationItemList[index].hasPool) {
					var offeringOpts = [];
					selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
						if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
							offeringOpts.push(oItem);
						}
					});
					component.set("v.offeringOpts", offeringOpts);
					component.set("v.showOfferingProduct", true);
					component.set("v.quotationItemList", quotationItemList);
				} else {
					var indexObj = {
						"startindex": 0
					};
					helper.addThresholdItems(quotationItemList, index, indexObj, component);
					helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);

					helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
					helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

					component.set("v.quotationItemList", []);
					component.set("v.quotationItemList", quotationItemList);
					helper.calculateTotal(component);
				}
			} else if (selectedPromotion.promotion.Promotion_Type__c == "Mix & Match") {
				var thresholdOpts = selectedPromotion.ruleList[0].thresholdList;
				thresholdOpts.forEach(function (tItem) {
					var products = tItem.products;
					products.forEach(function (pItem) {
						if (quotationItemList[index].ProductCode__c == pItem.Product__r.ProductCode) {
							pItem.selected = true;
							//modify by austin, new promotion develop， 计算unit price
							// if (pItem.Additional_Discount__c != null) {
							//     quotationItemList[index].Unit_Price__c = (quotationItemList[index].Unit_Price__c * pItem.Additional_Discount__c / 100).toFixed(2);
							// }
						}
					});
				});
				component.set("v.thresholdOpts", thresholdOpts);

				if (quotationItemList[index].hasPool) {
					var offeringOpts = [];
					selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
						if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
							offeringOpts.push(oItem);
						}
					});
					component.set("v.offeringOpts", offeringOpts);
				}

				component.set("v.showThresholdProduct", true);
				component.set("v.quotationItemList", []);
				component.set("v.quotationItemList", quotationItemList);
			} else if (selectedPromotion.promotion.Promotion_Type__c == "Price Discount") {
				quotationItemList[index].hasMix = false;
				quotationItemList[index].isMix = false;
				var indexObj = {
					"startindex": 0
				};
				helper.addThresholdItems(quotationItemList, index, indexObj, component);
				helper.updateThresholdItem(quotationItemList, index, selectedPromotion);

				helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
				helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

				component.set("v.quotationItemList", []);
				component.set("v.quotationItemList", quotationItemList);
				helper.calculateTotal(component);
			} else if (selectedPromotion.promotion.Promotion_Type__c == "Price Break") {
				var ruleItem = helper.getMiniRuleInPriceBreak(selectedPromotion);
				var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, selectedPromotion);
				if (avilableRuleItem) {
					ruleItem = avilableRuleItem;
				}
				var offeringOpts = [];
				ruleItem.offeringList.forEach(function (oItem) {
					if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
						offeringOpts.push(oItem);
					}
				});

				if (offeringOpts.length > 0) {
					quotationItemList[index].hasPool = true;
					component.set("v.offeringOpts", offeringOpts);
					component.set("v.showOfferingProduct", true);
					component.set("v.quotationItemList", quotationItemList);
				} else {
					quotationItemList[index].hasMix = false;
					quotationItemList[index].isMix = false;
					var indexObj = {
						"startindex": 0
					};
					helper.addThresholdItems(quotationItemList, index, indexObj, component);
					helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
					helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);

					helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
					helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

					component.set("v.quotationItemList", []);
					component.set("v.quotationItemList", quotationItemList);
					helper.calculateTotal(component);
				}
			} else if (selectedPromotion.promotion.Promotion_Type__c == "Full Pallet Promo") {
				if (quotationItemList[index].hasPool) {
					var offeringOpts = [];
					selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
						if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
							offeringOpts.push(oItem);
						}
					});
					component.set("v.offeringOpts", offeringOpts);
					component.set("v.showOfferingProduct", true);
					component.set("v.quotationItemList", quotationItemList);
				} else {
					quotationItemList[index].hasMix = false;
					quotationItemList[index].isMix = false;
					var indexObj = {
						"startindex": 0
					};
					helper.addThresholdItems(quotationItemList, index, indexObj, component);
					helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
					helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);

					helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
					helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

					component.set("v.quotationItemList", []);
					component.set("v.quotationItemList", quotationItemList);
					helper.calculateTotal(component);
				}
			} else if (selectedPromotion.promotion.Promotion_Type__c == "Others") {
				var thresholdList = selectedPromotion.ruleList[0].thresholdList;
				var thresholdOpts = [];
				thresholdList.forEach(function (tItem) {
					if (tItem.threshold.RecordType.DeveloperName == "By_Mix_Match") {
						thresholdOpts.push(tItem);
						var products = tItem.products;
						products.forEach(function (pItem) {
							if (quotationItemList[index].ProductCode__c == pItem.Product__r.ProductCode) {
								pItem.selected = true;
							}
						});
					}
				});
				component.set("v.thresholdOpts", thresholdOpts);

				var offeringOpts = [];
				if (quotationItemList[index].hasPool) {
					var offeringOpts = [];
					selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
						if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
							offeringOpts.push(oItem);
						}
					});
				}
				component.set("v.offeringOpts", offeringOpts);

				if (thresholdOpts.length > 0) {
					component.set("v.showThresholdProduct", true);
				} else if (offeringOpts.length > 0) {
					component.set("v.showOfferingProduct", true);
				} else {
					quotationItemList[index].hasMix = false;
					quotationItemList[index].isMix = false;
					if (quotationItemList[index].PromotionName__c) {
						helper.removePromotionQuotationItem(quotationItemList, index, component);
					}
					quotationItemList[index].Promotion = selectedPromotion;
					quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
					quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
					quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
					quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
					var indexObj = {
						"startindex": 0
					};
					helper.addThresholdItems(quotationItemList, index, indexObj, component);
					helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
					helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);

					helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
					helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

					component.set("v.quotationItemList", []);
					component.set("v.quotationItemList", quotationItemList);
					helper.calculateTotal(component);
				}
			}
		} else {
			var toastEvent = $A.get("e.force:showToast");
			toastEvent.setParams({
				"title": "Warning!",
				"message": "This promotion code is not available for the customer",
				"type": "Warning"
			});
			toastEvent.fire();
			if (quotationItemList[index].Promotion__c) {
				promotionList.forEach(function (pItem) {
					if (pItem.promotion.Id == quotationItemList[index].Promotion__c) {
						quotationItemList[index].PromotionName__c = pItem.promotion.Promotion_Code_For_External__c;
					}
				});
			} else {
				quotationItemList[index].PromotionName__c = undefined;
			}
			component.set("v.quotationItemList", quotationItemList);
		}
	},
	navigateToPromotion: function (component, event, helper) {
		var promotionId = event.currentTarget.name;
		var navService = component.find("navService");
		var pageReference = {
			"type": "standard__recordPage", //example for opening a record page, see bottom for other supported types
			"attributes": {
				"recordId": promotionId, //place your record id here that you wish to open
				"actionName": "view"
			}
		};

		navService.generateUrl(pageReference).then(
			$A.getCallback(function (url) {
				console.log("success: " + url); //you can also set the url to an aura attribute if you wish
				//window.open(url,'_blank'); //this opens your page in a seperate tab here
			}),
			$A.getCallback(function (error) {
				console.log("error: " + error);
			})
		);
	},
	closeThresholdProduct: function (component, event, helper) {
		component.set("v.showThresholdProduct", false);
		var index = component.get("v.operationRow");
		var quotationItemList = component.get("v.quotationItemList");
		var quotationItem = quotationItemList[index];
		if (!quotationItem.Promotion) {
			quotationItem.hasMix = false;
			quotationItem.isMix = false;
			quotationItem.hasPool = false;
			component.set("v.quotationItemList", quotationItemList);
		} else {
			var selectedPromotion = quotationItem.Promotion;
			if (!helper.hasMixMatch(selectedPromotion)) {
				quotationItem.hasMix = false;
				quotationItem.isMix = false;
			}
			quotationItem.hasPool = helper.isPoolFreeGoods(selectedPromotion.ruleList[0]);
			var selectedThresholdProducts = component.get("v.oldSelectedThresholdProducts");
			var ruleName = quotationItem.Promotion.ruleList[0].ruleName;
			if (selectedThresholdProducts && selectedThresholdProducts.length > 0) {
				var thresholdOpts = selectedPromotion.ruleList[0].thresholdList;
				thresholdOpts.forEach(function (tItem) {
					var products = tItem.products;
					products.forEach(function (pItem) {
						if (selectedThresholdProducts.indexOf(pItem.Product__r.ProductCode) != -1) {
							pItem.selected = true;
						} else {
							pItem.selected = false;
						}
					});
				});
				component.set("v.oldSelectedThresholdProducts", []);
			}
			component.set("v.quotationItemList", quotationItemList);
		}
	},
	finishThresholdProduct: function (component, event, helper) {
		var thresholdOpts = component.get("v.thresholdOpts");
		var isMeet = true;
		for (var i = 0; i < thresholdOpts.length; i++) {
			var tItem = thresholdOpts[i];
			var miniDiffModels = tItem.threshold.Minimum_Different_Tool_Models__c;
			var maxDiffModels = tItem.threshold.Max_Different_Tool_Models__c;
			var products = tItem.products;
			var selectedDiffModels = 0;
			products.forEach(function (pItem) {
				if (pItem.selected) {
					selectedDiffModels++;
				}
			});
			if (selectedDiffModels < miniDiffModels) {
				var toastEvent = $A.get("e.force:showToast");
				toastEvent.setParams({
					"title": "Warning!",
					"message": "Please select at least " + miniDiffModels + " different models in Mix & Match List " + (i + 1) + ".",
					"type": "Warning"
				});
				toastEvent.fire();
				isMeet = false;
				break;
			}
			if (maxDiffModels && selectedDiffModels > maxDiffModels) {
				var toastEvent = $A.get("e.force:showToast");
				toastEvent.setParams({
					"title": "Warning!",
					"message": "Please select at most " + maxDiffModels + " different models in Mix & Match List " + (i + 1) + ".",
					"type": "Warning"
				});
				toastEvent.fire();
				isMeet = false;
				break;
			}
		}

		if (!isMeet) {
			return;
		}

		component.set("v.showThresholdProduct", false);
		var isPoolFreeGoods = component.get("v.isPoolFreeGoods");
		if (isPoolFreeGoods) {
			component.set("v.showOfferingProduct", true);
		} else {
			var selectedPromotion = component.get("v.selectedPromotion");
			var index = component.get("v.operationRow");
			var quotationItemList = component.get("v.quotationItemList");
			var quotationItem = quotationItemList[index];
			quotationItem.hasMix = true;
			if (quotationItem.PromotionName__c) {
				helper.removePromotionQuotationItem(quotationItemList, index, component);
			}
			quotationItem.Promotion = selectedPromotion;
			quotationItem.PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
			quotationItem.Promotion__c = selectedPromotion.promotion.Id;
			quotationItem.Regular_Promotion_Window__c = selectedPromotion.windowId;

			var ruleItem = selectedPromotion.ruleList[0];
			if (selectedPromotion.promotion.Promotion_Type__c == "Price Break") {
				var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, selectedPromotion);
				if (avilableRuleItem) {
					ruleItem = avilableRuleItem;
				}
			}
			quotationItem.Promotion_Rule_Name__c = ruleItem.ruleName;

			var indexObj = {
				"startindex": 0
			};
			helper.addThresholdItems(quotationItemList, index, indexObj, component);
			if (helper.isMeetThreshold(quotationItemList, index, selectedPromotion)) {
				if (selectedPromotion.promotion.Promotion_Type__c == "Mix & Match") {
					helper.updateThresholdItemMixMatch(quotationItemList, index, selectedPromotion);
				} else {
					// check with threshold has mix & match, if has, use mix & match method
					if (helper.hasMixMatch(selectedPromotion)) {
						helper.updateThresholdItemMixMatch(quotationItemList, index, selectedPromotion);
					} else {
						helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
					}
				}
			}
			helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);

			helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
			helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

			component.set("v.quotationItemList", []);
			component.set("v.quotationItemList", quotationItemList);
			helper.calculateTotal(component);

			component.set("v.oldSelectedThresholdProducts", []);
		}
	},
	handleThresholdSelection: function (component, event, helper) {
		var index = component.get("v.operationRow");
		var quotationItemList = component.get("v.quotationItemList");
		var currentProduct = quotationItemList[index].SF_Description__c != undefined ? quotationItemList[index].SF_Description__c : quotationItemList[index].Product__r.SF_Description__c;
		var item = event.currentTarget;
		if (item && item.dataset) {
			var value = item.dataset.value;
			var name = item.getAttribute("data-name");
			var selected = item.dataset.selected;
			var options = component.get("v.thresholdOpts");
			options.forEach(function (element) {
				var threshold = element.threshold;
				var products = element.products;
				if (threshold.Name == name) {
					products.forEach(function (p) {
						if (p.Product__r.SF_Description__c == currentProduct) {
							p.selected = true;
						} else if (p.Product__r.SF_Description__c == value) {
							p.selected = p.selected ? false : true;
						}
					});
				}
			});
			component.set("v.thresholdOpts", options);
		}
	},
	closeOfferingProduct: function (component, event, helper) {
		component.set("v.showOfferingProduct", false);
		var index = component.get("v.operationRow");
		if (component.get("v.editRow") > -1) {
			index = component.get("v.editRow");
		}
		var quotationItemList = component.get("v.quotationItemList");
		var quotationItem = quotationItemList[index];
		if (!quotationItem.Promotion) {
			quotationItem.hasMix = false;
			quotationItem.isMix = false;
			quotationItem.hasPool = false;
			component.set("v.quotationItemList", quotationItemList);
		} else {
			var selectedPromotion = quotationItem.Promotion;
			if (!helper.hasMixMatch(selectedPromotion)) {
				quotationItem.hasMix = false;
				quotationItem.isMix = false;
			}
			var ruleItem = selectedPromotion.ruleList[0];
			if (selectedPromotion.promotion.Promotion_Type__c == "Price Break") {
				var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, selectedPromotion);
				if (avilableRuleItem) {
					ruleItem = avilableRuleItem;
					var oldRuleName = quotationItem.Promotion_Rule_Name__c;
					var newRuleName = avilableRuleItem.ruleName;
					if (oldRuleName != newRuleName) {
						helper.removePromotionQuotationItem(quotationItemList, index, component);
						ruleItem.offeringList.forEach(function (oItem) {
							if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
								var products = oItem.products;
								products.forEach(function (pItem) {
									pItem.selected = false;
								});
							}
						});
						var indexObj = {
							"startindex": 0
						};
						helper.addThresholdItems(quotationItemList, index, indexObj, component);
						helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
						helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);

						helper.checkMeetOfferingPoolLimit(quotationItemList, index, selectedPromotion);

						helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
						helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

						component.set("v.quotationItemList", []);
						component.set("v.quotationItemList", quotationItemList);
						helper.calculateTotal(component);
					}
				}
			}

			var offeringOpts = [];
			ruleItem.offeringList.forEach(function (oItem) {
				if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
					offeringOpts.push(oItem);
				}
			});
			if (offeringOpts.length > 0) {
				quotationItem.hasPool = true;
			} else {
				quotationItem.hasPool = false;
			}
			var selectedThresholdProducts = component.get("v.oldSelectedThresholdProducts");
			if (selectedThresholdProducts && selectedThresholdProducts.length > 0) {
				var thresholdOpts = ruleItem.thresholdList;
				thresholdOpts.forEach(function (tItem) {
					var products = tItem.products;
					products.forEach(function (pItem) {
						if (selectedThresholdProducts.indexOf(pItem.Product__r.ProductCode) != -1) {
							pItem.selected = true;
						} else {
							pItem.selected = false;
						}
					});
				});
				component.set("v.oldSelectedThresholdProducts", []);
			}
			var selectedOfferingProducts = component.get("v.oldSelectedOfferingProducts");
			if (selectedOfferingProducts && selectedOfferingProducts.length > 0) {
				var offeringOpts = ruleItem.offeringList;
				offeringOpts.forEach(function (oItem) {
					if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
						var products = oItem.products;
						products.forEach(function (pItem) {
							if (selectedOfferingProducts.indexOf(pItem.Product__r.ProductCode) != -1) {
								pItem.selected = true;
							} else {
								pItem.selected = false;
							}
						});
					}
				});
				component.set("v.oldSelectedOfferingProducts", []);
			}
			component.set("v.quotationItemList", quotationItemList);
		}

		component.set("v.editRow", -1);
	},
	finishOfferingProduct: function (component, event, helper) {
		component.set("v.showOfferingProduct", false);
		var selectedPromotion = component.get("v.selectedPromotion");
		var index = component.get("v.operationRow");
		if (component.get("v.editRow") > -1) {
			index = component.get("v.editRow");
		}
		//var index = component.get('v.operationRow');
		var quotationItemList = component.get("v.quotationItemList");
		var indexObj = {
			"startindex": 0
		};
		var quotationItem = quotationItemList[index];
		if (quotationItem.PromotionName__c) {
			helper.removePromotionQuotationItem(quotationItemList, index, component);
		}
		quotationItem.Promotion = selectedPromotion;
		quotationItem.PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
		quotationItem.Promotion__c = selectedPromotion.promotion.Id;
		quotationItem.Regular_Promotion_Window__c = selectedPromotion.windowId;

		var ruleItem = selectedPromotion.ruleList[0];
		if (selectedPromotion.promotion.Promotion_Type__c == "Price Break") {
			var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, selectedPromotion);
			if (avilableRuleItem) {
				ruleItem = avilableRuleItem;
			}
		}
		quotationItem.Promotion_Rule_Name__c = ruleItem.ruleName;

		if (helper.hasMixMatch(selectedPromotion)) {
			quotationItem.hasMix = true;
		}
		helper.addThresholdItems(quotationItemList, index, indexObj, component);
		if (helper.isMeetThreshold(quotationItemList, index, selectedPromotion)) {
			if (selectedPromotion.promotion.Promotion_Type__c == "Mix & Match") {
				helper.updateThresholdItemMixMatch(quotationItemList, index, selectedPromotion);
			} else {
				if (helper.hasMixMatch(selectedPromotion)) {
					helper.updateThresholdItemMixMatch(quotationItemList, index, selectedPromotion);
				} else {
					helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
				}
			}
		}
		helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
		helper.checkMeetOfferingPoolLimit(quotationItemList, index, selectedPromotion);

		helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
		helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

		component.set("v.quotationItemList", []);
		component.set("v.quotationItemList", quotationItemList);
		helper.calculateTotal(component);

		component.set("v.oldSelectedThresholdProducts", []);
		component.set("v.oldSelectedOfferingProducts", []);

		component.set("v.editRow", -1);
	},
	handleOfferingSelection: function (component, event, helper) {
		var item = event.currentTarget;
		if (item && item.dataset) {
			var value = item.dataset.value;
			var name = item.getAttribute("data-name");
			var selected = item.dataset.selected;
			var options = component.get("v.offeringOpts");
			options.forEach(function (element) {
				var offering = element.offering;
				var products = element.products;
				if (offering.Name == name) {
					products.forEach(function (p) {
						if (p.Product__r.SF_Description__c == value) {
							p.selected = p.selected ? false : true;
						}
					});
				}
			});
			component.set("v.offeringOpts", options);
		}
	},
	handleReSelectProductClick: function (component, event, helper) {
		var index = component.get("v.operationRow");
		var quotationItemList = component.get("v.quotationItemList");
		var quotationItem = quotationItemList[index];
		var selectedPromotion = quotationItem.Promotion;
		component.set("v.selectedPromotion", selectedPromotion);
		if (helper.hasMixMatch(selectedPromotion)) {
			var thresholdOpts = [];
			var thresholdList = selectedPromotion.ruleList[0].thresholdList;
			var selectedThresholdProducts = [];
			thresholdList.forEach(function (tItem) {
				if (tItem.threshold.RecordType.DeveloperName == "By_Mix_Match") {
					thresholdOpts.push(tItem);

					var products = tItem.products;
					products.forEach(function (pItem) {
						if (pItem.selected) {
							selectedThresholdProducts.push(pItem.Product__r.ProductCode);
						}
						pItem.hidden = false;
					});
				}
			});
			component.set("v.oldSelectedThresholdProducts", selectedThresholdProducts);

			var offeringOpts = [];
			var selectedOfferingProducts = [];
			selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
				if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
					offeringOpts.push(oItem);

					var products = oItem.products;
					products.forEach(function (pItem) {
						if (pItem.selected) {
							selectedOfferingProducts.push(pItem.Product__r.ProductCode);
						}
						pItem.hidden = false;
					});
				}
			});
			if (quotationItem.hasPool) {
				component.set("v.isPoolFreeGoods", true);
			} else {
				component.set("v.isPoolFreeGoods", false);
			}
			component.set("v.thresholdOpts", thresholdOpts);
			component.set("v.offeringOpts", offeringOpts);
			component.set("v.oldSelectedOfferingProducts", selectedOfferingProducts);
			component.set("v.showThresholdProduct", true);
		} else {
			var ruleItem = selectedPromotion.ruleList[0];
			var offeringOpts = [];
			var selectedOfferingProducts = [];
			if (selectedPromotion.promotion.Promotion_Type__c == "Price Break") {
				var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, selectedPromotion);
				if (avilableRuleItem) {
					ruleItem = avilableRuleItem;
				}
			}
			ruleItem.offeringList.forEach(function (oItem) {
				if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
					offeringOpts.push(oItem);

					var products = oItem.products;
					products.forEach(function (pItem) {
						if (pItem.selected) {
							selectedOfferingProducts.push(pItem.Product__r.ProductCode);
						}
						pItem.hidden = false;
					});
				}
			});
			component.set("v.offeringOpts", offeringOpts);
			component.set("v.oldSelectedOfferingProducts", selectedOfferingProducts);
			component.set("v.showOfferingProduct", true);
		}
	},
	backToEditOrder: function (component, event, helper) {
		component.set("v.showAvailablePromotions", false);
		component.set("v.showPromotionReminder", false);
	},
	nextToAvailablePromo: function (component, event, helper) {
		if (!helper.checkOrderQty(component)) {
			return;
		}

		let allValid = helper.validateShipDate(component);
		if (!allValid) {
			return;
		}

		var quotationItemList = component.get("v.quotationItemList");
		var ExpirateDate = component.get("v.ExpirateDate");

		//Yanko Start validate before click next
		for (var i = 0; i < quotationItemList.length; i++) {
			if (quotationItemList[i].counterror == true) {
				return;
			}

			var woqty = quotationItemList[i].Quantity__c;
			for (j = 0; j < quotationItemList.length; j++) {
				if (quotationItemList[j].ProductCode__c == quotationItemList[i].ProductCode__c) {
					if (j != i) {
						var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
						var needcheck = 1;
					}
				}
			}
			if (needcheck == 1) {
				var validateqty = Number(woqty) % Number(quotationItemList[i].CS_Exchange_Rate__c);
			} else {
				var validateqty = Number(quotationItemList[i].Quantity__c) % Number(quotationItemList[i].CS_Exchange_Rate__c);
			}

			if (validateqty != 0 && ExpirateDate == true) {
				var needcheckqty = false;
				var notonlyfreegoods = false;
				for (var x = 0; x < quotationItemList.length; x++) {
					if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[i].Product__c) {
						var notonlyfreegoods = true;
						if (quotationItemList[x].Promotion) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var b = 0; b <= quotationItemList[x].Promotion.ruleList.length; b++) {
									if (quotationItemList[x].Promotion.ruleList[b] && quotationItemList[x].Promotion.ruleList[b].thresholdList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[b].thresholdList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[b].thresholdList[j] && quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products[k].Multiple_Control__c;
														// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						} else {
							var needcheckqty = true;
						}
					}
				}

				if (notonlyfreegoods == false) {
					var needcheckfreegoodsqty = false;
					for (var x = 0; x < quotationItemList.length; x++) {
						if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[i].Product__c) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var b = 0; b <= quotationItemList[x].Promotion.ruleList.length; b++) {
									if (quotationItemList[x].Promotion.ruleList[b] && quotationItemList[x].Promotion.ruleList[b].offeringList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[b].offeringList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[b].offeringList[j] && quotationItemList[x].Promotion.ruleList[b].offeringList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[b].offeringList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[b].offeringList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[b].offeringList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[b].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[b].offeringList[j].products[k].Multiple_Control__c;
														// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckfreegoodsqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					if (needcheckfreegoodsqty == true) {
						quotationItemList[i].counterror = true;
						return;
					}
				} else {
					if (needcheckqty == true) {
						quotationItemList[i].counterror = true;
						return;
					}
				}
			}
		}
		//Yanko End validate before click next

		//check promotions
		let promoCodes = new Set();
		quotationItemList.forEach(function (qItem) {
			if (qItem.PromotionName__c) {
				promoCodes.add(qItem.PromotionName__c);
			}
		});

		var termsPromo = component.get("v.termsPromo");
		if (termsPromo) {
			promoCodes.add(termsPromo.promotion.Promotion_Code_For_External__c);
		}

		var wholeOrderPromo = component.get("v.wholeOrderPromo");
		if (wholeOrderPromo) {
			promoCodes.add(wholeOrderPromo.promotion.Promotion_Code_For_External__c);
		}

		if (promoCodes.size == 0) {
			var notApplyPromoList = helper.initNotApplyPromoList(quotationItemList);
			if (notApplyPromoList.length > 0) {
				component.set("v.notApplyPromoList", notApplyPromoList);
				component.set("v.showAvailablePromotions", true);
			} else {
				var a = component.get("c.nextToPromoReminder");
				$A.enqueueAction(a);
			}
		} else {
			component.set("v.isBusy", true);
			var action = component.get("c.checkPromotions");
			action.setParams({
				"promotionCodes": Array.from(promoCodes),
				"customerId": component.get("v.customerId")
			});
			action.setCallback(this, function (response) {
				var state = response.getState();
				console.log("state--->" + state);
				if (state === "SUCCESS") {
					var results = response.getReturnValue();
					if (results != null && results != undefined) {
						var data = JSON.parse(results);
						if (data != null && data != undefined) {
							if (data.isSuccess) {
								component.set("v.isBusy", false);
								var notApplyPromoList = helper.initNotApplyPromoList(quotationItemList);
								if (notApplyPromoList.length > 0) {
									component.set("v.notApplyPromoList", notApplyPromoList);
									component.set("v.showAvailablePromotions", true);
								} else {
									var a = component.get("c.nextToPromoReminder");
									$A.enqueueAction(a);
								}
							} else {
								var toastEvent = $A.get("e.force:showToast");
								toastEvent.setParams({
									"title": "Warning!",
									"message": data.errorMsg,
									"type": "Warning"
								});
								toastEvent.fire();
								component.set("v.isBusy", false);
							}
						}
					}
				} else {
					var errors = response.getError();
					if (errors) {
						if (errors[0] && errors[0].message) {
							component.set("v.isBusy", false);
							alert("ERROR: " + errors[0].message);
						}
					} else {
						component.set("v.isBusy", false);
						alert("ERROR: Unknown error");
					}
				}
			});
			$A.enqueueAction(action);
		}
	},
	nextToPromoReminder: function (component, event, helper) {
		component.set("v.showAvailablePromotions", false);
		var quotationItemList = component.get("v.quotationItemList");
		var reminderPromoList = helper.initReminderPromoList(quotationItemList);
		if (reminderPromoList.length > 0) {
			component.set("v.reminderPromoList", reminderPromoList);
			component.set("v.showPromotionReminder", true);
		} else {
			helper.doSaveAction(component, event, false);
		}
	},
	nextToFinal: function (component, event, helper) {
		component.set("v.showAvailablePromotions", false);
		component.set("v.showPromotionReminder", false);
		let allValid = helper.validateShipDate(component);
		if (!allValid) {
			return;
		}
		helper.doSaveAction(component, event, false);
	},
	handleTermsPromoChange: function (component, event, helper) {
		var termsPromoCode = event.getSource().get("v.value");
		component.set("v.termsPromoCode", termsPromoCode);
	},
	onApplyTermsPromo: function (component, event, helper) {
		var termsPromoCode = component.get("v.termsPromoCode");
		if (termsPromoCode) {
			var action = component.get("c.getPromotion");
			action.setParams({
				"promotionCode": termsPromoCode,
				"customerId": component.get("v.customerId"),
				"isDropShip": component.get("v.isDropShip"),
				"needCheckOrder": component.get("v.needCheckOrder")
			});
			action.setCallback(this, function (response) {
				var state = response.getState();
				if (state === "SUCCESS") {
					var result = response.getReturnValue();
					if (result) {
						var data = JSON.parse(result);
						if (data) {
							var promoObj = data.promotion;
							if (promoObj) {
								var promoType = promoObj.Promotion_Type__c;
								var quotationItemList = component.get("v.quotationItemList");
								if (promoType == "Whole Order Promo") {
									var isMeetWholeOrderPromo = helper.isMeetWholeOrderPromo(quotationItemList, data);

									var threshold = data.ruleList[0].thresholdList[0].threshold;
									if (threshold.Min_Different_Categories__c && threshold.Min_Different_Categories__c > 1) {
										helper.showToast("Info", "Please add at least " + threshold.Min_Different_Categories__c + " different categories.", "info");
									}

									if (isMeetWholeOrderPromo) {
										component.set("v.wholeOrderPromo", data);
										if (helper.isPoolFreeGoods(data.ruleList[0])) {
											var offeringOpts = [];
											data.ruleList[0].offeringList.forEach(function (oItem) {
												if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
													offeringOpts.push(oItem);
												}
											});
											component.set("v.offeringOpts", offeringOpts);
											component.set("v.showWholeOrderOfferingProduct", true);
										} else {
											helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
										}
										component.set("v.quotationItemList", []);
										component.set("v.quotationItemList", quotationItemList);
										helper.calculateTotal(component);
									} else {
										var toastEvent = $A.get("e.force:showToast");
										toastEvent.setParams({
											"title": "Warning!",
											"message": "Do Not Meet The Whole Order Promotion Rule!",
											"type": "Warning"
										});
										toastEvent.fire();
										//alert('Error: Do Not Meet The Whole Order Promotion Rule!');
									}
								} else if (promoType == "Payment Term Promo") {
									var isMeetPaymentTermPromo = helper.isMeetPaymentTermPromo(quotationItemList, data);
									if (isMeetPaymentTermPromo) {
										component.set("v.termsPromo", data);
										var termKey = data.ruleList[0].offeringList[0].offering.Payment_Term__c;
										var termLabel = data.ruleList[0].offeringList[0].offering.Payment_Term_Label__c;
										component.set("v.paymentTermLabel", termLabel.substring(termKey.length + 1));
										component.set("v.paymentTermValue", termKey);
										component.set("v.termsPromoPTValue", termKey);
										component.set("v.needResearch", false);
									} else {
										var toastEvent = $A.get("e.force:showToast");
										toastEvent.setParams({
											"title": "Warning!",
											"message": "Do Not Meet The Payment Term Promotion Rule!",
											"type": "Warning"
										});
										toastEvent.fire();
										//alert('Error: Do Not Meet The Payment Term Promotion Rule!');
									}
								}
							}
						}
					} else {
						var toastEvent = $A.get("e.force:showToast");
						toastEvent.setParams({
							"title": "Warning!",
							"message": "Invaild Promotion.",
							"type": "Warning"
						});
						toastEvent.fire();
						//alert("ERROR: Invaild Promotion.");
					}
				} else {
					var errors = response.getError();
					if (errors) {
						if (errors[0] && errors[0].message) {
							alert("ERROR: " + errors[0].message);
						}
					} else {
						alert("ERROR: Unknown error");
					}
				}
			});
			$A.enqueueAction(action);
			component.set("v.termsPromoCode", "");
		}
	},
	handleDeleteTermsPromo: function (component, event, helper) {
		//var termsPromoCode = component.get('v.termsPromoCode');
		component.set("v.termsPromo", "");
		//component.set("v.paymentTermLabel", component.get("v.prePaymentTermLabel"));
		helper.getPaymentTermRule(component);
		// helper.rebindPaymentHandler(component,event,helper,false);
	},
	handleDeleteWholeOrderPromo: function (component, event, helper) {
		var quotationItemList = component.get("v.quotationItemList");
		helper.removeWholeOrderPromotionOfferingItem(quotationItemList, component.get("v.wholeOrderPromo"), component);
		component.set("v.wholeOrderPromo", "");
		component.set("v.quotationItemList", []);
		component.set("v.quotationItemList", quotationItemList);
		helper.calculateTotal(component);
	},
	searchMixMatchProducts: function (component, event, helper) {
		var thresholdOpts = component.get("v.thresholdOpts");
		var value = event.getSource().get("v.value").toUpperCase();
		var index = event.getSource().get("v.name");
		var tItem = thresholdOpts[index];
		var products = tItem.products;
		if (!value) {
			products.forEach(function (p) {
				p.hidden = false;
			});
		} else {
			products.forEach(function (p) {
				var pName = p.Product__r.SF_Description__c.toUpperCase();
				if (pName.indexOf(value) != -1) {
					p.hidden = false;
				} else {
					p.hidden = true;
				}
			});
		}
		component.set("v.thresholdOpts", thresholdOpts);
	},
	searchPoolFreeGoods: function (component, event, helper) {
		var offeringOpts = component.get("v.offeringOpts");
		var value = event.getSource().get("v.value").toUpperCase();
		var index = event.getSource().get("v.name");
		var oItem = offeringOpts[index];
		var products = oItem.products;
		if (!value) {
			products.forEach(function (p) {
				p.hidden = false;
			});
		} else {
			products.forEach(function (p) {
				var pName = p.Product__r.SF_Description__c.toUpperCase();
				if (pName.indexOf(value) != -1) {
					p.hidden = false;
				} else {
					p.hidden = true;
				}
			});
		}
		component.set("v.offeringOpts", offeringOpts);
	},
	closeWholeOrderOfferingProduct: function (component, event, helper) {
		component.set("v.showWholeOrderOfferingProduct", false);
		var wholeOrderPromo = component.get("v.wholeOrderPromo");
		var selectedThresholdProducts = component.get("v.oldSelectedOfferingProducts");
		if (selectedThresholdProducts && selectedThresholdProducts.length > 0) {
			var offeringOpts = wholeOrderPromo.ruleList[0].offeringList;
			offeringOpts.forEach(function (oItem) {
				if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
					var products = oItem.products;
					products.forEach(function (pItem) {
						if (selectedThresholdProducts.indexOf(pItem.Product__r.ProductCode) != -1) {
							pItem.selected = true;
						} else {
							pItem.selected = false;
						}
					});
				}
			});
			component.set("v.oldSelectedOfferingProducts", []);
			component.set("v.wholeOrderPromo", wholeOrderPromo);
		} else {
			component.set("v.wholeOrderPromo", "");
		}
	},
	finishWholeOrderOfferingProduct: function (component, event, helper) {
		component.set("v.showWholeOrderOfferingProduct", false);
		var quotationItemList = component.get("v.quotationItemList");
		helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
		component.set("v.oldSelectedOfferingProducts", []);
		component.set("v.quotationItemList", []);
		component.set("v.quotationItemList", quotationItemList);
		helper.calculateTotal(component);
	},
	handleReSelectWholeOrderPromoProductClick: function (component, event, helper) {
		var wholeOrderPromo = component.get("v.wholeOrderPromo");
		var offeringOpts = [];
		var selectedOfferingProducts = [];
		wholeOrderPromo.ruleList[0].offeringList.forEach(function (oItem) {
			if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
				offeringOpts.push(oItem);

				var products = oItem.products;
				products.forEach(function (pItem) {
					if (pItem.selected) {
						selectedOfferingProducts.push(pItem.Product__r.ProductCode);
					}
					pItem.hidden = false;
				});
			}
		});
		component.set("v.offeringOpts", offeringOpts);
		component.set("v.oldSelectedOfferingProducts", selectedOfferingProducts);
		component.set("v.showWholeOrderOfferingProduct", true);
	},

	promFocusHandler: function (component, event, helper) {
		var indexnum = event.getSource().get("v.indexnum");
		var quotationItemList = component.get("v.quotationItemList");
		var currentItem = quotationItemList[indexnum];
		if (currentItem && currentItem.strCodes) {
			var promotionFilterCondition = "";
			if (currentItem.strCodes == "onfous") {
				promotionFilterCondition = '[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Error"}]';
			} else {
				promotionFilterCondition =
					'[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Open"},{"FieldName":"Promo_Code__c", "Condtion":"IN", "Value":"(' +
					currentItem.strCodes +
					')"}]';
			}

			if (promotionFilterCondition && promotionFilterCondition != currentItem.promotionFilterCondition) {
				currentItem.promotionFilterCondition = promotionFilterCondition;
			}
		}
		component.set("v.quotationItemList", quotationItemList);
	},

	//Yanko Start
	handleReduceClick: function (component, event, helper) {
		var quotationItemList = component.get("v.quotationItemList");
		let _index = component.get("v.operationRow");
		var qItem = quotationItemList[_index];
		var exchangerate = quotationItemList[_index].CS_Exchange_Rate__c;
		var promquantity = quotationItemList[_index].Quantity__c;
		var ExpirateDate = component.get("v.ExpirateDate");

		//for buy qty
		var buyqty = 1;
		var productBuyQty = 1;
        var productMinQty = 1;
		if (quotationItemList[_index].Promotion) {
			if (quotationItemList[_index].Promotion.ruleList) {
				for (var i = 0; i < quotationItemList[_index].Promotion.ruleList.length; i++) {
					if (quotationItemList[_index].Promotion.ruleList[i] && quotationItemList[_index].Promotion.ruleList[i].thresholdList) {
						for (var j = 0; j < quotationItemList[_index].Promotion.ruleList[i].thresholdList.length; j++) {
							//BUY QTY promotion如果是price Discount 或者 Price Break，查找Buy Qty
							if (
								quotationItemList[_index].Promotion.promotion.Promotion_Type__c == "Price Discount" ||
								quotationItemList[_index].Promotion.promotion.Promotion_Type__c == "Price Break"
							) {
								if (
									quotationItemList[_index].Promotion.ruleList[i].thresholdList[j]
                                    && ['By_Quantity_of_Specific_Product', 'By_Amount_of_Specific_Product'].includes(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName)
                                    && quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[0].Product__c === quotationItemList[_index].Product__c
								) {
									var buyqty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
								}
							}
							// add haibo: 新增 Therthod 为By Quantity of Specific Product && product buyQty, 按 product buyQty成倍修改数量（应用promotion时）
                            for(var k=0; k<quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products.length; k++){
                                if(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j]
                                    && (quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName == 'By_Quantity_of_Specific_Product'
                                    && quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c)){
                                    if (quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == qItem.Product__c) {
                                        if(quotationItemList[_index].Promotion.promotion.Promotion_Type__c == "Price Break"){
                                            let _productMinQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                            if(_productMinQty > productMinQty && productMinQty === 1){
                                                productMinQty = _productMinQty;
                                            }else if(_productMinQty < productMinQty){
                                                productMinQty = _productMinQty;
                                            }
                                        }else{
                                            productMinQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                        }
                                        console.log(JSON.stringify(quotationItemList[_index]), 'quotationItemList[_index]===============');
                                        // 根据promotion type获取不同的buy qty
                                        if (quotationItemList[_index].Promotion.promotion.Promotion_Type__c == 'Price Discount') {
                                            productBuyQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
                                        } else {
                                            productBuyQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c;
                                        }
                                    }
                                }
                            }
						}
					}
				}
			}
		}
		// add haibo
        console.log(productBuyQty, 'productBuyQty====================');
        console.log(_index, 'index====================');
        console.log(JSON.stringify(quotationItemList), 'quotationItemList====================');
        console.log(quotationItemList[_index].ProductCode__c, 'ProductCode__c====================');

		for (i = 0; i < quotationItemList.length; i++) {
			if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
				console.log(i, 'i==================');
				if (i != _index) {
					console.log(i, _index, 'i != _index==================');
					var promquantity = Number(promquantity) + Number(quotationItemList[i].Quantity__c);
					var needcheck = 1;
				}
			}
		}
		if (needcheck == 1) {
			console.log('needcheck================');
            console.log(quotationItemList[_index].Quantity__c, 'quotationItemList[_index].Quantity__c================');
            console.log(productBuyQty, 'productBuyQty====================');
            console.log(productMinQty, 'productMinQty====================');
			if (quotationItemList[_index].Quantity__c > 1) {
				// add haibo
                if (productBuyQty && productBuyQty != 0 && productBuyQty != 1) {
                    if(quotationItemList[_index].Quantity__c > productBuyQty){
                        quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - Number(productBuyQty);
                        promquantity = Number(promquantity) - Number(productBuyQty);
                        // 判断 promquantity是否小于Minqty
                        if(quotationItemList[_index].Quantity__c < productMinQty){
                            var toastEvent = $A.get("e.force:showToast");
                            toastEvent.setParams({
                                "title": "Warning!",
                                "message": "Qty(EA) cannot be further reduced. ",
                                "type": "Warning"
                            });
                            toastEvent.fire();
                            // productBuyQty向上取整
                            quotationItemList[_index].Quantity__c = productMinQty;
                        }
                    }else{
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Warning!",
                            "message": "Qty(EA) cannot be further reduced. ",
                            "type": "Warning"
                        });
                        toastEvent.fire();
                    }
                }
                else if(buyqty && buyqty != 0 && buyqty != 1 && productBuyQty == 1){
					//for buyqty
					if (quotationItemList[_index].Quantity__c > buyqty) {
						if (quotationItemList[_index].Quantity__c % Number(buyqty) != 0) {
							for (var i = buyqty; i > 0; i--) {
								if ((Number(quotationItemList[_index].Quantity__c) - i) % Number(buyqty) == 0) {
									quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - i;
									promquantity = Number(promquantity) - i;
								}
							}
						} else {
							quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - Number(buyqty);
							promquantity = Number(promquantity) - Number(buyqty);
						}
					} else {
						var toastEvent = $A.get("e.force:showToast");
						toastEvent.setParams({
							"title": "Warning!",
							"message": "Qty(EA) cannot be further reduced. ",
							"type": "Warning"
						});
						toastEvent.fire();
					}
				} else {
					quotationItemList[_index].Quantity__c = quotationItemList[_index].Quantity__c - 1;
					promquantity = Number(promquantity) - 1;
				}
				var promremainder = Number(promquantity) % Number(exchangerate);
				if (promremainder != 0 && ExpirateDate == true) {
					var needcheckqty = false;
					var notonlyfreegoods = false;
					for (var x = 0; x < quotationItemList.length; x++) {
						if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
							var notonlyfreegoods = true;
							if (quotationItemList[x].Promotion) {
								if (quotationItemList[x].Promotion.ruleList) {
									for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
										if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
											for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
												if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
													for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
														if (
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
														) {
															var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
															// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
															if (multiplecontrol == "Inner Box Multiple") {
																var needcheckqty = true;
															}
														}
													}
												}
											}
										}
									}
								}
							} else {
								var needcheckqty = true;
							}
						}
					}

					if (notonlyfreegoods == false) {
						var needcheckfreegoodsqty = false;
						for (var x = 0; x < quotationItemList.length; x++) {
							if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
								if (quotationItemList[x].Promotion.ruleList) {
									for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
										if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
											for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
												if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
													for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
														if (
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
														) {
															var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
															// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
															if (multiplecontrol == "Inner Box Multiple") {
																var needcheckfreegoodsqty = true;
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
						if (needcheckfreegoodsqty == true) {
							for (var i = 0; i < quotationItemList.length; i++) {
								if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
									quotationItemList[i].counterror = true;
								}
							}
						}
					} else {
						if (needcheckqty == true) {
							for (var i = 0; i < quotationItemList.length; i++) {
								if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
									quotationItemList[i].counterror = true;
								}
							}
						}
					}
				} else {
					component.set("v.showcaseqtyalert", false);
					quotationItemList[_index].counterror = false;
					for (var i = 0; i < quotationItemList.length; i++) {
						if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
							quotationItemList[i].counterror = false;
						}
					}
				}
			} else {
				var toastEvent = $A.get("e.force:showToast");
				toastEvent.setParams({
					"title": "Warning!",
					"message": "Qty(EA) cannot be further reduced. ",
					"type": "Warning"
				});
				toastEvent.fire();
			}
		} else {
			console.log('no needcheck================');
            console.log(quotationItemList[_index].Quantity__c, 'quotationItemList[_index].Quantity__c================');
            console.log(productBuyQty, 'productBuyQty====================');
            console.log(productMinQty, 'productMinQty====================');
            // add haibo
            if (productBuyQty && productBuyQty != 0 && productBuyQty != 1) {
                if(quotationItemList[_index].Quantity__c > productBuyQty){
                    quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - Number(productBuyQty);
                    promquantity = Number(promquantity) - Number(productBuyQty);
                    // 判断 promquantity是否小于Minqty
                    if(quotationItemList[_index].Quantity__c < productMinQty){
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Warning!",
                            "message": "Qty(EA) cannot be further reduced. ",
                            "type": "Warning"
                        });
                        toastEvent.fire();
                        // productBuyQty向上取整
                        quotationItemList[_index].Quantity__c = productMinQty;
                    }
                }else{
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Warning!",
                        "message": "Qty(EA) cannot be further reduced. ",
                        "type": "Warning"
                    });
                    toastEvent.fire();
                }
            }
            else if(buyqty && buyqty != 0 && buyqty != 1 && productBuyQty == 1){
				//for buyqty
				if (quotationItemList[_index].Quantity__c > buyqty) {
					if (quotationItemList[_index].Quantity__c % Number(buyqty) != 0) {
						for (var i = buyqty; i > 0; i--) {
							if ((Number(quotationItemList[_index].Quantity__c) - i) % Number(buyqty) == 0) {
								quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - i;
							}
						}
					} else {
						quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - Number(buyqty);
					}

					if (quotationItemList[_index].Quantity__c % Number(quotationItemList[_index].CS_Exchange_Rate__c) != 0) {
						var needcheckqty = false;
						var notonlyfreegoods = false;
						for (var x = 0; x < quotationItemList.length; x++) {
							if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
								var notonlyfreegoods = true;
								if (quotationItemList[x].Promotion) {
									if (quotationItemList[x].Promotion.ruleList) {
										for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
											if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
												for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
													if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
														for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
															if (
																quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
																quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
																quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
															) {
																var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
																// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
																if (multiplecontrol == "Inner Box Multiple") {
																	var needcheckqty = true;
																}
															}
														}
													}
												}
											}
										}
									}
								} else {
									var needcheckqty = true;
								}
							}
						}

						if (notonlyfreegoods == false) {
							var needcheckfreegoodsqty = false;
							for (var x = 0; x < quotationItemList.length; x++) {
								if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
									if (quotationItemList[x].Promotion.ruleList) {
										for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
											if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
												for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
													if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
														for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
															if (
																quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
																quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
																quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
															) {
																var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
																// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
																if (multiplecontrol == "Inner Box Multiple") {
																	var needcheckfreegoodsqty = true;
																}
															}
														}
													}
												}
											}
										}
									}
								}
							}
							if (needcheckfreegoodsqty == true) {
								for (var i = 0; i < quotationItemList.length; i++) {
									if (quotationItemList[i].ProductCode__c == quotationItemList[index].ProductCode__c) {
										quotationItemList[i].counterror = true;
									}
								}
							}
						} else {
							if (needcheckqty == true) {
								for (var i = 0; i < quotationItemList.length; i++) {
									if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
										quotationItemList[i].counterror = true;
									}
								}
							}
						}
					} else {
						quotationItemList[_index].counterror = false;
						for (var i = 0; i < quotationItemList.length; i++) {
							if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
								quotationItemList[i].counterror = false;
							}
						}
					}
				} else {
					var toastEvent = $A.get("e.force:showToast");
					toastEvent.setParams({
						"title": "Warning!",
						"message": "Qty(EA) cannot be further reduced. ",
						"type": "Warning"
					});
					toastEvent.fire();
				}
			} else {
				if (quotationItemList[_index].Quantity__c > quotationItemList[_index].CS_Exchange_Rate__c) {
					if (quotationItemList[_index].Quantity__c % quotationItemList[_index].CS_Exchange_Rate__c != 0) {
						for (var i = quotationItemList[_index].Quantity__c; i > quotationItemList[_index].Quantity__c - quotationItemList[_index].CS_Exchange_Rate__c; i--) {
							if (i % quotationItemList[_index].CS_Exchange_Rate__c == 0) {
								quotationItemList[_index].Quantity__c = i;
							}
						}
					} else {
						quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - Number(quotationItemList[_index].CS_Exchange_Rate__c);
					}

					component.set("v.showcaseqtyalert", false);
					quotationItemList[_index].counterror = false;
				} else {
					var toastEvent = $A.get("e.force:showToast");
					toastEvent.setParams({
						"title": "Warning!",
						"message": "Qty(EA) cannot be further reduced. ",
						"type": "Warning"
					});
					toastEvent.fire();
				}
			}
		}
		var unitPrice = quotationItemList[_index].List_Price__c;
		var subtotal = Number(unitPrice) * Number(quotationItemList[_index].Quantity__c);
		quotationItemList[_index].Sub_Total__c = subtotal.toFixed(2);
		// helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
		// helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);
		helper.calculateTotal(component);
		component.set("v.quotationItemList", quotationItemList);

		if (quotationItemList[_index].Promotion) {
			var promotion = quotationItemList[_index].Promotion;
			var hasRemoveItem = false;
			var hasPopup = false;
			var ruleName = quotationItemList[_index].Promotion_Rule_Name__c;
			if (promotion && (quotationItemList[_index].HasPromo || quotationItemList[_index].isThreshold)) {
				var isMeetThreshold = helper.isMeetThreshold(quotationItemList, _index, promotion);
				if (promotion.promotion.Promotion_Type__c == "BOGO" || promotion.promotion.Promotion_Type__c == "Full Pallet Promo") {
					if (isMeetThreshold) {
						helper.updateThresholdItem(quotationItemList, _index, promotion);
						helper.updateOfferingItems(quotationItemList, _index, promotion);
					} else {
						var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
						helper.removePromotionQuotationItem(quotationItemList, _index, component);
						helper.clearPromotionStatus(quotationItemList, initialIndex);
						hasRemoveItem = true;
					}
				} else if (promotion.promotion.Promotion_Type__c == "Price Break") {
					if (isMeetThreshold) {
						var oldRuleName = quotationItemList[_index].Promotion_Rule_Name__c;
						var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, _index, promotion);
						var newRuleName = avilableRuleItem.ruleName;
						if (oldRuleName == newRuleName) {
							helper.updateThresholdItem(quotationItemList, _index, promotion);
							helper.updateOfferingItems(quotationItemList, _index, promotion);
						} else {
							if (helper.isPoolFreeGoods(avilableRuleItem)) {
								helper.clearSelectedProducts(promotion);
								var offeringOpts = [];
								avilableRuleItem.offeringList.forEach(function (oItem) {
									if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
										offeringOpts.push(oItem);
									}
								});

								quotationItemList[_index].hasPool = true;
								component.set("v.offeringOpts", offeringOpts);
								component.set("v.selectedPromotion", promotion);
								component.set("v.showOfferingProduct", true);
								hasPopup = true;
								component.set("v.editRow", _index);
								return;
							} else {
								helper.removePromotionQuotationItem(quotationItemList, _index, component);
								var indexObj = {
									"startindex": 0
								};
								quotationItemList[_index].hasPool = false;
								helper.addThresholdItems(quotationItemList, _index, indexObj, component);
								helper.updateThresholdItem(quotationItemList, _index, promotion);
								helper.addOfferingItems(quotationItemList, _index, promotion, indexObj, component);
								hasRemoveItem = true;
							}
						}
					} else {
						helper.removePromotionQuotationItem(quotationItemList, _index, component);
						helper.clearPromotionStatus(quotationItemList, _index);
						hasRemoveItem = true;
					}
				} else if (promotion.promotion.Promotion_Type__c == "Price Discount") {
					if (isMeetThreshold) {
						helper.updateThresholdItem(quotationItemList, _index, promotion);
					} else {
						var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
						helper.removePromotionQuotationItem(quotationItemList, _index, component);
						helper.clearPromotionStatus(quotationItemList, initialIndex);
						hasRemoveItem = true;
					}
				} else if (promotion.promotion.Promotion_Type__c == "Mix & Match") {
					if (isMeetThreshold) {
						// helper.updateThresholdItem(quotationItemList, _index, promotion);
						helper.updateThresholdItemMixMatch(quotationItemList, _index, promotion);
						helper.updateOfferingItems(quotationItemList, _index, promotion);
					} else {
						quotationItemList.forEach(function (qItem) {
							if (qItem.Promotion_Rule_Name__c == ruleName && qItem.isMix) {
								qItem.isMeet = false;
								qItem.Promo_Discount_Amount__c = 0.0;
								if (qItem.Quantity__c && qItem.Quantity__c > 0) {
									qItem.Sub_Total__c = (
										Number(qItem.List_Price__c * qItem.Quantity__c) +
										Number(qItem.Discount_Amount__c) +
										Number(qItem.Promo_Discount_Amount__c) +
										Number(qItem.Whole_Order_Promo_Discount_Amount__c)
									).toFixed(2);
									qItem.Unit_Price__c = Number(qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2);
								} else {
									qItem.Sub_Total__c = 0.0;
									qItem.Unit_Price__c = qItem.List_Price__c;
								}
							}
						});
						helper.removeOfferingItemsOutOfPool(quotationItemList, _index, promotion);
					}
				} else if (promotion.promotion.Promotion_Type__c == "Others") {
					if (isMeetThreshold) {
						// check threshold has mix & match, if has, use mix & match method
						if (helper.hasMixMatch(promotion)) {
							helper.updateThresholdItemMixMatch(quotationItemList, _index, promotion);
						} else {
							helper.updateThresholdItem(quotationItemList, _index, promotion);
						}
						helper.updateOfferingItems(quotationItemList, _index, promotion);
					} else {
						if (helper.hasMixMatch(promotion)) {
							quotationItemList.forEach(function (qItem) {
								if (qItem.Promotion_Rule_Name__c == ruleName && (qItem.Is_Initial__c || qItem.isThreshold)) {
									qItem.Promo_Discount_Amount__c = 0.0;
									if (qItem.Quantity__c && qItem.Quantity__c > 0) {
										qItem.Sub_Total__c = (
											Number(qItem.List_Price__c * qItem.Quantity__c) +
											Number(qItem.Discount_Amount__c) +
											Number(qItem.Promo_Discount_Amount__c) +
											Number(qItem.Whole_Order_Promo_Discount_Amount__c)
										).toFixed(2);
										qItem.Unit_Price__c = Number(qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2);
									} else {
										qItem.Sub_Total__c = 0.0;
										qItem.Unit_Price__c = qItem.List_Price__c;
									}
									if (qItem.isMix) {
										qItem.isMeet = false;
									}
								}
							});
							helper.removeOfferingItemsOutOfPool(quotationItemList, _index, promotion);
						} else {
							var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
							helper.removePromotionQuotationItem(quotationItemList, _index, component);
							helper.clearPromotionStatus(quotationItemList, initialIndex);
							hasRemoveItem = true;
						}
					}
				}
			} else if (promotion && quotationItemList[_index].isOffering && quotationItemList[_index].isPool) {
				if (quotationItemList[_index].Promotion__c == quotationItemList[_index].Whole_Order_Promotion__c) {
					quotationItemList[_index].Whole_Order_Promo_Discount_Amount__c = Number(quotationItemList[_index].List_Price__c * quotationItemList[_index].Quantity__c) * -1;
				} else {
					quotationItemList[_index].Promo_Discount_Amount__c = Number(quotationItemList[_index].List_Price__c * quotationItemList[_index].Quantity__c) * -1;
				}

				quotationItemList[_index].Sub_Total__c = 0.0;
			}

			if (hasPopup) {
				return;
			}
			if (!hasRemoveItem) {
				if (promotion && quotationItemList[_index].Promotion) {
					var ruleItem = promotion.ruleList[0];
					if (promotion.promotion.Promotion_Type__c == "Price Break") {
						var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, _index, promotion);
						if (avilableRuleItem) {
							ruleItem = avilableRuleItem;
						}
					}
					if (quotationItemList[_index].Promotion__c != quotationItemList[_index].Whole_Order_Promotion__c) {
						if (helper.isPoolFreeGoods(ruleItem)) {
							helper.checkMeetOfferingPoolLimit(quotationItemList, _index, promotion);
						}
					} else {
						if (helper.isPoolFreeGoods(ruleItem)) {
							helper.checkMeetWholeOrderPromoOfferingPoolLimit(quotationItemList, component);
							component.set("v.quotationItemList", quotationItemList);
							helper.calculateTotal(component);
							return;
						}
					}
				}
			}

			helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
			helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

			if (hasRemoveItem) {
				component.set("v.quotationItemList", []);
			}
		}

		// 24.10.23: 修复移除了不符合Promotion Rule的产品后，执行到此行报错。
        if(quotationItemList[_index] && quotationItemList[_index].counterror == false){
			for (var s = 0; s < quotationItemList.length; s++) {
				var woqty = quotationItemList[s].Quantity__c;
				for (j = 0; j < quotationItemList.length; j++) {
					if (quotationItemList[j].ProductCode__c == quotationItemList[s].ProductCode__c) {
						if (j != s) {
							var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
							var needcheck = 1;
						}
					}
				}
				if (needcheck == 1) {
					var validateqty = Number(woqty) % Number(quotationItemList[s].CS_Exchange_Rate__c);
				} else {
					var validateqty = Number(quotationItemList[s].Quantity__c) % Number(quotationItemList[s].CS_Exchange_Rate__c);
				}

				if (validateqty != 0 && ExpirateDate == true) {
					var needcheckqty = false;
					var notonlyfreegoods = false;
					for (var x = 0; x < quotationItemList.length; x++) {
						if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
							var notonlyfreegoods = true;
							if (quotationItemList[x].Promotion) {
								if (quotationItemList[x].Promotion.ruleList) {
									for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
										if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
											for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
												if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
													for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
														if (
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
														) {
															var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
															// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
															if (multiplecontrol == "Inner Box Multiple") {
																var needcheckqty = true;
															}
														}
													}
												}
											}
										}
									}
								}
							} else {
								var needcheckqty = true;
							}
						}
					}

					if (notonlyfreegoods == false) {
						var needcheckfreegoodsqty = false;
						for (var x = 0; x < quotationItemList.length; x++) {
							if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
								if (quotationItemList[x].Promotion.ruleList) {
									for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
										if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
											for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
												if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
													for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
														if (
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
														) {
															var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
															// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
															if (multiplecontrol == "Inner Box Multiple") {
																var needcheckfreegoodsqty = true;
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
						if (needcheckfreegoodsqty == true) {
							quotationItemList[s].counterror = true;
						} else {
							quotationItemList[s].counterror = false;
						}
					} else {
						if (needcheckqty == true) {
							quotationItemList[s].counterror = true;
						} else {
							quotationItemList[s].counterror = false;
						}
					}
				} else {
					quotationItemList[s].counterror = false;
				}
			}
		}

		component.set("v.quotationItemList", quotationItemList);
		helper.calculateTotal(component);
	},

	handleIncreaseClick: function (component, event, helper) {
		console.log("handleIncreaseClickyyy");
		var quotationItemList = component.get("v.quotationItemList");
		let _index = component.get("v.operationRow");
		var qItem = quotationItemList[_index];
		var promquantity = quotationItemList[_index].Quantity__c;
		var ExpirateDate = component.get("v.ExpirateDate");
		var fullpalletprom = false;
		// add haibo
        var buyqty = 1;
        var productBuyQty = 1;
        console.log(JSON.stringify(qItem), 'qItem================');

		if (quotationItemList[_index].Promotion) {
			if (quotationItemList[_index].Promotion.ruleList) {
				for (var i = 0; i < quotationItemList[_index].Promotion.ruleList.length; i++) {
					if (quotationItemList[_index].Promotion.ruleList[i] && quotationItemList[_index].Promotion.ruleList[i].thresholdList) {
						console.log(JSON.stringify(quotationItemList[_index].Promotion.ruleList[i].thresholdList), 'thresholdList.products================');
						for (var j = 0; j < quotationItemList[_index].Promotion.ruleList[i].thresholdList.length; j++) {
							//点加号时判断promotion是否为FP，如果是，需要加fP的值
							if (quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName == "By_Full_Pallet_Quantity_of_Specific_Product") {
								if (quotationItemList[_index].Promotion.ruleList[i].thresholdList[j] && quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products) {
									for (var k = 0; k < quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
										if (
											quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Product__r.Full_Pallet_Quantity__c &&
											quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Product__r.Full_Pallet_Quantity__c != 0
										) {
											var fullpalletqty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Product__r.Full_Pallet_Quantity__c;
											var fullpalletprom = true;
										}
									}
								}
							}
							//BUY QTY promotion如果是price Discount 或者 Price Break，查找Buy Qty
							if (
								quotationItemList[_index].Promotion.promotion.Promotion_Type__c == "Price Discount" ||
								quotationItemList[_index].Promotion.promotion.Promotion_Type__c == "Price Break"
							) {
								if (
									quotationItemList[_index].Promotion.ruleList[i].thresholdList[j]
									&& ['By_Quantity_of_Specific_Product', 'By_Amount_of_Specific_Product'].includes(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName)
                                    && quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[0].Product__c === quotationItemList[_index].Product__c
								) {
									var buyqty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
								}
							}
							// add haibo: 新增 Therthod 为By Quantity of Specific Product && product buyQty, 按 product buyQty成倍修改数量（应用promotion时）

							for(var k=0; k<quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products.length; k++){
								if(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j]
									&& (quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName == 'By_Quantity_of_Specific_Product'
									&& quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c)){
									if (quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == qItem.Product__c) {
										// 根据promotion type获取不同的buy qty
										if (quotationItemList[_index].Promotion.promotion.Promotion_Type__c == 'Price Discount') {
											productBuyQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
										} else {
											productBuyQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c;
										}
									}
								}
							}
						}
					}
				}
			}
		}
		// add haibo
		console.log(productBuyQty, 'productBuyQty====================');
		console.log('buyqty ->',buyqty);

		//Change0215  WO在买品没有Promotion的情况下没有验证买品加赠品共同的QTY
		for (i = 0; i < quotationItemList.length; i++) {
			if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
				if (i != _index) {
					var promquantity = Number(promquantity) + Number(quotationItemList[i].Quantity__c);
					var needcheck = 1;
				}
			}
		}
		if (needcheck == 1) {
			console.log('needcheck================');
			if (fullpalletprom == true) {
				quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(fullpalletqty);
				promquantity = Number(promquantity) + Number(fullpalletqty);
			}
			// add haibo
			else if (productBuyQty && productBuyQty != 0 && productBuyQty != 1) {
				quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(productBuyQty);
				promquantity = Number(promquantity) + Number(productBuyQty);
			}
			else if(buyqty && buyqty != 0 && buyqty != 1 && productBuyQty == 1){
				//for buyqty
				if (quotationItemList[_index].Quantity__c % Number(buyqty) != 0) {
					for (var i = 0; i < buyqty; i++) {
						if ((Number(quotationItemList[_index].Quantity__c) + i) % Number(buyqty) == 0) {
							quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + i;
							promquantity = Number(promquantity) + i;
						}
					}
				} else {
					quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(buyqty);
					promquantity = Number(promquantity) + Number(buyqty);
				}
			} else {
				quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + 1;
				promquantity = Number(promquantity) + 1;
			}
			var promremainder = Number(promquantity) % Number(quotationItemList[_index].CS_Exchange_Rate__c);
			if (promremainder != 0 && ExpirateDate == true) {
				var needcheckqty = false;
				var notonlyfreegoods = false;
				for (var x = 0; x < quotationItemList.length; x++) {
					if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
						var notonlyfreegoods = true;
						if (quotationItemList[x].Promotion) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
														// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						} else {
							var needcheckqty = true;
						}
					}
				}

				if (notonlyfreegoods == false) {
					var needcheckfreegoodsqty = false;
					for (var x = 0; x < quotationItemList.length; x++) {
						if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
														// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckfreegoodsqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					if (needcheckfreegoodsqty == true) {
						for (var i = 0; i < quotationItemList.length; i++) {
							if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
								quotationItemList[i].counterror = true;
							}
						}
					}
				} else {
					if (needcheckqty == true) {
						for (var i = 0; i < quotationItemList.length; i++) {
							if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
								quotationItemList[i].counterror = true;
							}
						}
					}
				}
			} else {
				quotationItemList[_index].counterror = false;
				for (var i = 0; i < quotationItemList.length; i++) {
					if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
						quotationItemList[i].counterror = false;
					}
				}
			}
		} else {
			console.log('no needcheck================');
            console.log('handleIncreaseClick quotationItemList[_index].Quantity__c ->',quotationItemList[_index].Quantity__c);
			if (fullpalletprom == true) {
				quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(fullpalletqty);
			}
			// add haibo
			else if (productBuyQty && productBuyQty != 0 && productBuyQty != 1) {
				quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(productBuyQty);
				promquantity = Number(promquantity) + Number(productBuyQty);
			}
			else if(buyqty && buyqty != 0 && buyqty != 1 && productBuyQty == 1){
				//for buyqty
				if (quotationItemList[_index].Quantity__c % Number(buyqty) != 0) {
					for (var i = 0; i < buyqty; i++) {
						if ((Number(quotationItemList[_index].Quantity__c) + i) % Number(buyqty) == 0) {
							quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + i;
						}
					}
				} else {
					quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(buyqty);
				}

				if (quotationItemList[_index].Quantity__c % Number(quotationItemList[_index].CS_Exchange_Rate__c) != 0) {
					var needcheckqty = false;
					var notonlyfreegoods = false;
					for (var x = 0; x < quotationItemList.length; x++) {
						if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
							var notonlyfreegoods = true;
							if (quotationItemList[x].Promotion) {
								if (quotationItemList[x].Promotion.ruleList) {
									for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
										if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
											for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
												if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
													for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
														if (
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
														) {
															var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
															// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
															if (multiplecontrol == "Inner Box Multiple") {
																var needcheckqty = true;
															}
														}
													}
												}
											}
										}
									}
								}
							} else {
								var needcheckqty = true;
							}
						}
					}

					if (notonlyfreegoods == false) {
						var needcheckfreegoodsqty = false;
						for (var x = 0; x < quotationItemList.length; x++) {
							if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
								if (quotationItemList[x].Promotion.ruleList) {
									for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
										if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
											for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
												if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
													for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
														if (
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
														) {
															var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
															// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
															if (multiplecontrol == "Inner Box Multiple") {
																var needcheckfreegoodsqty = true;
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
						if (needcheckfreegoodsqty == true) {
							for (var i = 0; i < quotationItemList.length; i++) {
								if (quotationItemList[i].ProductCode__c == quotationItemList[index].ProductCode__c) {
									quotationItemList[i].counterror = true;
								}
							}
						}
					} else {
						if (needcheckqty == true) {
							for (var i = 0; i < quotationItemList.length; i++) {
								if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
									quotationItemList[i].counterror = true;
								}
							}
						}
					}
				} else {
					quotationItemList[_index].counterror = false;
					for (var i = 0; i < quotationItemList.length; i++) {
						if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
							quotationItemList[i].counterror = false;
						}
					}
				}
			} else {
				if (qItem.Quantity__c % qItem.CS_Exchange_Rate__c != 0) {
					for (var i = qItem.Quantity__c; i < qItem.Quantity__c + qItem.CS_Exchange_Rate__c; i++) {
						if (i % qItem.CS_Exchange_Rate__c == 0) {
							qItem.Quantity__c = i;
						}
					}
				} else {
					qItem.Quantity__c = Number(qItem.Quantity__c) + Number(qItem.CS_Exchange_Rate__c);
				}
				qItem.counterror = false;
			}
			console.log('quotationItemList[_index].Quantity__c -> ', quotationItemList[_index].Quantity__c);
		}
		var unitPrice = qItem.List_Price__c;
		var subtotal = Number(unitPrice) * Number(qItem.Quantity__c);
		qItem.Sub_Total__c = subtotal.toFixed(2);
		// helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
		// helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);
		helper.calculateTotal(component);
		component.set("v.quotationItemList", quotationItemList);

		if (quotationItemList[_index].Promotion) {
			//have a activated promotion
			var promotion = quotationItemList[_index].Promotion;
			var hasRemoveItem = false;
			var hasPopup = false;
			var ruleName = quotationItemList[_index].Promotion_Rule_Name__c;
			if (promotion && (quotationItemList[_index].HasPromo || quotationItemList[_index].isThreshold)) {
				console.log("freegoodserror = ", _index);
				var isMeetThreshold = helper.isMeetThreshold(quotationItemList, _index, promotion);
				if (promotion.promotion.Promotion_Type__c == "BOGO" || promotion.promotion.Promotion_Type__c == "Full Pallet Promo") {
					if (isMeetThreshold) {
						helper.updateThresholdItem(quotationItemList, _index, promotion);
						helper.updateOfferingItems(quotationItemList, _index, promotion);
					} else {
						var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
						helper.removePromotionQuotationItem(quotationItemList, _index, component);
						helper.clearPromotionStatus(quotationItemList, initialIndex);
						hasRemoveItem = true;
					}
				} else if (promotion.promotion.Promotion_Type__c == "Price Break") {
					if (isMeetThreshold) {
						var oldRuleName = quotationItemList[_index].Promotion_Rule_Name__c;
						var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, _index, promotion);
						var newRuleName = avilableRuleItem.ruleName;
						if (oldRuleName == newRuleName) {
							helper.updateThresholdItem(quotationItemList, _index, promotion);
							helper.updateOfferingItems(quotationItemList, _index, promotion);
						} else {
							if (helper.isPoolFreeGoods(avilableRuleItem)) {
								helper.clearSelectedProducts(promotion);
								var offeringOpts = [];
								avilableRuleItem.offeringList.forEach(function (oItem) {
									if (oItem.offering.RecordType.DeveloperName == "Pool_of_Free_Goods_of_Customer_Choice") {
										offeringOpts.push(oItem);
									}
								});

								quotationItemList[_index].hasPool = true;
								component.set("v.offeringOpts", offeringOpts);
								component.set("v.selectedPromotion", promotion);
								component.set("v.showOfferingProduct", true);
								hasPopup = true;
								component.set("v.editRow", _index);
								return;
							} else {
								helper.removePromotionQuotationItem(quotationItemList, _index, component);
								var indexObj = {
									"startindex": 0
								};
								quotationItemList[_index].hasPool = false;
								helper.addThresholdItems(quotationItemList, _index, indexObj, component);
								helper.updateThresholdItem(quotationItemList, _index, promotion);
								helper.addOfferingItems(quotationItemList, _index, promotion, indexObj, component);
								hasRemoveItem = true;
							}
						}
					} else {
						helper.removePromotionQuotationItem(quotationItemList, _index, component);
						helper.clearPromotionStatus(quotationItemList, _index);
						hasRemoveItem = true;
					}
				} else if (promotion.promotion.Promotion_Type__c == "Price Discount") {
					if (isMeetThreshold) {
						helper.updateThresholdItem(quotationItemList, _index, promotion);
					} else {
						var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
						helper.removePromotionQuotationItem(quotationItemList, _index, component);
						helper.clearPromotionStatus(quotationItemList, initialIndex);
						hasRemoveItem = true;
					}
				} else if (promotion.promotion.Promotion_Type__c == "Mix & Match") {
					if (isMeetThreshold) {
						helper.updateThresholdItemMixMatch(quotationItemList, _index, promotion);
						// helper.updateThresholdItem(quotationItemList, _index, promotion);
						helper.updateOfferingItems(quotationItemList, _index, promotion);
					} else {
						quotationItemList.forEach(function (qItem) {
							if (qItem.Promotion_Rule_Name__c == ruleName && qItem.isMix) {
								qItem.isMeet = false;
								qItem.Promo_Discount_Amount__c = 0.0;
								if (qItem.Quantity__c && qItem.Quantity__c > 0) {
									qItem.Sub_Total__c = (
										Number(qItem.List_Price__c * qItem.Quantity__c) +
										Number(qItem.Discount_Amount__c) +
										Number(qItem.Promo_Discount_Amount__c) +
										Number(qItem.Whole_Order_Promo_Discount_Amount__c)
									).toFixed(2);
									qItem.Unit_Price__c = Number(qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2);
								} else {
									qItem.Sub_Total__c = 0.0;
									qItem.Unit_Price__c = qItem.List_Price__c;
								}
							}
						});
						helper.removeOfferingItemsOutOfPool(quotationItemList, _index, promotion);
					}
				} else if (promotion.promotion.Promotion_Type__c == "Others") {
					if (isMeetThreshold) {
						// check threshold has mix & match, if has, use mix & match method
						if (helper.hasMixMatch(promotion)) {
							helper.updateThresholdItemMixMatch(quotationItemList, _index, promotion);
						} else {
							helper.updateThresholdItem(quotationItemList, _index, promotion);
						}
						helper.updateOfferingItems(quotationItemList, _index, promotion);
					} else {
						if (helper.hasMixMatch(promotion)) {
							quotationItemList.forEach(function (qItem) {
								if (qItem.Promotion_Rule_Name__c == ruleName && (qItem.Is_Initial__c || qItem.isThreshold)) {
									qItem.Promo_Discount_Amount__c = 0.0;
									if (qItem.Quantity__c && qItem.Quantity__c > 0) {
										qItem.Sub_Total__c = (
											Number(qItem.List_Price__c * qItem.Quantity__c) +
											Number(qItem.Discount_Amount__c) +
											Number(qItem.Promo_Discount_Amount__c) +
											Number(qItem.Whole_Order_Promo_Discount_Amount__c)
										).toFixed(2);
										qItem.Unit_Price__c = Number(qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2);
									} else {
										qItem.Sub_Total__c = 0.0;
										qItem.Unit_Price__c = qItem.List_Price__c;
									}
									if (qItem.isMix) {
										qItem.isMeet = false;
									}
								}
							});
							helper.removeOfferingItemsOutOfPool(quotationItemList, _index, promotion);
						} else {
							var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
							helper.removePromotionQuotationItem(quotationItemList, _index, component);
							helper.clearPromotionStatus(quotationItemList, initialIndex);
							hasRemoveItem = true;
						}
					}
				}
			} else if (promotion && quotationItemList[_index].isOffering && quotationItemList[_index].isPool) {
				if (quotationItemList[_index].Promotion__c == quotationItemList[_index].Whole_Order_Promotion__c) {
					quotationItemList[_index].Whole_Order_Promo_Discount_Amount__c = Number(quotationItemList[_index].List_Price__c * quotationItemList[_index].Quantity__c) * -1;
				} else {
					quotationItemList[_index].Promo_Discount_Amount__c = Number(quotationItemList[_index].List_Price__c * quotationItemList[_index].Quantity__c) * -1;
				}

				quotationItemList[_index].Sub_Total__c = 0.0;
			}

			if (hasPopup) {
				return;
			}
			if (!hasRemoveItem) {
				if (promotion && quotationItemList[_index].Promotion) {
					var ruleItem = promotion.ruleList[0];
					if (promotion.promotion.Promotion_Type__c == "Price Break") {
						var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, _index, promotion);
						if (avilableRuleItem) {
							ruleItem = avilableRuleItem;
						}
					}
					if (quotationItemList[_index].Promotion__c != quotationItemList[_index].Whole_Order_Promotion__c) {
						if (helper.isPoolFreeGoods(ruleItem)) {
							helper.checkMeetOfferingPoolLimit(quotationItemList, _index, promotion);
						}
					} else {
						if (helper.isPoolFreeGoods(ruleItem)) {
							helper.checkMeetWholeOrderPromoOfferingPoolLimit(quotationItemList, component);
							component.set("v.quotationItemList", quotationItemList);
							helper.calculateTotal(component);
							return;
						}
					}
				}
			}

			helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
			helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

			if (hasRemoveItem) {
				component.set("v.quotationItemList", []);
			}

			//YANKO CTRL V REDUCE
		}

		//change0207 qItem.meetalert = 1;  qItem.meetalertmodel = Number(intj) + 1;
		if (quotationItemList[_index].counterror == false) {
			for (var s = 0; s < quotationItemList.length; s++) {
				var woqty = quotationItemList[s].Quantity__c;
				for (j = 0; j < quotationItemList.length; j++) {
					if (quotationItemList[j].ProductCode__c == quotationItemList[s].ProductCode__c) {
						if (j != s) {
							var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
							var needcheck = 1;
						}
					}
				}
				if (needcheck == 1) {
					var validateqty = Number(woqty) % Number(quotationItemList[s].CS_Exchange_Rate__c);
				} else {
					var validateqty = Number(quotationItemList[s].Quantity__c) % Number(quotationItemList[s].CS_Exchange_Rate__c);
				}

				if (validateqty != 0 && ExpirateDate == true) {
					var needcheckqty = false;
					var notonlyfreegoods = false;
					for (var x = 0; x < quotationItemList.length; x++) {
						if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
							var notonlyfreegoods = true;
							if (quotationItemList[x].Promotion) {
								if (quotationItemList[x].Promotion.ruleList) {
									for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
										if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
											for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
												if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
													for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
														if (
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
															quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
														) {
															var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
															// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
															if (multiplecontrol == "Inner Box Multiple") {
																var needcheckqty = true;
															}
														}
													}
												}
											}
										}
									}
								}
							} else {
								var needcheckqty = true;
							}
						}
					}

					if (notonlyfreegoods == false) {
						var needcheckfreegoodsqty = false;
						for (var x = 0; x < quotationItemList.length; x++) {
							if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
								if (quotationItemList[x].Promotion.ruleList) {
									for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
										if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
											for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
												if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
													for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
														if (
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
															quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
														) {
															var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
															// var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
															if (multiplecontrol == "Inner Box Multiple") {
																var needcheckfreegoodsqty = true;
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
						if (needcheckfreegoodsqty == true) {
							quotationItemList[s].counterror = true;
						} else {
							quotationItemList[s].counterror = false;
						}
					} else {
						if (needcheckqty == true) {
							quotationItemList[s].counterror = true;
						} else {
							quotationItemList[s].counterror = false;
						}
					}
				} else {
					quotationItemList[s].counterror = false;
				}
			}
		}

		component.set("v.quotationItemList", quotationItemList);
		helper.calculateTotal(component);
	},
	//YankoEnd
	handleUploadFinish: function (component, event, helper) {
		component.set("v.isBusy", true);
		let productInfos = event.getParam("data");
		console.log(productInfos);
		productInfos = JSON.parse(JSON.stringify(productInfos));
		let shipDate = component.get("v.quotation.Expected_Delivery_Date__c");
		let quotationItemList = component.get("v.quotationItemList");
		let ExpirateDate = null;
		productInfos.forEach((item) => {
			let data = item.InitData;
			let quotationItem = {};
			let expirateDate = data.ExpirateDate;
			ExpirateDate = data.ExpirateDate;
			let orgCode = data.OrgCode;
			component.set("v.ExpirateDate", expirateDate);
			component.set("v.OrgCode", orgCode);

			if (shipDate) {
				quotationItem["Ship_Date__c"] = shipDate;
			}

			if (data.product) {
				if (data.product.CS_Exchange_Rate__c && orgCode != "CCA") {
					quotationItem["CS_Exchange_Rate__c"] = data.product.CS_Exchange_Rate__c;
				} else {
					quotationItem["CS_Exchange_Rate__c"] = 1;
				}
				quotationItem["Quantity__c"] = item.Quantity;
			} else {
				quotationItem["CS_Exchange_Rate__c"] = 1;
				quotationItem["Quantity__c"] = 1;
			}
			quotationItem["Product__c"] = item.ProductId;
			quotationItem["Product__r"] = {};
			quotationItem["Product__r"]["SF_Description__c"] = data.product.SF_Description__c;
			quotationItem["ExpirateDate"] = data.ExpirateDate;
			quotationItem["Brand__c"] = data.product.Brand_Name__c;
			quotationItem["ProductCode__c"] = data.product.ProductCode;
			quotationItem["Gross_Weight__c"] = data.product.Weight__c == undefined ? 0.0 : data.product.Weight__c;
			quotationItem["Is_Over_Size_Product__c"] = data.product.OverSize__c;

			quotationItem["ProductCode__c"] = item.ProductCode;
			quotationItem["Brand__c"] = data.product.Brand_Name__c;

			var unitPrice = 0.0;
			if (data.priceBookEntry) {
				unitPrice = data.priceBookEntry.UnitPrice;
				quotationItem["Price_Book__c"] = data.priceBookEntry.Pricebook2Id;
			}

			if (unitPrice) {
				quotationItem["List_Price__c"] = unitPrice.toFixed(2);
				quotationItem["Unit_Price__c"] = unitPrice.toFixed(2);
				quotationItem["Sub_Total__c"] = (unitPrice * quotationItem["Quantity__c"]).toFixed(2);
			} else {
				quotationItem["List_Price__c"] = 0.0;
				quotationItem["Unit_Price__c"] = 0.0;
				quotationItem["Sub_Total__c"] = 0.0;
			}
			quotationItem["Discount_Amount__c"] = 0.0;
			quotationItem["Promo_Discount_Amount__c"] = 0.0;
			quotationItem["Whole_Order_Promo_Discount_Amount__c"] = 0.0;

			quotationItem["promotionList"] = data.promotionList;
			if (data.promotionList && data.promotionList.length > 0) {
				quotationItem["HasPromo"] = true;
				var promotionCodesStr = "";
				data.promotionList.forEach(function (pItem) {
					promotionCodesStr += "'" + pItem.promotion.Promo_Code__c + "',";
				});
				var strCodes = promotionCodesStr.slice(0, -1);
				quotationItem["promotionFilterCondition"] =
					'[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Open"},{"FieldName":"Promo_Code__c", "Condtion":"IN", "Value":"(' +
					strCodes +
					')"}]';
			} else {
				quotationItem["HasPromo"] = false;
				quotationItem["promotionFilterCondition"] =
					'[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Error"}]';
			}
			quotationItem["Is_Initial__c"] = true;

			quotationItemList.push(quotationItem);
		});

		helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
		helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

		for (var s = 0; s < quotationItemList.length; s++) {
			var woqty = quotationItemList[s].Quantity__c;
			for (j = 0; j < quotationItemList.length; j++) {
				if (quotationItemList[j].ProductCode__c == quotationItemList[s].ProductCode__c) {
					if (j != s) {
						var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
						var needcheck = 1;
					}
				}
			}
			if (needcheck == 1) {
				var validateqty = Number(woqty) % Number(quotationItemList[s].CS_Exchange_Rate__c);
			} else {
				var validateqty = Number(quotationItemList[s].Quantity__c) % Number(quotationItemList[s].CS_Exchange_Rate__c);
			}

			if (validateqty != 0 && ExpirateDate == true) {
				var needcheckqty = false;
				var notonlyfreegoods = false;
				for (var x = 0; x < quotationItemList.length; x++) {
					if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
						var notonlyfreegoods = true;
						if (quotationItemList[x].Promotion) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						} else {
							var needcheckqty = true;
						}
					}
				}

				if (notonlyfreegoods == false) {
					var needcheckfreegoodsqty = false;
					for (var x = 0; x < quotationItemList.length; x++) {
						if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckfreegoodsqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					if (needcheckfreegoodsqty == true) {
						quotationItemList[s].counterror = true;
					} else {
						quotationItemList[s].counterror = false;
					}
				} else {
					if (needcheckqty == true) {
						quotationItemList[s].counterror = true;
					} else {
						quotationItemList[s].counterror = false;
					}
				}
			} else {
				quotationItemList[s].counterror = false;
			}
		}

		component.set("v.quotationItemList", []);
		component.set("v.quotationItemList", quotationItemList);
		helper.calculateTotal(component);
		helper.getPaymentTermRule(component);
		component.set("v.isBusy", false);
	}
});