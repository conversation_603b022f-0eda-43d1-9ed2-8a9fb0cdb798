@isTest
private class CCM_SubmitApprovalSyncProspectTest {
    static testMethod void testMethod1() {
        test.startTest();
        Lead l = new Lead();
        l.LastName = 'test1';
        l.Email = '<EMAIL>';
        l.Credit_Limit__c = '20000';
        l.Risk_Code__c = 'R01';
        Insert l;

        Contact con =  Test_SalesData.createContact(l.Id, null);

        Account_Address__c add = new Account_Address__c();
        add.Primary__c = true;
        add.Prospect__c = l.Id;
        add.Store_Number__c = '111';
        add.RecordTypeId = CCM_Contants.BILLING_ADDRESS_RECORDTYPEID;
        add.Contact__c = con.Id;
        insert add;


        Attachment_Management__c am = new Attachment_Management__c(
            Attachment_Type__c = 'Brand Program',
			Prospect__c = l.Id
        );

        insert am;

        ContentVersion cv = new ContentVersion();
        cv.Title = 'Test Document';
        cv.PathOnClient = 'TestDocument.pdf';
        cv.VersionData = Blob.valueOf('Test Content');
        cv.IsMajorVersion = true;
		insert cv;

		Id conDocId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id =:cv.Id].ContentDocumentId;

        ContentDocumentLink cdl = new ContentDocumentLink();
        cdl.LinkedEntityId = am.Id;
        cdl.ContentDocumentId = conDocId;
        cdl.shareType = 'V';
        insert cdl;

        Sales_Program__c sp = new Sales_Program__c();
        sp.Prospect__c = l.Id;
        sp.Approval_Status__c = 'Pending for Approval';
        sp.Payment_Term__c = 'NA001';
        sp.Authorized_Brand_Name_To_Oracle__c = 'test1';
        insert sp;

        Address_With_Program__c awp = new Address_With_Program__c();
        awp.Program__c = sp.Id;
        awp.Account_Address__c = add.Id;
        Insert awp;

        sp.Approval_Status__c = 'Approved';
        update sp;

        sp.Approval_Status__c = 'Pending for Approval';
        update sp;

        sp.Approval_Status__c = 'Rejected';
        update sp;

        test.stopTest();
    }
}