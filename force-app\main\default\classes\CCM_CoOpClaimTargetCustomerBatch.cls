/**
 * @name: CCM_CoOpClaimTargetCustomerBatch
 * @description: bulk update target customer, if customer existing in program customer, cannot delete target customer
 */
global without sharing class CCM_CoOpClaimTargetCustomerBatch implements Database.Batchable<SObject>,Database.Stateful{
    public static String currentYear = String.valueOf(Date.today().year());
    public String defaultORGCode = 'CCA';
    public String defalutFilterCondition = '=';
    public String queryFilter = '';
    public static String queryStr = 'SELECT Id, ORG_Code__c, Customer_Type__c, Approval_Status__c, Start_Date__c, End_Date__c, Program_Status__c, Program_Year__c, Payment_Method__c, Purchase_Type__c, Customer_Cluster__c, Customer_Sub_Cluster__c, Sales_Channel__c, Sales_Group__c, Eligible_Brands__c ' 
                                    + ' FROM Co_Op_Program__c WHERE Program_Year__c = :currentYear AND RecordType.DeveloperName = \'Standard\' ';
    

    global CCM_CoOpClaimTargetCustomerBatch(String queryCondition) {
        if (String.isNotEmpty(queryCondition)) {
            this.defalutFilterCondition = queryCondition;
        }
    }
    global CCM_CoOpClaimTargetCustomerBatch() {}
    /**
     * @description: batch start method
     */
    global List<SObject> start(Database.BatchableContext bc){ 
        queryStr += ' AND ORG_Code__c ' + this.defalutFilterCondition + ' \'' + defaultORGCode + '\'';
        queryFilter = ' AND ORG_Code__c ' + this.defalutFilterCondition + ' \'' + defaultORGCode + '\'';
        System.debug('*** queryStr: ' + queryStr);
        return Database.query(queryStr);
    }

    /**
     * @description: batch execute method to delete old customer and add new customer
     */
    global void execute(Database.BatchableContext bc, List<SObject> scope){ 
        List<Co_Op_Program__c> programs = (List<Co_Op_Program__c>)scope;
        System.debug('*** programs: ' + programs);

        Map<String, Set<String>> existingCoOpTargetCustomerMap = getExistingCoOpTargetCustomers(programs);

        List<Co_Op_Target_Customer__c> coOpTargetCustomerList = new List<Co_Op_Target_Customer__c>();
        
        List<String> salesGroupList         = new List<String>();
        List<String> salesChannelList       = new List<String>();
        List<String> customerTypeList       = new List<String>();
        List<String> customerClusterList    = new List<String>();
        List<String> customerSubClusterList = new List<String>();
        List<String> selectedBrands         = new List<String>();
        Set<String> orgCodeSet              = new Set<String>(); 
        
        Set<String> updateProgramIdSet  = new Set<String>();
        Set<String> programIdSet        = new Set<String>();
        for (Co_Op_Program__c coOpProgram : programs) {
            programIdSet.add(coOpProgram.Id);
            if (String.isEmpty(coOpProgram.ORG_Code__c)) {
                
            } else {
                orgCodeSet.add(coOpProgram.ORG_Code__c);
            }
            selectedBrands.addAll(getListForMultiSelect(coOpProgram.Eligible_Brands__c));
            salesGroupList.addAll(getListForMultiSelect(coOpProgram.Sales_Group__c));
            salesChannelList.addAll(getListForMultiSelect(coOpProgram.Sales_Channel__c));
            customerTypeList.addAll(getListForMultiSelect(coOpProgram.Customer_Type__c));
            customerSubClusterList.addAll(getListForMultiSelect(coOpProgram.Customer_Sub_Cluster__c));
            customerClusterList.addAll(getListForMultiSelect(coOpProgram.Customer_Cluster__c));
        }

        // get co-op program customer
        List<Co_Op_Program_Customer__c> programCustomerList = [SELECT Id, Customer__c, Co_Op_Program__c FROM Co_Op_Program_Customer__c WHERE Co_Op_Program__c IN :programIdSet];
        Set<String> programCustomerIdSet = new Set<String>();
        for (Co_Op_Program_Customer__c pc : programCustomerList) {
            programCustomerIdSet.add(pc.Customer__c);
        }

        // get customer for co-op program's fields
        List<Account> accountList = new List<Account>();
        if (salesGroupList.size() > 0 || salesChannelList.size() > 0 || customerTypeList.size() > 0 || customerSubClusterList.size() > 0 || customerClusterList.size() > 0) {
            String recordTypeName = 'Channel';
            String soql = 'SELECT Id, Customer_Sub_Cluster__c, Distributor_or_Dealer__c, Customer_Cluster__c, Sales_Group__c, Sales_Channel__c FROM Account WHERE RecordType.DeveloperName = :recordTypeName AND AccountNumber != null ';
            if (salesGroupList.size() > 0) {
                soql += ' AND Sales_Group__c IN :salesGroupList';
            }
            if (salesChannelList.size() > 0) {
                soql += ' AND Sales_Channel__c IN :salesChannelList';
            }
            if (customerTypeList.size() > 0) {
                soql += ' AND Distributor_or_Dealer__c IN :customerTypeList';
            }
            if (customerSubClusterList.size() > 0) {
                soql += ' AND Customer_Sub_Cluster__c IN :customerSubClusterList';
            }
            if (customerClusterList.size() > 0) {
                soql += ' AND Customer_Cluster__c IN :customerClusterList';
            }
            soql += this.queryFilter;
            accountList = Database.query(soql);
        }

        Map<Id, List<String>> customerBrandsMap = new Map<Id, List<String>>();
        for (Sales_Program__c program : [SELECT Id, Brands__c, Customer__c 
                                            FROM Sales_Program__c 
                                            WHERE Brands__c IN :selectedBrands 
                                            AND Customer__c IN :accountList 
                                            AND RecordType.DeveloperName != 'Service' 
                                            AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME 
                                            AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME 
                                            AND IsDeleted = FALSE]) {
            if (!customerBrandsMap.containsKey(program.Customer__c)) {
                customerBrandsMap.put(program.Customer__c, new List<String>());
            }
            customerBrandsMap.get(program.Customer__c).add(program.Brands__c.toUpperCase());
        }

        for (Co_Op_Program__c coOpProgram : programs) {
            for (Account acc : accountList) {
                if(existingCoOpTargetCustomerMap.containsKey(coOpProgram.Id)) {
                    Set<String> existingCustomerIds = existingCoOpTargetCustomerMap.get(coOpProgram.Id);
                    if(existingCustomerIds.contains(acc.Id)) {
                        continue;
                    }
                }
                Boolean available = true;
                Co_Op_Target_Customer__c coOpTargetCustomer = new Co_Op_Target_Customer__c();
                coOpTargetCustomer.Co_Op_Program__c = coOpProgram.Id;
                coOpTargetCustomer.Customer__c = acc.Id;
                
                if (String.isNotEmpty(coOpProgram.Eligible_Brands__c)) {
                    if (customerBrandsMap.get(acc.Id) == null || customerBrandsMap.get(acc.Id).size() == 0) {
                        available = false;
                    } else {
                        Boolean containsBrand = false;
                        for (String brand : customerBrandsMap.get(acc.Id)) {
                            if (selectedBrands.contains(brand)) {
                                containsBrand = true;
                                break;
                            }
                        }
                        if (!containsBrand) {
                            available = false;
                        }
                    }
                }
                if (String.isNotEmpty(coOpProgram.Sales_Group__c)) {
                    if (coOpProgram.Sales_Group__c.contains(acc.Sales_Group__c)) {
                        coOpTargetCustomer.Sales_Group__c = acc.Sales_Group__c;
                    } else {
                        available = false;
                    }
                }
                if (String.isNotEmpty(coOpProgram.Sales_Channel__c)) {
                    if (coOpProgram.Sales_Channel__c.contains(acc.Sales_Channel__c)) {
                        coOpTargetCustomer.Sales_Channel__c = acc.Sales_Channel__c;
                    } else {
                        available = false;
                    }
                }
                if (String.isNotEmpty(coOpProgram.Customer_Type__c)) {
                    if (coOpProgram.Customer_Type__c.contains(acc.Distributor_or_Dealer__c)) {
                        coOpTargetCustomer.Customer_Type__c = acc.Distributor_or_Dealer__c;
                    } else {
                        available = false;
                    }
                }
                if (String.isNotEmpty(coOpProgram.Customer_Sub_Cluster__c)) {
                    if (coOpProgram.Customer_Sub_Cluster__c.contains(acc.Customer_Sub_Cluster__c)) {
                        coOpTargetCustomer.Customer_Sub_Cluster__c = acc.Customer_Sub_Cluster__c;
                    } else {
                        available = false;
                    }
                }
                if (String.isNotEmpty(coOpProgram.Customer_Cluster__c)) {
                    if (coOpProgram.Customer_Cluster__c.contains(acc.Customer_Cluster__c)) {
                        coOpTargetCustomer.Customer_Cluster__c = acc.Customer_Cluster__c;
                    } else {
                        available = false;
                    }
                }

                if (available) {
                    updateProgramIdSet.add(coOpTargetCustomer.Co_Op_Program__c);
                    coOpTargetCustomerList.add(coOpTargetCustomer);
                }
            }
        }

        if (updateProgramIdSet.size() > 0) {
            delete [SELECT Id
                        FROM Co_Op_Target_Customer__c
                        WHERE 
                            Co_Op_Program__c IN :updateProgramIdSet 
                            AND Customer__c NOT IN :programCustomerIdSet 
                            AND RecordType.DeveloperName IN ('Target_Customer_Group', null) 
                    ];
        }
        
        if (coOpTargetCustomerList.size() > 0) {
            insert coOpTargetCustomerList;
        }
    }

    private static Map<String, Set<String>> getExistingCoOpTargetCustomers(List<Co_Op_Program__c> programs) {
        Map<String, Set<String>> existingCoOpTargetCustomerMap = new Map<String, Set<String>>();
        List<Co_Op_Target_Customer__c> targetCustomers = [SELECT Customer__c, Co_Op_Program__c FROM Co_Op_Target_Customer__c WHERE Co_Op_Program__c IN :programs];
        for (Co_Op_Target_Customer__c targetCustomer : targetCustomers) {
            if(!existingCoOpTargetCustomerMap.containsKey(targetCustomer.Co_Op_Program__c)){
                existingCoOpTargetCustomerMap.put(targetCustomer.Co_Op_Program__c, new Set<String>());
            }
            existingCoOpTargetCustomerMap.get(targetCustomer.Co_Op_Program__c).add(targetCustomer.Customer__c);
        }
        return existingCoOpTargetCustomerMap;
    }

    /**
     * @description: batch finish method
     */
    global void finish(Database.BatchableContext BC){
        System.debug('***** filterCondition: ' + this.defalutFilterCondition);
        if (this.defalutFilterCondition.trim() == '=') {
            Database.executeBatch(new CCM_CoOpClaimTargetCustomerBatch(' != '), 1);
        }
    }

    //get list for Multi-Select
    public static List<String> getListForMultiSelect(String str) {
        List<String> strList = new List<String>();
        if (String.isNotBlank(str)) {
            strList = str.split(';');
        }
        return strList;
    }
}