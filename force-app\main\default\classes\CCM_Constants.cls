/**
 * <AUTHOR>
 * @date 2021-07-16
 * @description This class is used to serve as a constant variable pool.
 */
public without sharing class CCM_Constants {
    @TestVisible
    private CCM_Constants() {
        System.debug(LoggingLevel.INFO, 'This is the constructor to stop being instantiated.');
    }
    // Common
    public static final String DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS = 'yyyy-MM-dd HH:mm:ss.SSS';
    public static final String SEMICOLON_REGEXP_SPACE_IGNORED = '\\s*;\\s*';
    public static final String CARRIAGE_RETURN = '\r';
    public static final String EMPTY = '';
    public static final String TAB = '\t';
    public static final String NEXT_LINE = '\n';
    public static final String COLON = ':';
    public static final String COMMA = ',';
    public static final String DOT = '.';

    public static final String ORG_CODE_CNA = 'CNA';
    public static final String ORG_CODE_CCA = 'CCA';

    public static final Set<String> CANADA_USER_ROLE_DEVELOPER_NAME = new Set<String>{'CA_General_Manager', 'CA_Inside_Sales', 'CA_TT_Sales_Manager', 'Nanjing_CA_BEAM_Manager', 'Nanjing_CA_BEAM',
                                                                                        'CA_Sales_Rep_1', 'CA_Sales_Rep_2', 'CA_Sales_Rep_3', 'CA_Sales_Rep_4', 'CA_Sales_Rep_5'};

    // @mark napoleon, 23-1-5, cca cs team's role can delegate parts order in the inner system
    public static final Set<String> CANADA_CS_TEAM_USER_ROLE_DEVELOPER_NAME = new Set<String>{
                                                                                        'CA_Inside_Sales',
                                                                                        'CA_TT_Sales_Manager',
                                                                                        'CA_Sales_Rep_1',
                                                                                        'CA_Sales_Rep_2',
                                                                                        'CA_Sales_Rep_3',
                                                                                        'CA_Sales_Rep_4',
                                                                                        'CA_Sales_Rep_5'};

    // add end

    public static final Set<String> CANADA_PAYMENT_TERM_PICKLIST_VALUE = new Set<String> {'NA999','CA001',
                                                                                            'CA003', 'CA005',
                                                                                            'CA006', 'CA014',
                                                                                            'CA017', 'CA019',
                                                                                            'CA020', 'CA055',
                                                                                            'CA064', '30 NET',
                                                                                            'NA051', 'NA085', 'NA086', 'NA087', 'NA088', 'NA089', 'NA090', 'NA072', 'NA081', 'NA068', 'NA074','NA115', 'NA116', 'NA117', 'NA118', 'NA127','NA113','NA133','NA114'
                                                                                            };
    public static final Set<String> US_PAYMENT_TERM_PICKLIST_VALUE = new Set<String> {
                        'NA001', 'NA002', 'NA007', 'NA009', 'NA010', 'NA012', 'NA015', 'NA016', 'NA017', 'NA019', 'NA022', 'NA027',
                        'NA028', 'NA031', 'NA032', 'NA035', 'NA036', 'NA037', 'NA038', 'NA039', 'NA040', 'NA041', 'NA042', 'NA043',
                        'NA052', 'NA055', 'NA057', 'NA059', 'NA060', 'NA061', 'NA0010', 'NA101', 'NA102', 'NA103', 'NA104', 'NA105',
                        'NA106', 'NA107', 'NA108', 'NA999', 'Z074', 'NA066', 'NA067', 'NA068', 'NA122', 'NA071', 'NA072', 'NA073',
                        'NA074', 'NA058', 'NA021', 'NA076', 'NA077', 'NA078', 'NA085', 'NA086', 'NA081', 'NA113', 'NA114', 'NA095', 'NA115', 'NA116', 'NA117', 'NA119', 'NA123', 'NA126', 'NA129','NA134','NA135'
                    };

    // Territory User Relationship
    public static final String TERRITORY_USER_RELATIONSHIP_ROLE_CA_GENERAL_MANAGER = 'CA General Manager';
    public static final String TERRITORY_USER_RELATIONSHIP_ROLE_CA_BEAM_MANAGER = 'CA BEAM Manager';
    public static final String TERRITORY_USER_RELATIONSHIP_ROLE_CA_TT_SALES_MANAGER = 'CA TT Sales Manager';
    public static final String TERRITORY_USER_RELATIONSHIP_ROLE_CA_TT_SALES_MANAGER_Developer_NAME = 'CA_TT_Sales_Manager';
    public static final String TERRITORY_USER_RELATIONSHIP_ROLE_CA_ACCOUNT_MANAGER = 'CA Account Manager (for Sales Rep)';
    public static final Set<String> TERRITORY_USER_RELATIONSHIP_ROLE_SET_CANADA = new Set<String>{TERRITORY_USER_RELATIONSHIP_ROLE_CA_GENERAL_MANAGER,
                                                                                                    TERRITORY_USER_RELATIONSHIP_ROLE_CA_BEAM_MANAGER,
                                                                                                    TERRITORY_USER_RELATIONSHIP_ROLE_CA_TT_SALES_MANAGER,
                                                                                                    TERRITORY_USER_RELATIONSHIP_ROLE_CA_ACCOUNT_MANAGER
                                                                                                    };

    // Profile
    public static final String PROFILE_NAME_PARTNER_COMMUNITY_SERVICE = 'Partner Community Service';
    public static final String PROFILE_NAME_SERVICE_LIVE = 'ServiceLive';
    // Customer
    public static final String CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_DEVELOPER_NAME = 'PersonAccount';
    public static final String CUSTOMER_AND_PROSPECT_RECORD_TYPE_CHANNEL_DEVELOPER_NAME = 'Channel';
    public static final String CUSTOMER_AND_PROSPECT_RECORD_TYPE_STORE_LOCATION_DEVELOPER_NAME = 'Store_Location';
    public static final String CUSTOMER_RECORD_TYPE_EXISTING_STORE_LOCATION_DEVELOPER_NAME = 'Existing_Store_Location';
    public static final String CUSTOMER_RECORD_TYPE_POTENTIAL_STORE_LOCATION_DEVELOPER_NAME = 'Potential_Store_Location';

    public static final Id CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_ID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id CUSTOMER_RECORD_TYPE_CHANNEL_ID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CUSTOMER_AND_PROSPECT_RECORD_TYPE_CHANNEL_DEVELOPER_NAME).getRecordTypeId();
    public static final Id CUSTOMER_RECORD_TYPE_STORE_LOCATION_ID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CUSTOMER_AND_PROSPECT_RECORD_TYPE_STORE_LOCATION_DEVELOPER_NAME).getRecordTypeId();
    public static final Id CUSTOMER_RECORD_TYPE_STORE_EXISTING_LOCATION_ID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CUSTOMER_RECORD_TYPE_EXISTING_STORE_LOCATION_DEVELOPER_NAME).getRecordTypeId();
    public static final Id CUSTOMER_RECORD_TYPE_STORE_POTENTIAL_LOCATION_ID = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(CUSTOMER_RECORD_TYPE_POTENTIAL_STORE_LOCATION_DEVELOPER_NAME).getRecordTypeId();

    public static final Id PROSPECT_RECORD_TYPE_CHANNEL_ID = Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get(CUSTOMER_AND_PROSPECT_RECORD_TYPE_CHANNEL_DEVELOPER_NAME).getRecordTypeId();
    // public static final Id PROSPECT_RECORD_TYPE_STORE_LOCATION_ID = Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get(CUSTOMER_AND_PROSPECT_RECORD_TYPE_STORE_LOCATION_DEVELOPER_NAME).getRecordTypeId();



    public static final String CUSTOMER_BUSINESS_TYPE_SALES = 'Sales';
    public static final String CUSTOMER_BUSINESS_TYPE_SERVICE = 'Service';
    public static final String CUSTOMER_BUSINESS_TYPE_SALES_AND_SERVICE = 'Sales & Service';

    // Account Team
    public static final String ACCOUNT_TEAM_ROLE_SALES_OPPORTUNITY = 'Sales Opportunity';
    public static final String ACCOUNT_TEAM_ROLE_SERVICE_OPPORTUNITY = 'Service Opportunity';

    // Customer Profile
    public static final String CUSTOMER_PROFILE_RECORD_TYPE_CUSTOMER_DEVELOPER_NAME = 'Customer';
    public static final String CUSTOMER_PROFILE_RECORD_TYPE_PROSPECT_DEVELOPER_NAME = 'Prospect';
    public static final Id CUSTOMER_PROFILE_RECORD_TYPE_CUSTOMER_ID = Schema.SObjectType.Customer_Profile__c.getRecordTypeInfosByDeveloperName().get(CUSTOMER_PROFILE_RECORD_TYPE_CUSTOMER_DEVELOPER_NAME).getRecordTypeId();
    public static final Id CUSTOMER_PROFILE_RECORD_TYPE_PROSPECT_ID = Schema.SObjectType.Customer_Profile__c.getRecordTypeInfosByDeveloperName().get(CUSTOMER_PROFILE_RECORD_TYPE_PROSPECT_DEVELOPER_NAME).getRecordTypeId();
    // Product2
    public static final String PRODUCT_PRODUCT_RECORD_TYPE_DEVELOPER_NAME = 'Product';
    public static final String PRODUCT_PARTS_RECORD_TYPE_DEVELOPER_NAME = 'Parts';
    public static final String PRODUCT_KIT_RECORD_TYPE_DEVELOPER_NAME = 'Kit';
    public static final String PRODUCT_ORACLE_MERCHANDISING_DEVELOPER_NAME = 'Oracle_Merchandising';
    public static final Id PRODUCT_PRODUCT_RECORD_TYPE_ID = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get(PRODUCT_PRODUCT_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id PRODUCT_PARTS_RECORD_TYPE_ID = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get(PRODUCT_PARTS_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id PRODUCT_KIT_RECORD_TYPE_ID = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get(PRODUCT_KIT_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id PRODUCT_ORACLE_MERCHANDISING_RECORD_TYPE_ID = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get(PRODUCT_ORACLE_MERCHANDISING_DEVELOPER_NAME).getRecordTypeId();
    public static final String PRODUCT_EBS_STATUS_DISCONTINUED = 'DISC';
    public static final String PRODUCT_EBS_STATUS_INACTIVE = 'Inactive';
    public static final String PRODUCT_EBS_STATUS_ACTIVE = 'Active';
    public static final String PRODUCT_EBS_STATUS_WIP = 'WIP';
    public static final String PRODUCT_EBS_STATUS_RTO = 'RTO';
    public static final String PRODUCT_SOURCE_PIM = 'PIM';
    public static final String PRODUCT_SOURCE_EBS = 'EBS';
    public static final String PRODUCT_CATEGORY_3_PROFESSIONAL = 'Professional';
    public static final String PRODUCT_CATEGORY_3_PROFESSIONAL_X = 'Professional-X';
    public static final String PRODUCT_PRODUCT_TYPE_PRODUCT = 'Product';
    public static final String PRODUCT_PRODUCT_TYPE_CHARGER = 'Charger';
    public static final String PRODUCT_PRODUCT_TYPE_BATTERY = 'Battery';
    // Warranty
    public static final String WARRANTY_PRODUCT_USE_TYPE_2_INDUSTRIAL_PROFESSIONAL_COMMERCIAL = 'Industrial/Professional/Commercial';
    public static final String WARRANTY_PRODUCT_USE_TYPE_2_RESIDENTIAL = 'Residential';
    public static final String WARRANTY_PURCHASE_PLACE_PICKLIST_UNAUTHORIZED_DEALER = 'Unauthorized Dealer';
    // Warranty Rule
    public static final String WARRANTY_RULE_RECORD_TYPE_DEVELOPER_NAME = 'Channel_Type';
    public static final Id WARRANTY_RULE_RECORD_TYPE_ID = Schema.SObjectType.Warranty_Rules__c.getRecordTypeInfosByDeveloperName().get(WARRANTY_RULE_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final String WARRANTY_RULE_PURCHASE_PLACE_UNAUTHORIZED_DEALER = 'Unauthorized Dealer';
    // Account Address
    public static final String ADDRESS_BILLING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME = 'Billing_Address';
    public static final String ADDRESS_DROPSHIP_BILLING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME = 'Dropship_Billing_Address';
    public static final String ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME = 'Dropship_Shipping_Address';
    public static final String ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME = 'Shipping_Address';
    public static final String ADDRESS_APPROVAL_STATUS_PENDING_FOR_APPROVAL = 'Pending for Approval';
    public static final Id ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(ADDRESS_BILLING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ADDRESS_DROPSHIP_BILLING_ADDRESS_RECORD_TYPE_ID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(ADDRESS_DROPSHIP_BILLING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_ID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(ADDRESS_DROPSHIP_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_ID = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByDeveloperName().get(ADDRESS_SHIPPING_ADDRESS_RECORD_TYPE_DEVELOPER_NAME).getRecordTypeId();
    // Authorized Brand
    public static final String SALES_PROGRAM_APPROVAL_STATUS_APPROVED = 'Approved';
    public static final String SALES_PROGRAM_APPROVAL_STATUS_PENDING_FOR_APPROVAL = 'Pending for Approval';
    public static final String SALES_PROGRAM_RECORD_TYPE_STANDARD_DEVELOPER_NAME = 'Standard';
    public static final String SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_DEVELOPER_NAME = 'Customized';
    public static final String SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_STANDARD_DEVELOPER_NAME = 'Dropship_Sales_Standard';
    public static final String SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_CUSTOMIZED_DEVELOPER_NAME = 'Dropship_Sales_Customized';
    public static final String SALES_PROGRAM_RECORD_TYPE_SERVICE_DEVELOPER_NAME = 'Service';
    public static final String SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME = 'Service_Customized';
    public static final String SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME = 'Service_Standard';
    public static final Id SALES_PROGRAM_RECORD_TYPE_STANDARD_ID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(SALES_PROGRAM_RECORD_TYPE_STANDARD_DEVELOPER_NAME).getRecordTypeId();
    public static final Id SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_ID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_DEVELOPER_NAME).getRecordTypeId();
    public static final Id SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_STANDARD_ID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_STANDARD_DEVELOPER_NAME).getRecordTypeId();
    public static final Id SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_CUSTOMIZED_ID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_CUSTOMIZED_DEVELOPER_NAME).getRecordTypeId();
    public static final Id SALES_PROGRAM_RECORD_TYPE_SERVICE_ID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(SALES_PROGRAM_RECORD_TYPE_SERVICE_DEVELOPER_NAME).getRecordTypeId();
    public static final Id SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_ID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME).getRecordTypeId();
    public static final Id SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_ID = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME).getRecordTypeId();
    public static final Set<Id> SALES_PROGRAM_RECORD_TYPE_SALES_IDS = new Set<Id> {SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_ID, SALES_PROGRAM_RECORD_TYPE_STANDARD_ID};
    public static final Set<Id> SALES_PROGRAM_RECORD_TYPE_SERVICE_IDS = new Set<Id> {SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_ID, SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_ID};
    public static final Set<Id> SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_IDS = new Set<Id> {SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_STANDARD_ID, SALES_PROGRAM_RECORD_TYPE_DROPSHIP_SALES_CUSTOMIZED_ID};
    public static final List<String> SALES_PROGRAM_SALES_GROUP = new List<String> {
        'SG01', 'SG02', 'SG03', 'SG04', 'SG05', 'SG06', 'SG07', 'SG08', 'SG09', 'SG10', 'SG21', 'SG22', 'SG23,', 'SG24', 'SG25', 'SG26', 'SG27', 'SG28', 'SG30', 'SG31', 'SG32', 'SG29',  // us
        'SG15', 'SG40', 'SG36', 'SG41', 'SG37', 'SG39', 'SG38', 'SG13' // canada
    };
    public static final List<String> SALES_PROGRAM_PAYMENT_TERM = new List<String> {
        'NA001', 'NA002', 'NA007', 'NA009', 'NA010', 'NA012', 'NA015', 'NA016', 'NA017', 'NA019', 'NA022', 'NA027', 'NA028', 'NA031', 'NA032', 'NA035', 'NA036', 'NA037',
        'NA038', 'NA039', 'NA040', 'NA041', 'NA042', 'NA043', 'NA052', 'NA055', 'NA057', 'NA059', 'NA060', 'NA061', 'NA0010', 'NA101', 'NA102', 'NA103', 'NA104', 'NA105',
        'NA106', 'NA107', 'NA108', 'NA999', 'Z074', 'NA066', 'NA067', 'NA068', 'NA122', 'NA071', 'NA072', 'NA073', 'NA074', 'NA058', 'NA021', 'NA076', 'NA077', 'NA051', 'NA078',
        'CA001', 'CA003', 'CA005', 'CA006', 'CA014', 'CA017', 'CA019', 'CA020', 'CA055', 'CA064', '30 NET' // Canada
    };
    public static final List<String> SALES_PROGRAM_FREIGHT_TERM = new List<String> {
                        'COLLECT', 'PICK', 'FOB', 'Paid', 'PPF', 'PPE', 'PPB', 'PPC', 'PPD', 'Due',
                        'PPA', 'DUECOST', 'THIRD_PARTY', 'TBD', 'PPG', 'PPH', 'PPJ', 'PPK', 'PPL', 'PPM',
                        'Prepaid @CAD150', 'Prepaid @CAD2000', 'CAD750'
    };
    public static final String SALES_PROGRAM_ORDER_TYPE_CA = 'CA Sales Order - CAD';
    public static final String SALES_PROGRAM_ORDER_TYPE_FOR_SERVICE = 'CNA Dropship Order';

    // Prospect Assignment Rule
    public static final String PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_CA_DISTRIBUTOR = 'Canada Distributor';
    public static final String PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_NA_DISTRIBUTOR = 'Distributor';
    public static final String PROSPECT_ASSIGNMENT_RULE_CLUSTER_CANADA_HOME_CENTER = 'CA-CG01';
    public static final String PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_CA = 'KA019';
    public static final String PROSPECT_ASSIGNMENT_RULE_CLUSTER_FOR_NA_CNA_CG01 = 'CNA-CG01';
    public static final String PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_NA_KA001 = 'KA001';
    // Sales Target
    public static final List<String> QUOTA_ALLOCATION_CHANNEL = new List<String> {
                        'PP-TT', 'OPE-TT', 'CA TT Team 1', 'CA TT Team 2'
    };
    // Process Instance
    public static final String PROCESS_INSTANCE_STATUS_PENDING = 'Pending';
    // Process Instance History
    public static final String PROCESS_INSTANCE_HISTORY_STEP_STATUS_PENDING = 'Pending';
    // Purchase Order
    public static final String PURCHASE_ORDER_RECORD_TYPE_PLACE_ORDER_DEVELOPER_NAME = 'Place_Order';
    public static final String PURCHASE_ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_DEVELOPER_NAME = 'Place_Parts_Order';
    public static final Id PURCHASE_ORDER_RECORD_TYPE_PLACE_ORDER_ID = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(PURCHASE_ORDER_RECORD_TYPE_PLACE_ORDER_DEVELOPER_NAME).getRecordTypeId();
    public static final Id PURCHASE_ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID = Schema.SObjectType.Purchase_Order__c.getRecordTypeInfosByDeveloperName().get(PURCHASE_ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_DEVELOPER_NAME).getRecordTypeId();
    public static final String PURCHASE_ORDER_ORDER_TYPE_CCA_PARTS_SALES_ORDER_CAD = 'CA Internal Dropship'; // CCA parts order, purchase order ,order type;
    public static final String PURCHASE_ORDER_ORDER_TYPE_CNA_PARTS_SALES_ORDER_USD = 'CNA Parts Sales Order - USD';
    public static final String PURCHASE_ORDER_ORDER_TYPE_CNA_SALES_ORDER_USD = 'CNA Sales Order - USD';
    public static final String PURCHASE_ORDER_STATUS_SUBMITTED = 'Submitted';
    public static final String PURCHASE_ORDER_SYNC_STATUS_IN_PROCESSING = 'In Processing';
    // Order
    public static final String ORDER_RECORD_TYPE_ACTIVATED_ORDER_DEVELOPER_NAME = 'Actived_Order';
    public static final String ORDER_RECORD_TYPE_CANCELED_ORDER_DEVELOPER_NAME = 'Canceled_Order';
    public static final String ORDER_RECORD_TYPE_DRAFT_ORDER_DEVELOPER_NAME = 'Draft_Order';
    public static final String ORDER_RECORD_TYPE_PLACE_ORDER_DEVELOPER_NAME = 'Place_Order';
    public static final String ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_DEVELOPER_NAME = 'Place_Parts_Order';
    public static final String ORDER_RECORD_TYPE_PLACE_REPOST_ORDER_DEVELOPER_NAME = 'Re_Post_Order';
    public static final String ORDER_RECORD_TYPE_ORDER_FOR_LACK_OF_STORAGE_ORDER_DEVELOPER_NAME = 'Order_For_Lack_of_Storage';
    public static final Id ORDER_RECORD_TYPE_ACTIVATED_ORDER_ID = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get(ORDER_RECORD_TYPE_ACTIVATED_ORDER_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ORDER_RECORD_TYPE_CANCELED_ORDER_ID = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get(ORDER_RECORD_TYPE_CANCELED_ORDER_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ORDER_RECORD_TYPE_DRAFT_ORDER_ID = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get(ORDER_RECORD_TYPE_DRAFT_ORDER_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ORDER_RECORD_TYPE_PLACE_ORDER_ID = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get(ORDER_RECORD_TYPE_PLACE_ORDER_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_ID = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get(ORDER_RECORD_TYPE_PLACE_PARTS_ORDER_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ORDER_RECORD_TYPE_REPOST_ORDER_ID = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get(ORDER_RECORD_TYPE_PLACE_REPOST_ORDER_DEVELOPER_NAME).getRecordTypeId();
    public static final Id ORDER_RECORD_TYPE_ORDER_FOR_LACK_OF_STORAGE_ORDER_ID = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get(ORDER_RECORD_TYPE_ORDER_FOR_LACK_OF_STORAGE_ORDER_DEVELOPER_NAME).getRecordTypeId();
    public static final String ORDER_TYPE_CNA_PARTS_SALES_ORDER_USD = 'CNA Parts Sales Order - USD';
    public static final String ORDER_TYPE_CNA_SALES_ORDER_USD = 'CNA Sales Order - USD';
    //Dropship Order @mark Austin, 2023.03.13
    public static final String SALES_HIERARCHY_APPROVAL_STATUS_APPROVED = 'Approved';
    public static final String SALES_HIERARCHY_APPROVAL_STATUS_DRAFT = 'Draft';
    public static final String SALES_HIERARCHY_APPROVAL_STATUS_PENDING_FOR_APPROVAL = 'Pending Approval';
    public static final String SALES_HIERARCHY_APPROVAL_STATUS_REJECTED = 'Approved';
    // Chervon Survey Question
    public static final String CHERVON_SURVEY_QUESTION_TYPE_FREE_TEXT = 'Free Text';
    public static final String CHERVON_SURVEY_QUESTION_TYPE_MULTI_SELECT = 'Multi Select';
    public static final String CHERVON_SURVEY_QUESTION_TYPE_SINGLE_SELECT = 'Single Select';
    // Claim Pack
    public static final String CLAIM_PACK_TYPE_SERVICE_CLAIM = 'Service Claim';
    public static final String CLAIM_PACK_TYPE_FLEET_CLAIM = 'Fleet Claim';
    // Warranty Claim
    public static final String WARRANTY_CLAIM_STATUS_APPROVED = 'Approved';
    // Fleet Claim
    public static final String FLEET_CLAIM_APPROVAL_STATUS_APPROVED = 'Approved';
    // System Configuration
    public static final String SYSTEM_CONFIGURATION_RECORD_TYPE_SN_FORMAT_DEVELOPER_NAME = 'SN_Format';
    public static final Id SYSTEM_CONFIGURATION_RECORD_TYPE_SN_FORMAT_ID = Schema.SObjectType.System_Configuration__c.getRecordTypeInfosByDeveloperName().get(SYSTEM_CONFIGURATION_RECORD_TYPE_SN_FORMAT_DEVELOPER_NAME).getRecordTypeId();

    // Co-Op Program
    public static final String CO_OP_PROGRAM_STANDARD_RECORD_TYPE_DEVELOPERNAME = 'Standard';
    public static final String CO_OP_PROGRAM_STANDARD_RECORD_TYPE_ID = Schema.SObjectType.CO_OP_Program__c.getRecordTypeInfosByDeveloperName().get(CO_OP_PROGRAM_STANDARD_RECORD_TYPE_DEVELOPERNAME).getRecordTypeId();

    // Kit Item
    public static final String KIT_ITEM_KITS_AND_PRODUCTS_RECORD_TYPE_DEVELOPERNAME = 'Kits_and_Products';
    public static final String KIT_ITEM_PRODUCTS_AND_DIAGRAM_RECORD_TYPE_DEVELOPERNAME = 'Products_and_Diagram';
    public static final String KIT_ITEM_PRODUCTS_AND_PARTS_RECORD_TYPE_DEVELOPERNAME = 'Products_and_Parts';
    public static final String KIT_ITEM_KITS_AND_PRODUCTS_RECORD_TYPE_ID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get(KIT_ITEM_KITS_AND_PRODUCTS_RECORD_TYPE_DEVELOPERNAME).getRecordTypeId();
    public static final String KIT_ITEM_PRODUCTS_AND_DIAGRAM_RECORD_TYPE_ID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get(KIT_ITEM_PRODUCTS_AND_DIAGRAM_RECORD_TYPE_DEVELOPERNAME).getRecordTypeId();
    public static final String KIT_ITEM_PRODUCTS_AND_PARTS_RECORD_TYPE_ID = Schema.SObjectType.Kit_Item__c.getRecordTypeInfosByDeveloperName().get(KIT_ITEM_PRODUCTS_AND_PARTS_RECORD_TYPE_DEVELOPERNAME).getRecordTypeId();

    public static final String WARRANTY_RETURN_POLICY_RECORD_TYPE_STANDARD_DEVELOPER_NAME = 'Standard';
    public static final String WARRANTY_RETURN_POLICY_RECORD_TYPE_CUSTOMIZED_DEVELOPER_NAME = 'Customized';
    public static final Id WARRANTY_RETURN_POLICY_RECORD_TYPE_STANDARD_ID = Schema.SObjectType.Warranty_Return_Policy__c.getRecordTypeInfosByDeveloperName().get(WARRANTY_RETURN_POLICY_RECORD_TYPE_STANDARD_DEVELOPER_NAME).getRecordTypeId();
    public static final Id WARRANTY_RETURN_POLICY_RECORD_TYPE_CUSTOMIZED_ID = Schema.SObjectType.Warranty_Return_Policy__c.getRecordTypeInfosByDeveloperName().get(WARRANTY_RETURN_POLICY_RECORD_TYPE_CUSTOMIZED_DEVELOPER_NAME).getRecordTypeId();

    public static final String SAMPLE_ORDER_RECORD_TYPE_DEMO_FREE_GOODS_DEVELOPER_NAME = 'Demo_Free_Goods';
    public static final String SAMPLE_ORDER_RECORD_TYPE_ID_DEMO_FREE_GOODS = Schema.SObjectType.Sample_Order__c.getRecordTypeInfosByDeveloperName().get(SAMPLE_ORDER_RECORD_TYPE_DEMO_FREE_GOODS_DEVELOPER_NAME).getRecordTypeId();

    // item switch to model
    /**
     * 回滚策略，
     * Label.Is_History_Product,默认是 false，改成 true 时，相当于代码回滚。
     */
    public static final Boolean blHistoryProduct = Boolean.valueOf(Label.Is_History_Product);
    //add by aria about visit jouney
    public static final String VJ_VisitPlan = '1VisitPlan';
    public static final String VJ_CheckIN = '2CheckIn';
    public static final String VJ_VisitSurvey = '3VisitSurvey';
    public static final String VJ_LogACall = '4LogACall';
    public static final String VJ_UploadFiles = '5UploadFiles';
}
