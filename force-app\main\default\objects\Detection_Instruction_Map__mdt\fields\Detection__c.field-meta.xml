<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Detection__c</fullName>
    <externalId>false</externalId>
    <fieldManageability>DeveloperControlled</fieldManageability>
    <label>Detection</label>
    <required>false</required>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>5 LED shine solid orange for 10s</fullName>
                <default>true</default>
                <label>5 LED shine solid orange for 10s</label>
            </value>
            <value>
                <fullName>5 LED shine solid red for 10s</fullName>
                <default>false</default>
                <label>5 LED shine solid red for 10s</label>
            </value>
            <value>
                <fullName>5 LED shine solid green for 10s</fullName>
                <default>false</default>
                <label>5 LED shine solid green for 10s</label>
            </value>
            <value>
                <fullName>5 LED shine red for 10s</fullName>
                <default>false</default>
                <label>5 LED shine red for 10s</label>
            </value>
            <value>
                <fullName>5 LED flash green and red alternately for 10s</fullName>
                <default>false</default>
                <label>5 LED flash green and red alternately for 10s</label>
            </value>
            <value>
                <fullName>No indicator light</fullName>
                <default>false</default>
                <label>No indicator light</label>
            </value>
            <value>
                <fullName>Others</fullName>
                <default>false</default>
                <label>Others</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
