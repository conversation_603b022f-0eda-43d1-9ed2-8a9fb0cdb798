/**
 * <AUTHOR>
 * @date 2021-09-17
 * @description This is the test class of CCM_LeadConvertHandler.
 */
@IsTest
private class CCM_LeadConvertHandlerTest {
    @TestSetup
    static void init() {
        Test.startTest();
        Lead objLead = new Lead(
            LastName = 'test',
            Email = System.now().getTime() + '<EMAIL>',
            Country = 'US',
            State = 'FL',
            City = 'Miami',
            Street = '3241 Marigold Lane',
            PostalCode = '33196',
            Credit_Limit__c = 1000,
            Invoicing_Method__c = 'EMAIL',
            Intended_Brand__c = 'SKIL/SKILSAW/FLEX',
            Company = 'test',
            Director_Approver__c = UserInfo.getUserId(),
            Customer_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_CLUSTER_FOR_NA_CNA_CG01,
            Distributor_or_Dealer__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_NA_DISTRIBUTOR,
            Customer_Sub_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_NA_KA001,
            Sales_Channel__c = 'SC01',
            Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[0],
            To_Be_Direct_Indirect_Customer__c = 'TO BE CHERVON DIRECT CUSTOMER',
            Customer_Business_Type__c = 'Sales'
        );
        insert objLead;
        Sales_Program__c objAuthBrand = new Sales_Program__c(Prospect__c = objLead.Id, Name = 'test', RecordTypeId = CCM_Constants.SALES_PROGRAM_RECORD_TYPE_CUSTOMIZED_ID , Approval_Status__c = CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED, Authorized_Brand_Name_To_Oracle__c = '111111');
        insert objAuthBrand;
        // List<Account_Address__c> lstAddress = [
        //     SELECT Id
        //     FROM Account_Address__c
        //     WHERE Prospect__c = :objLead.Id AND RecordTypeId = :CCM_Constants.ADDRESS_BILLING_ADDRESS_RECORD_TYPE_ID
        // ];
        // System.assertEquals(lstAddress.size(), 1);

        Account_Address__c add = new Account_Address__c();
        add.Prospect__c = objLead.Id;
        add.ORG_Id__c = 'CCA';
        add.Primary__c = true;
        // add.Customer__c = acc.Id;
        add.Store_Number__c = '111';
        add.Approval_Status__c = 'Approved';
        add.RecordTypeId = CCM_TESTDataUtil.getSobjectRecordTypeNameMap('Account_Address__c').get('Billing_Address');
        add.Sales_Director__c = UserInfo.getUserId();
        // add.Contact__c = c1.Id;
        add.Email_for_Invoicing__c = objLead.Email;
        insert add;

        insert new Address_With_Program__c(
            Account_Address__c = add.Id,
            Program__c = objAuthBrand.Id
        );
        Attachment_Management__c objContrastAndAgreement = new Attachment_Management__c(Prospect__c = objLead.Id, Attachment_Type__c = 'Brand Program');
        insert objContrastAndAgreement;
        ContentVersion objContentVersion = new ContentVersion(PathOnClient = 'test', VersionData = Blob.valueOf('test'));
        insert objContentVersion;
        List<ContentVersion> lstContentVersion = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :objContentVersion.Id];
        insert new ContentDocumentLink(ContentDocumentId = lstContentVersion[0].ContentDocumentId, LinkedEntityId = objContrastAndAgreement.Id);
        Test.stopTest();
    }
    @IsTest
    static void testLeadConversionFailure4MissingCustomerProfiles() {
        Test.startTest();
        List<Lead> lstLead = [SELECT Id FROM Lead LIMIT 1];
        insert new Customer_Profile__c(RecordTypeId = CCM_Constants.CUSTOMER_PROFILE_RECORD_TYPE_PROSPECT_ID, Prospect__c = lstLead[0].Id, Payment_Behavior_Code__c = 'C');
        Database.LeadConvert objLC = new Database.LeadConvert();
        objLC.setLeadId(lstLead[0].Id);
        objLC.setConvertedStatus('Converted');
        try {
            Database.convertLead(objLC);
        } catch (Exception e) {
            
        }
        
        Test.stopTest();
    }
    @IsTest
    static void testLeadConversionSuccess() {
        Test.startTest();
        List<Lead> lstLead = [SELECT Id FROM Lead LIMIT 1];
        insert new Customer_Profile__c(RecordTypeId = CCM_Constants.CUSTOMER_PROFILE_RECORD_TYPE_PROSPECT_ID, Prospect__c = lstLead[0].Id, Payment_Behavior_Code__c = 'C');
        Database.LeadConvert objLC = new Database.LeadConvert();
        objLC.setLeadId(lstLead[0].Id);
        objLC.setConvertedStatus('Converted');
        try{
            Database.LeadConvertResult objConvertResult = Database.convertLead(objLC);
            System.assertEquals(objConvertResult.isSuccess(), true);
        	System.assertNotEquals(null, objConvertResult.getAccountId());
        	System.assertNotEquals(null, objConvertResult.getContactId());
        } catch (Exception e) {
            
        }
        
        
        Test.stopTest();
    }

    /*@isTest 
    static void testSendEmailToServiceTeamAndCSR() {
        Group gp = new Group(Name = 'NA_Service_Team');
        insert gp;
        GroupMember objM = new GroupMember(UserOrGroupId = UserInfo.getUserId(), GroupId = gp.Id);
        insert objM;
        Lead objL = [SELECT Id, ConvertedAccountId FROM Lead LIMIT 1];
        
        CCM_LeadConvertHandler.sendEmailToServiceTeamAndCSR(objL);
    }*/
}