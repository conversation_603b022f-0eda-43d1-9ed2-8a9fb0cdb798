import { LightningElement } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import getSummaryList from '@salesforce/apex/CCM_WeeklySummaryCtrl.getSummaryList';
import deleteRecordImp from '@salesforce/apex/CCM_WeeklySummaryCtrl.deleteRecordImp';
import isSalesDirector from '@salesforce/apex/CCM_WeeklySummaryCtrl.getRecordData';

export default class CcmWeeklySummaryList extends NavigationMixin(LightningElement) {
    submittedWeeklySummarys;

    draftWeeklySummarys;

    weeklySummarys;

    isSalesDirector = true;

    connectedCallback() {
        this.initData();
    }

    initData() {
        isSalesDirector().then(result=>{
            if(result) {
                let resultJson = JSON.parse(result);
                this.isSalesDirector = resultJson.isSalesDirector;
            }
        });
        getSummaryList({'limitStr': '5'}).then(result=>{
            if(result) {
                let resultJson = JSON.parse(result);
                let temp = resultJson.weeklySummarys;
                temp.forEach(item=>{
                    item.detailLink = window.location.origin + '/lightning/n/Weekly_Summary?c__recordId=' + item.id + '&c__mode=view';
                });
                this.weeklySummarys = temp;
            }
        });
    }

    directToDetailPageNew() {
        let url = window.location.origin + '/lightning/n/Weekly_Summary?c__mode=edit';
        window.open(url);
    }

    directToDetailPageView(event) {
        // this[NavigationMixin.Navigate]({
        //     type: 'standard__navItemPage',
        //     attributes: {
        //         apiName: 'Weekly_Summary'
        //     },
        //     state: {
        //         c__recordId: event.target.dataset.recordid,
        //         c__mode: 'view'
        //     }
        // });
        let url = window.location.origin + '/lightning/n/Weekly_Summary?c__recordId=' + event.target.dataset.recordid + '&c__mode=view';
        window.open(url);
    }

    directToDetailPageEdit(event) {
        // this[NavigationMixin.Navigate]({
        //     type: 'standard__navItemPage',
        //     attributes: {
        //         apiName: 'Weekly_Summary'
        //     },
        //     state: {
        //         c__recordId: event.target.dataset.recordid,
        //         c__mode: 'edit'
        //     }
        // });
        let url = window.location.origin + '/lightning/n/Weekly_Summary?c__recordId=' + event.target.dataset.recordid + '&c__mode=edit';
        window.open(url);
    }

    directToList() {
        let url = window.location.origin + '/lightning/n/Weekly_Summary_List';
        window.open(url);
    }

    deleteRecord(event) {
        const recordId = event.target.dataset.recordid;
        let tempSummarys = this.draftWeeklySummarys;
        for(let i=tempSummarys.length-1; i>=0; i--) {
            if(tempSummarys[i].id === recordId) {
                tempSummarys.splice(i, 1);
            }
        }
        this.draftWeeklySummarys = tempSummarys;
        deleteRecordImp({recordId: recordId}).then(result=>{

        });
    }
}