.iconpoint {
    cursor: pointer;
}

.iconpoint:hover {
    opacity: 0.7;
}

.slds-table td {
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.slds-table th {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.slds-table {
    table-layout: fixed;
    width: 100%;
}

.slds-card {
    overflow-x: hidden;
}

.action-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
}

.sortable-header {
    position: relative;
    user-select: none;
}

.sortable-header:hover {
    background-color: #f3f3f3;
}

.sort-icon {
    margin-left: 4px;
    vertical-align: middle;
}

.sortable-header .slds-truncate {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
