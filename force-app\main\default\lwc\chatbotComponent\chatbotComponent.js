import { LightningElement, api, wire } from 'lwc';
import { subscribe, MessageContext, unsubscribe, publish } from 'lightning/messageService';
import LIVEX_CHATBOT_CHANNEL from '@salesforce/messageChannel/LiveXChatbot__c';

export default class ChatbotComponent extends LightningElement {
    @api chatbotId;

    scriptLoaded = false;
    pendingRequests = new Map();
    
    // Salesforce Lightning Message Service
    @wire(MessageContext)
    messageContext;
    
    connectedCallback() {
        this.subscribeToMessageChannel();
        this.addIframeMessageListener();
    }

    disconnectedCallback() {
        this.unsubscribeToMessageChannel();
        this.removeIframeMessageListener();
    }

    renderedCallback() {
        const iframe = this.template.querySelector('iframe');
        if (!this.scriptLoaded && this.chatbotId) {
            this.scriptLoaded = true;
            // const baseUrl = 'https://chat.dv.copilot.livex.ai';
            const baseUrl = 'https://chat.copilot.livex.ai';
            iframe.src = `${baseUrl}/chatbot/${this.chatbotId}?botMode=zendeskSideApp`;
        }
    }
    
    addIframeMessageListener() {
        this.messageListener = this.handleIframeMessage.bind(this);
        window.addEventListener('message', this.messageListener);
    }
    
    removeIframeMessageListener() {
        if (this.messageListener) {
            window.removeEventListener('message', this.messageListener);
        }
    }
    
    async handleIframeMessage(event) {
        const { data: messageData } = event;
        const { type, requestId, result, error } = messageData;

        if (type === 'LiveXAPIResponse' && this.pendingRequests.has(requestId)) {
            const originalRequest = this.pendingRequests.get(requestId);
            if (originalRequest.function === 'getConversationId') {
                // Forward the conversation id to Agent Recap
                const responseMessage = {
                    messageType: 'RESPONSE',
                    requestId: requestId,
                    result: result,
                    error: error,
                    function: originalRequest.function,
                    targetComponent: 'agentRecap'
                };
                
                publish(this.messageContext, LIVEX_CHATBOT_CHANNEL, responseMessage);
            }
            
            this.pendingRequests.delete(requestId);
        }
    }
    


    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    
    subscribeToMessageChannel() {
        this.subscription = subscribe(
            this.messageContext,
            LIVEX_CHATBOT_CHANNEL,
            (message) => this.handleMessage(message)
        );
    }

    unsubscribeToMessageChannel() {
        unsubscribe(this.subscription);
        this.subscription = null;
    }
    
    handleMessage(message) {
        // Only handle REQUEST type messages that are targeted to this chatbot component
        if (message && 
            message.messageType === 'REQUEST' && 
            message.targetComponent === 'chatbot' &&
            this.template.querySelector('iframe')) {
            
            const iframe = this.template.querySelector('iframe');

            if (iframe.contentWindow && iframe.contentWindow.postMessage) {
                const requestId = message.requestId || this.generateRequestId();
                
                // Store the request for tracking
                this.pendingRequests.set(requestId, {
                    function: message.function,
                    args: message.args,
                    targetComponent: message.targetComponent,
                    timestamp: Date.now()
                });
                
                const structuredMessage = {
                    type: 'LiveXAPI',
                    functionName: message.function,
                    args: message.args,
                    requestId: requestId
                };
                
                iframe.contentWindow.postMessage(structuredMessage, '*');
            }
        }
    }
}