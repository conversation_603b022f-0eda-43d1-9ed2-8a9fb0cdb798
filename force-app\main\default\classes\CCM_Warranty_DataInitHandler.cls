public without sharing class CCM_Warranty_DataInitHandler implements Triggers.Handler{
    public void handle() {
        
        if(Trigger.isUpdate){
            List<Warranty_Item__c> newItems = (List<Warranty_Item__c>)Trigger.new;
            for(Warranty_Item__c item : newItems){
                item.Is_First__c = false;
            }
        }
        
        CCM_Warranty_IOT_Process.classifySource((List<Warranty_Item__c>)Trigger.new);
    }
}