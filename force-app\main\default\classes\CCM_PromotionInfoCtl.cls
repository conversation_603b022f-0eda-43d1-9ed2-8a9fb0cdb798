/**
 * @description Class is used to get informatio for promotion itself
 */
public with sharing class CCM_PromotionInfoCtl {

    /**
     * add by jet
     * @description: get customer by price list
     */
    @AuraEnabled
    public static SearchResult searchCustomerByStandardGroup(
        String promotionId,
        String selectCustomerMethod,
        String[] priceList,
        String[] clusters,
        String[] subClusters,
        String[] salesChannels,
        String[] salesGroups,
        String[] excludeCustomers,
        Boolean isDropShip,
        Boolean notDropShip,
        Boolean isCCA
    ) {
        SearchResult result = new SearchResult();
        if (isCCA) {
            result = searchCustomerByStandardGroupCCA(promotionId, selectCustomerMethod, priceList, clusters, subClusters, salesChannels, salesGroups, excludeCustomers, isDropShip);
        } else {
            // CNA
            result = searchCustomerByStandardGroupCNA(promotionId, selectCustomerMethod, priceList, clusters, subClusters, salesChannels, salesGroups, excludeCustomers, isDropShip, notDropShip);
        }
        return result;
    }

    /**
     * add by jet
     * get customer by price list.(CNA)
    */
    @AuraEnabled
    public static SearchResult searchCustomerByStandardGroupCNA(
        String promotionId,
        String selectCustomerMethod,
        String[] priceList,
        String[] clusters,
        String[] subClusters,
        String[] salesChannels,
        String[] salesGroups,
        String[] excludeCustomers,
        Boolean isDropShip,
        Boolean notDropShip
    ) {
        SearchResult result = new SearchResult();
        SearchResult isDropShipResult = new SearchResult();
        SearchResult notDropShipResult = new SearchResult();


        Savepoint objSavepoint = Database.setSavepoint(); // 点Next失败的话，就要回滚

        try{
            // 找到当前这条promotion记录
            Promotion2__c objPromotion = [
                SELECT Id, Brands__c ,Is_Dropship_Promotion__c ,Non_Dropship_Promotion__c, Select_Customer_Method__c, Price_List__c, Customer_Cluster__c, Customer_Sub_Cluster__c, Sales_Channel__c, Sales_Group__c
                FROM Promotion2__c
                WHERE Id = :promotionId
            ];

            // update promotion
            objPromotion.Is_Dropship_Promotion__c = isDropShip;
            objPromotion.Non_Dropship_Promotion__c = notDropship;
            if (String.isNotEmpty(selectCustomerMethod)) {
                objPromotion.Select_Customer_Method__c = selectCustomerMethod;
            }

            // 找主副价格册，以主副价格册作为查询条件
            if (!priceList.isEmpty()) {
                String priceListStr = priceList.toString().subString(1, priceList.toString().length() - 1);
                objPromotion.Price_List__c = priceListStr;

                List<String> allPriceList = new List<String>();
                allPriceList.addAll(priceList);

                List<String> priceListName = new List<String>();

                // get selected price book's vice price book
                Set<String> oracleIds = new Set<String>();
                for (PriceBook2 pb : [SELECT Id, Name, Price_Book_OracleID__c FROM PriceBook2 WHERE Id IN :priceList]) {
                    oracleIds.add(pb.Price_Book_OracleID__c);
                    priceListName.add(pb.Name);
                }
                objPromotion.Selected_Price_List__c = String.join(priceListName,';');
                // 24.10.22: 不再查找关联的价格册，故注释以下代码
                // String priceBookQueryStr = 'SELECT Id, Price_Book_OracleID__c, Contract_Price_Book_OracleID__c FROM PriceBook2 WHERE IsActive = true ';
                // String filter = '';
                // for (String oracleId : oracleIds) {
                //     filter += ' OR Contract_Price_Book_OracleID__c LIKE \'%' + oracleId + '%\' ';
                // }

                // priceBookQueryStr += (' AND (' +filter.replaceFirst('OR', '') +')');

                // Set<String> contractOracleIdSet = new Set<String>();
                
                // for (Pricebook2 pb : (List<Pricebook2>) Database.query(priceBookQueryStr)) {
                //     priceList.add(pb.Id);
                //     contractOracleIdSet.addAll(pb.Contract_Price_Book_OracleID__c.split(','));
                // }
                // 24.10.22 end

                objPromotion.ALL_Root_Price_Book_For_Price_List__c = String.join(priceList,';');
            } else {
                objPromotion.Price_List__c = null;
                // 24.10.17: 修复若一开始选择了价格册作为筛选条件，后返回第一步移除所有价格册，以下两个字段未清空的问题
                objPromotion.Selected_Price_List__c = null;
                objPromotion.ALL_Root_Price_Book_For_Price_List__c = null;
                // 24.10.17 end
            }

            if (!clusters.isEmpty()) {
                objPromotion.Customer_Cluster__c = String.join(clusters, ';');
            } else {
                objPromotion.Customer_Cluster__c = null;
            }
            if (!subClusters.isEmpty()) {
                objPromotion.Customer_Sub_Cluster__c = String.join(subClusters,';');
            } else {
                objPromotion.Customer_Sub_Cluster__c = null;
            }
            if (!salesChannels.isEmpty()) {
                objPromotion.Sales_Channel__c = String.join(salesChannels, ';');
            } else {
                objPromotion.Sales_Channel__c = null;
            }

            if (!salesGroups.isEmpty()) {
                objPromotion.Sales_Group__c = String.join(salesGroups, ';');
            } else {
                objPromotion.Sales_Group__c = null;
            }

            if(!excludeCustomers.isEmpty()) {
                objPromotion.Exclude_Customers__c = String.join(excludeCustomers, ';');
            }
            else {
                objPromotion.Exclude_Customers__c = null;
            }

            update objPromotion;

            // 删除该条记录下所有相关target customer.
            List<Promotion_Target_Customer__c> needDelete = [SELECT Id FROM Promotion_Target_Customer__c WHERE Promotion__c = :promotionId];

            if (needDelete.size() > 0) {
                delete needDelete;
            }

            // 给前台返回brand等信息，同时upsert promotion target customer.
            // if dropship, query 2nd tier, buying group
            if (isDropShip) {
                isDropShipResult = searchDropShipCustomerByStandardGroupCNA(promotionId, selectCustomerMethod, priceList, clusters, subClusters, salesChannels, salesGroups, excludeCustomers);
            }

            // if non dropship, query 1st tier
            if (notDropShip) {
                notDropShipResult = searchNonDropShipCustomerByStandardGroupCNA(promotionId, selectCustomerMethod, priceList, clusters, subClusters, salesChannels, salesGroups, excludeCustomers);
            }
        } catch(Exception e) {
            Database.rollback(objSavepoint);
        }

        Set<String> customerNameSet = new Set<String>();
        customerNameSet.addAll(isDropShipResult.customerNameList);
        customerNameSet.addAll(notDropShipResult.customerNameList);
        result.customerNameList.addAll(customerNameSet);
        result.mixedAuthorizedBrands.addAll(isDropShipResult.mixedAuthorizedBrands);
        result.mixedAuthorizedBrands.addAll(notDropShipResult.mixedAuthorizedBrands);
        // result.customerNumber = isDropShipResult.customerNumber + notDropShipResult.customerNumber;
        result.customerNumber = result.customerNameList.size();

        return result;
    }

    /**
     * add by jet
     * dropship
     */
    public static SearchResult searchDropShipCustomerByStandardGroupCNA(
        String promotionId,
        String selectCustomerMethod,
        String[] priceList,
        String[] clusters,
        String[] subClusters,
        String[] salesChannels,
        String[] salesGroups,
        String[] excludeCustomers
    ) {
        Map<String, Set<String>> x1x2Map = new Map<String, Set<String>>(); // 存放一级和二级对应关系，一级和三级对应关系

        CCM_PromotionUtil.SalesHierarchyRelatedWrapper salesHierarchyResult = CCM_PromotionUtil.getSalesHierarchyData();
        x1x2Map = salesHierarchyResult.x1x2Map;

        // record type 限制为Dropship Sales Customized，Dropship Sales Standard
        String dropShipQueryStr = 'SELECT Id, Name, Brands__c, Price_Book__c, Customer__c, Customer__r.Name FROM Sales_Program__c WHERE Approval_Status__c = \'Approved\' AND Customer__r.Id != null AND Price_Book__r.IsActive = true AND Customer__r.RecordType.DeveloperName = \'Channel\' AND (RecordType.DeveloperName In (\'Dropship_Sales_Customized\',\'Dropship_Sales_Standard\') Or (RecordType.DeveloperName Not In (\'Customized\',\'Standard\',\'Dropship_Sales_Customized\',\'Dropship_Sales_Standard\') And Customer__r.Distributor_or_Dealer__c=\'Dealer Location\'))';
       
        if (!priceList.isEmpty()) {
            dropShipQueryStr += ' AND Price_Book__c IN :priceList ';
        }
        if (!clusters.isEmpty()) {
            dropShipQueryStr += ' AND Customer__r.Customer_Cluster__c IN :clusters ';
        }
        if (!subClusters.isEmpty()) {
            dropShipQueryStr += ' AND Customer__r.Customer_Sub_Cluster__c IN :subClusters ';
        } 
        if (!salesChannels.isEmpty()) {
            dropShipQueryStr += ' AND Customer__r.Sales_Channel__c IN :salesChannels ';
        } 
        if (!salesGroups.isEmpty()) {
            dropShipQueryStr += ' AND Customer__r.Sales_Group__c IN :salesGroups ';
        }
        if(!excludeCustomers.isEmpty()) {
            dropShipQueryStr += ' AND Customer__c NOT IN :excludeCustomers ';
        }
        dropShipQueryStr += ' ORDER BY Price_Book__r.Name ';

        List<String> customerNameList = new List<String>();
        Set<String> customerNameSet = new Set<String>();
        Set<Id> customerIdSet = new Set<Id>();
        SearchResult result = new SearchResult();
        // Set<String> uniqueCustomerIdSet = new Set<String>(); // 存放sales_program__c记录中不重复的customer id。
        // List<Set<String>> uniqueCustomerIdList = new List<Set<String>>(); // 存放sales_program__c记录中不重复的customer id。
        List<CCM_PromotionSaveCtl.CustomerWrapper> customerWrapperList = new List<CCM_PromotionSaveCtl.CustomerWrapper>();
        List<Sales_Program__c> salesProgramList = new List<Sales_Program__c>(); // 查询结果
        Set<String> brandResult = new Set<String>(); //返回给前台的brand并集
   
        // get customers from authorized brands
        salesProgramList = (List<Sales_Program__c>) Database.query(dropShipQueryStr);

        System.debug('salesProgramList===============> ' + salesProgramList);

        //add by jet，修改了customerIdSet的取值
        //即 获取brand的时候用当前二级客户对应的一级客户去获取；upsert target customer用当前的二级客户。
        for (Sales_Program__c sp : salesProgramList) {
            customerIdSet.add(sp.Customer__c);
            brandResult.add(sp.Brands__c);
            customerNameSet.add(sp.Customer__r.Name);
            // uniqueCustomerIdSet.add(sp.Customer__c);
        }

        // 对查到的id做了去重
        for (String customerId : customerIdSet) {
            CCM_PromotionSaveCtl.CustomerWrapper objWrapper = new CCM_PromotionSaveCtl.CustomerWrapper();
            // objWrapper.value = sp.Customer__c;
            objWrapper.value = customerId;
            customerWrapperList.add(objWrapper); 
        }
        System.debug('promotion info search customer Id===============> ' + customerWrapperList);

        if (customerIdSet.size() > 0) {
            result.customerNameList.addAll(customerNameSet);
            // 这里可以直接用上边的查询出来的brand。
            // result.mixedAuthorizedBrands = getDropAuthorizedBrands(customerIdSet);
            result.mixedAuthorizedBrands = brandResult;
            CCM_PromotionSaveCtl.WindowCustomerWrapper objWindowCustomerWrapper = new CCM_PromotionSaveCtl.WindowCustomerWrapper();
            objWindowCustomerWrapper.customers = customerWrapperList;

            CCM_PromotionSaveCtl.ResponseWrapper response = CCM_PromotionSaveCtl.upsertDropShipPromotionCustomer(
                promotionId,
                '',
                JSON.serialize(objWindowCustomerWrapper),
                true, 
                false,
                x1x2Map
            );
            System.debug('response: ======> ' + response);
        }

        // result.customerNumber = customerIdSet.size();
        System.debug('result ======> ' + result);
        return result;
    }

    /**
     * add by jet
     * non dropship
     */
    public static SearchResult searchNonDropShipCustomerByStandardGroupCNA(
        String promotionId,
        String selectCustomerMethod,
        String[] priceList,
        String[] clusters,
        String[] subClusters,
        String[] salesChannels,
        String[] salesGroups,
        String[] excludeCustomers
    ) {
        // record type 限制,不为Dropship Sales Customized，Dropship Sales Standard
        String nonDropQueryStr = 'SELECT Id, Name, Brands__c, Price_Book__c, Customer__c, Customer__r.Name FROM Sales_Program__c WHERE Approval_Status__c = \'Approved\' AND Customer__r.Id != null AND Price_Book__r.IsActive = true AND Customer__r.RecordType.DeveloperName = \'Channel\' AND RecordType.DeveloperName In (\'Customized\',\'Standard\',\'Dropship_Sales_Customized\',\'Dropship_Sales_Standard\')';
       
        // if (nonCustomerIds.size() > 0) {
        //     nonDropQueryStr += ' AND Customer__c IN :nonCustomerIds';
        // }
        if (!priceList.isEmpty()) {
            nonDropQueryStr += ' AND Price_Book__c IN :priceList ';
        }
        if (!clusters.isEmpty()) {
            nonDropQueryStr += ' AND Customer__r.Customer_Cluster__c IN :clusters ';
        }
        if (!subClusters.isEmpty()) {
            nonDropQueryStr += ' AND Customer__r.Customer_Sub_Cluster__c IN :subClusters ';
        } 
        if (!salesChannels.isEmpty()) {
            nonDropQueryStr += ' AND Customer__r.Sales_Channel__c IN :salesChannels ';
        } 
        if (!salesGroups.isEmpty()) {
            nonDropQueryStr += ' AND Customer__r.Sales_Group__c IN :salesGroups ';
        }
        if(!excludeCustomers.isEmpty()) {
            nonDropQueryStr += ' AND Customer__c NOT IN :excludeCustomers ';
        }
        nonDropQueryStr += ' ORDER BY Price_Book__r.Name ';

        List<String> customerNameList = new List<String>();
        Set<String> customerNameSet = new Set<String>();
        Set<Id> customerIdSet = new Set<Id>();
        SearchResult result = new SearchResult();
        List<CCM_PromotionSaveCtl.CustomerWrapper> customerWrapperList = new List<CCM_PromotionSaveCtl.CustomerWrapper>();
        List<Sales_Program__c> salesProgramList = new List<Sales_Program__c>();
        Set<String> brandResult = new Set<String>(); // 返回给前台的brands
        // Set<String> uniqueCustomerIdSet = new Set<String>(); // 存放sales_program__c记录中不重复的customer id。
   
        // get customers from authorized brands
        salesProgramList = (List<Sales_Program__c>) Database.query(nonDropQueryStr);
        System.debug('salesProgramList====='+salesProgramList);
        for (Sales_Program__c sp : salesProgramList) {
            if (customerIdSet.contains(sp.Customer__c)) {
                continue;
            }
            customerIdSet.add(sp.Customer__c);
            customerNameSet.add(sp.Customer__r.Name);
            brandResult.add(sp.Brands__c);
            // uniqueCustomerIdSet.add(sp.Customer__c);
        }

        // 对查到的id做了去重
        for (String customerId : customerIdSet) {
            CCM_PromotionSaveCtl.CustomerWrapper objWrapper = new CCM_PromotionSaveCtl.CustomerWrapper();
            // objWrapper.value = sp.Customer__c;
            objWrapper.value = customerId;
            customerWrapperList.add(objWrapper); 
        }
        
        if (customerIdSet.size() > 0) {
            result.customerNameList.addAll(customerNameSet);
            // result.mixedAuthorizedBrands = getAllAuthorizedBrands(customerIdSet);
            result.mixedAuthorizedBrands = brandResult;
            CCM_PromotionSaveCtl.WindowCustomerWrapper objWindowCustomerWrapper = new CCM_PromotionSaveCtl.WindowCustomerWrapper();
            objWindowCustomerWrapper.customers = customerWrapperList;

            CCM_PromotionSaveCtl.ResponseWrapper response = CCM_PromotionSaveCtl.upsertNonDropShipPromotionWindowAndCustomer(
                promotionId,
                JSON.serialize(objWindowCustomerWrapper),
                true
            );

            System.debug('response: ======> ' + response);
        }

        // result.customerNumber = customerIdSet.size();
        System.debug('result ======> ' + result);
        return result;
    }

    

    /**
     * @Description: get customer by price list
     * @Param: String promotionId, String selectCustomerMethod, String[] priceList, String[] clusters, String[] subclusters, String[] salesChannels, String[] salesGroups, Boolean isDropship
     */
    @AuraEnabled
    public static SearchResult searchCustomerByStandardGroupCCA(
        String promotionId,
        String selectCustomerMethod,
        String[] priceList,
        String[] clusters,
        String[] subClusters,
        String[] salesChannels,
        String[] salesGroups,
        String[] excludeCustomers,
        Boolean isDropShip
    ) {
        Promotion2__c objPromotion = [
            SELECT
                Id,
                Brands__c,
                Select_Customer_Method__c,
                Price_List__c,
                Customer_Cluster__c,
                Customer_Sub_Cluster__c,
                Sales_Channel__c,
                Sales_Group__c
            FROM Promotion2__c
            WHERE Id = :promotionId
        ];

        objPromotion.Is_Dropship_Promotion__c = isDropShip;
        if (String.isNotEmpty(selectCustomerMethod)) {
            objPromotion.Select_Customer_Method__c = selectCustomerMethod;
        }
        String queryStr = 'SELECT Id, Name, Brands__c, Price_Book__c, Customer__c, Customer__r.Name FROM Sales_Program__c WHERE Approval_Status__c = \'Approved\' AND Customer__r.Id != null AND Price_Book__r.IsActive = true AND Customer__r.RecordType.DeveloperName = \'Channel\' AND RecordType.DeveloperName != \'Service\' AND RecordType.DeveloperName != \'Service_Standard\' AND RecordType.DeveloperName != \'Service_Customized\' ';
        String specialDropShipQueryStr = 'SELECT Id, Price_Book__c, Price_Book__r.Price_Book_OracleID__c, Price_Book__r.Contract_Price_Book_OracleID__c, Billing_Address_With_Authorized_Brand__c, Special_Dropship_Address__c, Brand__c, Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__c, Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__r.Name FROM Customer_Brand_Pricebook_Mapping__c WHERE (Type__c = \'Dropship\' AND Is_Active__c = true ';
        String nonSpecialDropShipQueryStr = 'SELECT Id, Account_Address__r.Customer__c, Account_Address__r.Customer__r.Name, Program__r.Brands__c, Program__r.Price_Book__c FROM Address_With_Program__c WHERE Special_Dropship_Address__c = false AND Program__r.Approval_Status__c = \'Approved\' AND Program__r.RecordType.DeveloperName != \'Service\' AND Account_Address__r.Customer__r.RecordType.DeveloperName = \'Channel\' AND Address_Type__c = \'Dropship Billing Address\' AND Customer_Line_Oracle_ID__c != null ';
        if (!priceList.isEmpty()) {
            String priceListStr = priceList.toString()
                .subString(1, priceList.toString().length() - 1);
            objPromotion.Price_List__c = priceListStr;

            List<String> allPriceList = new List<String>();
            allPriceList.addAll(priceList);

            List<String> priceListName = new List<String>();

            // get selected price book's vice price book
            Set<String> oracleIds = new Set<String>();
            for (PriceBook2 pb : [
                SELECT Id, Name, Price_Book_OracleID__c
                FROM PriceBook2
                WHERE Id IN :priceList
            ]) {
                oracleIds.add(pb.Price_Book_OracleID__c);
                priceListName.add(pb.Name);
            }
            objPromotion.Selected_Price_List__c = String.join(
                priceListName,
                ';'
            );
            // 24.10.22: 不再查找关联的价格册，故注释以下代码
            // String priceBookQueryStr = 'SELECT Id, Price_Book_OracleID__c, Contract_Price_Book_OracleID__c FROM PriceBook2 WHERE IsActive = true ';
            // String filter = '';
            // for (String oracleId : oracleIds) {
            //     filter +=
            //         ' OR Contract_Price_Book_OracleID__c LIKE \'%' +
            //         oracleId +
            //         '%\' ';
            // }
            // priceBookQueryStr += (' AND (' +
            // filter.replaceFirst('OR', '') +
            // ')');
            // Set<String> contractOracleIdSet = new Set<String>();
            // for (
            //     Pricebook2 pb : (List<Pricebook2>) Database.query(
            //         priceBookQueryStr
            //     )
            // ) {
            //     priceList.add(pb.Id);
            //     contractOracleIdSet.addAll(
            //         pb.Contract_Price_Book_OracleID__c.split(',')
            //     );
            // }
            // 24.10.22 end
            /*for (PriceBook2 pb : [SELECT Id FROM PriceBook2 WHERE IsActive = true AND Price_Book_OracleID__c IN :contractOracleIdSet]) {
                if (!priceList.contains(pb.Id)) {
                    priceList.add(pb.Id);
                }
            }*/

            queryStr += ' AND Price_Book__c IN :priceList ';
            specialDropShipQueryStr += ' AND Price_Book__c IN :priceList ';

            objPromotion.ALL_Root_Price_Book_For_Price_List__c = String.join(
                priceList,
                ';'
            );
        } else {
            objPromotion.Price_List__c = null;
            // 24.10.17: 修复若一开始选择了价格册作为筛选条件，后返回第一步移除所有价格册，以下两个字段未清空的问题
            objPromotion.Selected_Price_List__c = null;
            objPromotion.ALL_Root_Price_Book_For_Price_List__c = null;
            // 24.10.17 end
        }
        if (!clusters.isEmpty()) {
            queryStr += ' AND Customer__r.Customer_Cluster__c IN :clusters ';
            specialDropShipQueryStr += ' AND Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__r.Customer_Cluster__c IN :clusters ';
            nonSpecialDropShipQueryStr += ' AND Account_Address__r.Customer__r.Customer_Cluster__c IN :clusters ';
            objPromotion.Customer_Cluster__c = String.join(clusters, ';');
        } else {
            objPromotion.Customer_Cluster__c = null;
        }
        if (!subClusters.isEmpty()) {
            queryStr += ' AND Customer__r.Customer_Sub_Cluster__c IN :subClusters ';
            specialDropShipQueryStr += ' AND Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__r.Customer_Sub_Cluster__c IN :subClusters ';
            nonSpecialDropShipQueryStr += ' AND Account_Address__r.Customer__r.Customer_Sub_Cluster__c IN :subClusters ';
            objPromotion.Customer_Sub_Cluster__c = String.join(
                subClusters,
                ';'
            );
        } else {
            objPromotion.Customer_Sub_Cluster__c = null;
        }
        if (!salesChannels.isEmpty()) {
            queryStr += ' AND Customer__r.Sales_Channel__c IN :salesChannels ';
            specialDropShipQueryStr += ' AND Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__r.Sales_Channel__c IN :salesChannels ';
            nonSpecialDropShipQueryStr += ' AND Account_Address__r.Customer__r.Sales_Channel__c IN :salesChannels ';
            objPromotion.Sales_Channel__c = String.join(salesChannels, ';');
        } else {
            objPromotion.Sales_Channel__c = null;
        }
        if (!salesGroups.isEmpty()) {
            queryStr += ' AND Customer__r.Sales_Group__c IN :salesGroups ';
            specialDropShipQueryStr += ' AND Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__r.Sales_Group__c IN :salesGroups ';
            nonSpecialDropShipQueryStr += ' AND Account_Address__r.Customer__r.Sales_Group__c IN :salesGroups ';
            objPromotion.Sales_Group__c = String.join(salesGroups, ';');
        } else {
            objPromotion.Sales_Group__c = null;
        }

        if(!excludeCustomers.isEmpty()) {
            queryStr += ' AND Customer__c NOT IN :excludeCustomers ';
            specialDropShipQueryStr += ' AND Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__c NOT IN :excludeCustomers ';
            nonSpecialDropShipQueryStr += ' AND Account_Address__r.Customer__c NOT IN :excludeCustomers ';
            objPromotion.Exclude_Customers__c = String.join(excludeCustomers, ';');
        }
        else {
            objPromotion.Exclude_Customers__c = null;
        }

        queryStr += ' ORDER BY Price_Book__r.Name ';
        specialDropShipQueryStr += ' ) OR (Billing_Address_With_Authorized_Brand__c = null AND Type__c = \'Dropship\' AND Is_Active__c = true AND Price_Book__c IN :priceList) ORDER BY Price_Book__r.Name ';

        List<String> customerNameList = new List<String>();
        Set<Id> customerIdSet = new Set<Id>();
        SearchResult result = new SearchResult();
        List<CCM_PromotionSaveCtl.CustomerWrapper> customerWrapperList = new List<CCM_PromotionSaveCtl.CustomerWrapper>();
        List<Sales_Program__c> salesProgramList = new List<Sales_Program__c>();
        if (!isDropShip) {
            // get customers from authorized brands
            salesProgramList = (List<Sales_Program__c>) Database.query(
                queryStr
            );
            for (Sales_Program__c sp : salesProgramList) {
                if (customerIdSet.contains(sp.Customer__c)) {
                    continue;
                }
                customerIdSet.add(sp.Customer__c);
                CCM_PromotionSaveCtl.CustomerWrapper objWrapper = new CCM_PromotionSaveCtl.CustomerWrapper();
                objWrapper.value = sp.Customer__c;
                customerWrapperList.add(objWrapper);
            }
        } else {
            Set<String> nonSpecialBrand = new Set<String>();
            Set<String> contractPriceBooks = new Set<String>();
            Set<String> brands = new Set<String>();
            Set<Id> dropShipCustomerSet = new Set<Id>();
            List<Customer_Brand_Pricebook_Mapping__c> dropshipPricebookMappingList = (List<Customer_Brand_Pricebook_Mapping__c>) Database.query(
                specialDropShipQueryStr
            );
            if (dropshipPricebookMappingList.size() > 0) {
                // get all customers which are using those dropship price books
                // special dropship address customers
                for ( Customer_Brand_Pricebook_Mapping__c m : dropshipPricebookMappingList ) {
                    if (m.Special_Dropship_Address__c) {
                        dropShipCustomerSet.add(
                            m.Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__c
                        );
                        customerNameList.add(
                            m.Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__r.Name
                        );
                        brands.add(m.Brand__c.toUpperCase());
                    } else {
                        nonSpecialBrand.add(m.Brand__c);
                    }
                }
                System.debug(
                    '*********** price and term reference =======> ' +
                    dropShipCustomerSet +
                    ' ***** ' +
                    customerNameList
                );
                System.debug(
                    '*********** nonSpecialBrand =======> ' + nonSpecialBrand
                );

                // non special dropship address customers
                Map<Id, Set<String>> customerBrandsMap = new Map<Id, Set<String>>();
                for (
                    Address_With_Program__c ap : (List<Address_With_Program__c>) Database.query(
                        nonSpecialDropShipQueryStr
                    )
                ) {
                    if (nonSpecialBrand.contains(ap.Program__r.Brands__c)) {
                        dropShipCustomerSet.add(
                            ap.Account_Address__r.Customer__c
                        );
                        customerNameList.add(
                            ap.Account_Address__r.Customer__r.Name
                        );
                    }
                }
                System.debug(
                    '*********** Billing address with authorized brand =======> ' +
                    dropShipCustomerSet +
                    ' ***** ' +
                    customerNameList
                );

                for (String customerId : dropShipCustomerSet) {
                    if (customerId != null) {
                        CCM_PromotionSaveCtl.CustomerWrapper objWrapper = new CCM_PromotionSaveCtl.CustomerWrapper();
                        objWrapper.value = customerId;
                        customerWrapperList.add(objWrapper);
                    }
                }

                result.customerNameList = customerNameList;
                // get all brands
                result.mixedAuthorizedBrands = getAllAuthorizedBrands(
                    dropShipCustomerSet
                );
                CCM_PromotionSaveCtl.WindowCustomerWrapper objWindowCustomerWrapper = new CCM_PromotionSaveCtl.WindowCustomerWrapper();
                objWindowCustomerWrapper.customers = customerWrapperList;
                // insert target customer
                CCM_PromotionSaveCtl.ResponseWrapper response = CCM_PromotionSaveCtl.upsertPromotionCustomerCCA(
                    promotionId,
                    '',
                    JSON.serialize(objWindowCustomerWrapper),
                    true
                );
                System.debug('response: ======> ' + response);
                update objPromotion;
                result.customerNumber = dropShipCustomerSet.size();
                return result;
            } else {
                System.debug(
                    LoggingLevel.INFO,
                    '*** : ' + 'No Customers are using this dropship pricebook'
                );
                result.customerNumber = 0;
                return result;
            }
        }
        if (customerIdSet.size() > 0) {
            result.customerNameList = customerNameList;
            result.mixedAuthorizedBrands = getAllAuthorizedBrands(
                customerIdSet
            );
            CCM_PromotionSaveCtl.WindowCustomerWrapper objWindowCustomerWrapper = new CCM_PromotionSaveCtl.WindowCustomerWrapper();
            objWindowCustomerWrapper.customers = customerWrapperList;
            CCM_PromotionSaveCtl.ResponseWrapper response = CCM_PromotionSaveCtl.upsertPromotionCustomerCCA(
                promotionId,
                '',
                JSON.serialize(objWindowCustomerWrapper),
                true
            );
            System.debug('response: ======> ' + response);

            update objPromotion;
        }
        result.customerNumber = customerIdSet.size();
        System.debug('result ======> ' + result);
        return result;
    }

    /**
     * Get customers or customer groups by search key
     * add by asutin
     * 
     * modify by jet
     */
    @AuraEnabled
    public static List<CustomerOrGroupWrapper> getAllCustomerOrGroup(
        String keyWord,
        Boolean isDropShip,
        Boolean notDropship,
        Boolean isCCA
    ) {
        List<CustomerOrGroupWrapper> result = new List<CustomerOrGroupWrapper>();
        
        if (isCCA) {
            result = getCustomerOrGroup(keyWord, isDropShip, isCCA);
        }else{
            result = getCustomerOrGroupCNA(keyWord, isCCA, isDropShip, notDropship);
        }
        return result;
    }

    /**
     * @description: 处理dropship promotion和direct dealer promotion都勾的情况
     */
    public static List<CustomerOrGroupWrapper> getCustomerOrGroupCNA(String keyWord, Boolean isCCA, Boolean isDropShip, Boolean notDropShip){
        List<CustomerOrGroupWrapper> result = new List<CustomerOrGroupWrapper>();
        // Map<String, CustomerOrGroupWrapper> result = new Map<String, CustomerOrGroupWrapper>();
        String likeKeyWord = keyWord;
        if (String.isBlank(likeKeyWord)) {
            return result;
        }
        if (isDropShip == false && notDropShip == false) {
            return result;
        }
        if (!likeKeyWord.startsWith('%')) {
            likeKeyWord = '%' + likeKeyWord;
        } else {
            likeKeyWord = likeKeyWord;
        }

        if (!likeKeyWord.endsWith('%')) {
            likeKeyWord = likeKeyWord + '%';
        } else {
            likeKeyWord = likeKeyWord;
        }

        User currentUser = [
            SELECT Id, Profile.Name
            FROM User
            WHERE Id = :UserInfo.getUserId()
        ];

        // Set<String> topCustomerIds = new Set<String>(); // 二级对应的一级的customer id
        // CCM_PromotionUtil.SalesHierarchyRelatedWrapper salesHierarchyData = CCM_PromotionUtil.getSalesHierarchyData();
        // topCustomerIds = salesHierarchyData.nonCustomerIds;
		// System.debug('salesHierarchyData.customer'+salesHierarchyData.topCustomerIds);

        // 不同情况走不同的sql
        String accountQueryStr = 'SELECT Id, Name, ORG_Code__c, AccountNumber, Distributor_or_Dealer__c, (SELECT Id, Segmentation__c, TM_Segmentation__c, Summary_of_Potential_Actual_Sales__c FROM Customer_Profile__r), (SELECT Id, Name, Brands__c, RecordType.Name, RecordType.DeveloperName FROM Sales_Program__r WHERE RecordType.Name != \'Service\' AND RecordType.DeveloperName != \'Service_Standard\' AND RecordType.DeveloperName != \'Service_Customized\' AND Approval_Status__c = \'Approved\') FROM Account WHERE (Name LIKE :likeKeyWord OR AccountNumber LIKE :likeKeyWord) AND RecordType.Name = \'Channel\'';
        // if (isDropShip == true && notDropShip == true) {
        //     accountQueryStr = 'SELECT Id, Name, ORG_Code__c, AccountNumber, Distributor_or_Dealer__c, (SELECT Id, Segmentation__c, TM_Segmentation__c, Summary_of_Potential_Actual_Sales__c FROM Customer_Profile__r), (SELECT Id, Name, Brands__c, RecordType.Name, RecordType.DeveloperName FROM Sales_Program__r WHERE (RecordType.Name != \'Service\' AND RecordType.DeveloperName != \'Service_Standard\' AND RecordType.DeveloperName != \'Service_Customized\') AND Approval_Status__c = \'Approved\') FROM Account WHERE (Name LIKE :likeKeyWord OR AccountNumber LIKE :likeKeyWord) AND RecordType.Name = \'Channel\'';
        // }
        // 23.11.6 start
        // if (isdropship == true && notDropShip == false) {
        //     accountQueryStr = 'SELECT Id, Name, ORG_Code__c, AccountNumber, Distributor_or_Dealer__c, (SELECT Id, Segmentation__c, TM_Segmentation__c, Summary_of_Potential_Actual_Sales__c FROM Customer_Profile__r), (SELECT Id, Name, Brands__c, RecordType.Name, RecordType.DeveloperName FROM Sales_Program__r WHERE (RecordType.DeveloperName = \'Dropship_Sales_Customized\' OR RecordType.DeveloperName = \'Dropship_Sales_Standard\') AND Approval_Status__c = \'Approved\') FROM Account WHERE (Name LIKE :likeKeyWord OR AccountNumber LIKE :likeKeyWord) AND RecordType.Name = \'Channel\'';
        // }
        // if (isdropship == false && notDropShip == true) {
        //     accountQueryStr = 'SELECT Id, Name, ORG_Code__c, AccountNumber, Distributor_or_Dealer__c, (SELECT Id, Segmentation__c, TM_Segmentation__c, Summary_of_Potential_Actual_Sales__c FROM Customer_Profile__r), (SELECT Id, Name, Brands__c, RecordType.Name, RecordType.DeveloperName FROM Sales_Program__r WHERE RecordType.Name != \'Service\' AND RecordType.DeveloperName != \'Service_Standard\' AND RecordType.DeveloperName != \'Service_Customized\' AND RecordType.DeveloperName != \'Dropship_Sales_Customized\' AND RecordType.DeveloperName != \'Dropship_Sales_Standard\' AND Approval_Status__c = \'Approved\') FROM Account WHERE (Name LIKE :likeKeyWord OR AccountNumber LIKE :likeKeyWord) AND RecordType.Name = \'Channel\'';
        // }
        // -- 23.11.6 end
        String groupQueryStr = 'SELECT Id, Name ';

        // accountQueryStr += ' AND Id IN :topCustomerIds ';
        // groupQueryStr += ' , (SELECT Id FROM Promotion_Customer_In_Group__r WHERE Customer__c IN :topCustomerIds) ';
        groupQueryStr += ' FROM Promotion_Customer_Group__c WHERE Name LIKE :likeKeyWord';

        if (currentUser.Profile.Name != 'Channel') {
            if (isCCA) {
                accountQueryStr += ' AND ORG_Code__c = \'CCA\' ';
            } else {
                accountQueryStr += ' AND ORG_Code__c != \'CCA\' ';
            }
        }

        List<Account> matchingAccounts = new List<Account>();
        List<Account> allAccounts = (List<Account>)Database.query(accountQueryStr);
        // 23.12.23: 获取2nd tier dealer id
        CCM_PromotionUtil.SalesHierarchyRelatedWrapper salesHierarchyResult = CCM_PromotionUtil.getSalesHierarchyData();
        Set<String> secondCustomerIds = salesHierarchyResult.secondCustomerIds;
        // 23.12.23 end
        for (Account acc : allAccounts) {
            if (isDropShip) {
                //没有sales 的几个类型的authorized brand，但是customer type = 2nd tier dealer (sales)，则他是2nd tier dealer，dropship promotion可以选到
                // 23.12.23: 增加了当acc在secondCustomerIds时，也认为他是2nd tier dealer的判断
                if ((acc.Sales_Program__r.size() == 0 && acc.Distributor_or_Dealer__c == 'Dealer Location') || secondCustomerIds.contains(acc.Id)) {
                    matchingAccounts.add(acc);
                }
                // 有dropship authorized brand，则他是有dropship业务的direct dealer，dropship promotion可以选到，存为top customer，选他则名下的2nd tier dealer都被选进来。
                if (acc.Sales_Program__r.size() > 0) {
                    for (Sales_Program__c sp : acc.Sales_Program__r) {
                        if (sp.RecordType.DeveloperName=='Dropship_Sales_Customized'||sp.RecordType.DeveloperName=='Dropship_Sales_Standard') {
                            matchingAccounts.add(acc);
                            break;
                        }
                    }
                }
            }
            // 23.12.23 isDropship和notDropship应该分开并列处理，而不是直接if...else...
            if(notDropShip){
                //有sales 的几个类型的authorized brand，则他是direct dealer，direct dealer promotion可以选到
                if (acc.Sales_Program__r.size() > 0) {
                    for (Sales_Program__c sp : acc.Sales_Program__r) {
                        if (sp.RecordType.DeveloperName == 'Customized' || sp.RecordType.DeveloperName == 'Standard') {
                            matchingAccounts.add(acc);
                            break;
                        }
                    }
                }
            }
        }

        for (Account acc : matchingAccounts) {
            CustomerOrGroupWrapper one = new CustomerOrGroupWrapper();
            one.Id = acc.Id;
            one.Name = acc.Name;
            one.Type = 'CUSTOMER';
            one.customerType = acc.Distributor_or_Dealer__c;
            // 23.12.24 存储当前Customer是否是二级
            one.is2ndTierDealer = (acc.Sales_Program__r.size() == 0 && acc.Distributor_or_Dealer__c == 'Dealer Location') || secondCustomerIds.contains(acc.Id) ? true : false;
            // 23.12.24 end
            one.orgCode = acc.ORG_Code__c;
            one.AccountNumber = acc.AccountNumber;
            // one.isDropship = true;
            for (Customer_Profile__c profile : acc.Customer_Profile__r) {
                one.autualSegmentation = profile.Summary_of_Potential_Actual_Sales__c;
                one.tmSegmentation = profile.TM_Segmentation__c;
            }

            if (acc.Customer_Profile__r.size() > 0) {
                one.hasCustomerProfile = true;
            } else {
                one.hasCustomerProfile = false;
            }

            Set<String> brands = new Set<String>();
            for (Sales_Program__c salesP : acc.Sales_Program__r) {
                if (String.isNotEmpty(salesP.Brands__c)) {
                    // 23.12.24 分isDropship和nonDropship两种情况判断，并且只要acc上有sales或者dropship类型的auth brand，那他就是个一级客户
                    if (isDropShip && (salesP.RecordType.DeveloperName == 'Dropship_Sales_Customized' || salesP.RecordType.DeveloperName == 'Dropship_Sales_Standard')) {
                        brands.add(salesP.Brands__c.toUpperCase());
                        one.is1stTierDealer = true;
                        one.is1stTierDealerWithDropship = true;
                    }
                    if (notDropShip && (salesP.RecordType.DeveloperName == 'Customized' || salesP.RecordType.DeveloperName == 'Standard')) {
                        brands.add(salesP.Brands__c.toUpperCase());
                        one.is1stTierDealer = true;
                    }
                    // 23.12.24 end
                }
            }
            one.authorizedBrands = brands.toString().removeStart('{').removeEnd('}').replaceAll(', ', ';');
            result.add(one);
            // result.put(one.Name + one.AccountNumber, one);
        }

        for (Promotion_Customer_Group__c customerGroup : (List<Promotion_Customer_Group__c>) Database.query(groupQueryStr)) {
            if (customerGroup.Promotion_Customer_In_Group__r.size() == 0) {
                continue;
            }
            CustomerOrGroupWrapper one = new CustomerOrGroupWrapper();
            one.Id = customerGroup.Id;
            one.Name = customerGroup.Name;
            one.Type = 'GROUP';
            // one.isDropship = true;
            result.add(one);
        }

        return result;
    }
    
    /**
     * @description Get the customers  by search key
     */
    @AuraEnabled
    public static List<CustomerOrGroupWrapper> getCustomerOrGroup(
        String keyWord,
        Boolean isDropShip,
        Boolean isCCA
    ) {
        List<CustomerOrGroupWrapper> result = new List<CustomerOrGroupWrapper>();
        String likeKeyWord = keyWord;
        if(String.isBlank(likeKeyWord)){
            return result;
        }
        if(!likeKeyWord.startsWith('%')) {
            likeKeyWord = '%' + likeKeyWord;
        } else {
            likeKeyWord = likeKeyWord;
        }

        if(!likeKeyWord.endsWith('%')) {
            likeKeyWord = likeKeyWord + '%' ;
        } else {
            likeKeyWord = likeKeyWord;
        }

        User currentUser = [SELECT Id, Profile.Name FROM User WHERE Id = :UserInfo.getUserId()];
        String accountQueryStr = 'SELECT Id, Name, ORG_Code__c, AccountNumber, Distributor_or_Dealer__c, (SELECT Id, Segmentation__c, TM_Segmentation__c, Summary_of_Potential_Actual_Sales__c FROM Customer_Profile__r), (SELECT Id, Name, Brands__c, RecordType.Name FROM Sales_Program__r WHERE RecordType.Name != \'Service\' AND RecordType.DeveloperName != \'Service_Standard\' AND RecordType.DeveloperName != \'Service_Customized\' AND Approval_Status__c = \'Approved\') FROM Account WHERE (Name LIKE :likeKeyWord OR AccountNumber LIKE :likeKeyWord) AND RecordType.Name = \'Channel\'';
        String groupQueryStr = 'SELECT Id, Name ';
        
        Map<String, Set<String>> dropShipCustomers = new Map<String, Set<String>>();
        if (isDropShip) {
            dropShipCustomers = CCM_PromotionUtil.getDropShipCustomerWithBrandsForIndividual(new Promotion2__c());
            Set<String> dropshipCustomerIdSet = dropShipCustomers.keySet();
            accountQueryStr += ' AND Id IN :dropshipCustomerIdSet ';
            groupQueryStr += ' , (SELECT Id FROM Promotion_Customer_In_Group__r WHERE Customer__c IN :dropshipCustomerIdSet) ';
        }
        groupQueryStr += ' FROM Promotion_Customer_Group__c WHERE Name LIKE :likeKeyWord';

        if (currentUser.Profile.Name != 'Channel') {
            if(isCCA){
                accountQueryStr += ' AND ORG_Code__c = \'CCA\' ';
            }else{
                accountQueryStr += ' AND ORG_Code__c != \'CCA\' ';
            }
        }

        for(Account acc : (List<Account>)Database.query(accountQueryStr)) {
            CustomerOrGroupWrapper one = new CustomerOrGroupWrapper();
            one.Id = acc.Id;
            one.Name = acc.Name;
            one.Type = 'CUSTOMER';
            one.orgCode = acc.ORG_Code__c;
            one.AccountNumber = acc.AccountNumber;
            for(Customer_Profile__c profile : acc.Customer_Profile__r) {
                one.autualSegmentation = profile.Summary_of_Potential_Actual_Sales__c;
                one.tmSegmentation = profile.TM_Segmentation__c;
            }
            
            if(acc.Customer_Profile__r.size()>0){
                one.hasCustomerProfile = true;
            }else{
                one.hasCustomerProfile = false;
            }

            Set<String> brands = new Set<String>();
            if (isDropShip) {
                brands = dropShipCustomers.get(one.Id);
            } else {
                for (Sales_Program__c salesP : acc.Sales_Program__r) {
                    if (String.isNotEmpty(salesP.Brands__c)) {
                        brands.add(salesP.Brands__c.toUpperCase());
                    }
                }
            }
            one.authorizedBrands = brands.toString().removeStart('{').removeEnd('}').replaceAll(', ', ';');
            result.add(one);
        }

        for(Promotion_Customer_Group__c customerGroup : (List<Promotion_Customer_Group__c>)Database.query(groupQueryStr)) {
            if (isDropShip && customerGroup.Promotion_Customer_In_Group__r.size() == 0) {
                continue;
            }
            CustomerOrGroupWrapper one = new CustomerOrGroupWrapper();
            one.Id = customerGroup.Id;
            one.Name = customerGroup.Name;
            one.Type = 'GROUP';
            result.add(one);
        }

        return result;
    }

    /**
     * add by austin
     * 获取 dropship target customer 下面的对应的二级经销商
     * customerId: 一级Id
     * 
     * 2023-01-13 modify by jet
     */
    @AuraEnabled
    public static List<CustomerOrGroupWrapper> getSecondCustomer(String customerId, Boolean isCCA){

        User currentUser = [
            SELECT Id, Profile.Name
            FROM User
            WHERE Id = :UserInfo.getUserId()
        ];

        List<CustomerOrGroupWrapper> result = new List<CustomerOrGroupWrapper>(); //返回值
        Map<String, Set<String>> x1x2Map = new Map<String, Set<String>>(); // 一二级对应关系
        Set<String> secondIds = new Set<String>(); // 一级对应的二级Id


        // // 这里加上dropship的record type的限制，拿到 dropship brand
        // List<Sales_Program__c> firstCustomerBrand = [
        //     SELECT Id, Name, Brands__c  
        //     FROM Sales_Program__c 
        //     WHERE RecordType.Name != 'Service' 
        //     AND RecordType.DeveloperName != 'Service_Standard' 
        //     AND RecordType.DeveloperName != 'Service_Customized' 
        //     AND (RecordType.DeveloperName = 'Dropship_Sales_Customized' OR RecordType.DeveloperName = 'Dropship_Sales_Standard')
        //     AND Approval_Status__c = 'Approved'
        //     AND Customer__c = :customerId
        // ];

        // CCM_PromotionUtil.SalesHierarchyRelatedWrapper customerData  = CCM_PromotionUtil.getSalesHierarchyData();
        // x1x2Map = customerData.x1x2Map;

        // //找到一级对应的二级
        // if (x1x2Map.containsKey(customerId)) {
        //     secondIds = x1x2Map.get(customerId);
        // }

        //修改选择customer逻辑:
        // Customer没有sales 的几个类型的authorized brand，但是customer type = 2nd tier dealer (sales)，则他是2nd tier dealer，dropship promotion可以选到
        // List<Sales_Program__c> firstCustomerBrand = [			
        //     SELECT Id, Name, Brands__c, Customer__c  
        //     FROM Sales_Program__c 
        //     WHERE RecordType.Name Not In ('Customized','Standard','Dropship_Sales_Customized','Dropship_Sales_Standard') 
        //     AND Approval_Status__c = 'Approved' 
        //     AND Customer__r.Distributor_or_Dealer__c = 'Dealer Location'
        //     AND Customer__c = :customerId
        // ];
        // if (firstCustomerBrand.size() > 0) {
        //     for (Sales_Program__c sp : firstCustomerBrand) {
        //         secondIds.add(sp.Customer__c);
        //     }
        // }
        // Customer 有dropship authorized brand，则他是有dropship业务的direct dealer，dropship promotion可以选到，存为top customer，选他则名下的2nd tier dealer都被选进来。
        // 23.11.6:修改了RecordType.Name为RecordType.DeveloperName
        List<Sales_Program__c> secondCustomerBrand = [			
            SELECT Id, Name, Brands__c, Customer__c  
            FROM Sales_Program__c 
            WHERE RecordType.DeveloperName In ('Dropship_Sales_Customized','Dropship_Sales_Standard') 
            AND Approval_Status__c = 'Approved' 
            AND Customer__c = :customerId
        ];

        // if (secondCustomerBrand.size() > 0) {
            CCM_PromotionUtil.SalesHierarchyRelatedWrapper customerData  = CCM_PromotionUtil.getSalesHierarchyData();
            x1x2Map = customerData.x1x2Map;
    
            //找到一级对应的二级
            if (x1x2Map.containsKey(customerId)) {
                secondIds = x1x2Map.get(customerId);
            }
        // }

        // String accountQueryStr = 'SELECT Id, Name, ORG_Code__c, AccountNumber, (SELECT Id, Segmentation__c, TM_Segmentation__c, Summary_of_Potential_Actual_Sales__c FROM Customer_Profile__r), (SELECT Id, Name, Brands__c FROM Sales_Program__r WHERE RecordType.Name != \'Service\' AND RecordType.DeveloperName != \'Service_Standard\' AND RecordType.DeveloperName != \'Service_Customized\' AND Approval_Status__c = \'Approved\') FROM Account WHERE RecordType.Name = \'Channel\'';
        // String accountQueryStr = 'SELECT Id, Name, ORG_Code__c, AccountNumber, (SELECT Id, Segmentation__c, TM_Segmentation__c, Summary_of_Potential_Actual_Sales__c FROM Customer_Profile__r) FROM Account WHERE RecordType.Name = \'Channel\'';

        // 查询二级customer信息
        List<Account> accounts = [
            SELECT Id, Name, ORG_Code__c, AccountNumber, 
            (SELECT Id, Segmentation__c, TM_Segmentation__c, Summary_of_Potential_Actual_Sales__c FROM Customer_Profile__r),
            (SELECT Id, Name, Brands__c, Customer__c FROM Sales_Program__r )
            FROM Account
            WHERE Id IN :secondIds
        ];
 
        // 这段逻辑弃用，调用的方法没有删
        /*
        Map<String, Set<String>> dropShipCustomers = new Map<String, Set<String>>();
        dropShipCustomers = CCM_PromotionUtil.getIsDropShipCustomerForIndividual1(customerId);
        Set<String> dropshipCustomerIdSet = dropShipCustomers.keySet();
        String dropshipId = '';
        for (String dropShipCustomer : dropshipCustomerIdSet) {
            dropshipId += '\'' + dropShipCustomer + '\',';
        }
        */

        // String dropshipId = '';
        // for (String dropShipCustomer : secondIds) {
        //     dropshipId += '\'' + secondIds + '\',';
        // }

        // accountQueryStr += ' AND Id IN ' +'(' + dropshipId.removeEnd(',') + ')';
        // accountQueryStr += ' AND Id IN ' +'(' + customerId.removeEnd(',') + ')';

        // if (currentUser.Profile.Name != 'Channel') {
        //     if (isCCA) {
        //         accountQueryStr += ' AND ORG_Code__c = \'CCA\' ';
        //     } else {
        //         accountQueryStr += ' AND ORG_Code__c != \'CCA\' ';
        //     }
        // }

        // for (Account acc : (List<Account>) Database.query(accountQueryStr)) {
        for (Account acc : accounts) {
            CustomerOrGroupWrapper one = new CustomerOrGroupWrapper();
            one.Id = acc.Id;
            one.Name = acc.Name;
            one.Type = 'CUSTOMER';
            one.orgCode = acc.ORG_Code__c;
            one.AccountNumber = acc.AccountNumber;
            // one.isDropship = false; 
            for (Customer_Profile__c profile : acc.Customer_Profile__r) {
                one.autualSegmentation = profile.Summary_of_Potential_Actual_Sales__c;
                one.tmSegmentation = profile.TM_Segmentation__c;
            }

            if (acc.Customer_Profile__r.size() > 0) {
                one.hasCustomerProfile = true;
            } else {
                one.hasCustomerProfile = false;
            }

            Set<String> brands = new Set<String>();
            // if (isDropShip) {
            //     brands = dropShipCustomers.get(one.Id);
            // } else {
            // for (Sales_Program__c salesP : acc.Sales_Program__r) {
            for (Sales_Program__c salesP : secondCustomerBrand) {
                if (String.isNotEmpty(salesP.Brands__c)) {
                    brands.add(salesP.Brands__c.toUpperCase());
                }
            }
            // }
            one.authorizedBrands = brands.toString()
                .removeStart('{')
                .removeEnd('}')
                .replaceAll(', ', ';');
            result.add(one);
        }

        return result;
    }

    /**
     * 根据二级Id，获取其Dropship Authorized Brands
     * secondId: 二级Id
     * 
     * 2023-11-07
     */
    @AuraEnabled
    public static List<CustomerOrGroupWrapper> getDropshipAuthorizedBrandsBy2ndCustomerId(String secondId, Boolean isCCA){
        List<CustomerOrGroupWrapper> result = new List<CustomerOrGroupWrapper>(); //返回值
        CCM_PromotionUtil.SalesHierarchyRelatedWrapper customerData = CCM_PromotionUtil.getSalesHierarchyData();
        Set<Id> setX1Ids = new Set<Id>();// 存储当前二级客户的一级Id
        Map<String, Set<String>> x1x2Map = customerData.x1x2Map;
        for (String strX1Id : x1x2Map.keySet()) {
            Set<String> setX2Ids = x1x2Map.get(strX1Id);
            if (setX2Ids.contains(secondId)) {
                setX1Ids.add(strX1Id);
            }
        }

        // 查询一级客户的dropship authorized brand
        List<Sales_Program__c> lst1stCustomerDropshipAuthorizedBrand = [
            SELECT 
                Id, Name, Brands__c 
            FROM 
                Sales_Program__c 
            WHERE 
                RecordType.DeveloperName IN ('Dropship_Sales_Customized','Dropship_Sales_Standard') 
                AND Approval_Status__c = 'Approved' 
                AND Customer__c = :setX1Ids 
        ];

        // 查询二级customer信息
        List<Account> lst2ndAccounts = [
            SELECT Id, Name, ORG_Code__c, AccountNumber, 
            (SELECT Id, Segmentation__c, TM_Segmentation__c, Summary_of_Potential_Actual_Sales__c FROM Customer_Profile__r), 
            (SELECT Id, Name, Brands__c, Customer__c FROM Sales_Program__r) 
            FROM Account 
            WHERE Id = :secondId 
        ];

        if (lst2ndAccounts != null && lst2ndAccounts.size() > 0) {
            CustomerOrGroupWrapper one = new CustomerOrGroupWrapper();
            one.Id = lst2ndAccounts[0].Id;
            one.Name = lst2ndAccounts[0].Name;
            one.Type = 'CUSTOMER';
            one.orgCode = lst2ndAccounts[0].ORG_Code__c;
            one.AccountNumber = lst2ndAccounts[0].AccountNumber;
            for (Customer_Profile__c profile : lst2ndAccounts[0].Customer_Profile__r) {
                one.autualSegmentation = profile.Summary_of_Potential_Actual_Sales__c;
                one.tmSegmentation = profile.TM_Segmentation__c;
            }

            if (lst2ndAccounts[0].Customer_Profile__r.size() > 0) {
                one.hasCustomerProfile = true;
            } else {
                one.hasCustomerProfile = false;
            }

            Set<String> brands = new Set<String>();
            for (Sales_Program__c salesP : lst1stCustomerDropshipAuthorizedBrand) {
                if (String.isNotBlank(salesP.Brands__c)) {
                    brands.add(salesP.Brands__c.toUpperCase());
                }
            }
            one.authorizedBrands = brands.toString()
                .removeStart('{')
                .removeEnd('}')
                .replaceAll(', ', ';');
            one.topCustomersId = new List<Id>(setX1Ids);
            result.add(one);
        }
        System.debug('result ->' + result);
        return result;
    }

    /**
     * @description Click on different options to get all customers in one promotion customer group
     * add by austin
     */
    @AuraEnabled
    public static List<CustomerOrGroupWrapper> getAllCustomerByGroup(String groupId,Boolean isDropShip,Boolean notDropship) {
        List<CustomerOrGroupWrapper> result = new List<CustomerOrGroupWrapper>();
        List<CustomerOrGroupWrapper> isDropshipCustomer = new List<CustomerOrGroupWrapper>();
        List<CustomerOrGroupWrapper> notDropshipCustomer = new List<CustomerOrGroupWrapper>();
        if(isDropShip){
            isDropshipCustomer = getDropshipCustomerByGroup(groupId);
        }
        if(notDropship){
            notDropshipCustomer = getNotDropshipCustomerByGroup(groupId);
        }
        result.addAll(isDropshipCustomer);
        result.addAll(notDropshipCustomer);

        return result;
    }

    /**
     * @description 获取DropshipCustomerByGroup
     * add by austin
     */
    public static List<CustomerOrGroupWrapper> getDropshipCustomerByGroup(String groupId){
        String groupQueryStr = 'SELECT Customer__c FROM Promotion_Customer_In_Group__c WHERE Promotion_Customer_Group__c = :groupId ';
        Map<String, Set<String>> dropShipCustomers = new Map<String, Set<String>>();
        dropShipCustomers = CCM_PromotionUtil.getDropShipCustomerWithBrandsForIndividual(new Promotion2__c());
        Set<String> dropShipCustomerIds = dropShipCustomers.keySet();
        // String dropshipId = '';
        // if (dropShipCustomerIds.size() > 0) {
        //     for (String dropShipCustomer : dropShipCustomerIds) {
        //         dropshipId += '\'' + dropShipCustomer + '\',';
        //     }
        //     groupQueryStr += ' AND Customer__c IN ' +'(' + dropshipId.removeEnd(',') + ') ';
        // }else{
        groupQueryStr += ' AND Customer__c IN : dropShipCustomerIds ';
        // }
        
        List<Promotion_Customer_In_Group__c> customersInGroup = (List<Promotion_Customer_In_Group__c>) Database.query(groupQueryStr);
        Set<Id> customerIds = new Set<Id>();
        for (Promotion_Customer_In_Group__c customer : customersInGroup) {
            customerIds.add(customer.Customer__c);
        }

        //Make sure customer must be channel and with profile
        List<Account> customerList = [
            SELECT
                Id,
                Name,
                (
                    SELECT Id, Segmentation__c, TM_Segmentation__c
                    FROM Customer_Profile__r
                )
            FROM Account
            WHERE Id IN :customerIds AND RecordType.Name = 'Channel'
        ];
        Set<Id> customerWithProfileIds = new Set<Id>();
        for (Account customer : customerList) {
            if (customer.Customer_Profile__r != null) {
                for (
                    Customer_Profile__c customerProfile : customer.Customer_Profile__r
                ) {
                    if (customerProfile != null) {
                        customerWithProfileIds.add(customer.Id);
                    }
                }
                customerWithProfileIds.add(customer.Id);
            }
        }
        Map<Id, Account> accountMap = new Map<Id, Account>(
            [
                SELECT
                    Id,
                    Name,
                    AccountNumber,
                    (
                        SELECT
                            Id,
                            Segmentation__c,
                            TM_Segmentation__c,
                            Summary_of_Potential_Actual_Sales__c
                        FROM Customer_Profile__r
                    ),
                    (
                        SELECT Id, Name, Brands__c
                        FROM Sales_Program__r
                        WHERE
                            RecordType.Name != 'Service'
                            AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME
                            AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME
                            AND Approval_Status__c = 'Approved'
                    )
                FROM Account
                WHERE Id IN :customerWithProfileIds
            ]
        );
        List<CustomerOrGroupWrapper> result = new List<CustomerOrGroupWrapper>();
        for (Id oneId : accountMap.keySet()) {
            CustomerOrGroupWrapper one = new CustomerOrGroupWrapper();
            one.Id = oneId;
            one.Name = accountMap.get(oneId).Name;
            one.AccountNumber = accountMap.get(oneId).AccountNumber;
            one.Type = 'CUSTOMER';
            for (
                Customer_Profile__c profile : accountMap.get(oneId)
                    .Customer_Profile__r
            ) {
                one.autualSegmentation = profile.Summary_of_Potential_Actual_Sales__c;
                one.tmSegmentation = profile.TM_Segmentation__c;
            }
            if (accountMap.get(oneId).Customer_Profile__r.size() > 0) {
                one.hasCustomerProfile = true;
            } else {
                one.hasCustomerProfile = false;
            }
            Set<String> brands = new Set<String>();
            // if (isDropShip) {
                brands = dropShipCustomers.get(one.Id);
            // } else {
            //     for (
            //         Sales_Program__c salesP : accountMap.get(oneId)
            //             .Sales_Program__r
            //     ) {
            //         if (String.isNotEmpty(salesP.Brands__c)) {
            //             brands.add(salesP.Brands__c.toUpperCase());
            //         }
            //     }
            // }
            one.authorizedBrands = brands.toString()
                .removeStart('{')
                .removeEnd('}')
                .replaceAll(', ', ';');
            result.add(one);
        }
        return result;
    }

    /**
     * @description Get all customers in one promotion customer group
     * update by austin
     */
    @AuraEnabled
    public static List<CustomerOrGroupWrapper> getNotDropshipCustomerByGroup(String groupId) {
        String groupQueryStr = 'SELECT Customer__c FROM Promotion_Customer_In_Group__c WHERE Promotion_Customer_Group__c = :groupId ';
        Map<String, Set<String>> dropShipCustomers = new Map<String, Set<String>>();
        // if (isDropShip) {
        //     dropShipCustomers = CCM_PromotionUtil.getDropShipCustomerWithBrandsForIndividual(
        //         new Promotion2__c()
        //     );
        //     Set<String> dropShipCustomerIds = dropShipCustomers.keySet();
        //     groupQueryStr += ' AND Customer__c IN :dropShipCustomerIds ';
        // }
        List<Promotion_Customer_In_Group__c> customersInGroup = (List<Promotion_Customer_In_Group__c>) Database.query(
            groupQueryStr
        );
        Set<Id> customerIds = new Set<Id>();
        for (Promotion_Customer_In_Group__c customer : customersInGroup) {
            customerIds.add(customer.Customer__c);
        }

        //Make sure customer must be channel and with profile
        List<Account> customerList = [
            SELECT
                Id,
                Name,
                (
                    SELECT Id, Segmentation__c, TM_Segmentation__c
                    FROM Customer_Profile__r
                )
            FROM Account
            WHERE Id IN :customerIds AND RecordType.Name = 'Channel'
        ];
        Set<Id> customerWithProfileIds = new Set<Id>();
        for (Account customer : customerList) {
            if (customer.Customer_Profile__r != null) {
                for (
                    Customer_Profile__c customerProfile : customer.Customer_Profile__r
                ) {
                    if (customerProfile != null) {
                        customerWithProfileIds.add(customer.Id);
                    }
                }
                customerWithProfileIds.add(customer.Id);
            }
        }
        Map<Id, Account> accountMap = new Map<Id, Account>(
            [
                SELECT
                    Id,
                    Name,
                    AccountNumber,
                    (
                        SELECT
                            Id,
                            Segmentation__c,
                            TM_Segmentation__c,
                            Summary_of_Potential_Actual_Sales__c
                        FROM Customer_Profile__r
                    ),
                    (
                        SELECT Id, Name, Brands__c
                        FROM Sales_Program__r
                        WHERE
                            RecordType.Name != 'Service'
                            AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME
                            AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME
                            AND Approval_Status__c = 'Approved'
                    )
                FROM Account
                WHERE Id IN :customerWithProfileIds
            ]
        );
        List<CustomerOrGroupWrapper> result = new List<CustomerOrGroupWrapper>();
        for (Id oneId : accountMap.keySet()) {
            CustomerOrGroupWrapper one = new CustomerOrGroupWrapper();
            one.Id = oneId;
            one.Name = accountMap.get(oneId).Name;
            one.AccountNumber = accountMap.get(oneId).AccountNumber;
            one.Type = 'CUSTOMER';
            for (
                Customer_Profile__c profile : accountMap.get(oneId)
                    .Customer_Profile__r
            ) {
                one.autualSegmentation = profile.Summary_of_Potential_Actual_Sales__c;
                one.tmSegmentation = profile.TM_Segmentation__c;
            }
            if (accountMap.get(oneId).Customer_Profile__r.size() > 0) {
                one.hasCustomerProfile = true;
            } else {
                one.hasCustomerProfile = false;
            }
            Set<String> brands = new Set<String>();
            // if (isDropShip) {
            //     brands = dropShipCustomers.get(one.Id);
            // } else {
                for (
                    Sales_Program__c salesP : accountMap.get(oneId)
                        .Sales_Program__r
                ) {
                    if (String.isNotEmpty(salesP.Brands__c)) {
                        brands.add(salesP.Brands__c.toUpperCase());
                    }
                }
            // }
            one.authorizedBrands = brands.toString()
                .removeStart('{')
                .removeEnd('}')
                .replaceAll(', ', ';');
            result.add(one);
        }
        return result;
    }

    /**
     * @description get second tier customer under target customer 
     * Add by Austin
     */
    // public static List<CustomerOrGroupWrapper> getTargetCustomerSecTier(String targetId){
    //     List<CustomerOrGroupWrapper> result = new List<CustomerOrGroupWrapper>();
    //     Set<String> customerId = new Set<String>();
    //     List<Sales_Hierarchy__c> salesList = [SELECT X1st_tier_dealer__c, X1st_tier_dealer__r.Name, X2st_tier_dealer__c FROM Sales_Hierarchy__c WHERE X1st_tier_dealer__c =: targetId];
    //     if (salesList.size() > 0) {
    //         for (Sales_Hierarchy__c store : salesList) {
    //             customerId.add(store.X2st_tier_dealer__c);
    //         }
    //         List<Sales_Hierarchy__c> buyingGroupList = [SELECT X1st_tier_dealer__c, X1st_tier_dealer__r.Name, X2st_tier_dealer__c FROM Sales_Hierarchy__c WHERE X1st_tier_dealer__c =: targetId];
    //     }
    // }

    /**
     * @description Get Promotion2__c and promotion type list based on sell-in/sell-through
     *              isSaved is used by front-end
     */
    @AuraEnabled
    public static PromotionInfoWrapper getPromotionInfo(String promotionId) {
        if (String.isBlank(promotionId)) {
            return null;
        }
        List<Promotion2__c> result = [
            SELECT
                Id,
                Name,
                // add haibo: promotion (french) 翻译
                Promotion_Name_French__c,
                Promotion_Details_French__c,
                RecordTypeId,
                RecordType.DeveloperName,
                Brands__c,
                Product_Category__c,
                Core_Reason__c,
                Promotion_Type__c,
                Promotion_Type_for_External__c,
                CurrencyIsoCode,
                Operating_Unit__c,
                (SELECT Id, Campaign__c FROM Promotion_Campaign__r),
                Recon_Tools__c,
                Opening_Order_Discount__c,
                Promo_Details__c,
                Purpose_Target__c,
                Description_for_Purpose_Target__c,
                Promo_Code__c,
                Promotion_Code_For_External__c,
                Promotion_Status__c,
                List_View_Name__c,
                PMAPP__c,
                Is_Merchandising_Promotion__c,
                Is_DropShip_Promotion__c,
                Non_Dropship_Promotion__c,
                CreatedBy.Name,
                CreatedDate,
                LastModifiedDate,
                LastModifiedBy.Name,
                ORG_Code__c,
                OwnerId,
                First_Time_Purchase_Promotion__c
            FROM Promotion2__c
            WHERE Id = :promotionId
        ];
        if (result.size() == 0) {
            return null;
        }
        PromotionInfoWrapper wrapper = new PromotionInfoWrapper();
        wrapper.promotion = result[0];
        //only for frontend display
        wrapper.isSaved = false;

        // get promotion type by record type
        Schema.DescribeFieldResult fieldDescribe = Promotion2__c.Promotion_Type__c.getDescribe();
        List<CCM_PromotionUtil.PromotionPickWrapper> promoTypeList = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        promoTypeList = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            promoTypeList
        );
        String[] sellThroughType = new List<String>{
            CCM_PromotionUtil.PROMOTION_TYPE_BOGO_NAME,
            CCM_PromotionUtil.PROMOTION_TYPE_PRICE_DISCOUNT_NAME
        };
        if (
            CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_APINAME.equalsIgnoreCase(
                result[0].RecordType.DeveloperName
            )
        ) {
            for (Integer i = promoTypeList.size() - 1; i >= 0; i--) {
                CCM_PromotionUtil.PromotionPickWrapper pick = promoTypeList.get(
                    i
                );
                if (
                    sellThroughType[0].equalsIgnoreCase(pick.Name) ||
                    sellThroughType[1].equalsIgnoreCase(pick.Name)
                ) {
                    continue;
                } else {
                    promoTypeList.remove(i);
                }
            }
        }
        wrapper.promotionType = promoTypeList;

        List<CCM_PromotionUtil.PromotionPickWrapper> listViewName = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        listViewName = CCM_PromotionUtil.getPickListMap(
            Promotion2__c.List_View_Name__c.getDescribe(),
            listViewName
        );
        for (Integer i = listViewName.size() - 1; i >= 0; i--) {
            CCM_PromotionUtil.PromotionPickWrapper pick = listViewName.get(i);
            if (result[0].List_View_Name__c != NULL && result[0].List_View_Name__c.contains(pick.Id)) {
                continue;
            } else {
                listViewName.remove(i);
            }
        }
        wrapper.listViewName = listViewName;

        //populate promotion campaign
        wrapper.campaignSource = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        if (result[0].Promotion_Campaign__r.size() > 0) {
            List<String> campaignList = new List<String>();
            for (
                Promotion_Campaign__c proCam : result[0].Promotion_Campaign__r
            ) {
                campaignList.add(proCam.Campaign__c);
            }
            wrapper.campaignList = campaignList;
            List<CCM_PromotionUtil.PromotionPickWrapper> campaignSource = new List<CCM_PromotionUtil.PromotionPickWrapper>();
            //query campaign
            for (Campaign cam : [
                SELECT Id, Name
                FROM Campaign
                WHERE id IN :campaignList
            ]) {
                CCM_PromotionUtil.PromotionPickWrapper pick = new CCM_PromotionUtil.PromotionPickWrapper();
                pick.Id = cam.id;
                pick.ApiName = cam.id;
                pick.Name = cam.Name;
                campaignSource.add(pick);
            }
            wrapper.campaignSource = campaignSource;
        }

        User userObj = [
            SELECT Id, Profile.Name
            FROM User
            WHERE id = :UserInfo.getUserId()
        ];
        //whether current user is promotion owner
        wrapper.isOwner = false;
        if (userObj.Id == result[0].OwnerId) {
            wrapper.isOwner = true;
        }
        //current user's profile
        wrapper.currentUserProfile = userObj.Profile.Name;

        wrapper.isChannel = CCM_PromotionUtil.checkIsChannel(promotionId);
        return wrapper;
    }

    /**
     * @description Get Price Books based Chosen Customer List and Chosen Brand List
     *              Price Books are from Customer's Authorized Brand's Price Book
     *              Price Books are grouped by Customer's Authorized Brand's Brand
     */
    @AuraEnabled
    public static List<PriceListWrapper> searchPriceList(
        String[] customerIds,
        String[] brands
    ) {
        List<PriceListWrapper> result = new List<PriceListWrapper>();
        if (customerIds == null || customerIds.size() == 0) {
            return result;
        }
        if (brands == null || brands.size() == 0) {
            return result;
        }

        List<Sales_Program__c> customerPriceList = [
            SELECT Id, Brands__c, Price_Book__c, Price_Book__r.Name
            FROM Sales_Program__c
            WHERE
                Customer__c IN :customerIds
                AND Brands__c IN :brands
                AND RecordType.Name != 'Service'
                AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME
                AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME
                AND Approval_Status__c = 'Approved'
        ];
        //Brand, PriceBookId
        Map<String, Set<Id>> brandPriceListMap = new Map<String, Set<Id>>();
        Set<Id> priceBookIds = new Set<Id>();
        for (Sales_Program__c customerBrand : customerPriceList) {
            priceBookIds.add(customerBrand.Price_Book__c);

            if (!brandPriceListMap.containsKey(customerBrand.Brands__c)) {
                Set<Id> tmpList = new Set<Id>();
                brandPriceListMap.put(customerBrand.Brands__c, tmpList);
            }
            brandPriceListMap.get(customerBrand.Brands__c)
                .add(customerBrand.Price_Book__c);
        }

        Map<Id, Pricebook2> priceBookMap = new Map<Id, Pricebook2>(
            [
                SELECT Id, Name
                FROM Pricebook2
                WHERE Id IN :priceBookIds AND IsActive = TRUE
            ]
        );
        for (String brand : brandPriceListMap.keySet()) {
            Set<Id> bookSet = brandPriceListMap.get(brand);
            if (bookSet == null || bookSet.size() == 0) {
                continue;
            }
            PriceListWrapper priceWrapper = new PriceListWrapper();
            priceWrapper.brand = brand;
            priceWrapper.priceBooks = new List<Pricebook2>();
            for (Id bookId : bookSet) {
                priceWrapper.priceBooks.add(priceBookMap.get(bookId));
            }
            result.add(priceWrapper);
        }

        return result;
    }

    /**
     * @description Get Price Books based Chosen Customer List and Chosen Brand List
     *              Price Books are from Customer's Authorized Brand's Price Book
     *              Price Books are grouped by Customer's Authorized Brand's Brand
     */
    @AuraEnabled
    public static List<Pricebook2> searchPriceListByName(
        String keyWord,
        Boolean isDropShip,
        Boolean notDropship,
        Boolean isCCA
    ) {
        System.debug('keyWord: ' + keyWord);
        if (String.isEmpty(keyWord)) {
            return null;
        }
        User currentUser = [
            SELECT Id, Profile.Name, Contact.AccountId
            FROM User
            WHERE Id = :UserInfo.getUserId()
        ];
        String key = '%' + keyWord + '%';
        Set<Id> priceListId = new Set<Id>();
        List<Pricebook2> priceBookList = new List<Pricebook2>();
        List<Pricebook2> IsDropshipPriceList = new List<Pricebook2>();
        List<Pricebook2> notDropshipPriceBookList = new List<Pricebook2>();
        List<Pricebook2> resultList = new List<Pricebook2>();
        if (isDropShip) {
            // for (Customer_Brand_Pricebook_Mapping__c pricebook : [
            //     SELECT
            //         Price_Book__c,
            //         Billing_Address_With_Authorized_Brand__c,
            //         Billing_Address_With_Authorized_Brand__r.Account_Address__r.Customer__c,
            //         Special_Dropship_Address__c,
            //         Brand__c
            //     FROM Customer_Brand_Pricebook_Mapping__c
            //     WHERE Type__c = 'Dropship' AND Is_Active__c = TRUE
            // ]) {
            //     priceListId.add(pricebook.Price_Book__c);
            // }
            IsDropshipPriceList = getIsDropshipPriceList(currentUser, key, isCCA);
        }
        if (notDropship) {
            notDropshipPriceBookList = getNotDropshipPriceBook(currentUser, key, isCCA);
        }
        if (currentUser.Profile.Name != 'Channel') {
            if (!isCCA) {
                priceBookList = [
                    SELECT Id, Name
                    FROM Pricebook2
                    WHERE
                        Name LIKE :key
                        AND IsActive = TRUE
                        AND ORG_Code__c != 'CCA'
                ];
            } else {
                priceBookList = [
                    SELECT Id, Name
                    FROM Pricebook2
                    WHERE
                        Name LIKE :key
                        AND IsActive = TRUE
                        AND ORG_Code__c = 'CCA'
                ];
            }
        } else {
            priceBookList = [
                SELECT Id, Name
                FROM Pricebook2
                WHERE Name LIKE :key AND IsActive = TRUE
            ];
        }

        if (IsDropshipPriceList.size() > 0 || notDropshipPriceBookList.size() > 0 ) {
            Set<Pricebook2> resultSet = new Set<Pricebook2>();
            resultSet.addAll(IsDropshipPriceList);
            resultSet.addAll(notDropshipPriceBookList);
            resultList.addAll(resultSet);
            return resultList;
        }
        
        resultList.addAll(priceBookList);
        return resultList;
    }

    /**
     * Description: 获取二级经销商pricebookId
     * add by austin
     */
    public static List<Pricebook2> getIsDropshipPriceList(User currentUser,String key, Boolean isCCA){
        Set<Id> setPriceBookId = new Set<Id>();
        List<Pricebook2> priceBookList = new List<Pricebook2>();
        for (Sales_Program__c priceBook : [
            SELECT 
                id, name, Price_Book__c 
            FROM Sales_Program__c 
            WHERE recordtype.developername IN ('Dropship_Sales_Standard', 'Dropship_Sales_Customized')
        ]) {
            setPriceBookId.add(priceBook.Price_Book__c); 
        }
        if (currentUser.Profile.Name != 'Channel') {
            if (!isCCA) {
                priceBookList = [
                    SELECT Id, Name
                    FROM Pricebook2
                    WHERE
                        Name LIKE :key
                        AND IsActive = TRUE
                        AND Id IN :setPriceBookId
                        AND ORG_Code__c != 'CCA'
                ];
            } else {
                priceBookList = [
                    SELECT Id, Name
                    FROM Pricebook2
                    WHERE
                        Name LIKE :key
                        AND IsActive = TRUE
                        AND Id IN :setPriceBookId
                        AND ORG_Code__c = 'CCA'
                ];
            }
        } else {
            priceBookList = [
                SELECT Id, Name
                FROM Pricebook2
                WHERE
                    Name LIKE :key
                    AND IsActive = TRUE
                    AND Id IN :setPriceBookId
            ];
        }
        return priceBookList;
    }

    /**
     * description: 获取一级经销商pricebookid
     */
    public static List<Pricebook2> getNotDropshipPriceBook(User currentUser, String key, Boolean isCCA){
         Set<Id> setPriceBookId = new Set<Id>();
         List<Pricebook2> priceBookList = new List<Pricebook2>();
         for (Sales_Program__c PriceBook : [
            SELECT id, name, Price_Book__c
            FROM Sales_Program__c
            WHERE recordtype.developername IN ('Customized', 'Standard')
            ]) {
                setPriceBookId.add(PriceBook.Price_Book__c);
        }
        if (currentUser.Profile.Name != 'Channel') {
                if (!isCCA) {
                    priceBookList = [
                        SELECT Id, Name
                        FROM Pricebook2
                        WHERE
                            Name LIKE :key
                            AND IsActive = TRUE
                            AND Id IN: setPriceBookId
                            AND ORG_Code__c != 'CCA'
                    ];
                } else {
                    priceBookList = [
                        SELECT Id, Name
                        FROM Pricebook2
                        WHERE
                            Name LIKE :key
                            AND IsActive = TRUE
                            AND Id IN: setPriceBookId
                            AND ORG_Code__c = 'CCA'
                    ];
                }
            } else {
                priceBookList = [
                    SELECT Id, Name
                    FROM Pricebook2
                    WHERE Name LIKE :key AND IsActive = TRUE AND Id IN: setPriceBookId
                ];
            }
            return priceBookList;
    }

    /**
     * @description: get mixed brands for customer.
     */
    public static Set<String> getMixedAuthorizedBrands(
        List<Sales_Program__c> salesProgramList
    ) {
        Set<String> mixedAuthorizedBrands = new Set<String>();
        Set<Id> customerIdSet = new Set<Id>();
        Map<String, Integer> brandNumberMap = new Map<String, Integer>();
        for (Sales_Program__c sp : salesProgramList) {
            customerIdSet.add(sp.Customer__c);

            if (!brandNumberMap.containsKey(sp.Brands__c)) {
                brandNumberMap.put(sp.Brands__c, 0);
            }
            brandNumberMap.put(
                sp.Brands__c,
                (brandNumberMap.get(sp.Brands__c) + 1)
            );
        }
        for (String brand : brandNumberMap.keySet()) {
            if (brandNumberMap.get(brand) == customerIdSet.size()) {
                mixedAuthorizedBrands.add(brand);
            }
        }
        return mixedAuthorizedBrands;
    }

    /**
     * @description: get all brands for customer.
     */
    public static Set<String> getAllAuthorizedBrands(Set<Id> customerIds) {
        AggregateResult[] groupedResults = [
            SELECT Brands__c
            FROM Sales_Program__c
            WHERE
                Approval_Status__c = 'Approved'
                AND Customer__r.Id != NULL
                AND Price_Book__r.IsActive = TRUE
                AND Customer__r.RecordType.DeveloperName = 'Channel'
                AND RecordType.DeveloperName != 'Service'
                AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME
                AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME
                AND Brands__c != NULL
                AND Customer__c IN :customerIds
            GROUP BY Brands__c
        ];

        Set<String> authorizedBrands = new Set<String>();

        for (AggregateResult ar : groupedResults) {
            authorizedBrands.add(String.valueOf(ar.get('Brands__c')));
        }
        return authorizedBrands;
    }

    // /**
    //  * add by jet
    //  * @description: get all brands for customer.
    //  */
    // public static Set<String> getDropAuthorizedBrands(Set<Id> customerIds) {
    //     AggregateResult[] groupedResults = [
    //         SELECT Brands__c
    //         FROM Sales_Program__c
    //         WHERE
    //             Approval_Status__c = 'Approved'
    //             AND Customer__r.Id != NULL
    //             AND Price_Book__r.IsActive = TRUE
    //             AND Customer__r.RecordType.DeveloperName = 'Channel'
    //             AND RecordType.DeveloperName != 'Service'
    //             AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_CUSTOMIZED_NAME
    //             AND RecordType.DeveloperName != :CCM_Constants.SALES_PROGRAM_RECORD_TYPE_SERVICE_STANDARD_NAME
    //             AND (RecordType.DeveloperName = 'Dropship_Sales_Customized' OR RecordType.DeveloperName = 'Dropship_Sales_Standard')
    //             AND Brands__c != NULL
    //             AND Customer__c IN :customerIds
    //         GROUP BY Brands__c
    //     ];

    //     Set<String> authorizedBrands = new Set<String>();

    //     for (AggregateResult ar : groupedResults) {
    //         authorizedBrands.add(String.valueOf(ar.get('Brands__c')));
    //     }
    //     return authorizedBrands;
    // }

    /**
     * @description Get all necessary Drop Down List for Promotion Info page
     */
    @AuraEnabled
    public static PromotionPickListWrapper getPromotionBasicInfo(
        String recordType
    ) {
        if (String.isBlank(recordType)) {
            recordType = CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME;
        }

        PromotionPickListWrapper result = new PromotionPickListWrapper();
        result.recordType = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.promotionType = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.currencyType = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.operatingUnit = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        //result.tradeChannel = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.brand = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.priceList = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.coreReason = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.promotionTypeExt = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.purposeTarget = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.productCategory = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.selectCustomerMethod = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.listViewName = new List<CCM_PromotionUtil.PromotionPickWrapper>();

        Schema.DescribeFieldResult fieldDescribe;

        //Get Record Type
        Schema.DescribeSObjectResult describle = Promotion2__c.SObjectType.getDescribe();
        result.recordType = CCM_PromotionUtil.getRecordTypeList(
            describle,
            CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME
        );

        fieldDescribe = Promotion2__c.Promotion_Type__c.getDescribe();
        result.promotionType = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            result.promotionType
        );
        String[] sellThroughType = new List<String>{
            CCM_PromotionUtil.PROMOTION_TYPE_BOGO_NAME,
            CCM_PromotionUtil.PROMOTION_TYPE_PRICE_DISCOUNT_NAME,
            CCM_PromotionUtil.PROMOTION_TYPE_WHOLE_ORDER_NAME, // update by austin
            CCM_PromotionUtil.PROMOTION_TYPE_BUY_MORE_NAME // Added by zoe on 2025-01-16 for new promotion type BMSM for sell-through
        };
        // Start : Added by zoe on 2025-01-16 for new promotion type BMSM for sell-through
        String[] sellInExcludeType = new List<String>{
            CCM_PromotionUtil.PROMOTION_TYPE_BUY_MORE_NAME 
        };
        // End : Added by zoe on 2025-01-16 for new promotion type BMSM for sell-through
        if (
            CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_APINAME.equalsIgnoreCase(
                recordType
            )
        ) {
            for (Integer i = result.promotionType.size() - 1; i >= 0; i--) {
                CCM_PromotionUtil.PromotionPickWrapper pick = result.promotionType.get(
                    i
                );
                if (
                    sellThroughType[0].equalsIgnoreCase(pick.Id) ||
                    sellThroughType[1].equalsIgnoreCase(pick.Id) ||
                    sellThroughType[2].equalsIgnoreCase(pick.Id) ||// update by asutin 
                    sellThroughType[3].equalsIgnoreCase(pick.Id) // Added by zoe on 2025-01-16 for new promotion type BMSM for sell-through
                ) {
                    continue;
                } else {
                    result.promotionType.remove(i);
                }
            }
        }
        // Start : Added by zoe on 2025-01-16 for new promotion type BMSM for sell-through
        if (
            CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME.equalsIgnoreCase(
                recordType
            )
        ) {
            for (Integer i = result.promotionType.size() - 1; i >= 0; i--) {
                CCM_PromotionUtil.PromotionPickWrapper pick = result.promotionType.get(
                    i
                );
                if (
                    sellInExcludeType[0].equalsIgnoreCase(pick.Id) 
                ) {
                    result.promotionType.remove(i);
                } else {
                    continue;
                }
            }
        }
        // End: Added by zoe on 2025-01-16 for new promotion type BMSM for sell-through

        fieldDescribe = Promotion2__c.CurrencyIsoCode.getDescribe();
        result.currencyType = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            result.currencyType,
            UserInfo.getDefaultCurrency()
        );

        fieldDescribe = Promotion2__c.Operating_Unit__c.getDescribe();
        result.operatingUnit = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            result.operatingUnit
        );

        fieldDescribe = Promotion2__c.Brands__c.getDescribe();
        result.brand = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            result.brand
        );

        Map<Id, Pricebook2> priceBookMap = new Map<Id, Pricebook2>(
            [SELECT Name FROM Pricebook2 WHERE IsActive = TRUE]
        );
        for (Id id : priceBookMap.keySet()) {
            Pricebook2 priceBook = priceBookMap.get(id);
            CCM_PromotionUtil.PromotionPickWrapper pick = new CCM_PromotionUtil.PromotionPickWrapper();
            pick.Id = priceBook.Id;
            pick.Name = priceBook.Name;
            result.priceList.add(pick);
        }

        fieldDescribe = Promotion2__c.Core_Reason__c.getDescribe();
        result.coreReason = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            result.coreReason
        );

        fieldDescribe = Promotion2__c.Promotion_Type_for_External__c.getDescribe();

        // get promotion type external value by record type
        result.promotionTypeExt = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            result.promotionTypeExt
        );
        // List<CCM_PromotionUtil.PromotionPickWrapper> targetPromtionTypeExt = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        if (
            CCM_PromotionUtil.PROMOTION_SELLTHROUGH_RECORDTYPE_APINAME.equalsIgnoreCase(
                recordType
            )
        ) {
            for (Integer i = result.promotionTypeExt.size() - 1; i >= 0; i--) {
                CCM_PromotionUtil.PromotionPickWrapper pick = result.promotionTypeExt.get(
                    i
                );
                if (
                    sellThroughType[0].equalsIgnoreCase(pick.Id) ||
                    sellThroughType[1].equalsIgnoreCase(pick.Id) ||
                    sellThroughType[2].equalsIgnoreCase(pick.Id) ||
                    sellThroughType[3].equalsIgnoreCase(pick.Id) // Added by zoe on 2025-01-16 for new promotion type BMSM for sell-through
                ) {
                    // targetPromtionTypeExt.add(pick);
                    continue;
                } else {
                    result.promotionTypeExt.remove(i);
                }
            }
            // result.promotionTypeExt = targetPromtionTypeExt;
        }
        // Start : Added by zoe on 2025-01-16 for new promotion type BMSM for sell-through
        if (
            CCM_PromotionUtil.PROMOTION_SELLIN_RECORDTYPE_APINAME.equalsIgnoreCase(
                recordType
            )
        ) {
            for (Integer i = result.promotionTypeExt.size() - 1; i >= 0; i--) {
                CCM_PromotionUtil.PromotionPickWrapper pick = result.promotionTypeExt.get(
                    i
                );
                if (
                    sellInExcludeType[0].equalsIgnoreCase(pick.Id) 
                ) {
                    result.promotionTypeExt.remove(i);
                } else {
                    continue;
                }
            }
        }
        // End: Added by zoe on 2025-01-16 for new promotion type BMSM for sell-through
        fieldDescribe = Promotion2__c.Purpose_Target__c.getDescribe();
        result.purposeTarget = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            result.purposeTarget
        );

        fieldDescribe = Promotion2__c.Product_Category__c.getDescribe();
        result.productCategory = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            result.productCategory
        );

        fieldDescribe = Promotion2__c.Select_Customer_Method__c.getDescribe();
        result.selectCustomerMethod = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            result.selectCustomerMethod,
            'Standard Groups'
        );
        Profile objProfile = [
            SELECT Id, Name
            FROM Profile
            WHERE Id = :UserInfo.getProfileId()
        ];
        if (objProfile.Name.contains('Sales Manager')) {
            result.selectCustomerMethod.remove(0);
        }

        fieldDescribe = Promotion2__c.List_View_Name__c.getDescribe();
        result.listViewName = CCM_PromotionUtil.getPickListMap(
            fieldDescribe,
            result.listViewName
        );

        User currentUsr = Util.getUserInfo(UserInfo.getUserId());
        if (currentUsr.UserRole.DeveloperName.contains('CA')) {
            // for(Integer i = result.recordType.size()-1;i>=0;i--){
            //     CCM_PromotionUtil.PromotionPickWrapper p = result.recordType[i];
            //     if(p.ApiName == 'Sell_Through_Promotion'){
            //         result.recordType.remove(i);
            //     }
            // }

            for (Integer i = result.operatingUnit.size() - 1; i >= 0; i--) {
                CCM_PromotionUtil.PromotionPickWrapper p = result.operatingUnit[
                    i
                ];
                if (p.Id == 'US') {
                    result.operatingUnit.remove(i);
                }
            }
        } else {
            for (Integer i = result.operatingUnit.size() - 1; i >= 0; i--) {
                CCM_PromotionUtil.PromotionPickWrapper p = result.operatingUnit[
                    i
                ];
                if (p.Id == 'Canada') {
                    result.operatingUnit.remove(i);
                }
            }
        }
        return result;
    }

    /**
     * @description Get all necessary Drop Down List for Promotion Info page
     */
    @AuraEnabled
    public static PromotionPickListWrapper getPromotionBasicInfoById(String promotionId) {
        PromotionPickListWrapper result = new PromotionPickListWrapper();
        if (String.isBlank(promotionId)) {
            return result;
        }
        List<Promotion2__c> promotionList = [
            SELECT RecordType.DeveloperName, Owner.Profile.Name
            FROM Promotion2__c
            WHERE Id = :promotionId
        ];
        if (promotionList.size() == 0) {
            return result;
        }

        result = getPromotionBasicInfo(
            promotionList[0].RecordType.DeveloperName
        );

        result.selectCustomerMethod = new List<CCM_PromotionUtil.PromotionPickWrapper>();
        result.selectCustomerMethod = CCM_PromotionUtil.getPickListMap(
            Promotion2__c.Select_Customer_Method__c.getDescribe(),
            result.selectCustomerMethod,
            'Standard Groups'
        );
        if (
            promotionList[0].Owner.Profile.Name != null &&
            promotionList[0].Owner.Profile.Name.contains('Sales Manager')
        ) {
            result.selectCustomerMethod.remove(0);
        }

        return result;
    }

    @AuraEnabled
    public static List<Account> searchExcludeCustomers(String keyWord){
        List<Account> customers = null;
        keyWord = '%' + keyWord + '%';
        customers = [SELECT Id, Name FROM Account WHERE Name like :keyWord AND RecordType.Name = 'Channel'];
        return customers;
    }

    /**
     * @description Wrapper class for Customer or Customer Groups
     */
    public class CustomerOrGroupWrapper {
        @AuraEnabled
        public String Id;

        @AuraEnabled
        public String Name;

        @AuraEnabled
        public String Type;

        @AuraEnabled
        public String customerType;

        @AuraEnabled
        public String autualSegmentation;

        @AuraEnabled
        public String tmSegmentation;

        @AuraEnabled
        public Boolean hasCustomerProfile;

        @AuraEnabled
        public String AccountNumber;

        @AuraEnabled
        public String authorizedBrands;

        @AuraEnabled
        public String orgCode;
        // update by winfried on 2022.12.27
        // 控制GST/HST、QST两个字段可见性
        @AuraEnabled
        public Boolean isCCA;
        /**
         * update by austin 2022.12.18
         * 判断是否为dropship状态的一级经销商和group
         */
        @AuraEnabled
        public Boolean isDropship;
        // add on 23.11.7 记录一级客户id
        @AuraEnabled
        public List<Id> topCustomersId; 
        // add on 23.12.24 记录当前Customer是一级/有dropship业务的一级/二级
        @AuraEnabled
        public Boolean is1stTierDealer; 
        @AuraEnabled
        public Boolean is1stTierDealerWithDropship; 
        @AuraEnabled
        public Boolean is2ndTierDealer;

        public CustomerOrGroupWrapper() {
            this.topCustomersId = new List<Id>();
        }
    }

    /**
     * @description Wrapper class for price books group by brand
     */
    public class PriceListWrapper {
        @AuraEnabled
        public String brand;

        @AuraEnabled
        public List<Pricebook2> priceBooks;
    }

    /**
     * @description Wrapper Class for Promotion2__c
     *              Save status for front end
     *              promotionType for all promotion types
     */
    public class PromotionInfoWrapper {
        @AuraEnabled
        public Promotion2__c promotion;

        @AuraEnabled
        public boolean isSaved;

        @AuraEnabled
        public boolean isChannel;

        @AuraEnabled
        public List<String> campaignList { get; set; }

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> promotionType;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> campaignSource;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> listViewName;

        @AuraEnabled
        public String currentUserProfile { get; set; }

        @AuraEnabled
        public Boolean isOwner { get; set; }
    }

    /**
     * @description Wrapper Class for all record type and pick list in Promotion Info page
     */
    public class PromotionPickListWrapper {
        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> recordType;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> brand;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> priceList;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> coreReason;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> promotionType;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> currencyType;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> operatingUnit;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> promotionTypeExt;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> purposeTarget;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> productCategory;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> selectCustomerMethod;

        @AuraEnabled
        public List<CCM_PromotionUtil.PromotionPickWrapper> listViewName;
    }

    /**
     * @description: search result for standard group
     */
    public class SearchResult {
        @AuraEnabled
        public Integer customerNumber { get; set; }
        @AuraEnabled
        public List<String> customerNameList { get; set; }
        @AuraEnabled
        public Set<String> mixedAuthorizedBrands { get; set; }
        @AuraEnabled
        public String validResult { get; set; }

        public SearchResult() {
            this.customerNumber = 0;
            this.mixedAuthorizedBrands = new Set<String>();
            this.customerNameList = new List<String>();
        }
    }

    //Exception
    public class CustomException extends Exception {
    }
}