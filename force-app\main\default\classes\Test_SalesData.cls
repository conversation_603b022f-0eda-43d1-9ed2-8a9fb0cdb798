@isTest
public class Test_SalesData {

	public static User createUser(String lastName, String roleDevName, String profileName) {
		Id roleId = [SELECT Id, DeveloperName FROM UserRole where DeveloperName =:roleDevName].Id;
		Id profileId = [SELECT Id, Name FROM Profile where Name =:profileName].Id;
		User theUser = new User();
		theUser.UserRoleId = roleId;
		theUser.ProfileId = profileId;
		theUser.UserName = 'test' + Math.rint(Math.random() * 100000) + '@company.com';
		theUser.LastName = lastName;
		theUser.Email = '<EMAIL>';
		theUser.Alias = 'test';
		theUser.TimeZoneSidKey = 'America/Indiana/Indianapolis';
        theUser.EmailEncodingKey = 'ISO-8859-1';
        theUser.LanguageLocaleKey = theUser.LanguageLocaleKey == null
            ? 'en_US' : theUser.LanguageLocaleKey;
        theUser.localesidkey='en_US';
        theUser.IsActive = True;
        insert theUser;
        return theUser;
	}

	public static void createProspectAssignmentRules(Id salesManagerId, Id salesManagerId2, Id salesDirectorId) {
		List<Prospect_Assignment_Rule__c> rules = new List<Prospect_Assignment_Rule__c>();
		Prospect_Assignment_Rule__c rule1 = new Prospect_Assignment_Rule__c(
			ORG_Code__C = CCM_Constants.ORG_CODE_CNA,
			Brand__c = 'EGO',
			Nation__c = 'United States',
			State__c = 'NY',
			Distributor_or_Dealer__c = 'Direct Dealer',
			Cluster__c = 'CNA-CG11',
			Sub_Cluster__c = 'KA001',
			Sales_Channel__c = 'SC01',
			Sales_Group__c = 'SG21',
			User__c = salesManagerId,
			Director__c = salesDirectorId,
            Is_Flex_Auto_Only__c = false
		);
		rules.add(rule1);
		Prospect_Assignment_Rule__c rule2 = new Prospect_Assignment_Rule__c(
			ORG_Code__c = CCM_Constants.ORG_CODE_CNA,
			Brand__c = 'SKIL/SKILSAW/FLEX',
			Nation__c = 'United States',
			State__c = 'AK',
			Distributor_or_Dealer__c = 'Distributor;Direct Dealer',
			Cluster__c = 'CNA-CG02',
			Sub_Cluster__c = 'DT-WR',
			Sales_Channel__c = 'SC03',
			Sales_Group__c = 'SG08',
			User__c = salesManagerId2,
			Director__c = salesDirectorId,
            Is_Flex_Auto_Only__c = false
		);
		rules.add(rule2);
		insert rules;
	}

	public static void createProspectAssignmentRules_CA(Id salesManagerId, Id salesManagerId2, Id salesDirectorId) {
		List<Prospect_Assignment_Rule__c> rules = new List<Prospect_Assignment_Rule__c>();
		Prospect_Assignment_Rule__c rule1 = new Prospect_Assignment_Rule__c(
			ORG_Code__C = CCM_Constants.ORG_CODE_CCA,
			Brand__c = 'EGO',
			Nation__c = 'CA',
			State__c = 'OK',
			Distributor_or_Dealer__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_DISTRIBUTOR_OR_DEALERER_CA_DISTRIBUTOR,
			Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_CLUSTER_CANADA_HOME_CENTER,
			Sub_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_CA,
			Sales_Channel__c = 'SC01',
			Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
			User__c = salesManagerId,
			Director__c = salesDirectorId,
            Is_Flex_Auto_Only__c = false
		);
		rules.add(rule1);
		Prospect_Assignment_Rule__c rule2 = new Prospect_Assignment_Rule__c(
			ORG_Code__c = CCM_Constants.ORG_CODE_CCA,
			Brand__c = 'SKIL/SKILSAW/FLEX',
			Nation__c = 'CA',
			State__c = 'BC',
			Distributor_or_Dealer__c = 'Canada Distributor;Canada Home Center',
			Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_CLUSTER_CANADA_HOME_CENTER,
			Sub_Cluster__c = CCM_Constants.PROSPECT_ASSIGNMENT_RULE_SUB_CLUSTER_FOR_CA,
			Sales_Channel__c = 'SC01',
			Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22],
			User__c = salesManagerId2,
			Director__c = salesDirectorId,
            Is_Flex_Auto_Only__c = false
		);
		rules.add(rule2);
		insert rules;
	}

    public static Lead createProspectData() {
        Lead theLead = new Lead(
        	LastName = 'TestLead001',
        	Company = 'TestCompanyName',
        	User_Type__c = 'Enterprise',
        	Invoicing_Method__c = 'MAIL',
        	Email = '<EMAIL>',
        	Credit_Limit__c = '1',
        	Risk_Code__c = 'L',
            Sales_Group__c = 'SG01',
			Org_Code__c = 'CNA'
        );
        insert theLead;
        return theLead;
    }

	public static Lead createProspectData_CA() {
        Lead theLead = new Lead(
			ORG_Code__c = CCM_Constants.ORG_CODE_CCA,
        	LastName = 'TestLead002',
        	Company = 'TestCompanyName',
        	User_Type__c = 'Enterprise',
        	Invoicing_Method__c = 'MAIL',
        	Email = '<EMAIL>',
        	Credit_Limit__c = '1',
        	Risk_Code__c = 'L',
            Sales_Group__c = CCM_Constants.SALES_PROGRAM_SALES_GROUP[22]
        );
        insert theLead;
        return theLead;
    }

    public static Sales_Program__c createDefaultAuthorizedBrand(Id prospectId, Id customerId) {
    	Sales_Program__c sp = new Sales_Program__c(
    		Name = 'TestSP001',
    		Brands__c = 'EGO',
	    	Payment_Term__c = 'NA059',
			Payment_Term_Description__c = '2% discount is given if the invoice is paid within 10 days ,Or One whole invoice by 6 payments installment without discount.',
	    	Freight_Term__c = 'PPC',
	    	Price_List__c = 'Standard Price List',
	    	Starting_Date_of_Payment_Term__c = 'Invoice Date',
	    	Order_Lead_Time__c = 5,
	    	Prospect__c = prospectId,
	    	Customer__c = customerId,
			Sales_Group__c = 'SG01',
			Authorized_Brand_Name_To_Oracle__c = 'test1'
    	);
    	insert sp;
    	return sp;

    }

    /*public static Account_Address__c createAddress(String typeName) {
    	Account_Address__c aa = new Account_Address__c(
    		Address1__c = '111',

    	);
    }*/

    public static Attachment_Management__c createAttachmentManagement(Id prospectId, Id customerId, String type){
    	Attachment_Management__c am = new Attachment_Management__c(
    		Attachment_Type__c = type,
    		Prospect__c = prospectId,
	    	Customer__c = customerId
    	);
    	insert am;

    	Blob beforeblob=Blob.valueOf('Unit Test Attachment Body');
        ContentVersion cv = new ContentVersion();
        cv.title = 'test content trigger';
        cv.PathOnClient ='test';
        cv.VersionData =beforeblob;
        insert cv;

        ContentVersion testContent = [SELECT id, ContentDocumentId FROM ContentVersion where Id = :cv.Id];

    	ContentDocumentLink cdl = new ContentDocumentLink(
    		LinkedEntityId = am.Id,
    		ContentDocumentId = testContent.ContentDocumentId,
    		ShareType= 'V',
    		Visibility = 'AllUsers'
    	);
    	insert cdl;

    	return am;
    }


    public static ContentDocumentLink createContentDocument(Id parentId){
        Blob beforeblob=Blob.valueOf('Unit Test Attachment Body');
        ContentVersion cv = new ContentVersion();
        cv.title = 'test content trigger';
        cv.PathOnClient ='test';
        cv.VersionData =beforeblob;
        insert cv;

        ContentVersion testContent = [SELECT id, ContentDocumentId FROM ContentVersion where Id = :cv.Id];

        ContentDocumentLink cdl = new ContentDocumentLink(
            LinkedEntityId = parentId,
            ContentDocumentId = testContent.ContentDocumentId,
            ShareType= 'V',
            Visibility = 'AllUsers'
        );
        insert cdl;
        return cdl;
    }

    public static Contact createContact(Id prospectId, Id customerId) {
    	Contact con = new Contact(
    		LastName = 'Test001',
    		AccountId = customerId,
    		Prospect__c = prospectId
    	);
    	insert con;
    	return con;
    }

    public static Event createEvent(Id prospectId){
    	Event e = new Event(
    		Subject = 'test',
    		WhoId = prospectId,
    		DurationInMinutes = 60,
    		ActivityDateTime = Datetime.now()
    	);
    	insert e;
    	return e;
    }

    public static Task createTask(Id prospectId){
        Task t = new Task(
            Subject = 'test',
            WhoId = prospectId,
            Status = 'Completed'//,
            //DurationInMinutes = 60,
            //ActivityDateTime = Datetime.now()
        );
        insert t;
        return t;
    }

    public static Customer_Profile__c createCustomerProfile(Id prospectId, Id customerId){
    	Customer_Profile__c cp = new Customer_Profile__c(
    		Prospect__c = prospectId,
    		Customer__c = customerId,
    		Store_Size__c = '10*20',
    		OPE_Electric_Tools_Last_Year__c = 'PV1',
    		OPE_Electric_Tools_This_Year__c = 'PV1',
    		OPE_Electric_Tools_Next_Year__c = 'PV1',
            Overall_Estimated_Revenue__c = 1000,
    		EGO_Sales__c = 'CBV1',
    		Show_Room__c = 'SR1',
			Buying_groups_Marketing_Group_Co_op__c = 'Do It Best',
    		DIY_Share__c = 10,
    		EGO_Target_Sales__c = 10,
    		FLEX_Target_Sales__c = 10,
    		Number_of_Staff__c = 10,
    		OPE_Tool__c = 10,
    		Pro_Share__c = 10,
    		PT_Tool__c = 0.8,
    		Sales_Target__c = 10,
    		SKIL_Target_Sales__c = 10,
    		Number_of_Store_Locations__c = 10,
    		TOP1_Share__c = 'TOP1_Share__c',
    		Competition_Brand_Top_2_Share__c = 'Competition_Brand_Top_2_Share__c',
    		Competition_Brand_Top_3_Share__c = 'Competition_Brand_Top_3_Share__c',
    		Competition_Brand_Top_4_Share__c = 'Competition_Brand_Top_4_Share__c',
    		Competition_Brand_Top_5_Share__c = 'Competition_Brand_Top_5_Share__c',
    		Number_of_Warehouses__c = 10,
    		Are_they_Authorized_to_Sell_via_Amazon__c = 'Yes'
    	);
    	insert cp;
    	Business_Volume__c bv = new Business_Volume__c(
    		Customer_Profile__c = cp.Id,
    		Competitor_Brand__c = 'Stihl',
    		OPE_Electric_Tools__c = 'PV1'
    	);
    	insert bv;
    	return cp;
    }

    public static Account createChannelAccount() {
    	Schema.DescribeSObjectResult d = Schema.SObjectType.Account;
    	Map<String, Schema.RecordTypeInfo> rtNameToInfoMap = d.getRecordTypeInfosByName();
    	Id channelRT = rtNameToInfoMap.get('Channel').getRecordTypeId();
    	Account acc = new Account(
    		Name = 'TestAcc',
    		RecordTypeId = channelRT,
			AccountNumber = '11111',
			TaxID__c = 'test',
            PaymentMethod__c = 'CHECK',
			ORG_Code__c = 'CNA',
			Distributor_or_Dealer__c = 'Dealer'
    	);
    	insert acc;
    	return acc;
    }

	public static Account createChannelAccount(String orgCode, String customerType, String Name) {
    	Schema.DescribeSObjectResult d = Schema.SObjectType.Account;
    	Map<String, Schema.RecordTypeInfo> rtNameToInfoMap = d.getRecordTypeInfosByName();
    	Id channelRT = rtNameToInfoMap.get('Channel').getRecordTypeId();
    	Account acc = new Account(
    		Name = Name,
    		RecordTypeId = channelRT,
			AccountNumber = '11111',
			TaxID__c = 'test',
            PaymentMethod__c = 'CHECK',
			ORG_Code__c = orgCode,
			Distributor_or_Dealer__c = customerType
    	);
    	insert acc;
    	return acc;
    }

	public static Account_Address__c createBillingAddress(String customerId, String prospectId, String name, String orgCode) {
		Account_Address__c address = new Account_Address__c();
		address.RecordTypeId = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByName().get('Billing Address').getRecordTypeId();
		address.Customer__c = customerId;
		address.Prospect__c = prospectId;
		address.Name = name;
		address.ORG_ID__c = orgCode;
		address.Active__c = true;
		insert address;
		return address;
	}

	public static Account_Address__c createShippingAddress(String customerId, String prospectId, String name, String orgCode) {
		Account_Address__c address = new Account_Address__c();
		address.RecordTypeId = Schema.SObjectType.Account_Address__c.getRecordTypeInfosByName().get('Shipping Address').getRecordTypeId();
		address.Customer__c = customerId;
		address.Prospect__c = prospectId;
		address.Name = name;
		address.ORG_ID__c = orgCode;
		address.Active__c = true;
		insert address;
		return address;
	}

	public static Sales_Program__c createAuthorizedBrand(String customerId, String prospectId, String name, String brand, String recordTypeId, String orgCode) {
		Sales_Program__c sp = new Sales_Program__c(
    		Name = name,
    		Brands__c = brand,
	    	Payment_Term__c = 'NA059',
	    	Freight_Term__c = 'PPC',
			Payment_Term_Description__c = '2% discount is given if the invoice is paid within 10 days ,Or One whole invoice by 6 payments installment without discount.',
	    	Price_List__c = 'Standard Price List',
	    	Starting_Date_of_Payment_Term__c = 'Invoice Date',
	    	Order_Lead_Time__c = 5,
	    	Prospect__c = prospectId,
	    	Customer__c = customerId,
			Sales_Group__c = 'SG01',
			Authorized_Brand_Name_To_Oracle__c = 'test1',
			RecordTypeId = recordTypeId,
			ORG_Code__c = orgCode,
			Approval_Status__c = 'Approved'
    	);
    	insert sp;
    	return sp;
	}

	public static Address_With_Program__c createBAWB(String programId, String addressId) {
		Address_With_Program__c bawb = new Address_With_Program__c();
		bawb.Program__c = programId;
		bawb.Account_Address__c = addressId;
		insert bawb;
		return bawb;
	}

}