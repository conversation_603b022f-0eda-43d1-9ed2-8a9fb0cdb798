@isTest
private class Test_CCM_NewOrEditAuthorizedBrandsCtl {
    static testMethod void testMethod1() {
        Lead theLead = Test_SalesData.createProspectData();
        Lead theLeadCCA = new Lead(
            LastName = 'TestLead001',
        	Company = 'TestCompanyName',
        	User_Type__c = 'Enterprise',
        	Invoicing_Method__c = 'MAIL',
        	Email = '<EMAIL>',
        	Credit_Limit__c = '1',
        	Risk_Code__c = 'L',
            Org_Code__c = 'CCA'
        );
        insert theLeadCCA;

        CCM_NewOrEditAuthorizedBrandsCtl.getTargetObjName(theLead.Id);

        CCM_NewOrEditAuthorizedBrandsCtl.getRecordTypeId('Standard', 'EGO', theLead.Id);
        CCM_NewOrEditAuthorizedBrandsCtl.getRecordTypeId('Customized', 'EGO', theLead.Id);
        CCM_NewOrEditAuthorizedBrandsCtl.getRecordTypeId('Service Standard', 'EGO', theLeadCCA.Id);


        Account acc =  Test_SalesData.createChannelAccount();
        Account accCCA =  new Account(Name='accCCA1', Org_Code__c = 'CCA', AccountNumber = 'B123', TaxID__c = 'Test', PaymentMethod__c = 'CHECK');
        insert accCCA;

        List<Sales_Program__c> spList = new List<Sales_Program__c>();
        Sales_Program__c objAuthBrand1 = new Sales_Program__c(Customer__c = acc.Id, Name = 'test1', Approval_Status__c = CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED, Brands__C = 'SKIL', Authorized_Brand_Name_To_Oracle__c = 'SKIL', Payment_Term__c = 'NA095');
        spList.add(objAuthBrand1);
        Sales_Program__c objAuthBrand2 = new Sales_Program__c(Customer__c = acc.Id, Name = 'test2', Approval_Status__c = CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED, Brands__C = 'EGO', Authorized_Brand_Name_To_Oracle__c = 'EGO', Payment_Term__c = 'NA095');
        spList.add(objAuthBrand2);
        Sales_Program__c objAuthBrand3 = new Sales_Program__c(Customer__c = acc.Id, Name = 'test3', Approval_Status__c = CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED, Brands__C = 'FLEX', Authorized_Brand_Name_To_Oracle__c = 'FLEX', Payment_Term__c = 'NA095');
        spList.add(objAuthBrand3);
        Sales_Program__c objAuthBrand4 = new Sales_Program__c(Customer__c = acc.Id, Name = 'test4', Approval_Status__c = CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED, Brands__C = 'SKILSAW', Authorized_Brand_Name_To_Oracle__c = 'SKILSAW', Payment_Term__c = 'NA095');
        spList.add(objAuthBrand4);
        Sales_Program__c objAuthBrand5 = new Sales_Program__c(Org_Code__c = 'CCA',Customer__c = acc.Id, Name = 'test2', Approval_Status__c = CCM_Constants.SALES_PROGRAM_APPROVAL_STATUS_APPROVED, Brands__C = 'EGO', Authorized_Brand_Name_To_Oracle__c = 'EGO', Payment_Term__c = 'NA095');
        spList.add(objAuthBrand5);
        insert spList;

        CCA_Authorize_Brand_Configuration__mdt mdt = [select id from CCA_Authorize_Brand_Configuration__mdt limit 1];

        CCM_NewOrEditAuthorizedBrandsCtl.getTargetObjName(acc.Id);
        CCM_NewOrEditAuthorizedBrandsCtl.getRecordTypeId('Standard', 'EGO', acc.Id);
        CCM_NewOrEditAuthorizedBrandsCtl.getRecordTypeId('Service', 'EGO', acc.Id);
        CCM_NewOrEditAuthorizedBrandsCtl.getRecordTypeId('Service Standard', 'EGO', accCCA.Id);
        CCM_NewOrEditAuthorizedBrandsCtl.getPaymentTermDescription();
        CCM_NewOrEditAuthorizedBrandsCtl.doCustomerDropshipValidation(acc.Id);

        CCM_NewOrEditAuthorizedBrandsCtl.ConfigurationInfoWrapper objWrapper = new CCM_NewOrEditAuthorizedBrandsCtl.ConfigurationInfoWrapper();
        objWrapper.brandName = 'EGO';
        objWrapper.deliverFrom = 'PPG';
        objWrapper.freightTerm = 'PPG';
        objWrapper.orderLeadTime = 100;
        objWrapper.ORGCode = 'CNA';
        objWrapper.paymentDiscount = 1;
        objWrapper.paymentLeadTime = 1;
        objWrapper.paymentTerm = 'test';
        objWrapper.startingDateofPaymentTerm = '';
        objWrapper.orderType = '';
        objWrapper.pricebookId = '';

    }
}