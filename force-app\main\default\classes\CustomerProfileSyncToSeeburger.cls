/**
 * <AUTHOR> @date 2023-01-05
 * @description: 本年度Summary of Potential & Actual Sales变更时，同步给erp
 */
public with sharing class CustomerProfileSyncToSeeburger implements Triggers.Handler {
    public static Boolean isRun = true;

    public void handle() {
        if (!isRun) {
            return;
        }

        String currentYear = Date.today().year() + '';
        Map<Id, Customer_Profile__c> mapOld = (Map<Id, Customer_Profile__c>) Trigger.oldMap;
        Map<String, String> accSummaryPotentialMap = new Map<String, String>();
        Map<String, String> accPaymentCodeMap = new Map<String, String>();
        Set<String> customerTypeSet = new Set<String>{'2nd Tier Distributor','Dealer Location','2nd Tier Dealer'};
        for (Customer_Profile__c objCP : (List<Customer_Profile__c>) Trigger.new) {
            if(objCP.Effective_Year__c == currentYear
                && (
                    Trigger.isInsert
                    || (
                        Trigger.isUpdate
                        && (mapOld.get(objCP.Id).Summary_of_Potential_Actual_Sales__c <> objCP.Summary_of_Potential_Actual_Sales__c
                            || mapOld.get(objCP.Id).Payment_Behavior_Code__c <> objCP.Payment_Behavior_Code__c)
                    )
                )
                && String.isNotBlank(objCP.Customer__c)
            ){
                accSummaryPotentialMap.put(objCP.Customer__c, objCP.Summary_of_Potential_Actual_Sales__c);
                accPaymentCodeMap.put(objCP.Customer__c, objCP.Payment_Behavior_Code__c);
            }
        }

        for(Account acc : [
            SELECT Customer_SF_ID__c,Customer_Oracle_ID__c,AccountNumber,Customer_Cluster__c,Customer_Sub_Cluster__c,
            Ship_Complete__c,Shipment_Priority__c,Sales_Channel__c,Name,Cerdit__c,Credit_Limit__c,Risk_Code__c,
            CurrencyIsoCode,Invoicing_Method__c,Status__c,TaxID__c,PaymentMethod__c,Distributor_or_Dealer__c
            FROM Account WHERE Id IN :accSummaryPotentialMap.keySet()

        ]){
            if(String.isNotBlank(acc.Customer_Oracle_ID__c) && !customerTypeSet.contains(acc.Distributor_or_Dealer__c)){
                String param = getAccountInfo(acc, accSummaryPotentialMap, accPaymentCodeMap);
                CCM_SyncToSeeburgerService.updateOracleIDQueueable updateJob = new CCM_SyncToSeeburgerService.updateOracleIDQueueable(param,'customer');
                System.enqueueJob(updateJob);
            }
        }
    }

    private static String getAccountInfo(Account acct, Map<String, String> accSummaryPotentialMap, Map<String, String> accPaymentCodeMap){
        String acctInfoStr = '';
        acctInfoStr += '{';
        acctInfoStr += '"CUSTOMER_HEADER_ID":' + '"Salesforce-'+acct.Id +'-' + System.now().getTime() +'",';
        acctInfoStr += acct.Customer_SF_ID__c != null ? '"Customer_SF_ID__c":"'+ acct.Customer_SF_ID__c +'",' : '"Customer_SF_ID__c":"",';
        acctInfoStr += acct.Customer_Oracle_ID__c != null ? '"Customer_Oracle_ID__c":'+ '"'+acct.Customer_Oracle_ID__c +'",' : '"Customer_Oracle_ID__c":"",';
        acctInfoStr += acct.AccountNumber != null ? '"AccountNumber":"'+ acct.AccountNumber +'",' : '"AccountNumber":"",';
        acctInfoStr += acct.Customer_Cluster__c !=null ? '"Customer_Cluster__c":"'+ acct.Customer_Cluster__c +'",' : '"Customer_Cluster__c":"",';
        acctInfoStr += acct.Customer_Sub_Cluster__c !=null ? '"Customer_Sub_Cluster__c":"'+ acct.Customer_Sub_Cluster__c +'",' : '"Customer_Sub_Cluster__c":"",';
        acctInfoStr += acct.Ship_Complete__c !=null ? '"Ship_Complete__c":"'+ acct.Ship_Complete__c +'",' : '"Ship_Complete__c":"",';
        acctInfoStr += acct.Shipment_Priority__c !=null ? '"Shipment_Priority__c":"'+ acct.Shipment_Priority__c +'",' : '"Shipment_Priority__c":"",';
        acctInfoStr += acct.Sales_Channel__c !=null ? '"Sales_Channel__c":"'+ acct.Sales_Channel__c +'",' : '"Sales_Channel__c":"",';
        acctInfoStr += acct.Name !=null ? '"Name":"'+ acct.Name +'",' : '"Name":"",';
        acctInfoStr += acct.Credit_Limit__c !=null && acct.Credit_Limit__c != 'N/A'? '"Cerdit__c":"'+ acct.Credit_Limit__c +'",' : '"Cerdit__c":"",';
        if(accPaymentCodeMap.size()>0 && accPaymentCodeMap.containsKey(acct.Id)){
            acctInfoStr += acct.Risk_Code__c !=null ? '"Risk_Code__c":"'+ accPaymentCodeMap.get(acct.Id)+'-'+acct.Risk_Code__c +'",' : '"Risk_Code__c":"",';
        }else{
            acctInfoStr += acct.Risk_Code__c !=null ? '"Risk_Code__c":"'+ acct.Risk_Code__c +'",' : '"Risk_Code__c":"",';
        }

        acctInfoStr += '"Account_Type__c":' + '"External",';
        acctInfoStr += acct.CurrencyIsoCode !=null ? '"Currency":"'+ acct.CurrencyIsoCode +'",' : '"Currency":"",';
        acctInfoStr += acct.Invoicing_Method__c !=null ? '"Invoicing_Method__c":"'+ acct.Invoicing_Method__c +'",' : '"Invoicing_Method__c":"",';
        acctInfoStr += acct.Status__c !=null ? '"Status__c":"'+ acct.Status__c+'",' : '"Status__c":"",';
        acctInfoStr += '"Attribute1":"' + acct.TaxID__c + '",';
        acctInfoStr += '"Attribute2":"' + acct.PaymentMethod__c + '",';
        acctInfoStr += accSummaryPotentialMap.containsKey(acct.Id) ? '"Attribute3":"' + accSummaryPotentialMap.get(acct.Id) + '",' : '"Attribute3":"",';
        acctInfoStr += '"Attribute4":"",';
        acctInfoStr += '"Attribute5":"",';
        acctInfoStr += '"Attribute6":"",';
        acctInfoStr += '"Attribute7":"",';
        acctInfoStr += '"Attribute8":"",';
        acctInfoStr += '"Attribute9":"",';
        acctInfoStr += '"Attribute10":""';
        acctInfoStr += '}';

        return acctInfoStr;
    }
}