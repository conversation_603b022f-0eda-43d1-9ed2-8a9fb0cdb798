global with sharing class BatchAndScheduleCreditCardSave implements Schedulable,Database.Batchable<sObject>{
    public BatchAndScheduleCreditCardSave() {

    }
    //运行计划任务
    global void execute(SchedulableContext sc) {
        BatchAndScheduleCreditCardSave updateCdbatech = new BatchAndScheduleCreditCardSave();
        Database.executeBatch(updateCdbatech, 1);
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {
        return Database.getQueryLocator([SELECT Id, Name,AccountNumber, CreatedDate, BillingAddress,Org_Code__c,TaxID__c,toLabel(Customer_Cluster__c),toLabel(Customer_Sub_Cluster__c),Credit_Limit__c,Risk_Code__c FROM Account WHERE Status__c ='A' AND AccountNumber != null]);

    }

    global void execute(Database.BatchableContext BC, list<Account> accountList) {
        List<Credit_Card__mdt> creditCardMdtList = [SELECT End_Range__c,Final_Score__c,Name__c,Range__c,Score__c,Start_Range__c,Type__c,Weight__c FROM Credit_Card__mdt ORDER BY Type__c];
        List<Customer_Credit_Card__c> creditCardList = new List<Customer_Credit_Card__c>();
        for(Account ac : accountList){
            Customer_Credit_Card__c creditCard = new Customer_Credit_Card__c(Credit_Limit_Type__c = '',
            Current_Payment_Term__c = '',
            Current_Credit_Limit__c = 0,
            Tax_ID__c = '',
            Additional_Payment_Terms__c = '',
            Order_Type__c = '',
            Calculation_Terms__c = 0,
            Address__c = '',
            Actual_Annual_Sales_Volume__c = 0,
            Legal_Entity__c = '',
            Forecasted_Annual_Sales_Volume__c = 0,
            Cluster__c = '',
            Purchasing_Amount_Based_On_Term__c = 0,
            Sub_Cluster__c = '',
            Chervon_Days_Beyond_Terms__c = 0,
            Sales_volume__c = '',
            Tools_Purchase_From_Chervon__c = '',
            Days_Beyond_Terms__c = '',
            Years_In_Business__c = '',
            Business_With_Chervon__c = '',
            On_Time_Payment_10_days__c = '',
            Are_There_Any_Current_Legal_Case__c = '',
            Payment_Behavior_Code__c = '',
            Credit_Report_Risk_Code__c = '',
            Score1__c = 0,
            Score2__c = 0,
            Score3__c = 0,
            Score4__c = 0,
            Score5__c = 0,
            Score6__c = 0,
            Score7__c = 0,
            Score8__c = 0,
            Score9__c = 0,
            FinalScore1__c = 0,
            FinalScore2__c = 0,
            FinalScore3__c = 0,
            FinalScore4__c = 0,
            FinalScore5__c = 0,
            FinalScore6__c = 0,
            FinalScore7__c = 0,
            FinalScore8__c = 0,
            FinalScore9__c = 0,
            Total_Score__c = 0,
            Maximum_Recommended_Limit__c = 0);
            Decimal yearInBusiness = 0;
            Decimal oneTime;
            String areThereCase = '';
            String paymentBehaviorCode = '';
            List<Order_Item__c> orderItemList = [Select Order__c,Order__r.Date_Order__c,Order__r.EffectiveDate,Price__c ,Order__r.AccountId,Order_Quantity__c FROM Order_Item__c WHERE Order__r.AccountId =:ac.Id AND Line_Status__c !='CANCELLED' AND Order_Quantity__c >0 ORDER BY Order__r.Date_Order__c LIMIT 1];

            List<Customer_Profile__c> cp = [SELECT Id, Name,Sales_potential__c,Chervon_Days_Beyond_Terms__c,OPE_Tool__c,PT_Tool__c,Years_in_business__c,Are_there_any_current_legal_case__c,Payment_Behavior_Code__c,Actual_Inputs__c,OPE_PT_Category_Revenue__c FROM Customer_Profile__c WHERE Customer__c = :ac.Id ORDER BY Effective_Year__c DESC LIMIT 1];

            List<Sales_Program__c> sp = [SELECT Id,Payment_Term__c,Brands__c FROM Sales_Program__c WHERE Customer__c = :ac.Id AND Approval_Status__c = 'Approved'];

            List<Customer_Credit_Report_Dashboard__c> ccrd = [SELECT Id,Name,DSO__c,Current_10days__c,Chervon_Days_Beyond_Terms__c FROM Customer_Credit_Report_Dashboard__c WHERE Account_Number__c = :ac.AccountNumber];
            Integer diffYear = 0;
            if(System.today().year()- ac.CreatedDate.year() != 1){
                if(System.today().year()- ac.CreatedDate.year() != 0){
                    diffYear = System.today().year()- ac.CreatedDate.year();
                }
            }else{
                if(System.today().month() == ac.CreatedDate.month()){
                    if(System.today().day() == ac.CreatedDate.day()){
                        diffYear = 1;
                    }else if(System.today().day() > ac.CreatedDate.day()){
                        diffYear = 1;
                    }
                }else if(System.today().month() > ac.CreatedDate.month()){
                    diffYear = 1;
                }
            }

            Integer diffMonth = 0;
            if(System.today().month() == ac.CreatedDate.month()){
                diffMonth = 1;
            }else if(System.today().month() > ac.CreatedDate.month()){
                diffMonth = System.today().month() - ac.CreatedDate.month() + 1;
            }else{
                diffMonth = System.today().month() - ac.CreatedDate.month() + 12;
            }
            Decimal opePt = 0;
            Decimal totalAmount = 0;
            Integer orderDiffYear = 0;
            Address addre = new Address();
            addre= ac.BillingAddress;
            creditCard.Credit_Limit_Type__c = 'Credit Review';
            creditCard.Customer_Name__c = ac.Id;
            creditCard.Tax_ID__c = ac.TaxID__c;
            Date currentDate = System.today();
            Date lastDate = Date.newInstance(System.today().year() - 1, System.today().month(), System.today().day());
            String invoiceSource = 'CNA_Auto_Invoice';
            creditCard.CurrencyIsoCode = 'USD';
            if (ac.Org_Code__c == CCM_Constants.ORG_CODE_CCA) {
                invoiceSource = 'CA_Auto_Invoice';
                creditCard.CurrencyIsoCode = 'CAD';
            }
            for (List<AggregateResult> objARs : [
                SELECT Invoice__r.Customer__c, SUM(Amount__c) SUM_AMOUNT
                FROM Invoice_Item__c
                WHERE
                    Invoice__r.Customer__c = :ac.Id
                    AND Invoice__r.Invoice_Source__c = :invoiceSource
                    AND Is_Parts__c = FALSE
                    AND Is_parts_EBS__c = FALSE
                    AND Catalog_Item_Text__c != '9999999999'
                    AND Catalog_Item_Text__c != 'V20'
                    AND Invoice_Date__c >= :lastDate
                    AND Invoice_Date__c <= :currentDate
                WITH SECURITY_ENFORCED
                GROUP BY Invoice__r.Customer__c
            ]) {
                for(AggregateResult objAR : objARs){
                    totalAmount = (Decimal)objAR.get('SUM_AMOUNT');
                }
            }
            if(addre != null){
                creditCard.Address__c = addre.street + ' ' + addre.city + ','  + addre.state + ' ' + addre.postalCode + ' ' + addre.country;
            }
            creditCard.Legal_Entity__c = ac.Org_Code__c;
            creditCard.Cluster__c = ac.Customer_Cluster__c;
            creditCard.Sub_Cluster__c = ac.Customer_Sub_Cluster__c;
            if(ac.Credit_Limit__c!=null && ac.Credit_Limit__c!= 'N/A'){
                creditCard.Current_Credit_Limit__c = Decimal.valueOf(ac.Credit_Limit__c).setScale(2);
            }
            creditCard.Forecasted_Annual_Sales_Volume__c = null;
            Integer currentYear = System.today().year();
            creditCard.Effective_Year__c = String.valueOf(currentYear);
            if(ccrd.size() > 0){
                creditCard.Calculation_Terms__c = ccrd[0].DSO__c;
                oneTime = ccrd[0].Current_10days__c;
                if(ccrd[0].Chervon_Days_Beyond_Terms__c != null && ccrd[0].DSO__c != null){
                    creditCard.Chervon_Days_Beyond_Terms__c = ccrd[0].Chervon_Days_Beyond_Terms__c - ccrd[0].DSO__c;
                }
            }
            if(orderItemList.size() > 0){
                if(orderItemList[0].Order__r != null){
                    orderDiffYear = System.today().year()- orderItemList[0].Order__r.EffectiveDate.year();
                }
            }
            if(cp.size() > 0){
                if(diffYear > 0){
                    creditCard.Actual_Annual_Sales_Volume__c = totalAmount;
                }else{
                    creditCard.Actual_Annual_Sales_Volume__c = (totalAmount / diffMonth *12).setScale(2);
                }
                if(cp[0].OPE_PT_Category_Revenue__c > 0){
                    opePt = (totalAmount / cp[0].OPE_PT_Category_Revenue__c).setScale(2);
                }
                yearInBusiness = cp[0].Years_in_business__c;
                areThereCase = cp[0].Are_there_any_current_legal_case__c;
                paymentBehaviorCode = cp[0].Payment_Behavior_Code__c;
            }
            for(Sales_Program__c spr : sp){
                creditCard.Current_Payment_Term__c += spr.Brands__c + '-' + spr.Payment_Term__c + '/';
            }
            if(String.isNotBlank(creditCard.Current_Payment_Term__c) && creditCard.Current_Payment_Term__c.endsWith('/')){
                creditCard.Current_Payment_Term__c = creditCard.Current_Payment_Term__c.removeEnd('/');
            }
            if(creditCard.Calculation_Terms__c > 0){
                creditCard.Purchasing_Amount_Based_On_Term__c = (creditCard.Actual_Annual_Sales_Volume__c / 360 *  creditCard.Calculation_Terms__c).setScale(2);
            }
            for(Credit_Card__mdt ccm : creditCardMdtList){
                if(ccm.Type__c == 'Sales Volume'){
                    if((creditCard.Actual_Annual_Sales_Volume__c > ccm.Start_Range__c && creditCard.Actual_Annual_Sales_Volume__c <= ccm.End_Range__c) || (ccm.Start_Range__c == null && (creditCard.Actual_Annual_Sales_Volume__c <= ccm.End_Range__c || creditCard.Actual_Annual_Sales_Volume__c == null) ) || (creditCard.Actual_Annual_Sales_Volume__c > ccm.Start_Range__c && ccm.End_Range__c == null)){
                        creditCard.Total_Score__c += ccm.Final_Score__c;
                        creditCard.FinalScore1__c = ccm.Final_Score__c;
                        creditCard.Sales_volume__c = ccm.Name__c;
                        creditCard.Score1__c = ccm.Score__c;
                    }
                }else if(ccm.Type__c == 'Tools Purchase from Chervon'){
                    if((opePt > ccm.Start_Range__c && opePt <= ccm.End_Range__c) || (ccm.Start_Range__c == null && (opePt <= ccm.End_Range__c || opePt == null) ) || (opePt > ccm.Start_Range__c && ccm.End_Range__c == null)){
                        creditCard.Total_Score__c += ccm.Final_Score__c;
                        creditCard.FinalScore2__c = ccm.Final_Score__c;
                        creditCard.Tools_Purchase_From_Chervon__c = ccm.Name__c;
                        creditCard.Score2__c = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Days Beyond Terms'){
                    if((creditCard.Chervon_Days_Beyond_Terms__c >= ccm.Start_Range__c && creditCard.Chervon_Days_Beyond_Terms__c <= ccm.End_Range__c) || (ccm.Start_Range__c == null && (creditCard.Chervon_Days_Beyond_Terms__c <= ccm.End_Range__c || creditCard.Chervon_Days_Beyond_Terms__c == null) ) || (creditCard.Chervon_Days_Beyond_Terms__c >= ccm.Start_Range__c && ccm.End_Range__c == null)){
                        creditCard.Total_Score__c += ccm.Final_Score__c;
                        creditCard.FinalScore3__c = ccm.Final_Score__c;
                        creditCard.Days_Beyond_Terms__c = ccm.Name__c;
                        creditCard.Score3__c = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Years in Business'){
                    if((yearInBusiness >= ccm.Start_Range__c && yearInBusiness < ccm.End_Range__c) || (ccm.Start_Range__c == null && (yearInBusiness < ccm.End_Range__c || yearInBusiness == null) ) || (yearInBusiness >= ccm.Start_Range__c && ccm.End_Range__c == null)){
                        creditCard.Total_Score__c += ccm.Final_Score__c;
                        creditCard.FinalScore4__c = ccm.Final_Score__c;
                        creditCard.Years_In_Business__c = ccm.Name__c;
                        creditCard.Score4__c = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Business with Chervon'){
                    if((orderDiffYear >= ccm.Start_Range__c && orderDiffYear < ccm.End_Range__c) || (ccm.Start_Range__c == null && (orderDiffYear < ccm.End_Range__c || orderDiffYear == null) ) || (orderDiffYear >= ccm.Start_Range__c && ccm.End_Range__c == null)){
                        creditCard.Total_Score__c += ccm.Final_Score__c;
                        creditCard.FinalScore5__c = ccm.Final_Score__c;
                        creditCard.Business_With_Chervon__c = ccm.Name__c;
                        creditCard.Score5__c = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'On time Payment'){
                    if((oneTime >= ccm.Start_Range__c && oneTime < ccm.End_Range__c) || (ccm.Start_Range__c == null && (oneTime < ccm.End_Range__c ) ) || (oneTime >= ccm.Start_Range__c && ccm.End_Range__c == null) || (oneTime == null && ccm.Start_Range__c == null && ccm.End_Range__c == null)){
                        creditCard.Total_Score__c += ccm.Final_Score__c;
                        creditCard.FinalScore6__c = ccm.Final_Score__c;
                        creditCard.On_Time_Payment_10_days__c = ccm.Name__c;
                        creditCard.Score6__c = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Are there any current legal case'){
                    if(areThereCase == ccm.Range__c){
                        creditCard.Total_Score__c += ccm.Final_Score__c;
                        creditCard.FinalScore7__c = ccm.Final_Score__c;
                        creditCard.Are_There_Any_Current_Legal_Case__c = ccm.Name__c;
                        creditCard.Score7__c = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Payment Behavior Code'){
                    if(paymentBehaviorCode == ccm.Range__c){
                        creditCard.Total_Score__c += ccm.Final_Score__c;
                        creditCard.FinalScore8__c = ccm.Final_Score__c;
                        creditCard.Payment_Behavior_Code__c = ccm.Name__c;
                        creditCard.Score8__c = ccm.Score__c;
                    }

                }else if(ccm.Type__c == 'Credit Report Risk Code'){
                    if(ac.Risk_Code__c == ccm.Range__c){
                        creditCard.Total_Score__c += ccm.Final_Score__c;
                        creditCard.FinalScore9__c = ccm.Final_Score__c;
                        creditCard.Credit_Report_Risk_Code__c = ccm.Name__c;
                        creditCard.Score9__c = ccm.Score__c;
                    }
                }else{
                    if(creditCard.Total_Score__c >= ccm.Start_Range__c && creditCard.Total_Score__c < ccm.End_Range__c){
                        creditCard.Maximum_Recommended_Limit__c = creditCard.Purchasing_Amount_Based_On_Term__c * ccm.Weight__c / 100;
                    }

                }
            }
            creditCardList.add(creditCard);
        }
        if(creditCardList.size() > 0){
            insert creditCardList;
        }
    }

    global void finish(Database.BatchableContext BC) {

    }
}