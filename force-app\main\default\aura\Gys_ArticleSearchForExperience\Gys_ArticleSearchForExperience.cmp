<aura:component controller = "Gys_ArticleSearchForExperienceApex" implements="forceCommunity:availableForAllPageTypes,forceCommunity:searchInterface" access="global" >
    <aura:attribute name = "resultCount" type = "Integer" default = "10" />
    <aura:attribute name = "result" type = "Object" default = "{'articles':[], 'discussions':[]}" />
    <aura:attribute name = "showDropDown" type = "Boolean" default = "false" />
    
    
    <!-- <div class="slds-is-relative" style = "background-color: #a7a8a5; height: 4rem; min-height: 45px;">
        <div class="searchBar" onfocusout = "{!c.focusOut}">
            <lightning:input aura:id = "search-key" type="search" name="search" oncommit="{!c.redirectToViewAll}" label="" placeholder="Search Articles And Discussions" variant = "label-hidden" onfocus = "{!c.searchResult}"  onchange = "{!c.searchResult}" />
        </div>
        <aura:if isTrue = "{!v.showDropDown}">
            <div>
                <ul style = "width: 40%; " class="slds-listbox slds-listbox_vertical slds-m-top_medium slds-dropdown slds-dropdown_fluid slds-p-left_small" role="presentation">
                    <div class = "slds-text-heading_medium slds-p-around_small">ARTICLES</div>
                    <aura:if isTrue = "{!v.result.articles.length == 0}">
                        <span style = "color: red;" class = "slds-p-around_xx-small">There is no article matching your search.</span>
                    </aura:if>
                    <aura:iteration items="{!v.result.articles}" var="item">
                        <div class="slds-p-around_xx-small list-item" Id = "{!item.value}" onmousedown = "{!c.redirectToArticle}">{!item.label}</div>
                    </aura:iteration>
                    
                    <div class = "slds-text-heading_medium slds-p-around_small">DISCUSSION</div>
                    <aura:if isTrue = "{!v.result.discussions.length == 0}">
                        <span style = "color: red;" class = "slds-p-around_xx-small">There is no discussion matching your search.</span>
                    </aura:if>
                    <aura:iteration items="{!v.result.discussions}" var="item">
                        <div class="slds-p-around_xx-small list-item" Id = "{!item.value}" onmousedown = "{!c.redirectToDiscussion}">{!item.label}</div>
                    </aura:iteration>
                    
                    <aura:if isTrue = "{!v.result.discussions.length != 0 || v.result.articles.length !=0}">
                        <div class = "slds-p-around_small list-item" style = "font-weight: 700;" onmousedown = "{!c.redirectToViewAll}">View all results &#8594;</div>
                    </aura:if>
                </ul>
            </div>
        </aura:if>
    </div> -->
</aura:component>