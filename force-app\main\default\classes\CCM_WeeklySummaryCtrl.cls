/**
 * <AUTHOR>
 * @date 2025-08-08
 * @description Weekly Summary Utility
 */
public with sharing class CCM_WeeklySummaryCtrl {

    @AuraEnabled
    public static String getAllPicklist(){
        Map<String, Map<String, Object>> resultMap = new Map<String, Map<String, Object>>();
        Map<String, Object> weekInfo = getCurrentWeekAndTotalWeek();
        resultMap.put('weekinfo', weekInfo);

        Map<String, Object> monthInfo = getCurrentMonth();
        resultMap.put('monthInfo', monthInfo);

        Map<String, Object> yearInfo = getCurrentYear();
        resultMap.put('yearInfo', yearInfo);

        Map<String, Object> regionInfo = getRegionAndUsers();
        resultMap.put('regionInfo', regionInfo);
        return JSON.serialize(resultMap);
    }
    
    private static Map<String, Object> getCurrentWeekAndTotalWeek(){
        Integer totalWeeks = getTotalWeeksInCurrentYear();
        String weekStr = Datetime.now().format('w');
        Integer currentWeek = Integer.valueOf(weekStr);
        return new Map<String, Object>{'currentWeek' => currentWeek, 'totalWeeks' => totalWeeks};
    }

    private static Integer getTotalWeeksInCurrentYear() {
        // 获取当前年份
        Integer currentYear = System.Date.today().year();
        
        // 获取当年第一天
        Date firstDayOfYear = Date.newInstance(currentYear, 1, 1);
        
        // 获取下一年第一天
        Date firstDayOfNextYear = Date.newInstance(currentYear + 1, 1, 1);
        
        // 计算两个日期之间的天数差
        Integer daysInYear = firstDayOfYear.daysBetween(firstDayOfNextYear);
        System.debug(daysInYear);
        // 计算总周数（向上取整）
        Integer totalWeeks = (daysInYear + 6) / 7;
        
        return totalWeeks;
    }

    private static Map<String, Object> getCurrentMonth() {
        Integer currentMonth = System.Date.today().month();
        return new Map<String, Object>{'currentMonth' => currentMonth};
    }

    private static Map<String, Object> getCurrentYear() {
        Integer currentYear = System.Date.today().year();
        return new Map<String, Object>{'currentYear' => currentYear};
    }

    private static Map<String, Object> getRegionAndUsers() {
        String userEmail = UserInfo.getUserEmail();
        Map<String, Set<String>> userRegionMap = new Map<String, Set<String>>{
            'East' => new Set<String>{'Ron Martins', 'John Licata', 'Brian Sarno', 'Tom Stevens', 'Mike Lisk', 'Chris Metcalf'},
            'Central' => new Set<String>{'Guy Rutter', 'Bob Landreth', 'Stephen Moity', '<EMAIL>'},
            'West' => new Set<String>{'David Shultz', 'Juan Hernandez', 'Ted Hendricks', 'Ray Gonzalez', 'JR Huerta', 'Kyle Decker'}
        };
        String currentUserDefaultRegion = null;
        String currentUserEmail = UserInfo.getUserEmail();
        for(String region : userRegionMap.keySet()) {
            Set<String> users = userRegionMap.get(region);
            if(users.contains(currentUserEmail)) {
                currentUserDefaultRegion = region;
                break;
            }
        }
        Map<String, Object> regionMap = new Map<String, Object>{
            'Region' => 'East,Central,West',
            'DefaultRegion' => currentUserDefaultRegion
        };
        return regionMap;
    }

    @AuraEnabled
    public static String getRecordData(String recordId){
        Map<String, Object> resultMap = new Map<String, Object>();
        List<Log__c> logs = [SELECT Next_Week_Focus__c, Weekly_Highlights__c, Competitive_Info__c, Other__c, Week__c, Year__c, Month__c, Region__c, Submit_Date__c, Owner.Name FROM Log__c WHERE Id = :recordId];
        if(!logs.isEmpty()) {
            resultMap.put('data', logs[0]);
        }
        String currentUserId = UserInfo.getUserId();
        User userInfo = [SELECT UserRole.Name FROM User WHERE Id = :currentUserId LIMIT 1];
        if(userInfo.UserRole.Name == 'OPE Sales Director') {
            resultMap.put('isSalesDirector', true);
        }
        else {
            resultMap.put('isSalesDirector', false);
        }
        return JSON.serialize(resultMap);
    }

    @AuraEnabled
    public static String saveSummary(String summaryParam, Boolean isSubmit){
        Map<String, Object> summaryObj = (Map<String, Object>)JSON.deserializeUntyped(summaryParam);
        Log__c log = new Log__c();
        log.Is_Weekly_Summary__c = true;
        log.Next_Week_Focus__c = (String)summaryObj.get('nextWeekFocus');
        log.Weekly_Highlights__c = (String)summaryObj.get('weeklyHighlights');
        log.Competitive_Info__c = (String)summaryObj.get('competitiveInfo');
        log.Other__c = (String)summaryObj.get('other');
        log.Week__c = (String)summaryObj.get('week');
        log.Year__c = (String)summaryObj.get('year');
        log.Month__c = (String)summaryObj.get('month');
        log.Region__c = (String)summaryObj.get('region');
        log.Comments__c = (String)summaryObj.get('comments');
        if(summaryObj.get('recordId') != null) {
            log.Id = (String)summaryObj.get('recordId');
        }
        if(isSubmit) {
            log.Submit_Date__c = Date.today();
        }
        log.OwnerId = UserInfo.getUserId();
        upsert log;
        return log.Id;
    }

    @AuraEnabled
    public static void updateDraft(String recordId){
        Log__c log = new Log__c();
        log.Id = recordId;
        log.Submit_Date__c = null;
        update log;
    }

    @AuraEnabled
    public static String getSummaryList(String limitStr){
        Boolean needAllData = false;
        String currentUser = UserInfo.getUserId();
        User userInfo = [SELECT Profile.Name, UserRole.Name FROM User WHERE Id = :currentUser LIMIT 1];
        if(userInfo.Profile.Name == 'System Administrator' || userInfo.UserRole.Name == 'OPE Sales Director') {
            needAllData = true;
        }
        String currentYear = String.valueOf(Date.today().year());
        String currentWeek = Datetime.now().format('w');
        List<Log__c> logs = new List<Log__c>();
        Integer limitNum;
        if(needAllData) {
            String query = 'SELECT Id, Weekly_Highlights__c, Competitive_Info__c, Other__c, Next_Week_Focus__c, Week__c, Month__c, Year__c, Owner.Name, Region__c, Submit_Date__c FROM Log__c WHERE Is_Weekly_Summary__c = true and Submit_Date__c != null Order By Year__c DESC, Month__c DESC, Week__c DESC';
            if(String.isNotBlank(limitStr)) {
                limitNum = Integer.valueOf(limitStr);
                query += ' LIMIT :limitNum';
            }
            logs = Database.query(query);
        }
        else {
            String query = 'SELECT Id, Weekly_Highlights__c, Competitive_Info__c, Other__c, Next_Week_Focus__c, Week__c, Month__c, Year__c, Owner.Name, Region__c, Submit_Date__c FROM Log__c WHERE Is_Weekly_Summary__c = true and OwnerId = :currentUser Order By Year__c DESC, Month__c DESC, Week__c DESC ';
            if(String.isNotBlank(limitStr)) {
                limitNum = Integer.valueOf(limitStr);
                query += ' LIMIT :limitNum'; 
            }
            logs = Database.query(query);
        }
        Map<String, List<SummaryWrapper>> resultMap = new Map<String, List<SummaryWrapper>>();
        List<SummaryWrapper> summarys = new List<SummaryWrapper>();
        for(Log__c log : logs) {
            SummaryWrapper wrapper = new SummaryWrapper();
            wrapper.id = log.Id;
            wrapper.weeklyHighlights = log.Weekly_Highlights__c;
            wrapper.yearWeek = String.valueOf(log.Year__c) + '-' + String.valueOf(log.Week__c);
            wrapper.year = log.Year__c;
            wrapper.month = log.Month__c;
            wrapper.week = log.Week__c;
            wrapper.createdBy = log.Owner.Name;
            wrapper.region = log.Region__c;
            wrapper.competitiveInfo = log.Competitive_Info__c;
            wrapper.other = log.Other__c;
            wrapper.nextWeekFocus = log.Next_Week_Focus__c;
            if(wrapper.Submit_Date__c != null) {
                wrapper.showEdit = false;
            }
            summarys.add(wrapper);
        }
        resultMap.put('weeklySummarys', summarys);
        return JSON.serialize(resultMap);
    }

    @AuraEnabled
    public static void deleteRecordImp(String recordId) {
        List<Log__c> logs = [SELECT Id FROM Log__c WHERE Id = :recordId];
        delete logs;
    }

    private class SummaryWrapper {
        public String id {get; set;}
        public Boolean showView {get; set;}
        public Boolean showEdit {get; set;} 
        public Boolean showDelete {get; set;}
        public String yearWeek {get; set;}
        public String year {get; set;}
        public String month {get; set;}
        public String week {get; set;}
        public String createdBy {get; set;}
        public String region {get; set;}
        public String weeklyHighlights {get; set;}
        public String competitiveInfo {get; set;}
        public String other {get; set;}
        public String nextWeekFocus {get; set;}
        public SummaryWrapper() {
            this.showView = true;
            this.showEdit = true;
            this.showDelete = true;
        }
    }
}