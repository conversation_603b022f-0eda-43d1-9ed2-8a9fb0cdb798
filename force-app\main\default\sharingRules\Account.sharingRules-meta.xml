<?xml version="1.0" encoding="UTF-8"?>
<SharingRules xmlns="http://soap.sforce.com/2006/04/metadata">
    <sharingCriteriaRules>
        <fullName>CA_Operation_Team_Access</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CA Operation Team Access</label>
        <sharedTo>
            <role>CA_Operation_Team</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CCA</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>CCA_Customer_CA_BEAM</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CCA Customer CA BEAM</label>
        <sharedTo>
            <role>Nanjing_CA_BEAM</role>
        </sharedTo>
        <booleanFilter>1 AND 2</booleanFilter>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CCA</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>CCA_Customer_CA_Inside_Sales</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CCA Customer CA Inside Sales</label>
        <sharedTo>
            <role>CA_Inside_Sales</role>
        </sharedTo>
        <booleanFilter>1 AND 2</booleanFilter>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CCA</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>CCA_Customer_CA_TT_Sales_Manager</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CCA Customer CA TT Sales Manager</label>
        <sharedTo>
            <role>CA_TT_Sales_Manager</role>
        </sharedTo>
        <booleanFilter>1</booleanFilter>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CCA</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>CCA_Customer_For_CA_BEAM_Manager</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CCA Customer For CA BEAM Manager</label>
        <sharedTo>
            <role>Nanjing_CA_BEAM_Manager</role>
        </sharedTo>
        <booleanFilter>1 OR 2</booleanFilter>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CNA</value>
        </criteriaItems>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value></value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>CCA_Customer_For_CA_General_Manager</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CCA Customer For CA General Manager</label>
        <sharedTo>
            <role>CA_General_Manager</role>
        </sharedTo>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CCA</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>CCA_Customer_NA_Finance</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CCA Customer NA Finance</label>
        <sharedTo>
            <role>Finance</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CCA</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>CCA_Customer_Sales_Rep_1</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CCA Customer - Sales Rep 1</label>
        <sharedTo>
            <role>CA_Sales_Rep_1</role>
        </sharedTo>
        <booleanFilter>1 AND (2 OR 3 OR 4 OR 5)</booleanFilter>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CCA</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG 40;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG 38;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Authorized_Brand_Owner_Role__c</field>
            <operation>contains</operation>
            <value>CA_Sales_Rep_1</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>CA_Sales_Rep_1</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>CCA_Customer_Sales_Rep_2</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CCA Customer Sales Rep 2</label>
        <sharedTo>
            <role>CA_Sales_Rep_2</role>
        </sharedTo>
        <booleanFilter>1 AND (2 OR 3 OR 4)</booleanFilter>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CCA</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG 39;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>CA_Sales_Rep_2</value>
        </criteriaItems>
        <criteriaItems>
            <field>Authorized_Brand_Owner_Role__c</field>
            <operation>contains</operation>
            <value>CA_Sales_Rep_2</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>CCA_Customer_Sales_Rep_3</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CCA Customer Sales Rep 3</label>
        <sharedTo>
            <role>CA_Sales_Rep_3</role>
        </sharedTo>
        <booleanFilter>1 AND (2 OR 3 OR 4)</booleanFilter>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CCA</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG 41;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>CA_Sales_Rep_3</value>
        </criteriaItems>
        <criteriaItems>
            <field>Authorized_Brand_Owner_Role__c</field>
            <operation>contains</operation>
            <value>CA_Sales_Rep_3</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>CCA_Customer_Sales_Rep_4</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CCA Customer Sales Rep 4</label>
        <sharedTo>
            <role>CA_Sales_Rep_4</role>
        </sharedTo>
        <booleanFilter>1 AND (2 OR 3 OR 4)</booleanFilter>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CCA</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG 36;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>CA_Sales_Rep_4</value>
        </criteriaItems>
        <criteriaItems>
            <field>Authorized_Brand_Owner_Role__c</field>
            <operation>contains</operation>
            <value>CA_Sales_Rep_4</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>CCA_Customer_Sales_Rep_5</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>CCA Customer Sales Rep 5</label>
        <sharedTo>
            <role>CA_Sales_Rep_5</role>
        </sharedTo>
        <booleanFilter>1 AND (2 OR 3 OR 4)</booleanFilter>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CCA</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG 37;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>CA_Sales_Rep_5</value>
        </criteriaItems>
        <criteriaItems>
            <field>Authorized_Brand_Owner_Role__c</field>
            <operation>contains</operation>
            <value>CA_Sales_Rep_5</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Aspen_Marketing</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Aspen Marketing</label>
        <sharedTo>
            <role>Agency_Aspen_Marketing</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP32;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_BP_Sales</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to BP Sales</label>
        <sharedTo>
            <role>Agency_BP_Sales</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP09;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_BT_Global_Sales_Marketing_Corporation</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to BT Global Sales &amp; Marketing Corporation</label>
        <sharedTo>
            <role>Agency_BT_Global_Sales_Marketing_Corporation</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP18;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_CPS</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to CPS</label>
        <sharedTo>
            <role>Agency_CPS</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP17;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_CSI_Marketing_Inc</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to CSI Marketing Inc.</label>
        <sharedTo>
            <role>Agency_CSI_Marketing_Inc</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP15;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Canow_Western</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Canow-Western</label>
        <sharedTo>
            <role>Agency_Canow_Western</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP02;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Continental_Sales_Marketing</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Continental Sales &amp; Marketing</label>
        <sharedTo>
            <role>Agency_Continental_Sales_Marketing</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP01;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_District_Manager_West_1</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to District Manager West 1</label>
        <sharedTo>
            <role>District_Manager_West_I</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG08;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG08, Field Sales, TT-West&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG08_Sales_Manager;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_District_Manager_West_2</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to District Manager West 2</label>
        <sharedTo>
            <role>District_Manager_West_II</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG08;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG08, Field Sales, TT-West&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG08_Sales_Manager;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Field_Service</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Field Service</label>
        <sharedTo>
            <role>Field_Service</role>
        </sharedTo>
        <criteriaItems>
            <field>OwnerId</field>
            <operation>notEqual</operation>
            <value>null</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Finance_North_America</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Finance North America</label>
        <sharedTo>
            <role>Finance_NA</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Flex_Auto_Sales_Manager</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Flex Auto Sales Manager</label>
        <sharedTo>
            <role>Flex_Auto_Sales_Manager</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG28;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG28, Field Sales, Flex Auto, Chris Metcalf&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>Flex_Auto_Sales_Manager;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_GRS_Stohler</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to GRS Stohler</label>
        <sharedTo>
            <role>Agency_GRS_Stohler</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP26;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Hendrikse_Belden_and_Despres</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Hendrikse, Belden, and Despres</label>
        <sharedTo>
            <role>Agency_Hendrikse_Belden_and_Despres</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP30;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_ISI</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to ISI</label>
        <sharedTo>
            <role>Agency_ISI</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP04;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Inside_Sales</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Inside Sales</label>
        <sharedTo>
            <role>Sales_Operation</role>
        </sharedTo>
        <booleanFilter>1 AND (2 OR 3)</booleanFilter>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value>CNA</value>
        </criteriaItems>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>equals</operation>
            <value></value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_JSS_East</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to JSS East</label>
        <sharedTo>
            <role>Job_Site_Specialist_East</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG07</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG07, Field Sales, TT-East&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG07_Sales_Manager</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_JSS_West</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to JSS West</label>
        <sharedTo>
            <role>Job_Site_Specialist_West</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG08</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG08, Field Sales, TT-West&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG08_Sales_Manager</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_KA_Sales_Manager</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to KA Sales Manager</label>
        <sharedTo>
            <role>KA_Sales_Manager</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG04;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG04, Field Sales, TTKA, MS&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>KA_Sales_Manager;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_MK_Breckan_Associates</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to MK Breckan &amp; Associates</label>
        <sharedTo>
            <role>Agency_MK_Breckan_Associates</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP27;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Meridian_Pacific_Sales</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Meridian Pacific Sales</label>
        <sharedTo>
            <role>Agency_Meridian_Pacific_Sales</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP22;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Morris_Marketing</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Morris Marketing</label>
        <sharedTo>
            <role>Agency_Morris_Marketing</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP33;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_NA_Administrator</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>Edit</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>Edit</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to NA Administrator</label>
        <sharedTo>
            <role>NA_Administrator</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_NA_CS_Zedd</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>Edit</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to NA CS Zedd</label>
        <sharedTo>
            <role>NA_CS_Zedd</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_NA_Customer_Service</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>Edit</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to NA Customer Service</label>
        <sharedTo>
            <role>NA_Customer_Service</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Nanjing_BEAM</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>Edit</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>Edit</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Nanjing BEAM</label>
        <sharedTo>
            <role>BEAM</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <criteriaItems>
            <field>ORG_Code__c</field>
            <operation>notEqual</operation>
            <value>CCA</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Nanjing_Finance</fullName>
        <accessLevel>Read</accessLevel>
        <accountSettings>
            <caseAccessLevel>Read</caseAccessLevel>
            <contactAccessLevel>Read</contactAccessLevel>
            <opportunityAccessLevel>Read</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Nanjing Finance</label>
        <sharedTo>
            <role>Finance</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Nanjing_Sales_Operation_Team</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Nanjing Sales Operation Team</label>
        <sharedTo>
            <role>Nanjing_Sales_Operation_Team</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_National_Sales_Manager</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to National Sales Manager</label>
        <sharedTo>
            <role>National_Sales_Manager</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG05;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG05, Field Sales, TTKA, JS&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>National_Sales_Manager</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_North_American_Operation_Team</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to North American Operation Team</label>
        <sharedTo>
            <role>NA_Operation_Team</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_National_Manager</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE National Manager</label>
        <sharedTo>
            <role>OPE_National_Manager</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG21;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG21</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_National_Manager;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <!-- <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Pac_Southwest</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Pac-Southwest</label>
        <sharedTo>
            <role>OPE_Pac_Southwest</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG44</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG44 - OPE Pac-South (RG)</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Pac_Southwest</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules> -->
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Sales_Director</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Sales Director</label>
        <sharedTo>
            <role>OPE_Sales_Director</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG22;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG22 - OPE Key Accounts</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Sales_Director;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_MW_East</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - MW, East</label>
        <sharedTo>
            <role>OPE_Territory_Manager_MW_East</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG48</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG48</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_MW_East</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_Mid_Atlantic</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - Mid-Atlantic</label>
        <sharedTo>
            <role>OPE_Territory_Manager_North_East</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG24;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG24 - OPE Mid-Atlantic (BS)</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_North_East;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_Mid_West</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - Mid-West</label>
        <sharedTo>
            <role>OPE_Territory_Manager_Mid_West_New</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG46</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG46 - OPE Midwest (BL)</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_Mid_West_New</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_Mtn_West</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - Mtn-West</label>
        <sharedTo>
            <role>OPE_Territory_Manager_Mid_West</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG30;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG30 - OPE Mtn-West (AO)</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_Mid_West;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_New_England</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - New England</label>
        <sharedTo>
            <role>OPE_Territory_Manager_New_England</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG43</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG43 - OPE New England (TG)</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_New_England</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_Nocal_PNW</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - Nocal&amp;PNW</label>
        <sharedTo>
            <role>OPE_Territory_Manager_Nocal_PNW</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG31;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG31 - OPE Nor Cal (TH)</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_Nocal_PNW;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_North_East</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - North East</label>
        <sharedTo>
            <role>OPE_Territory_Manager_North_East_New</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG45</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG45 - OPE Northeast (JL)</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_North_East_New</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <!-- <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_PNW</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - PNW</label>
        <sharedTo>
            <role>OPE_Territory_Manager_PNW</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG47</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG47 - OPE PNW</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_PNW</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules> -->
    <!-- <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_SoCal</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - SoCal</label>
        <sharedTo>
            <role>OPE_Territory_Manager_SoCal</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG32;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG32 - OPE Pac-Central</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_SoCal;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules> -->
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_South_Central</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - South Central</label>
        <sharedTo>
            <role>OPE_Territory_Manager_South_Central</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG25;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG25 - OPE Southern States (ZL)</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_South_Central;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_South_East</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - South East</label>
        <sharedTo>
            <role>OPE_Territory_Manager_South_East</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG26;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG26 - Southeast (LB)</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_South_East;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_OPE_Territory_Manager_TX</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to OPE Territory Manager - TX</label>
        <sharedTo>
            <role>OPE_Territory_Manager_TX</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG23;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG23 - OPE South-Plains (JC)</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>OPE_Territory_Manager_TX</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Power_Tool_Incentives</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Power Tool Incentives</label>
        <sharedTo>
            <role>Agency_Power_Tool_Incentives</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP05;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Region_Sales_Manager_Central</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Region Sales Manager (Central)</label>
        <sharedTo>
            <role>SG06_Sales_Manager</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG06;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG06, Field Sales, TT-Central&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG06_Sales_Manager;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Region_Sales_Manager_East</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Region Sales Manager (East)</label>
        <sharedTo>
            <role>SG07_Sales_Manager</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG07;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG07, Field Sales, TT-East&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG07_Sales_Manager;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Region_Sales_Manager_West</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Region Sales Manager (West)</label>
        <sharedTo>
            <role>SG08_Sales_Manager</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG08;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG08, Field Sales, TT-West&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG08_Sales_Manager;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Rennebohm</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Rennebohm</label>
        <sharedTo>
            <role>Agency_Rennebohm</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP12;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_SG27</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <description>Share Account to ACE_Territory_Manager_Mid_West_Region, ACE_Territory_Manager_Northeast_Region, ACE_Territory_Manager_Southern_Region_I, ACE_Territory_Manager_Southern_Region_II, ACE_Territory_Manager_Upper_Midwest_Region, ACE_Territory_Manager_Western_Region</description>
        <label>Share Account to SG27</label>
        <sharedTo>
            <roleAndSubordinatesInternal>ACE_Hardware_Account_Manager</roleAndSubordinatesInternal>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3 OR 4 OR 5 OR 6 OR 7 OR 8 OR 9</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG27</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG27, OPE TM-MB&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>ACE_Territory_Manager_Mid_West_Region</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>ACE_Territory_Manager_Northeast_Region</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>ACE_Territory_Manager_Southern_Region_I</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>ACE_Territory_Manager_Southern_Region_II</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>ACE_Territory_Manager_Upper_Midwest_Region</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>ACE_Territory_Manager_Western_Region</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>ACE_Key_Account_Manager</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Sales_NW</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Sales NW</label>
        <sharedTo>
            <role>Agency_Sales_NW</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP21;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_Strategic_Marketing_Group_Inc</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to Strategic Marketing Group Inc.</label>
        <sharedTo>
            <role>Agency_Strategic_Marketing_Group_Inc</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP31;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_TR</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to TR</label>
        <sharedTo>
            <role>Territory_Representative_West_I</role>
        </sharedTo>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG08;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG08, Field Sales, TT-West&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG08_Sales_Manager;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_TR_2</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to TR 2</label>
        <sharedTo>
            <role>Territory_Representative_West_II</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG08;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG08, Field Sales, TT-West&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG08_Sales_Manager;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_TR_East</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to TR East</label>
        <sharedTo>
            <role>Territory_Representative_East</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG07</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG07, Field Sales, TT-East&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG07_Sales_Manager</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_TR_West</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to TR West</label>
        <sharedTo>
            <role>Territory_Representative_West</role>
        </sharedTo>
        <booleanFilter>1 OR 2 OR 3</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG08;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG08, Field Sales, TT-West&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG08_Sales_Manager;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_TR_West_1</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to TR West 1</label>
        <sharedTo>
            <role>Territory_Representative_West_I</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG08</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>&quot;SG08, Field Sales, TT-West&quot;</value>
        </criteriaItems>
        <criteriaItems>
            <field>Address_Owner_Role__c</field>
            <operation>contains</operation>
            <value>SG08_Sales_Manager</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_Account_to_The_Lawless_Group</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Account to The Lawless Group</label>
        <sharedTo>
            <role>Agency_The_Lawless_Group</role>
        </sharedTo>
        <criteriaItems>
            <field>Sales_Agencies__c</field>
            <operation>contains</operation>
            <value>REP13;</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_End_User_Account_to_JSS_TR</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share End User Account and Store Location to JSS/TR</label>
        <sharedTo>
            <group>JSS_TR</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Existing Store Location,Potential Store Location,End User Account</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_End_User_Account_to_Portal_Roles</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share End User Account to Portal Roles</label>
        <sharedTo>
            <group>Portal_Roles_Group</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <!-- <sharingCriteriaRules>
        <fullName>Share_OPE_TT_to_Jones_Jason</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share OPE TT to Jones Jason</label>
        <sharedTo>
            <group>Edit_OPE_TT_Customer_s_User</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>Channel</value>
        </criteriaItems>
        <criteriaItems>
            <field>Sales_Group__c</field>
            <operation>equals</operation>
            <value>SG21,SG22 - OPE Key Accounts,SG23 - OPE South-Plains (JC),SG24 - OPE Mid-Atlantic (BS),SG25 - OPE Southern States (ZL),SG26 - Southeast (LB),SG30 - OPE Mtn-West (AO),SG31 - OPE Nor Cal (TH),SG32 - OPE Pac-Central</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules> -->
    <sharingCriteriaRules>
        <fullName>Share_Service_Customer_to_Service_Group</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share Service Customer to Service Group</label>
        <sharedTo>
            <group>NA_Service_Team</group>
        </sharedTo>
        <criteriaItems>
            <field>Customer_Business_Type__c</field>
            <operation>equals</operation>
            <value>Service,Sales &amp; Service</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_WebSite_User</fullName>
        <accessLevel>Read</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Read</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share WebSite User</label>
        <sharedTo>
            <group>Share_WebSite_User</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>Share_to_CSR_TEMP</fullName>
        <accessLevel>Read</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Read</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share to CSR TEMP</label>
        <sharedTo>
            <role>NA_CS_Zedd</role>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account,Channel</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>share_to_Keoki_Landreth</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>None</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>share to Keoki Landreth</label>
        <sharedTo>
            <group>Keoki_Landreth</group>
        </sharedTo>
        <booleanFilter>1 or 2</booleanFilter>
        <criteriaItems>
            <field>Sales_Managers__c</field>
            <operation>contains</operation>
            <value>SG08;</value>
        </criteriaItems>
        <criteriaItems>
            <field>OwnerId</field>
            <operation>equals</operation>
            <value>0050h00000D7h1N</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>true</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingCriteriaRules>
        <fullName>share_to_guest</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>Read</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>share to guest</label>
        <sharedTo>
            <group>Administrator</group>
        </sharedTo>
        <criteriaItems>
            <field>RecordTypeId</field>
            <operation>equals</operation>
            <value>End User Account</value>
        </criteriaItems>
        <includeRecordsOwnedByAll>false</includeRecordsOwnedByAll>
    </sharingCriteriaRules>
    <sharingOwnerRules>
        <fullName>Share_End_User_Customer_to_Portal_Roles</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>Edit</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share End User Customer to Portal Roles</label>
        <sharedTo>
            <group>Portal_Roles_Group</group>
        </sharedTo>
        <sharedFrom>
            <group>Share_WebSite_User</group>
        </sharedFrom>
    </sharingOwnerRules>
    <sharingOwnerRules>
        <fullName>Share_End_User_Customers_to_NA_Customer_Service</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>Edit</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share End User Customers to NA Customer Service</label>
        <sharedTo>
            <role>NA_Customer_Service</role>
        </sharedTo>
        <sharedFrom>
            <group>Share_WebSite_User</group>
        </sharedFrom>
    </sharingOwnerRules>
    <sharingOwnerRules>
        <fullName>Share_End_User_Customers_to_Zedd</fullName>
        <accessLevel>Edit</accessLevel>
        <accountSettings>
            <caseAccessLevel>Edit</caseAccessLevel>
            <contactAccessLevel>Edit</contactAccessLevel>
            <opportunityAccessLevel>None</opportunityAccessLevel>
        </accountSettings>
        <label>Share End User Customers to Zedd</label>
        <sharedTo>
            <role>NA_CS_Zedd</role>
        </sharedTo>
        <sharedFrom>
            <group>Share_WebSite_User</group>
        </sharedFrom>
    </sharingOwnerRules>
</SharingRules>
