<aura:component description="CCM_Community_OrderApplicationDetail"
                implements="forceCommunity:availableForAllPageTypes,force:hasRecordId,flexipage:availableForAllPageTypes,force:appHostable"
                controller="CCM_Community_OrderApplicationDetailCtl">
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="poRecordId" type="String" default=""/>
    <aura:attribute name="currencySymbol" type="String" default="$"/>
    <aura:attribute name="brandScope" type="String" default=""/>
    <aura:attribute name="customerId" type="String" default=""/>
    <aura:attribute name="order" type="Object" default=""/>
    <aura:attribute name="orderItemList" type="List" default="[]"/>
    <aura:attribute name="shipmentInfo" type="List" default="[]"/>
    <aura:attribute name="invoiceInfo" type="List" default="[]"/>
    <aura:attribute name="shipmentColumns" type="List" default="[]"/>
    <aura:attribute name="invoiceColumns" type="List" default="[]"/>
    <aura:attribute name="processData" type="List" default="[]"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="currentStep" type="Integer" default="1"/>
    <aura:attribute name="isDelegate" type="Boolean" default="false"/>
    <aura:attribute name="paymentTermVal" type="String" default=""/>
    <aura:attribute name="freightTermVal" type="String" default=""/>
    <aura:attribute name="isInnerUser" type="Boolean" default="false"/>
    <aura:attribute name="isShow" type="Boolean" default="false"/>
    <aura:attribute name="hasAmwareProducts" type="Boolean" default="false"/>
    <aura:attribute name="hasAmwareShipments" type="Boolean" default="false"/>
    <aura:attribute name="isCanReverse" type="Boolean" default="false"/>
    <aura:attribute name="reverseOrderLink" type="String" default=""/>
    <aura:attribute name="isSampleOrder" type="Boolean" default="false"/>
    <aura:attribute name="dataInitComplete" type="Boolean" default="false" />

    <!-- ORG Code: CCA -->
    <aura:attribute name="isCCA" type="Boolean" default="false"/>

    <!-- 临时关闭订单 button 按钮 begin -->
    <aura:attribute name="blLockOrderFuncTmp" type="Boolean" default="false"/>
    <!-- 临时关闭订单 button 按钮 end -->

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

	<aura:if isTrue="{!v.dataInitComplete}">
    <div class="slds-box slds-theme_default">
        <div class="halp-step-box-tabset-box slds-p-horizontal--medium slds-p-top--medium">
            <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed' : 'slds-hide' }"/>
            <aura:if isTrue="{!v.order.orderStatus != 'Cancelled'}">  
                <c:CCM_PathProcess processData="{!v.processData}" currentStep="{!v.currentStep}"/>
            </aura:if>
            <c:CCM_Section title="{!$Label.c.CCM_Portal_BasicInformation}" expandable="true">
                <lightning:layout multipleRows="true">
                    <aura:if isTrue="{!v.isInnerUser}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Portal_OrderSource}">
                            {!v.order.orderSource}
                        </c:CCM_Field>
                        </lightning:layoutItem>
                        <aura:if isTrue="{!!v.isSampleOrder}">

                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_CustomerType}">
                                    {!v.order.customerType}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                        </aura:if>
                        
                    </aura:if>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Portal_OrderType}">
                            {!v.order.orderType}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <aura:if isTrue="{!v.isInnerUser}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="Customer Name">
                                <a href="{!('/' + v.order.customerId)}" target="_blank">{!v.order.customeName}</a>
                            </c:CCM_Field>
                        </lightning:layoutItem>
                        <aura:set attribute="else">
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_CustomerName}">
                                    {!v.order.customeName}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                        </aura:set>
                    </aura:if>

                    <aura:if isTrue="{!v.isInnerUser}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="{!$Label.c.CCM_Portal_OrderSubtype}">
                                {!v.order.orderSubType}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="{!$Label.c.CCM_Portal_AccountNumber}">
                                {!v.order.accountNumber}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                    </aura:if>
					<lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Portal_CustomerPONumber}">
                            {!v.order.customerPONum}
                        </c:CCM_Field>
                    </lightning:layoutItem> 
                    
                    <aura:if isTrue="{!!v.isInnerUser}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="{!$Label.c.CCM_Portal_PONumber}">
                                {!v.order.poNumber}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                    </aura:if>   
                    
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Portal_OrderNumberinSF}">
                            {!v.order.orderNumber}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    

                    <aura:if isTrue="{!v.isInnerUser}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="{!$Label.c.CCM_Portal_OrderNumberinOMS}">
                                {!v.order.orderOracleId}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                        <aura:if isTrue="{!!v.isSampleOrder}">
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_PurchaseOrderNumber}">
                                    <a href="{!('/lightning/n/Order_Apply_Detail_Page?0.recordId=' + v.order.orderId)}" target="_blank">{!v.order.poNumber}</a>
                                </c:CCM_Field>
                            </lightning:layoutItem>
                            <aura:set attribute="else">
                                <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                    <c:CCM_Field label="{!$Label.c.CCM_Portal_SampleOrderNumber}">
                                        <a href="{!('/' + v.order.sampleOrderId)}" target="_blank">{!v.order.poNumber}</a>
                                    </c:CCM_Field>
                                </lightning:layoutItem>
                            </aura:set>
                        </aura:if>
                    </aura:if>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Portal_OrderStatus}">
                            {!v.order.orderStatus}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <aura:if isTrue="{!!v.isSampleOrder}">
                        <aura:if isTrue="{!v.isInnerUser}">
                            <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                                <c:CCM_Field label="{!$Label.c.CCM_Portal_SalesGroup}">
                                    {!v.order.salesGroup}
                                </c:CCM_Field>
                            </lightning:layoutItem>
                        </aura:if>
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="{!$Label.c.CCM_Portal_Brand}">
                                {!v.order.brandScopeName}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="{!$Label.c.CCM_Portal_SalesManager}">
                                {!v.order.salesManagerName}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                    </aura:if>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Portal_PaymentTerm}">
                            {!v.paymentTermVal}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <aura:if isTrue="{!!v.isSampleOrder}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="{!$Label.c.CCM_Portal_SalesAgency}">
                                {!v.order.salesAgency}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                    </aura:if>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Portal_FreightTerm}">
                            {!v.freightTermVal}
                        </c:CCM_Field>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Portal_SubmitDate}">
                            {!v.order.submitDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <aura:if isTrue="{!!v.isSampleOrder}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="{!$Label.c.CCM_Portal_IsDropShipOrder}">
                                {!v.order.isDropShipOrder}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                    </aura:if>
                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Portal_CreatedBy}">
                            {!v.order.createdBy}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Portal_CreatedDate}">
                            {!v.order.createdDate}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <aura:if isTrue="{!v.order.orderCancelledType != 'Return'}">  
                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <p><strong>{!$Label.c.CCM_Portal_IsHold}</strong></p>
                                <p>{!v.order.isHold}</p>
                            </div>
                        </lightning:layoutItem>

                        <lightning:layoutItem padding="around-small" size="6">
                            <div class="header-column">
                                <p><strong>{!$Label.c.CCM_Portal_HoldReason}</strong></p>
                                <p>{!v.order.holdReason}</p>
                            </div>
                        </lightning:layoutItem>
                    </aura:if>

                    <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                        <c:CCM_Field label="{!$Label.c.CCM_Portal_IsCancelled}">
                            {!v.order.isCancelled}
                        </c:CCM_Field>
                    </lightning:layoutItem>

                    <aura:if isTrue="{!v.order.wholeOrderPromoCode}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="{!$Label.c.CCM_Portal_WholeOrderPromotion}">
                                {!v.order.wholeOrderPromoCode}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                     </aura:if>

                    <aura:if isTrue="{!v.order.paymentTermPromoCode}">
                        <lightning:layoutItem padding="horizontal-small" size="6" class="slds-p-bottom--small">
                            <c:CCM_Field label="{!$Label.c.CCM_Portal_PaymentTermPromotion}">
                                {!v.order.paymentTermPromoCode}
                            </c:CCM_Field>
                        </lightning:layoutItem>
                    </aura:if>

                </lightning:layout>
            </c:CCM_Section>
                
            <c:CCM_Section title="{!$Label.c.CCM_Portal_DeliveryInformation}" expandable="true" >
                <lightning:layout multipleRows="true">
                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <p><strong>{!$Label.c.CCM_Portal_BillingTo}</strong></p>
                            <p>
                                <lightning:formattedAddress
                                    street="{!(v.order.billingAddress1 + v.order.billingAddress2)}"
                                    city="{!v.order.billingAddressCity}"
                                    country="{!v.order.billingAddressCountry}"
                                    province="{!v.order.billingAddressState}"
                                    postalCode="{!v.order.billingAddressPostalCode}"/>
                                <ul>
                                    <li> {!v.order.billingAddressContactName}</li>
                                </ul>
                            </p>
                        </div>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <aura:if isTrue="{!v.isCCA}">
                                <p><strong>Chervon Canada, Inc.</strong></p>
                                <lightning:formattedAddress
                                    street="1-3480 Laird Rd."
                                    city="Mississauga"
                                    country="Canada"
                                    province="ON"
                                    postalCode="L5L 5Y4"/>
                                <p>{!v.order.salesManagerName}</p>
                                <aura:set attribute="else">
                                    <p><strong>Chervon North American</strong></p>
                                    <lightning:formattedAddress
                                        street="1203 East Warrenville Road"
                                        city="Naperville"
                                        country="US"
                                        province="IL"
                                        postalCode="60563"/>
                                    <p>{!v.order.salesManagerName}</p>
                                </aura:set>
                            </aura:if>
                            
                        </div>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="around-small" size="6">
                        <p><strong>{!$Label.c.CCM_Portal_ShippingTo}</strong></p>
                        <p>
                            <aura:if isTrue="{!v.order.isAlternativeAddress == false}">
                                <!-- <lightning:formattedAddress
                                    street="{!(v.order.shippingAddress1 + v.order.shippingAddress2)}"
                                    city="{!v.order.shippingAddressCity}"
                                    country="{!v.order.shippingAddressCountry}"
                                    province="{!v.order.shippingAddressState}"
                                    postalCode="{!v.order.shippingAddressPostalCode}"/>
                                <ul>
                                    <li> {!v.order.shippingAddressContactName}</li>
                                </ul> -->
                                <c:CCM_Field>
                                    <a href="{!('/' + v.order.shippingAddressId)}" target="_blank">{!v.order.shiptoName}</a>
                                </c:CCM_Field>
                                <aura:set attribute="else">
                                    <lightning:formattedAddress
                                        street="{!v.order.additionalShipAddressStreet}"
                                        city="{!v.order.additionalShipAddressCity}"
                                        country="{!v.order.additionalShipAddressCountry}"
                                        province="{!v.order.additionalShipAddressProvince}"
                                        postalCode="{!v.order.additionalShipAddressPostCode}"
                                    />
                                    <ul>
                                        <li> {!v.order.additionalContactName}</li>
                                        <li>{!v.order.additionalContactPhone}</li>
                                        <li>{!v.order.additionalContactEmail}</li>
                                    </ul>
                                </aura:set>
                            </aura:if>
                        </p>
                    </lightning:layoutItem>
                    <lightning:layoutItem padding="around-small" size="6">
                        <div>
                            <p><strong>{!$Label.c.CCM_Portal_ShippingBy}</strong></p>
                            <p>{!v.order.shippingByLabel}</p>
                        </div>
                        <aura:if isTrue="{!v.order.shippingBy != 'Chervon'}">
                            <div>
                                <p><strong>{!$Label.c.CCM_Portal_PreferCarrier}</strong></p>
                                <p>{!v.order.deliverySupplier}</p>
                            </div>
                            <div>
                                <p><strong>{!$Label.c.CCM_Portal_YourCustomerFreightAccount}</strong></p>
                                <p>{!v.order.freightAccount}</p>
                            </div>
                        <aura:set attribute="else">
                            <div>
                                <p><strong>{!$Label.c.CCM_Portal_ShippingMethod}</strong></p>
                                <p>{!v.order.shippingMethod}</p>
                            </div>
                        </aura:set>
                        </aura:if>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <p><strong>{!$Label.c.CCM_Portal_ExpectedDeliveryDate}</strong></p>
                            <p>{!v.order.expectedDeliveryDate}</p>
                        </div>
                    </lightning:layoutItem>

                    <!-- <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <p><strong>Promotion Code</strong></p>
                            <p>{!v.order.promotionCode}</p>
                        </div>
                    </lightning:layoutItem> -->

                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <p><strong>{!$Label.c.CCM_Portal_BuyerEmail}</strong></p>
                            <p>{!v.order.buyerEmail}</p>
                        </div>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="around-small" size="6">
                        <div class="header-column">
                            <p><strong>{!$Label.c.CCM_Portal_ShippingPriority}</strong></p>
                            <p>{!v.order.shippingPriority}</p>
                        </div>
                    </lightning:layoutItem>

                    <lightning:layoutItem padding="around-small" size="12">
                        <p><strong>{!$Label.c.CCM_Portal_ShippingandRoutingInstruction}</strong></p>
                        <p>{!v.order.notes}</p>
                    </lightning:layoutItem>
                </lightning:layout>
            </c:CCM_Section>

            <c:CCM_Section title="{!$Label.c.CCM_Portal_OrderItemInformation}" expandable="true" >
                <div class="slds-p-left_medium slds-p-right--medium">
                    <table class="slds-p-horizontal_medium slds-m-bottom_medium"></table>
                    <!-- <p><a href="{!v.reverseOrderLink}">Returns &amp; Replacements in batch</a></p> -->
                    <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols slds-table_striped productTable" role="grid">
                        <thead>
                            <tr class="slds-line-height_reset">
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Line}">{!$Label.c.CCM_Portal_Line}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_ProductDescription}">{!$Label.c.CCM_Portal_ProductDescription}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_ModelNum}">{!$Label.c.CCM_Portal_ModelNum}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Brand}">{!$Label.c.CCM_Portal_Brand}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_PromotionCode}">{!$Label.c.CCM_Portal_PromotionCode}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumXSWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_EstimatedShipDate}">{!$Label.c.CCM_Portal_EstimatedShipDate}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Qty}">{!$Label.c.CCM_Portal_Qty}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallXSWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate datepicker-table">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Unit}">{!$Label.c.CCM_Portal_Unit}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_ListPrice}">{!$Label.c.CCM_Portal_ListPrice}</span>
                                        </div>
                                    </a>
                                </th>
                                <!-- <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="Edited List Price">Edited List Price</span>
                                        </div>
                                    </a>
                                </th> -->
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_InvoicePrice}">{!$Label.c.CCM_Portal_InvoicePrice}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_Subtotal}">{!$Label.c.CCM_Portal_Subtotal}</span>
                                        </div>
                                    </a>
                                </th>
                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                            <span class="slds-truncate" title="{!$Label.c.CCM_Portal_LineStatus}">{!$Label.c.CCM_Portal_LineStatus}</span>
                                        </div>
                                    </a>
                                </th>
                                <aura:if isTrue="{! AND(v.order.orderCancelledType != 'Return', v.isInnerUser)}">
                                    <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable mediumWidth" scope="col" style="">
                                        <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                            <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                <span class="slds-truncate" title="{!$Label.c.CCM_Portal_HoldReason}">{!$Label.c.CCM_Portal_HoldReason}</span>
                                            </div>
                                        </a>
                                    </th>
                                </aura:if>
                                <!-- true -> 临时关闭功能， false -> 正常使用 -->
                                <aura:if isTrue="{!v.blLockOrderFuncTmp}">
                                    <aura:set attribute="else">
                                        <aura:if isTrue="{!v.isCanReverse}">
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col" style="">
                                                <a class="slds-th__action slds-text-link_reset" href="{!v.reverseOrderLink}" role="button" tabindex="0">
                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                        <lightning:button class="slds-p-horizontal_x-small" variant="brand" label="{!$Label.c.CCM_Portal_InitiateaClaimMultipleItems}" title="{!$Label.c.CCM_Portal_InitiateaClaimMultipleItems}" />
                                                    </div>
                                                </a>
                                            </th>
                                        </aura:if>
                                    </aura:set>
                                </aura:if>
                            </tr>
                        </thead>
                        <tbody>
                            <aura:iteration items="{!v.orderItemList}" var="orderItem" indexVar="index">
                                <tr aria-selected="false" class="slds-hint-parent" id="{!row_index}">
                                    <th scope="row">
                                        <div class="slds-truncate" title="">
                                            {!index + 1}
                                        </div>
                                    </th>
                                    <td role="gridcell" title="{!orderItem.productName}">
                                        <div class="slds-truncate clear-user-agent-styles" >{!orderItem.productName}</div>
                                    </td>

                                    <td role="gridcell" title="{!orderItem.productCode}">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            <span>{!orderItem.productCode}</span>
                                        </div>
                                    </td>

                                    <td role="gridcell" title="{!orderItem.brandName}">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            <span>{!orderItem.brandName}</span>
                                        </div>
                                    </td>

                                    <td role="gridcell" title="{!orderItem.promotionCode}">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            <span>{!orderItem.promotionCode}</span>
                                        </div>
                                    </td>

                                    <td role="gridcell" title="{!orderItem.shipDate}">
                                        <div class="slds-truncate clear-user-agent-styles">
                                            <span>{!orderItem.shipDate}</span>
                                        </div>
                                    </td>

                                    <td role="gridcell" title="{!orderItem.quantity}">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            <span>{!orderItem.quantity}</span>
                                        </div>
                                    </td>

                                    <td role="gridcell" title="EA">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            <span>EA</span>
                                        </div>
                                    </td>

                                    <td role="gridcell" title="{!orderItem.listPrice}">
                                        <div class="slds-truncate">
                                            <lightning:formattedNumber value="{!orderItem.listPrice}" style="currency" currencyCode="{!orderItem.currencyIsoCode}" currencyDisplayAs="code"/>
                                        </div>
                                    </td>

                                    <!-- <td role="gridcell" title="{!orderItem.editedListPrice}">
                                        <div class="slds-truncate">
                                            <lightning:formattedNumber value="{!orderItem.editedListPrice}" style="currency" currencyCode="{!orderItem.currencyIsoCode}" currencyDisplayAs="code"/>
                                        </div>
                                    </td> -->

                                    <td role="gridcell" title="{!orderItem.unitPrice}">
                                        <div class="slds-truncate">
                                            <lightning:formattedNumber value="{!orderItem.unitPrice}" style="currency" currencyCode="{!orderItem.currencyIsoCode}" currencyDisplayAs="code"/>
                                        </div>
                                    </td>

                                    <td role="gridcell" title="{!orderItem.subTotal}">
                                        <div class="slds-truncate">
                                            <lightning:formattedNumber value="{!orderItem.subTotal}" style="currency" currencyCode="{!orderItem.currencyIsoCode}" currencyDisplayAs="code"/>
                                        </div>
                                    </td>
                                    <td role="gridcell" title="{!orderItem.lineStatus}">
                                        <div class="slds-truncate clear-user-agent-styles" >
                                            <span>{!orderItem.lineStatus}</span>
                                        </div>
                                    </td>
                                    <aura:if isTrue="{! AND(v.orderCancelledType != 'Return', v.isInnerUser)}">
                                        <td role="gridcell" title="{!orderItem.holdStatus}">
                                            <div class="slds-truncate clear-user-agent-styles" >
                                                <span>{!orderItem.holdStatus}</span>
                                            </div>
                                        </td>
                                    </aura:if>

                                    <!-- true -> 临时关闭功能， false -> 正常使用 -->
                                    <aura:if isTrue="{!v.blLockOrderFuncTmp}">
                                        <aura:set attribute="else">
                                            <aura:if isTrue="{!v.isCanReverse}">
                                                <td role="gridcell">
                                                    <div>
                                                        <a href="{!orderItem.reverseOrderLink}" role="button" tabindex="0">
                                                            <lightning:button class="slds-p-horizontal_x-small" variant="brand" label="{!$Label.c.CCM_Portal_InitiateaClaim}" title="{!$Label.c.CCM_Portal_InitiateaClaim}" />
                                                        </a>
                                                    </div>
                                                </td>
                                            </aura:if>
                                        </aura:set>
                                    </aura:if>
                                </tr>
                            </aura:iteration>
                        </tbody>
                    </table>
                    <div class="slds-clearfix slds-m-top--medium">
                        <div class="slds-grid slds-float--right">
                        <div class="slds-text-align--right">
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_TotalQuantity}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_ProductAmount}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_DiscountAmount}:&nbsp;</div>
                            <aura:if isTrue="{!v.order.surchargeAmt > 0}">                                                    
                                <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_Surcharge}:</div>
                            </aura:if>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_FreightFee}:&nbsp;</div>
                            <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_HandlingFee}:&nbsp;</div>
                            <aura:if isTrue="{!v.isCCA}">
                                <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_QST}:</div>
                                <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_GST}:</div>
                                <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_Portal_HST}:</div>
                            </aura:if>
                            <div class="slds-border_bottom ccm_paddingTop" />
                            <div class="slds-truncate ccm_padding ccm_paddingTop" title=""><strong>{!$Label.c.CCM_Portal_TotalAmount}:&nbsp;</strong></div>
                        </div>
                        <div>
                            <div class="slds-truncate" title=""><strong>{!v.order.totalQuantity}</strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.order.productPrice + abs(v.order.discountAmt)}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/> </strong></div>
                            <div class="slds-truncate" title=""><strong class="ccm_fontColor2">-<lightning:formattedNumber value="{!abs(v.order.discountAmt)}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/> </strong></div>
                            <aura:if isTrue="{!v.order.surchargeAmt > 0}">
                                <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.order.surchargeAmt}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                            </aura:if>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.order.freightFee}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                            <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.order.handingFee}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                            <aura:if isTrue="{!v.isCCA}">
                                <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.order.QST}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.order.GST}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                                <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.order.HST}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/></strong></div>
                            </aura:if>
                            <div class="slds-border_bottom ccm_paddingTop" />
                            <div class="slds-truncate ccm_paddingTop" title=""><strong><lightning:formattedNumber value="{!v.order.totalAmount}" style="currency" currencyCode="{!v.currencySymbol}" currencyDisplayAs="code"/> </strong></div>
                        </div>

                    </div>
                    </div>
                </div>
            </c:CCM_Section>
            <div class="shipmentInfo">
                <c:CCM_Section title="{!$Label.c.CCM_Portal_ShipmentInformation}" expandable="true">
                    <div class="slds-p-left_medium slds-p-right--medium">
                        <aura:if isTrue="{!v.shipmentInfo.length > 0}">
                            <aura:iteration items="{!v.shipmentInfo}" var="shipment">
                                    <c:CCM_Section title="{!($Label.c.CCM_Portal_TrackingNumber + ' : ' + shipment.shipment.trackingNum + ' &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;')}" expandable="true" state="close">
                                    <lightning:layout multipleRows="true">
                                                                                
                                        <lightning:layoutItem padding="around-small" size="6">
                                            <div class="header-column">
                                                <p><strong>{!$Label.c.CCM_Portal_ShipDate}:</strong></p>
                                                <p>{!shipment.shipment.shipDate}</p>
                                            </div>
                                        </lightning:layoutItem>

                                        <lightning:layoutItem padding="around-small" size="6">
                                            <div class="header-column">
                                                <p><strong>{!$Label.c.CCM_Portal_ShipFrom}</strong></p>
                                                <p>
                                                    <lightning:formattedAddress
                                                            street="{!shipment.shipment.deliveryAddress}"
                                                            city="{!shipment.shipment.deliveryCity}"
                                                            country="{!shipment.shipment.deliveryCountry}"
                                                            province="{!shipment.shipment.deliveryState}"
                                                            postalCode="{!shipment.shipment.deliveryPostalCode}"/>
                                                </p>
                                            </div>
                                        </lightning:layoutItem>


                                        <lightning:layoutItem padding="around-small" size="6">
                                            <div class="header-column">
                                                <p><strong>{!$Label.c.CCM_Portal_ShipTo}</strong></p>
                                                <lightning:formattedAddress
                                                        street="{!shipment.shipment.receiptAddress}"
                                                        city="{!shipment.shipment.receiptCity}"
                                                        country="{!shipment.shipment.receiptCountry}"
                                                        province="{!shipment.shipment.receiptState}"
                                                        postalCode="{!shipment.shipment.receiptPostalCode}"/>
                                            </div>
                                        </lightning:layoutItem>
                                        <lightning:layoutItem padding="around-small" size="6">
                                            <div class="header-column">
                                                <p><strong>{!$Label.c.CCM_Portal_ExpectedDeliveryDate}</strong></p>
                                                <p>{!shipment.shipment.forecastDate}</p>
                                            </div>
                                        </lightning:layoutItem>

                                        <lightning:layoutItem padding="around-small" size="6">
                                            <div class="header-column">
                                                <p><strong>{!$Label.c.CCM_Portal_ShipMethod}</strong></p>
                                                <p>{!shipment.shipment.shipMethod}</p>
                                            </div>
                                        </lightning:layoutItem>
                                        <lightning:layoutItem padding="around-small" size="6">
                                            <div class="header-column">
                                                <p><strong>{!$Label.c.CCM_Portal_Carrier}</strong></p>
                                                <p>{!shipment.shipment.shipper}</p>
                                            </div>
                                        </lightning:layoutItem>

                                        <lightning:layoutItem padding="around-small" size="6">
                                            <div class="header-column">
                                                <p><strong>{!$Label.c.CCM_Portal_Weight}</strong></p>
                                                <p>{!shipment.shipment.weight} &nbsp; {!shipment.shipment.weightUnit} </p>
                                            </div>
                                        </lightning:layoutItem>
                                    </lightning:layout>
                                    <div class="slds-p-left--medium slds-p-right--medium">
                                        <c:CCM_Section title="{!$Label.c.CCM_Portal_ShipmentItemInformation}" expandable="true" state="close">
                                            <c:CCM_DataTable columns="{!v.shipmentColumns}" data="{!shipment.shipmentItemSummary}"/>
                                        </c:CCM_Section>
                                    </div>
                                    <!-- <div>
                                        <c:CCM_Section title="Shipment Item Detail" expandable="true" state="close">
                                            <c:CCM_DataTable columns="{!v.shipmentColumns}" data="{!shipment.shipmentItems}"/>
                                        </c:CCM_Section>
                                    </div> -->
                                </c:CCM_Section>
                            </aura:iteration>
                            <aura:set attribute="else">
                                <p class="slds-text-align_center slds-p-around--medium">{!$Label.c.CCM_Portal_TableNoDataTips}</p>
                            </aura:set>
                        </aura:if>
                    </div>
                </c:CCM_Section>
            </div>

            <aura:if isTrue="{!!v.isSampleOrder}">
                <c:CCM_Section title="{!$Label.c.CCM_Portal_InvoiceInformation}" expandable="true">
                    <c:CCM_DataTable columns="{!v.invoiceColumns}" data="{!v.invoiceInfo}"/>
                </c:CCM_Section>
            </aura:if>
            

            <!-- <aura:if isTrue="{!v.isDelegate}">
                <c:CCM_ApprovalHistory objectId="{!v.poRecordId}"/>
            </aura:if> -->

            <div class="CCM_PaddingTop slds-m-bottom_medium">
                <aura:if isTrue="{!v.hasAmwareProducts}">
                    <aura:if isTrue="{!not(v.hasAmwareShipments)}">
                        <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Portal_AddAmwareShipment}" title="{!$Label.c.CCM_Portal_AddAmwareShipment}" onclick="{!c.showAddAmwareShipment}"/>
                    </aura:if>
                </aura:if>
                <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Portal_Back}" title="{!$Label.c.CCM_Portal_Back}" onclick="{!c.doBack}"/>
            </div>
        </div>
    </div>
	</aura:if>
    
    <!-- amware shipment dialog -->
    <aura:if isTrue="{!v.isShow}">
        <c:ccmAddAmwareShipment 
            oncancel="{!c.hiddenAddAmwareShipment}"
            recordId="{!v.recordId}">
        </c:ccmAddAmwareShipment>
    </aura:if>
</aura:component>